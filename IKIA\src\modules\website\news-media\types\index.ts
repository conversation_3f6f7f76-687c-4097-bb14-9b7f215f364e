// types/NewsItem.ts

export interface MediaSize {
  url: string | null
  width: number | null
  height: number | null
  mimeType: string | null
  filesize: number | null
  filename: string | null
}

export interface Media {
  id: number
  alt: string
  caption: string | null
  updatedAt: string
  createdAt: string
  url: string
  thumbnailURL: string
  filename: string
  mimeType: string
  filesize: number
  width: number
  height: number
  focalX: number
  focalY: number
  sizes: {
    thumbnail: MediaSize
    square: MediaSize
    small: MediaSize
    medium: MediaSize
    large: MediaSize
    xlarge: MediaSize
    og: MediaSize
  }
}

export type NewsCategory =
  | 'news-feed'
  | 'press-releases'
  | 'blogs'
  | 'social-media'
  | 'event-updates'

export type NewsType = 'featured' | 'highlight' | 'regular'

export interface NewsItem {
  id: number
  title: string
  summary: string
  content: string | null
  image: Media | null
  eventDate: string | null // ISO date
  category: NewsCategory
  type: NewsType
  author?: string
  slug: string | null
  slugLock: boolean
  updatedAt: string
  createdAt: string
}
