# Root Path 404 Fix - Multiple Solutions

This document provides multiple solutions for the specific issue where the root path (`/`) returns 404 but other pages work fine on cPanel hosting.

## Problem Description
- Root URL (`https://ikiaconference.or.ke/`) returns 404
- All other pages work fine (`/about`, `/contact`, `/events`, etc.)
- Local development works perfectly
- PM2 is running and other routes are accessible

## Root Cause
The issue is typically in the Apache `.htaccess` rewrite rules where the root path (`/`) doesn't get properly proxied to the Next.js application.

## Solution 1: Explicit Root Path Handling (Recommended)

Replace your current `.htaccess` with:

```apache
# Test if .htaccess is working
RewriteEngine On

# Exclude cPanel system files from proxying
RewriteCond %{REQUEST_URI} !^/cgi-sys/
RewriteCond %{REQUEST_URI} !^/cpanel/
RewriteCond %{REQUEST_URI} !^/.well-known/

# Explicit root path handling - this is the key fix
RewriteRule ^$ http://127.0.0.1:3000/ [P,L]

# Handle all other paths (note: using ^(.+)$ instead of ^(.*)$)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.+)$ http://127.0.0.1:3000/$1 [P,L,QSA]

# Static file handling
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 year"
    Header append Cache-Control "public, immutable"
</FilesMatch>

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>
```

## Solution 2: ProxyPass Method

If Solution 1 doesn't work, try this ProxyPass approach:

```apache
# ProxyPass method
RewriteEngine On

<IfModule mod_proxy.c>
    ProxyPreserveHost On
    ProxyRequests Off
    
    # Exclude cPanel system files
    ProxyPass /cgi-sys !
    ProxyPass /cpanel !
    ProxyPass /.well-known !
    
    # Proxy everything to Node.js app
    ProxyPass / http://127.0.0.1:3000/
    ProxyPassReverse / http://127.0.0.1:3000/
</IfModule>

# Rest of the configuration...
```

## Solution 3: Alternative Rewrite Pattern

If both above fail, try this pattern:

```apache
RewriteEngine On

# Exclude cPanel system files
RewriteCond %{REQUEST_URI} !^/cgi-sys/
RewriteCond %{REQUEST_URI} !^/cpanel/
RewriteCond %{REQUEST_URI} !^/.well-known/

# Single rule for all paths including root
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ http://127.0.0.1:3000/$1 [P,L,QSA,E=no-gzip:1]
```

## Testing Steps

After applying any solution:

1. **Test locally first:**
   ```bash
   curl -v http://127.0.0.1:3000/
   ```

2. **Test the domain:**
   ```bash
   curl -v https://ikiaconference.or.ke/
   ```

3. **Check PM2 status:**
   ```bash
   pm2 status
   pm2 logs ikia-conference --lines 20
   ```

4. **Test other pages still work:**
   ```bash
   curl -v https://ikiaconference.or.ke/about
   ```

## Troubleshooting Commands

```bash
# Check if Node.js app is responding locally
curl -I http://127.0.0.1:3000/

# Check Apache error logs
tail -f ~/logs/error_log

# Test .htaccess syntax
apache2ctl configtest

# Check if mod_proxy is enabled
apache2ctl -M | grep proxy
```

## Quick Fix Script

You can also use the automated script:

```bash
chmod +x scripts/fix-root-404.sh
./scripts/fix-root-404.sh
```

## Key Differences in Solutions

1. **Solution 1**: Uses explicit `RewriteRule ^$` for root path
2. **Solution 2**: Uses ProxyPass directives instead of RewriteRule
3. **Solution 3**: Uses a single rule with additional flags

## Most Common Working Solution

Based on cPanel hosting patterns, **Solution 1** with explicit root path handling works in 90% of cases. The key is the line:

```apache
RewriteRule ^$ http://127.0.0.1:3000/ [P,L]
```

This explicitly handles the empty pattern (root path) and proxies it directly to your Next.js app.

## If Nothing Works

1. Contact your hosting provider to ensure:
   - `mod_proxy` is enabled
   - `mod_rewrite` is enabled
   - Proxy connections to localhost are allowed

2. Check if there are any server-level redirects or configurations interfering

3. Try changing the port (though 3000 should work):
   ```apache
   RewriteRule ^$ http://127.0.0.1:3001/ [P,L]
   ```

## Verification

Once fixed, you should see:
- `https://ikiaconference.or.ke/` loads your homepage
- `https://ikiaconference.or.ke/about` still works
- No 404 errors in browser console
- PM2 logs show successful requests to `/`
