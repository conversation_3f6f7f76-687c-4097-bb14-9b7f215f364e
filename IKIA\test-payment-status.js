#!/usr/bin/env node

/**
 * Test script for payment status endpoint
 * Tests both local and external API calls
 */

const BASE_URL = 'http://localhost:3000/api'

async function testPaymentStatus() {
  console.log('🧪 PAYMENT STATUS ENDPOINT TEST')
  console.log('=' .repeat(50))
  
  const testCases = [
    { ref: '5', description: 'Simple ref_no=5' },
    { ref: 'PAID123456', description: 'Paid status test' },
    { ref: 'PARTIAL123456', description: 'Partial payment test' },
    { ref: 'INV123456', description: 'Pending payment test' },
  ]
  
  for (const testCase of testCases) {
    console.log(`\n📋 Testing: ${testCase.description}`)
    console.log(`   Request: GET ${BASE_URL}/invoice/payment/status?ref_no=${testCase.ref}`)
    
    try {
      const startTime = Date.now()
      const response = await fetch(`${BASE_URL}/invoice/payment/status?ref_no=${testCase.ref}`)
      const endTime = Date.now()
      
      console.log(`   Response: ${response.status} ${response.statusText} (${endTime - startTime}ms)`)
      
      const data = await response.json()
      console.log('   Data:', JSON.stringify(data, null, 2))
      
      if (response.ok) {
        console.log('   ✅ Success')
      } else {
        console.log('   ❌ Error')
      }
      
    } catch (error) {
      console.error(`   ❌ Network Error:`, error.message)
    }
  }
  
  console.log('\n🔧 EXPECTED BEHAVIOR:')
  console.log('1. If PESAFLOW_UAT_SERVER_URL is set:')
  console.log('   - Should call external API: https://uat.ecitizen.go.ke/api/PaymentAPI/paymentstatus')
  console.log('   - Should log detailed request/response information')
  console.log('   - Should fall back to local if external fails')
  console.log('2. If PESAFLOW_UAT_SERVER_URL is empty:')
  console.log('   - Should use local mock data only')
  console.log('3. Local mock responses:')
  console.log('   - PAID* → status: "settled"')
  console.log('   - PARTIAL* → status: "partial"')
  console.log('   - Other → status: "pending"')
}

// Run the test
testPaymentStatus().catch(console.error)
