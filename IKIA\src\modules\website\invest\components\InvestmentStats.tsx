"use client"

import { useState, useEffect } from "react"
import { DollarSign, TrendingUp, Users, Building } from "lucide-react"

const stats = [
  {
    icon: DollarSign,
    value: 25000000,
    suffix: "+",
    label: "Total Investment Potential",
    description: "USD in IKIA opportunities",
    color: "from-[#159147] to-[#81B1DB]",
  },
  {
    icon: TrendingUp,
    value: 85,
    suffix: "%",
    label: "Success Rate",
    description: "Of funded projects thriving",
    color: "from-[#E8B32C] to-[#C86E36]",
  },
  {
    icon: Users,
    value: 1200,
    suffix: "+",
    label: "Active Investors",
    description: "Global investment community",
    color: "from-[#7E2518] to-[#C86E36]",
  },
  {
    icon: Building,
    value: 150,
    suffix: "+",
    label: "IKIA Projects",
    description: "Ready for investment",
    color: "from-[#81B1DB] to-[#159147]",
  },
]

function AnimatedCounter({ value, duration = 2000 }: { value: number; duration?: number }) {
  const [count, setCount] = useState(0)

  useEffect(() => {
    let startTime: number
    let animationFrame: number

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)

      setCount(Math.floor(progress * value))

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate)
      }
    }

    animationFrame = requestAnimationFrame(animate)
    return () => cancelAnimationFrame(animationFrame)
  }, [value, duration])

  return <span>{count.toLocaleString()}</span>
}

export default function InvestmentStats() {
  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="font-myriad font-bold text-sm uppercase tracking-wider text-muted-foreground mb-4" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
            INVESTMENT IMPACT
          </h2>
          <h3 className="font-myriad font-bold text-3xl lg:text-4xl text-primary mb-6" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
            Investment Impact at Scale
          </h3>
          <p className="font-myriad text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
            Our platform has facilitated meaningful connections between investors and Indigenous Knowledge holders,
            creating sustainable economic opportunities while preserving cultural heritage.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="border-2 border-muted hover:border-secondary/40 transition-all duration-300 hover:shadow-lg group bg-background"
            >
              <div className="p-6 text-center space-y-4">
                {/* Icon */}
                <div className="flex justify-center">
                  <div className={`w-12 h-12 bg-gradient-to-br ${stat.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                    <stat.icon className="w-6 h-6 text-white" />
                  </div>
                </div>

                {/* Value */}
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-primary" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                    {stat.value >= 1000000 ? (
                      <>
                        $<AnimatedCounter value={stat.value / 1000000} />M
                      </>
                    ) : (
                      <>
                        <AnimatedCounter value={stat.value} />
                        {stat.suffix}
                      </>
                    )}
                  </div>
                  <h4 className="font-myriad font-bold text-lg text-primary" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                    {stat.label}
                  </h4>
                  <p className="font-myriad text-sm text-muted-foreground" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                    {stat.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-primary to-secondary p-8 text-primary-foreground">
            <h3 className="text-2xl font-bold mb-4" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>Ready to Make an Impact?</h3>
            <p className="text-lg mb-6 opacity-90" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
              Join our community of forward-thinking investors and be part of preserving Indigenous wisdom while
              generating sustainable returns.
            </p>
            <a href="/registration/form/investor">
              <button className="bg-background text-primary font-bold px-8 py-4 hover:bg-muted transition-colors transform hover:scale-105 duration-300" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                Start Investing Today
              </button>
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}
