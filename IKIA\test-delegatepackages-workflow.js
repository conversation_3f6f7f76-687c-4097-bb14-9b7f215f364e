#!/usr/bin/env node

/**
 * Test script for the DelegatePackages Workflow
 * Tests the renamed collection and updated endpoints
 */

const BASE_URL = 'http://localhost:3000'

async function apiCall(endpoint, method = 'GET', data = null, headers = {}) {
  const url = `${BASE_URL}${endpoint}`
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  }

  if (data) {
    options.body = JSON.stringify(data)
  }

  try {
    const response = await fetch(url, options)
    const result = await response.json()
    
    console.log(`\n📡 ${method} ${endpoint}`)
    console.log(`Status: ${response.status}`)
    console.log('Response:', JSON.stringify(result, null, 2))
    
    return { response, result }
  } catch (error) {
    console.error(`❌ Error calling ${endpoint}:`, error.message)
    return { error }
  }
}

async function getAuthToken() {
  try {
    const response = await fetch(`${BASE_URL}/api/users/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '01000010',
      }),
    })

    const result = await response.json()
    
    if (response.ok && result.token) {
      console.log('✅ Authentication successful')
      return result.token
    } else {
      console.log('❌ Authentication failed:', result.error || result.message)
      return null
    }
  } catch (error) {
    console.error('❌ Authentication error:', error.message)
    return null
  }
}

async function createTestDelegatePackage(packageData, token) {
  try {
    const response = await fetch(`${BASE_URL}/api/delegatepackages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `JWT ${token}`,
      },
      body: JSON.stringify(packageData),
    })

    const result = await response.json()
    
    if (response.ok) {
      console.log(`✅ Created delegate package: ${result.doc?.name || result.name}`)
      return result
    } else {
      console.log(`❌ Failed to create package: ${result.error || result.message}`)
      return null
    }
  } catch (error) {
    console.error(`❌ Error creating package:`, error.message)
    return null
  }
}

async function testDelegatePackagesWorkflow() {
  console.log('📦 Testing DelegatePackages Workflow')
  console.log('=' .repeat(60))

  // Get authentication token
  const token = await getAuthToken()
  if (!token) {
    console.log('❌ Cannot proceed without authentication')
    return
  }

  // Test 1: Check if delegatepackages API endpoint exists
  console.log('\n1️⃣ Testing DelegatePackages API Endpoint')
  const packagesTest = await apiCall('/api/delegatepackages')
  
  if (packagesTest.response?.status === 200) {
    console.log('✅ DelegatePackages API endpoint is working')
    console.log('Existing packages:', packagesTest.result?.docs?.length || 0)
  } else {
    console.log('❌ DelegatePackages API endpoint failed')
  }

  // Test 2: Create test delegate packages
  console.log('\n2️⃣ Creating Test Delegate Packages')
  
  const testPackages = [
    {
      name: 'Basic Delegate Package',
      description: 'Essential package for individual delegates',
      price: 15000,
      currency: 'KES',
      duration: '3 days',
      packageType: 'delegate',
      isActive: true,
      isFeatured: false,
      displayOrder: 1,
      features: [
        { feature: 'Conference access', included: true },
        { feature: 'Welcome kit', included: true },
        { feature: 'Lunch and refreshments', included: true },
        { feature: 'Certificate of attendance', included: true }
      ],
      inclusions: {
        conferenceAccess: true,
        meals: 'lunch',
        materials: true,
        certificate: true,
        networking: false,
        accommodation: false,
        transport: false
      }
    },
    {
      name: 'Premium Exhibition Package',
      description: 'Comprehensive package for exhibitors',
      price: 45000,
      currency: 'KES',
      duration: '3 days',
      packageType: 'exhibition',
      isActive: true,
      isFeatured: true,
      displayOrder: 2,
      features: [
        { feature: '3x6m exhibition space', included: true },
        { feature: 'Premium furniture package', included: true },
        { feature: 'Electricity and internet', included: true },
        { feature: 'Marketing materials', included: true },
        { feature: 'Networking events access', included: true }
      ],
      inclusions: {
        conferenceAccess: true,
        meals: 'all',
        materials: true,
        certificate: true,
        networking: true,
        accommodation: false,
        transport: false
      }
    }
  ]

  let createdPackages = []
  for (const packageData of testPackages) {
    const result = await createTestDelegatePackage(packageData, token)
    if (result) {
      createdPackages.push(result)
    }
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  console.log(`\n✅ Successfully created ${createdPackages.length}/${testPackages.length} delegate packages`)

  // Test 3: Test citizen registration with delegate packages
  console.log('\n3️⃣ Testing Citizen Registration with DelegatePackages')
  
  if (createdPackages.length > 0) {
    const selectedPackage = createdPackages[0].doc || createdPackages[0]
    
    const citizenData = {
      name: 'Test Delegate User',
      email: '<EMAIL>',
      phone_number: '254712345687',
      id_number: '87654329',
      county: '1', // Assuming county ID 1 exists
      password: 'TestPass123!',
      selected_package: selectedPackage.id,
      business_type: 'Individual Delegate',
      registration_purpose: 'Conference Attendance'
    }

    const citizenRegistration = await apiCall('/api/citizens/register', 'POST', citizenData)
    
    if (citizenRegistration.result?.success) {
      console.log('✅ Citizen registration with delegate package successful!')
      console.log('User ID:', citizenRegistration.result.data.user.id)
      console.log('Invoice ID:', citizenRegistration.result.data.invoice.id)
    } else {
      console.log('❌ Citizen registration failed:', citizenRegistration.result?.error)
    }
  }

  // Test 4: Test exhibitor registration with delegate packages
  console.log('\n4️⃣ Testing Exhibitor Registration with DelegatePackages')
  
  if (createdPackages.length > 1) {
    const selectedPackage = createdPackages[1].doc || createdPackages[1]
    
    const exhibitorData = {
      firstName: 'Jane',
      lastName: 'Exhibitor',
      email: '<EMAIL>',
      phone: '************',
      position: 'Marketing Director',
      country: 'Kenya',
      city: 'Nairobi',
      hasCompany: true,
      companyName: 'Traditional Arts Ltd',
      selectedPackage: selectedPackage.id,
      termsAccepted: true,
      exhibitorGuidelines: true
    }

    const exhibitorRegistration = await apiCall('/api/exhibitors/register', 'POST', exhibitorData)
    
    if (exhibitorRegistration.result?.success) {
      console.log('✅ Exhibitor registration with delegate package successful!')
      console.log('Exhibitor ID:', exhibitorRegistration.result.data.exhibitor.id)
      console.log('Invoice ID:', exhibitorRegistration.result.data.invoice.id)
    } else {
      console.log('❌ Exhibitor registration failed:', exhibitorRegistration.result?.error)
    }
  }

  // Test 5: Verify frontend can load delegate packages
  console.log('\n5️⃣ Testing Frontend Package Loading')
  const frontendPackagesTest = await apiCall('/api/delegatepackages')
  
  if (frontendPackagesTest.result?.docs?.length > 0) {
    console.log('✅ Frontend can load delegate packages')
    console.log('Available packages for frontend:')
    frontendPackagesTest.result.docs.forEach(pkg => {
      console.log(`   • ${pkg.name} - ${pkg.currency} ${pkg.price} (${pkg.packageType})`)
    })
  } else {
    console.log('❌ Frontend cannot load delegate packages')
  }

  console.log('\n' + '='.repeat(60))
  console.log('🎉 DelegatePackages Workflow Test Complete!')
  console.log('\n📋 Summary:')
  console.log('   • DelegatePackages collection ✅')
  console.log('   • API endpoint (/api/delegatepackages) ✅')
  console.log('   • Package creation ✅')
  console.log('   • Citizen registration integration ✅')
  console.log('   • Exhibitor registration integration ✅')
  console.log('   • Frontend package loading ✅')
  console.log('\n🌐 Form URLs:')
  console.log('   • Guest: http://localhost:3000/registration/form/guest')
  console.log('   • Exhibitor: http://localhost:3000/registration/form/exhibitor')
  console.log('📊 Admin Panel: http://localhost:3000/admin/collections/delegatepackages')
}

// Run the test
testDelegatePackagesWorkflow().catch(console.error)
