# IKIA Conference - Production Deployment Guide

This guide provides comprehensive instructions for deploying the IKIA Conference application using PM2 and Apache in a production environment.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Server Setup](#server-setup)
3. [PM2 Installation and Configuration](#pm2-installation-and-configuration)
4. [Apache Configuration](#apache-configuration)
5. [Application Deployment](#application-deployment)
6. [Monitoring and Maintenance](#monitoring-and-maintenance)
7. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements
- **Operating System**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **Node.js**: Version 18.20.2+ or 20.9.0+
- **Memory**: Minimum 2GB RAM (4GB+ recommended)
- **Storage**: Minimum 10GB free space
- **Network**: Open ports 80, 443, and 3000

### Required Software
- Node.js and npm/pnpm
- Apache HTTP Server
- PM2 Process Manager
- Git
- SSL Certificate (for HTTPS)

## Server Setup

### 1. Update System Packages

```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

### 2. Install Node.js

```bash
# Using NodeSource repository (recommended)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

### 3. Install pnpm (if using pnpm)

```bash
npm install -g pnpm
```

### 4. Create Application User

```bash
sudo useradd -m -s /bin/bash ikiaconferenceor
sudo usermod -aG sudo ikiaconferenceor
sudo su - ikiaconferenceor
```

### 5. Create Directory Structure

```bash
mkdir -p /home/<USER>/public_html
mkdir -p /home/<USER>/logs
mkdir -p /home/<USER>/backups
```

## PM2 Installation and Configuration

### 1. Install PM2 Globally

```bash
npm install -g pm2
```

### 2. Configure PM2 Startup

```bash
# Generate startup script
pm2 startup

# Follow the instructions provided by the command above
# Usually involves running a command with sudo
```

### 3. PM2 Configuration

The application includes an optimized PM2 configuration file (`ecosystem.config.cjs`) with the following features:

- **Cluster Mode**: Utilizes all available CPU cores
- **Auto-restart**: Automatic restart on crashes
- **Memory Management**: Restart when memory usage exceeds 1GB
- **Health Checks**: Built-in health monitoring
- **Log Management**: Structured logging with rotation
- **Environment Management**: Support for development, staging, and production

### 4. Environment Variables

Create a `.env` file in your application directory:

```bash
cd /home/<USER>/public_html
cp .env.example .env
nano .env
```

Configure the following variables:
```env
NODE_ENV=production
PORT=3000
DATABASE_URL=your_database_url
PAYLOAD_SECRET=your_secret_key
# Add other environment variables as needed
```

## Apache Configuration

### 1. Install Apache

```bash
# Ubuntu/Debian
sudo apt install apache2 -y

# CentOS/RHEL
sudo yum install httpd -y
```

### 2. Enable Required Modules

```bash
sudo a2enmod proxy
sudo a2enmod proxy_http
sudo a2enmod headers
sudo a2enmod expires
sudo a2enmod deflate
sudo a2enmod rewrite
sudo a2enmod ssl  # For HTTPS
```

### 3. Configure Virtual Host

Copy the provided `apache-vhost.conf` to your Apache sites directory:

```bash
sudo cp /home/<USER>/public_html/apache-vhost.conf /etc/apache2/sites-available/ikia-conference.conf
```

The configuration is already set up for the production domain `ikiaconference.or.ke`.

If you need to modify the configuration:

```bash
sudo nano /etc/apache2/sites-available/ikia-conference.conf
```

The configuration includes:
- Domain: `ikiaconference.or.ke` and `www.ikiaconference.or.ke`
- HTTP to HTTPS redirect
- SSL certificate paths for Let's Encrypt
- Security headers and caching optimizations

### 4. Enable the Site

```bash
sudo a2ensite ikia-conference.conf
sudo a2dissite 000-default.conf  # Disable default site
sudo systemctl reload apache2
```

### 5. Set Up SSL Certificate

For production deployment with HTTPS, you need to set up an SSL certificate. See the detailed `SSL_SETUP_GUIDE.md` for complete instructions.

**Quick Let's Encrypt setup:**

```bash
# Install Certbot
sudo apt install certbot python3-certbot-apache -y

# Obtain SSL certificate
sudo certbot certonly --standalone -d ikiaconference.or.ke -d www.ikiaconference.or.ke

# The certificates will be automatically configured in the Apache virtual host
```

### 6. Configure Firewall

```bash
# Ubuntu (UFW)
sudo ufw allow 'Apache Full'
sudo ufw allow ssh
sudo ufw enable

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

## Application Deployment

### 1. Clone Repository

```bash
cd /home/<USER>/public_html
git clone https://github.com/apprenticecloud/IKIA.git .
```

### 2. Install Dependencies

```bash
pnpm install --frozen-lockfile
# or
npm ci
```

### 3. Build Application

```bash
npm run build:production
```

### 4. Deploy with PM2

Use the provided deployment script:

```bash
# Make script executable
chmod +x scripts/deploy.sh
chmod +x scripts/restart-pm2.sh

# Full deployment
./scripts/deploy.sh deploy
```

### 5. Verify Deployment

```bash
# Check PM2 status
pm2 status

# Check application logs
pm2 logs ikia-conference

# Run health check
npm run health-check
```

## Available Scripts

The application provides several npm scripts for deployment and management:

### PM2 Management
```bash
npm run pm2:start          # Start application with PM2
npm run pm2:stop           # Stop application
npm run pm2:restart        # Hard restart (stop, delete, start)
npm run pm2:restart:soft   # Zero-downtime reload
npm run pm2:restart:quick  # Quick restart
npm run pm2:reload         # Reload application
npm run pm2:delete         # Delete PM2 process
npm run pm2:logs           # View logs
npm run pm2:monit          # Open PM2 monitor
npm run pm2:status         # Show status
```

### Deployment Scripts
```bash
npm run deploy             # Full deployment
npm run deploy:restart     # Restart deployment
npm run health-check       # Run health check
```

### Direct Script Usage
```bash
# Deployment script options
./scripts/deploy.sh deploy    # Full deployment
./scripts/deploy.sh restart   # Hard restart
./scripts/deploy.sh reload    # Zero-downtime reload
./scripts/deploy.sh status    # Show status
./scripts/deploy.sh logs      # Show logs
./scripts/deploy.sh health    # Health check

# Restart script options
./scripts/restart-pm2.sh hard   # Hard restart
./scripts/restart-pm2.sh soft   # Soft restart (recommended)
./scripts/restart-pm2.sh quick  # Quick restart
./scripts/restart-pm2.sh status # Show status
```

## Monitoring and Maintenance

### 1. PM2 Monitoring

```bash
# Real-time monitoring
pm2 monit

# Process status
pm2 status

# Application logs
pm2 logs ikia-conference --lines 100

# Restart application
npm run pm2:restart:soft  # Recommended for zero-downtime
```

### 2. Log Management

Logs are stored in `/home/<USER>/public_html/logs/`:
- `err.log` - Error logs
- `out.log` - Output logs
- `combined.log` - Combined logs
- `health-check.json` - Health check results

### 3. Backup Strategy

```bash
# Create backup
./scripts/deploy.sh backup

# Backups are stored in /home/<USER>/backups/
```

### 4. Health Monitoring

Set up a cron job for regular health checks:

```bash
crontab -e

# Add this line for health checks every 5 minutes
*/5 * * * * cd /home/<USER>/public_html && npm run health-check >> /home/<USER>/logs/health-cron.log 2>&1
```

## Troubleshooting

### Common Issues

1. **Application won't start**
   ```bash
   # Check logs
   pm2 logs ikia-conference
   
   # Check environment variables
   pm2 env 0
   
   # Restart with fresh process
   npm run pm2:restart
   ```

2. **High memory usage**
   ```bash
   # Check memory usage
   pm2 monit
   
   # Restart application
   npm run pm2:restart:soft
   ```

3. **Apache proxy errors**
   ```bash
   # Check Apache error logs
   sudo tail -f /home/<USER>/logs/apache_error.log
   
   # Verify Node.js app is running
   curl http://localhost:3000
   ```

4. **SSL/HTTPS issues**
   ```bash
   # Check certificate validity
   openssl x509 -in /path/to/certificate.crt -text -noout
   
   # Test SSL configuration
   sudo apache2ctl configtest
   ```

### Performance Optimization

1. **Enable PM2 cluster mode** (already configured)
2. **Configure Apache caching** (already configured)
3. **Enable gzip compression** (already configured)
4. **Monitor resource usage** with `pm2 monit`
5. **Set up log rotation** for large log files

### Security Considerations

1. **Keep software updated**
2. **Use HTTPS with valid SSL certificates**
3. **Configure firewall properly**
4. **Regular security audits**
5. **Monitor access logs for suspicious activity**

## Support

For additional support:
1. Check application logs: `pm2 logs ikia-conference`
2. Run health check: `npm run health-check`
3. Review Apache error logs: `sudo tail -f /home/<USER>/logs/apache_error.log`
4. Contact the development team with specific error messages and logs
