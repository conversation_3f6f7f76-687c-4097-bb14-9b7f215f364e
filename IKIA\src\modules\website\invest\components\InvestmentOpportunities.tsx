'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  TrendingUp,
  MapPin,
  Users,
  Coins,
  Clock,
  Star,
  ArrowRight,
  AlertTriangle,
} from 'lucide-react'
import { useGetInvestmentReadyAssetsQuery, type IKIAAsset } from '@/lib/api/ikiaAssetApi'
import { useGetActiveThematicAreasQuery } from '@/lib/api/thematicAreasApi'

// Helper functions
const getThematicAreaInfo = (thematicArea: IKIAAsset['thematicArea']) => {
  if (typeof thematicArea === 'object' && thematicArea !== null) {
    return {
      id: thematicArea.id,
      name: thematicArea.name,
      color: thematicArea.color || '#159147',
    }
  }
  return null
}

const getCountyInfo = (county: IKIAAsset['county']) => {
  if (typeof county === 'object' && county !== null) {
    return {
      id: county.id,
      name: county.name,
      code: county.code,
    }
  }
  return null
}

const formatCurrency = (amount: number | null | undefined): string => {
  if (!amount) return 'TBA'
  return `Ksh${(amount / 1000).toFixed(0)}K`
}

const getImageUrl = (image: IKIAAsset['image']): string => {
  if (typeof image === 'object' && image !== null) {
    return (
      image.sizes?.medium?.url ||
      image.url ||
      '/placeholder.svg?height=200&width=300&text=IKIA+Asset'
    )
  }
  return '/placeholder.svg?height=200&width=300&text=IKIA+Asset'
}

export default function InvestmentOpportunities() {
  const [selectedCategory, setSelectedCategory] = useState('All')

  // Get investment-ready IKIA assets from API
  const {
    data: assetsData,
    error: assetsError,
    isLoading: assetsLoading,
  } = useGetInvestmentReadyAssetsQuery({
    limit: 50,
    sort: '-createdAt',
  })

  // Get thematic areas for filters
  const {
    data: thematicData,
    error: thematicError,
    isLoading: thematicLoading,
  } = useGetActiveThematicAreasQuery({
    limit: 0,
  })

  const assets = assetsData?.docs || []
  const thematicAreas = thematicData?.docs || []

  // Create categories array with "All" plus thematic areas
  const categories = ['All', ...thematicAreas.map((area) => area.name)]

  // Filter assets by selected category
  const filteredAssets =
    selectedCategory === 'All'
      ? assets
      : assets.filter((asset) => {
          const thematicInfo = getThematicAreaInfo(asset.thematicArea)
          return thematicInfo?.name === selectedCategory
        })

  // Loading state
  if (assetsLoading || thematicLoading) {
    return (
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="font-myriad font-bold text-sm uppercase tracking-wider text-muted-foreground mb-4">
              OPPORTUNITIES
            </h2>
            <h3 className="font-myriad font-bold text-3xl lg:text-4xl text-primary mb-6">
              Investment Opportunities
            </h3>
          </div>
          <div className="grid lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white border border-gray-200 shadow-xl animate-pulse">
                <div className="h-48 bg-gray-200"></div>
                <div className="p-6 space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-full"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  // Error state
  if (assetsError || thematicError) {
    return (
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center">
            <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h3 className="font-myriad font-bold text-xl text-gray-900 mb-2">
              Unable to Load Investment Opportunities
            </h3>
            <p className="text-gray-600 mb-6">
              We&apos;re having trouble loading the investment opportunities. Please try again
              later.
            </p>
            <Button
              onClick={() => window.location.reload()}
              className="bg-[#7E2518] hover:bg-[#6B1F14] text-white font-semibold"
            >
              Try Again
            </Button>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2
            className="font-myriad font-bold text-sm uppercase tracking-wider text-muted-foreground mb-4"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            OPPORTUNITIES
          </h2>
          <h3
            className="font-myriad font-bold text-3xl lg:text-4xl text-primary mb-6"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            Investment Opportunities
          </h3>
          <p
            className="font-myriad text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            Discover vetted investment opportunities that combine traditional knowledge with modern
            business models for sustainable returns and community impact.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-3 mb-12">
          {categories.map((category) => (
            <Button
              key={category}
              onClick={() => setSelectedCategory(category)}
              variant={selectedCategory === category ? 'default' : 'outline'}
              className={`px-6 py-2 transition-all duration-300 ${
                selectedCategory === category
                  ? 'bg-[#7E2518] text-white shadow-lg'
                  : 'border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white'
              }`}
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Investment Opportunities Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {filteredAssets.map((asset) => {
            const thematicInfo = getThematicAreaInfo(asset.thematicArea)
            const countyInfo = getCountyInfo(asset.county)
            const imageUrl = getImageUrl(asset.image)

            return (
              <div
                key={asset.id}
                className="bg-white border border-gray-200 shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden group hover:-translate-y-2"
              >
                {/* Image Header */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={imageUrl}
                    alt={asset.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute top-4 left-4">
                    <span
                      className="bg-[#159147] text-white text-xs font-bold px-3 py-1"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      {thematicInfo?.name || 'IKIA Asset'}
                    </span>
                  </div>
                  {asset.impactScore && (
                    <div className="absolute top-4 right-4">
                      <div className="bg-white/90 backdrop-blur-sm px-3 py-1 flex items-center">
                        <Star className="w-3 h-3 text-[#E8B32C] fill-current mr-1" />
                        <span
                          className="text-xs font-semibold"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {asset.impactScore}
                        </span>
                      </div>
                    </div>
                  )}
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
                    <div
                      className="flex items-center text-white text-sm"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      <MapPin className="w-4 h-4 mr-1" />
                      {countyInfo?.name || asset.location}
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3
                    className="text-xl font-bold text-[#7E2518] mb-3 line-clamp-2"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    {asset.title}
                  </h3>
                  <p
                    className="text-gray-600 text-sm mb-4 line-clamp-2"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    {asset.description || 'Investment opportunity in indigenous knowledge assets'}
                  </p>

                  {/* Key Metrics */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center p-3 bg-gray-50">
                      <Coins className="w-5 h-5 text-[#159147] mx-auto mb-1" />
                      <div
                        className="text-lg font-bold text-[#7E2518]"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        {formatCurrency(asset.investmentNeeded)}
                      </div>
                      <div
                        className="text-xs text-gray-600"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        Investment Needed
                      </div>
                    </div>
                    <div className="text-center p-3 bg-gray-50">
                      <TrendingUp className="w-5 h-5 text-[#E8B32C] mx-auto mb-1" />
                      <div
                        className="text-lg font-bold text-[#7E2518]"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        {asset.expectedReturn || 'TBA'}
                      </div>
                      <div
                        className="text-xs text-gray-600"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        Expected Return
                      </div>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span
                        className="text-gray-600"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        Investment Status
                      </span>
                      <span
                        className="font-semibold text-[#159147]"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        {asset.readyForInvestment ? 'Ready' : 'In Development'}
                      </span>
                    </div>
                    {asset.riskLevel && (
                      <div className="flex justify-between text-sm mt-2">
                        <span
                          className="text-gray-600"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          Risk Level
                        </span>
                        <span
                          className={`font-semibold ${
                            asset.riskLevel === 'low'
                              ? 'text-green-600'
                              : asset.riskLevel === 'medium'
                                ? 'text-yellow-600'
                                : 'text-red-600'
                          }`}
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {asset.riskLevel.charAt(0).toUpperCase() + asset.riskLevel.slice(1)}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Additional Info */}
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <div
                      className="flex items-center"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      <Users className="w-4 h-4 mr-1" />
                      {asset.investmentPotential || 'Medium'} Potential
                    </div>
                    <div
                      className="flex items-center"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      <Clock className="w-4 h-4 mr-1" />
                      {asset.timeline || 'TBA'}
                    </div>
                  </div>

                  {/* Highlights */}
                  {asset.highlights && asset.highlights.length > 0 && (
                    <div className="mb-4">
                      <h4
                        className="font-semibold text-gray-700 text-sm mb-2"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        Key Highlights:
                      </h4>
                      <ul className="space-y-1">
                        {asset.highlights.slice(0, 2).map((item, index) => (
                          <li
                            key={index}
                            className="text-xs text-gray-600 flex items-center"
                            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                          >
                            <div className="w-1.5 h-1.5 bg-[#159147] mr-2"></div>
                            {item.highlight}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Action Button */}
                  <Button
                    className="w-full bg-[#7E2518] hover:bg-[#6B1F14] text-white font-semibold"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    Learn More
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </div>
            )
          })}
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-[#7E2518] to-[#C86E36] p-8 text-white">
            <h3
              className="text-2xl font-bold mb-4"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Ready to Invest in Impact?
            </h3>
            <p
              className="text-lg mb-6 opacity-90 max-w-2xl mx-auto"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Join our platform to access exclusive investment opportunities and connect directly
              with Indigenous Knowledge holders across Kenya.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                className="bg-white text-[#7E2518] font-bold px-8 py-3 hover:bg-gray-100 transition-colors"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                View All Opportunities
              </Button>
              <Button
                variant="outline"
                className="border-2 border-white text-white hover:bg-white hover:text-[#7E2518] bg-transparent font-bold px-8 py-3"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                Schedule Consultation
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
