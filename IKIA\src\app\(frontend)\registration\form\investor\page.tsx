'use client'

import type React from 'react'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { ArrowLeft, TrendingUp, ChevronRight, ChevronLeft } from 'lucide-react'
import { StepIndicator } from '@/modules/website/registration/components/StepIndicator'
import { OTPVerification } from '@/modules/website/registration/components/OTPVerification'
import { saveFormDataToStorage } from '@/modules/website/registration/lib/registration-utils'
import { GroupRegistration } from '@/modules/website/registration/components/GroupRegistration'
import {
  initializeGroupRegistration,
  addGroupMember,
  updateGroupMember,
  removeGroupMember,
  navigateToMember,
  type GroupRegistrationState,
} from '@/modules/website/registration/lib/registration-utils'

// Investor steps configuration
const investorSteps = [
  { id: "details", title: "Details", description: "Personal & investment info" },
  { id: "review", title: "Review", description: "Confirm details" }
]

const investmentAreas = [
  'Traditional Foods & Nutrition',
  'Local Remedies & Traditional Medicine',
  'Musicology & Cultural Arts',
  'Cultural Tourism & Heritage',
  'Indigenous Technologies & Innovations',
  'Sui Generis Intellectual Property Systems',
]

const investmentTypes = [
  'Angel Investment',
  'Venture Capital',
  'Private Equity',
  'Impact Investment',
  'Grant Funding',
  'Strategic Partnership',
]

const investmentRanges = [
  'Under ksh 10,000',
  'ksh 10,000 - ksh 50,000',
  'ksh 50,000 - ksh 100,000',
  'ksh 100,000 - ksh 500,000',
  'ksh 500,000 - ksh 1,000,000',
  'Over ksh1,000,000',
]

const countries = [
  'Kenya',
  'Uganda',
  'Tanzania',
  'Rwanda',
  'Burundi',
  'South Sudan',
  'Ethiopia',
  'Somalia',
  'United States',
  'United Kingdom',
  'Other',
]

export default function InvestorRegistrationForm() {
  const router = useRouter()
  const [groupState, setGroupState] = useState<GroupRegistrationState>(
    initializeGroupRegistration(),
  )

  // Multi-step state management
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [isOtpVerified, setIsOtpVerified] = useState(false)
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    organization: '',
    position: '',
    country: '',
    city: '',

    // Investment Profile
    investmentAreas: [] as string[],
    investmentTypes: [] as string[],
    investmentRange: '',
    investmentExperience: '',
    portfolioDescription: '',

    // Interests
    specificInterests: '',
    dueDiligenceRequirements: '',
    partnershipPreferences: '',

    // Background
    professionalBackground: '',
    previousInvestments: '',

    // Agreements
    termsAccepted: false,
    confidentialityAgreement: false,
    marketingConsent: false,
  })

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleInvestmentAreaChange = (area: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      investmentAreas: checked
        ? [...prev.investmentAreas, area]
        : prev.investmentAreas.filter((a) => a !== area),
    }))
  }

  const handleInvestmentTypeChange = (type: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      investmentTypes: checked
        ? [...prev.investmentTypes, type]
        : prev.investmentTypes.filter((t) => t !== type),
    }))
  }

  // Group registration handlers
  const handleToggleGroupMode = () => {
    setGroupState((prev: GroupRegistrationState) => ({
      ...prev,
      isGroupMode: !prev.isGroupMode,
    }))
  }

  const handleAddMember = () => {
    const newState = addGroupMember(groupState, formData)
    setGroupState(newState)
  }

  const handleNavigateToMember = (index: number) => {
    const newState = navigateToMember(groupState, index)
    setGroupState(newState)

    // Load the member's data into the form
    if (index < newState.members.length) {
      const memberData = newState.members[index] as any
      setFormData(memberData)
    } else {
      // New member - reset form
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        organization: '',
        position: '',
        country: '',
        city: '',
        investmentAreas: [] as string[],
        investmentTypes: [] as string[],
        investmentRange: '',
        investmentExperience: '',
        portfolioDescription: '',
        specificInterests: '',
        dueDiligenceRequirements: '',
        partnershipPreferences: '',
        professionalBackground: '',
        previousInvestments: '',
        termsAccepted: false,
        confidentialityAgreement: false,
        marketingConsent: false,
      })
    }
  }

  const handleRemoveMember = (index: number) => {
    setGroupState((prev: GroupRegistrationState) => removeGroupMember(prev, index))
  }

  // Multi-step navigation handlers
  const nextStep = () => {
    if (canProceedToNextStep()) {
      setCompletedSteps(prev => [...prev, currentStep])
      setCurrentStep(prev => Math.min(prev + 1, investorSteps.length))
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const canProceedToNextStep = () => {
    if (currentStep === 1) {
      return formData.firstName &&
             formData.lastName &&
             formData.email &&
             formData.phone &&
             formData.country &&
             formData.investmentAreas.length > 0 &&
             formData.investmentTypes.length > 0 &&
             formData.investmentRange &&
             formData.termsAccepted &&
             isOtpVerified
    }
    return true
  }

  const handleOtpVerification = (verified: boolean) => {
    setIsOtpVerified(verified)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Prepare registration data
    let registrationData

    if (groupState.isGroupMode) {
      // Update current member data and include all group members
      const updatedGroupState = updateGroupMember(
        groupState,
        groupState.currentMemberIndex,
        formData,
      )
      registrationData = {
        registrationType: 'Investment Intent',
        isGroupRegistration: true,
        groupMembers: [...updatedGroupState.members, formData],
        submissionDate: new Date().toISOString(),
        formType: 'investment-intent',
      }
    } else {
      // Single registration
      registrationData = {
        ...formData,
        registrationType: 'Investment Intent',
        isGroupRegistration: false,
        submissionDate: new Date().toISOString(),
        formType: 'investment-intent',
      }
    }

    saveFormDataToStorage('ikia-investment-intent', registrationData)

    console.log('Investment intent form submitted:', registrationData)
    router.push('/registration/success?type=investment-intent')
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8 font-myriad">
      {/* Enhanced Header with Homepage Design */}
      <div className="mb-12">
        <Button variant="ghost" onClick={() => router.back()} className="mb-8 font-myriad hover:bg-primary/10 text-primary">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Registration Types
        </Button>

        {/* Hero Section matching homepage style */}
        <div className="text-center mb-8 relative">
          <div className="relative bg-gradient-to-br from-background via-card to-muted/20 p-8 md:p-12 border border-border overflow-hidden main-shadow">
            {/* Heritage Elements */}
            <div className="absolute top-6 left-8 heritage-dot heritage-dot-accent"></div>
            <div
              className="absolute top-12 right-12 heritage-dot heritage-dot-primary"
              style={{ animationDelay: '1s' }}
            ></div>
            <div
              className="absolute bottom-8 left-12 heritage-dot heritage-dot-secondary"
              style={{ animationDelay: '2s' }}
            ></div>

            <div className="relative z-10">
              {/* Icon */}
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-accent to-accent/80 mb-6 shadow-lg">
                <TrendingUp className="w-8 h-8 text-accent-foreground" />
              </div>

              <h1 className="text-3xl md:text-5xl font-bold text-foreground mb-4 font-myriad">
                Investment <span className="text-accent">Intent</span>
              </h1>

              <p className="text-base md:text-lg text-muted-foreground mb-4 font-myriad">
                Express your <span className="text-accent font-semibold">Investment Interest</span> in{' '}
                <span className="text-secondary font-semibold">Indigenous Innovation</span>
              </p>

              <div className="max-w-2xl mx-auto mb-4 p-4 bg-accent/10 border border-accent/20 rounded-lg">
                <p className="text-sm text-accent-foreground font-myriad">
                  <strong>Investment Intent Form:</strong> This form captures your investment
                  interests and preferences to help us match you with relevant Indigenous Knowledge
                  opportunities. No payment or commitment is required at this stage.
                </p>
              </div>

              <p className="text-muted-foreground text-sm italic font-myriad">
                &ldquo;Where <span className="text-accent">opportunity</span> meets{' '}
                <span className="text-primary">purpose</span>&rdquo;
              </p>
            </div>
          </div>
        </div>

        {/* Enhanced Step Indicator */}
        <div className="step-indicator mb-8">
          <StepIndicator
            steps={investorSteps}
            currentStep={currentStep}
            completedSteps={completedSteps}
            className="font-myriad"
          />
        </div>
      </div>

      {/* Step Content */}
      <div className="space-y-8">

        {/* Step 1: Personal Information & Investment Profile */}
        {currentStep === 1 && (
          <>
            {/* Group Registration */}
            <GroupRegistration
              groupState={groupState}
              onToggleGroupMode={handleToggleGroupMode}
              onAddMember={handleAddMember}
              onNavigateToMember={handleNavigateToMember}
              onRemoveMember={handleRemoveMember}
              currentMemberData={formData}
            />

            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle
                  className="text-xl font-bold"
                  style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
                >
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="organization">Organization/Fund</Label>
                    <Input
                      id="organization"
                      value={formData.organization}
                      onChange={(e) => handleInputChange('organization', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="position">Position/Title</Label>
                    <Input
                      id="position"
                      value={formData.position}
                      onChange={(e) => handleInputChange('position', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="country">Country *</Label>
                    <Select
                      value={formData.country}
                      onValueChange={(value) => handleInputChange('country', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select your country" />
                      </SelectTrigger>
                      <SelectContent>
                        {countries.map((country) => (
                          <SelectItem key={country} value={country}>
                            {country}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={formData.city}
                      onChange={(e) => handleInputChange('city', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Investment Intent Profile */}
            <Card>
              <CardHeader>
                <CardTitle
                  className="text-xl font-bold"
                  style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
                >
                  Investment Intent & Preferences
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-2">
                  Help us understand your investment interests to match you with relevant opportunities
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label className="text-base font-semibold mb-4 block">
                    Areas of Investment Interest (Select all that apply) *
                  </Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {investmentAreas.map((area) => (
                      <div key={area} className="flex items-center space-x-2">
                        <Checkbox
                          id={area}
                          checked={formData.investmentAreas.includes(area)}
                          onCheckedChange={(checked) =>
                            handleInvestmentAreaChange(area, checked as boolean)
                          }
                        />
                        <Label htmlFor={area} className="text-sm">
                          {area}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="text-base font-semibold mb-4 block">
                    Preferred Investment Types (Select all that apply) *
                  </Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {investmentTypes.map((type) => (
                      <div key={type} className="flex items-center space-x-2">
                        <Checkbox
                          id={type}
                          checked={formData.investmentTypes.includes(type)}
                          onCheckedChange={(checked) =>
                            handleInvestmentTypeChange(type, checked as boolean)
                          }
                        />
                        <Label htmlFor={type} className="text-sm">
                          {type}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="investmentRange">Typical Investment Range *</Label>
                    <Select
                      value={formData.investmentRange}
                      onValueChange={(value) => handleInputChange('investmentRange', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select investment range" />
                      </SelectTrigger>
                      <SelectContent>
                        {investmentRanges.map((range) => (
                          <SelectItem key={range} value={range}>
                            {range}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="investmentExperience">Investment Experience</Label>
                    <Input
                      id="investmentExperience"
                      value={formData.investmentExperience}
                      onChange={(e) => handleInputChange('investmentExperience', e.target.value)}
                      placeholder="Years of investment experience"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="portfolioDescription">Portfolio Description</Label>
                  <Textarea
                    id="portfolioDescription"
                    placeholder="Describe your current investment portfolio and focus areas..."
                    value={formData.portfolioDescription}
                    onChange={(e) => handleInputChange('portfolioDescription', e.target.value)}
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="specificInterests">Specific Investment Interests</Label>
                  <Textarea
                    id="specificInterests"
                    placeholder="What specific types of Indigenous Knowledge projects or companies interest you for potential investment?"
                    value={formData.specificInterests}
                    onChange={(e) => handleInputChange('specificInterests', e.target.value)}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>

            {/* OTP Verification */}
            <OTPVerification
              email={formData.email}
              phone={formData.phone}
              onVerificationComplete={handleOtpVerification}
            />

            {/* Terms and Conditions */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="terms"
                      checked={formData.termsAccepted}
                      onCheckedChange={(checked) => handleInputChange('termsAccepted', checked)}
                    />
                    <Label htmlFor="terms" className="text-sm leading-relaxed">
                      I agree to the <a href="#" className="text-blue-600 hover:underline">Terms and Conditions</a> and
                      <a href="#" className="text-blue-600 hover:underline ml-1">Privacy Policy</a> *
                    </Label>
                  </div>
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="confidentiality"
                      checked={formData.confidentialityAgreement}
                      onCheckedChange={(checked) => handleInputChange('confidentialityAgreement', checked)}
                    />
                    <Label htmlFor="confidentiality" className="text-sm leading-relaxed">
                      I agree to the <a href="#" className="text-blue-600 hover:underline">Confidentiality Agreement</a>
                    </Label>
                  </div>
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="marketing"
                      checked={formData.marketingConsent}
                      onCheckedChange={(checked) => handleInputChange('marketingConsent', checked)}
                    />
                    <Label htmlFor="marketing" className="text-sm leading-relaxed">
                      I consent to receive marketing communications about relevant investment opportunities
                    </Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {/* Step 2: Review Details */}
        {currentStep === 2 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
                Review Your Investment Intent
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="font-semibold">Name</Label>
                    <p>{formData.firstName} {formData.lastName}</p>
                  </div>
                  <div>
                    <Label className="font-semibold">Email</Label>
                    <p>{formData.email}</p>
                  </div>
                  <div>
                    <Label className="font-semibold">Phone</Label>
                    <p>{formData.phone}</p>
                  </div>
                  <div>
                    <Label className="font-semibold">Country</Label>
                    <p>{formData.country}</p>
                  </div>
                  {formData.organization && (
                    <div>
                      <Label className="font-semibold">Organization</Label>
                      <p>{formData.organization}</p>
                    </div>
                  )}
                  {formData.position && (
                    <div>
                      <Label className="font-semibold">Position</Label>
                      <p>{formData.position}</p>
                    </div>
                  )}
                </div>

                <div>
                  <Label className="font-semibold">Investment Areas of Interest</Label>
                  <p>{formData.investmentAreas.join(', ')}</p>
                </div>

                <div>
                  <Label className="font-semibold">Preferred Investment Types</Label>
                  <p>{formData.investmentTypes.join(', ')}</p>
                </div>

                <div>
                  <Label className="font-semibold">Investment Range</Label>
                  <p>{formData.investmentRange}</p>
                </div>

                {formData.portfolioDescription && (
                  <div>
                    <Label className="font-semibold">Portfolio Description</Label>
                    <p>{formData.portfolioDescription}</p>
                  </div>
                )}

                {formData.specificInterests && (
                  <div>
                    <Label className="font-semibold">Specific Interests</Label>
                    <p>{formData.specificInterests}</p>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Label className="font-semibold">OTP Verification</Label>
                  <span className={`px-2 py-1 rounded text-sm ${isOtpVerified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {isOtpVerified ? 'Verified' : 'Not Verified'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={currentStep === 1 ? () => router.push('/registration') : prevStep}
            className="flex items-center space-x-2"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>{currentStep === 1 ? 'Back to Registration Types' : 'Previous'}</span>
          </Button>

          <Button
            type="button"
            onClick={currentStep === investorSteps.length ? handleSubmit : nextStep}
            disabled={!canProceedToNextStep()}
            className="flex items-center space-x-2"
          >
            <span>
              {currentStep === investorSteps.length ? 'Submit Investment Intent' : 'Next Step'}
            </span>
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
