import type { CollectionConfig } from 'payload'

export const Enquiry: CollectionConfig = {
  slug: 'enquiry',
  admin: {
    useAsTitle: 'fullname',
  },
  fields: [
    {
      name: 'fullname',
      type: 'text',
      required: true,
    },
    {
      name: 'email',
      type: 'email',
      required: true,
    },
    {
      name: 'subject',
      type: 'text',
    },
    {
      name: 'message',
      type: 'textarea',
      required: true,
    },
  ],
}

export default Enquiry