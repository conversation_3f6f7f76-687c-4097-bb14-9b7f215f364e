#!/bin/bash

# Quick PM2 Restart Script for IKIA Conference Application
# This script provides quick restart options for the PM2 application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="ikia-conference"
APP_DIR="/home/<USER>/public_html"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if PM2 is installed
check_pm2() {
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 is not installed"
        exit 1
    fi
}

# Hard restart (stop, delete, start)
hard_restart() {
    log_info "Performing hard restart of $APP_NAME..."
    
    cd "$APP_DIR"
    
    # Stop the application if running
    if pm2 describe "$APP_NAME" > /dev/null 2>&1; then
        log_info "Stopping application..."
        pm2 stop "$APP_NAME"
        
        log_info "Deleting PM2 process..."
        pm2 delete "$APP_NAME"
    else
        log_warning "Application not currently running"
    fi
    
    # Start fresh instance
    log_info "Starting fresh application instance..."
    pm2 start ecosystem.config.cjs --env production
    
    # Wait for stabilization
    sleep 3
    
    # Verify restart
    if pm2 describe "$APP_NAME" | grep -q "online"; then
        log_success "Hard restart completed successfully"
        pm2 save
        pm2 status
    else
        log_error "Application failed to start after restart"
        pm2 logs "$APP_NAME" --lines 10
        exit 1
    fi
}

# Soft restart (zero-downtime reload)
soft_restart() {
    log_info "Performing zero-downtime reload of $APP_NAME..."
    
    cd "$APP_DIR"
    
    if pm2 describe "$APP_NAME" > /dev/null 2>&1; then
        pm2 reload ecosystem.config.cjs --env production
        log_success "Zero-downtime reload completed"
        pm2 status
    else
        log_warning "Application not found. Starting new instance..."
        pm2 start ecosystem.config.cjs --env production
        pm2 save
    fi
}

# Quick restart (PM2 restart command)
quick_restart() {
    log_info "Performing quick restart of $APP_NAME..."
    
    if pm2 describe "$APP_NAME" > /dev/null 2>&1; then
        pm2 restart "$APP_NAME"
        log_success "Quick restart completed"
        pm2 status
    else
        log_warning "Application not found. Starting new instance..."
        cd "$APP_DIR"
        pm2 start ecosystem.config.cjs --env production
        pm2 save
    fi
}

# Show application status
show_status() {
    log_info "Current PM2 status:"
    pm2 status
    
    if pm2 describe "$APP_NAME" > /dev/null 2>&1; then
        echo ""
        log_info "Detailed status for $APP_NAME:"
        pm2 describe "$APP_NAME"
    fi
}

# Show recent logs
show_logs() {
    log_info "Recent logs for $APP_NAME:"
    pm2 logs "$APP_NAME" --lines 50
}

# Main execution
check_pm2

case "${1:-help}" in
    "hard"|"h")
        hard_restart
        ;;
    "soft"|"s")
        soft_restart
        ;;
    "quick"|"q")
        quick_restart
        ;;
    "status"|"st")
        show_status
        ;;
    "logs"|"l")
        show_logs
        ;;
    "help"|*)
        echo "PM2 Restart Script for $APP_NAME"
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  hard, h     - Hard restart (stop, delete, start) - Most thorough"
        echo "  soft, s     - Soft restart (zero-downtime reload) - Recommended"
        echo "  quick, q    - Quick restart (PM2 restart command) - Fastest"
        echo "  status, st  - Show current status"
        echo "  logs, l     - Show recent logs"
        echo "  help        - Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0 hard     # Complete restart with fresh process"
        echo "  $0 soft     # Zero-downtime reload (recommended)"
        echo "  $0 quick    # Fast restart using PM2 restart"
        echo "  $0 status   # Check current status"
        ;;
esac
