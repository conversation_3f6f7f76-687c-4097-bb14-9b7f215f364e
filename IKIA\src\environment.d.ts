declare global {
  namespace NodeJS {
    interface ProcessEnv {
      // Core Application
      PAYLOAD_SECRET: string
      DATABASE_URL: string
      DATABASE_URI?: string // Backward compatibility
      NEXT_PUBLIC_SERVER_URL: string
      VERCEL_PROJECT_PRODUCTION_URL: string
      CRON_SECRET: string
      PREVIEW_SECRET: string

      // Email Configuration
      SMTP_HOST: string
      SMTP_PORT: string
      SMTP_SECURE: string
      SMTP_USER: string
      SMTP_PASS: string
      FROM_EMAIL: string
      FROM_NAME: string

      // Pesaflow Payment Integration
      PESAFLOW_API_CLIENT_ID: string
      PESAFLOW_CLIENT_SECRET: string
      PESAFLOW_CLIENT_KEY: string
      PESAFLOW_UAT_SERVER_URL: string
      PESAFLOW_REQUEST_SERVICE_ID: string
      PESAFLOW_BILL_DESC: string
      PESAFLOW_NOTIFICATION_URL: string
      PESAFLOW_CALLBACK_SUCCESS_URL: string
      PESAFLOW_PICTURE_URL?: string // Optional

      // eCitizen SSO Integration
      ECITIZEN_CLIENT_ID: string
      ECITIZEN_CLIENT_SECRET: string
      ECITIZEN_AUTHORIZATION_URL: string
      ECITIZEN_TOKEN_URL: string
      ECITIZEN_INTROSPECTION_URL: string
      ECITIZEN_USERINFO_URL: string

      // eCitizen USSD Integration
      ECITIZEN_MERCHANT_KEY: string
      ECITIZEN_MERCHANT_SECRET: string
    }
  }
}

// If this file has no import/export statements (i.e. is a script)
// convert it into a module by adding an empty export statement.
export {}
