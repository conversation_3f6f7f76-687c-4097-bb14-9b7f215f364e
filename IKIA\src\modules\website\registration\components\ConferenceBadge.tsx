"use client"

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { QRCodeSVG } from 'qrcode.react'
import { Calendar, MapPin, User, Building, Crown, Store, TrendingUp } from 'lucide-react'

interface ConferenceBadgeProps {
  registrationData: {
    firstName: string
    lastName: string
    email: string
    position?: string
    companyName?: string
    registrationType: 'guest' | 'vip' | 'sponsor' | 'exhibitor' | 'investor'
    registrationId: string
    qrCode: string
  }
  className?: string
}

const typeConfig = {
  guest: {
    icon: User,
    color: 'bg-secondary',
    textColor: 'text-secondary-foreground',
    label: 'Delegate'
  },
  vip: {
    icon: Crown,
    color: 'bg-accent',
    textColor: 'text-accent-foreground',
    label: 'VIP'
  },
  sponsor: {
    icon: Building,
    color: 'bg-primary',
    textColor: 'text-primary-foreground',
    label: 'Sponsor'
  },
  exhibitor: {
    icon: Store,
    color: 'bg-accent',
    textColor: 'text-accent-foreground',
    label: 'Exhibitor'
  },
  investor: {
    icon: TrendingUp,
    color: 'bg-accent',
    textColor: 'text-accent-foreground',
    label: 'Investor'
  }
}

export function ConferenceBadge({ registrationData, className = "" }: ConferenceBadgeProps) {
  const config = typeConfig[registrationData.registrationType]
  const IconComponent = config.icon

  return (
    <Card className={`w-full max-w-md mx-auto bg-gradient-to-br from-background to-muted/20 border-2 ${className}`}>
      <CardHeader className="text-center pb-4">
        <div className="flex items-center justify-center mb-4">
          <div className={`inline-flex items-center justify-center w-12 h-12 ${config.color} rounded-full mb-2`}>
            <IconComponent className={`w-6 h-6 ${config.textColor}`} />
          </div>
        </div>
        <CardTitle className="text-lg font-bold text-primary">
          Indigenous Knowledge & Innovation Africa
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          December 2024 • Nairobi, Kenya
        </p>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Participant Info */}
        <div className="text-center space-y-2">
          <h3 className="text-xl font-bold text-foreground">
            {registrationData.firstName} {registrationData.lastName}
          </h3>
          {registrationData.position && (
            <p className="text-sm text-muted-foreground">{registrationData.position}</p>
          )}
          {registrationData.companyName && (
            <p className="text-sm font-medium text-foreground">{registrationData.companyName}</p>
          )}
          <Badge variant="secondary" className={`${config.color} ${config.textColor}`}>
            {config.label}
          </Badge>
        </div>

        {/* QR Code */}
        <div className="flex justify-center">
          <div className="bg-white p-3 rounded-lg border">
            <QRCodeSVG
              value={registrationData.qrCode}
              size={120}
              level="M"
              includeMargin={false}
            />
          </div>
        </div>

        {/* Registration ID */}
        <div className="text-center">
          <p className="text-xs text-muted-foreground">Registration ID</p>
          <p className="text-sm font-mono font-medium">{registrationData.registrationId}</p>
        </div>

        {/* Conference Details */}
        <div className="border-t pt-4 space-y-2">
          <div className="flex items-center justify-center text-xs text-muted-foreground">
            <Calendar className="w-3 h-3 mr-1" />
            December 5-7, 2024
          </div>
          <div className="flex items-center justify-center text-xs text-muted-foreground">
            <MapPin className="w-3 h-3 mr-1" />
            Kenyatta International Convention Centre
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
