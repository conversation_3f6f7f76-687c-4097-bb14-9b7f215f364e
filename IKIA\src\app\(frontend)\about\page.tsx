'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import Image from 'next/image'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Users,
  Target,
  TrendingUp,
  Globe,
  CheckCircle,
  Lightbulb,
  ArrowRight,
  Download,
  Handshake,
  Shield,
  Sparkles,
  Zap,
  Play,
} from 'lucide-react'
import { TargetAudience } from '@/modules/website/aboutconference/components'

export default function AboutPage() {
  const [activeCard, setActiveCard] = useState<number | null>(null)

  const objectives = [
    {
      id: 1,
      title: 'Innovation & Knowledge Sharing',
      description:
        'Foster innovation and knowledge sharing among indigenous communities and modern enterprises to create sustainable development pathways',
      icon: Target,
      gradient: 'from-[#159147] via-[#159147] to-[#0f7a3a]',
      hoverGradient: 'from-[#1ba855] via-[#159147] to-[#0f7a3a]',
      accentColor: 'text-[#159147]',
      bgGlow: 'shadow-[#159147]/30',
      borderGlow: 'border-[#159147]/40',
      delay: '0ms',
    },
    {
      id: 2,
      title: 'Sustainable Investment',
      description:
        'Create sustainable investment opportunities that preserve cultural heritage while driving economic growth and community empowerment',
      icon: Users,
      gradient: 'from-[#7E2518] via-[#7E2518] to-[#5a1a10]',
      hoverGradient: 'from-[#9a2e20] via-[#7E2518] to-[#5a1a10]',
      accentColor: 'text-[#7E2518]',
      bgGlow: 'shadow-[#7E2518]/30',
      borderGlow: 'border-[#7E2518]/40',
      delay: '150ms',
    },
    {
      id: 3,
      title: 'Strategic Partnerships',
      description:
        'Build strategic partnerships between traditional knowledge holders and contemporary investors for mutual benefit and growth',
      icon: Handshake,
      gradient: 'from-[#E8B32C] via-[#E8B32C] to-[#d19d1f]',
      hoverGradient: 'from-[#edc247] via-[#E8B32C] to-[#d19d1f]',
      accentColor: 'text-[#E8B32C]',
      bgGlow: 'shadow-[#E8B32C]/30',
      borderGlow: 'border-[#E8B32C]/40',
      delay: '300ms',
    },
    {
      id: 4,
      title: 'Intellectual Property Protection',
      description:
        'Develop comprehensive frameworks for protecting and commercializing indigenous intellectual property rights',
      icon: Shield,
      gradient: 'from-[#C86E36] via-[#C86E36] to-[#a85a2b]',
      hoverGradient: 'from-[#d4804a] via-[#C86E36] to-[#a85a2b]',
      accentColor: 'text-[#C86E36]',
      bgGlow: 'shadow-[#C86E36]/30',
      borderGlow: 'border-[#C86E36]/40',
      delay: '450ms',
    },
    {
      id: 5,
      title: 'Digital Transformation',
      description:
        'Leverage cutting-edge technology to bridge traditional wisdom with modern digital solutions for global impact',
      icon: Zap,
      gradient: 'from-[#81B1DB] via-[#81B1DB] to-[#6a9bc7]',
      hoverGradient: 'from-[#95c1e3] via-[#81B1DB] to-[#6a9bc7]',
      accentColor: 'text-[#81B1DB]',
      bgGlow: 'shadow-[#81B1DB]/30',
      borderGlow: 'border-[#81B1DB]/40',
      delay: '600ms',
    },
    {
      id: 6,
      title: 'Global Network Expansion',
      description:
        'Establish worldwide connections and collaborative networks to amplify indigenous voices on the international stage',
      icon: Globe,
      gradient: 'from-[#159147] via-[#7E2518] to-[#E8B32C]',
      hoverGradient: 'from-[#1ba855] via-[#9a2e20] to-[#edc247]',
      accentColor: 'text-[#159147]',
      bgGlow: 'shadow-[#159147]/30',
      borderGlow: 'border-[#159147]/40',
      delay: '750ms',
    },
  ]

  const importancePoints = [
    {
      icon: Globe,
      title: 'Global Recognition',
      description:
        'IKIAs represent a vital part of cultural heritage with significant ecological and economic value for sustainable development.',
    },
    {
      icon: TrendingUp,
      title: 'Economic Development',
      description:
        'They play a key role across sectors including health, cosmetics, agriculture, and eco-tourism, driving innovation.',
    },
    {
      icon: Target,
      title: 'Strategic Innovation',
      description:
        'Transforming traditional knowledge and natural resources helps protect biodiversity and promote sustainable practices.',
    },
    {
      icon: Users,
      title: 'Community Empowerment',
      description:
        'They empower local communities by generating ownership, innovation, and sustainable livelihoods for future generations.',
    },
    {
      icon: CheckCircle,
      title: 'Policy Support',
      description:
        'IKIAs support inclusive and sustainable development by bridging cultural identity with modern innovation frameworks.',
    },
  ]

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section with Smaller Geometric Box */}
      <section className="relative min-h-screen bg-gradient-to-br from-[#7E2518]/3 via-white to-[#159147]/3 overflow-hidden">
        {/* Subtle Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -left-40 w-60 h-60 bg-[#E8B32C]/5 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -right-40 w-72 h-72 bg-[#81B1DB]/5 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 pt-24 pb-16 lg:pt-28 lg:pb-24 relative z-10">
          <div className="grid lg:grid-cols-12 gap-12 items-center min-h-[80vh]">
            {/* Left Side - Smaller Geometric Design */}
            <div className="lg:col-span-5 relative">
              <div className="relative w-full h-[350px] lg:h-[400px] max-w-md mx-auto">
                {/* Main geometric container - much smaller */}
                <div className="absolute inset-0 bg-gradient-to-br from-[#7E2518]/8 via-[#7E2518]/4 to-[#159147]/8 transform rotate-45 rounded-2xl shadow-lg border border-[#7E2518]/15">
                  {/* Inner pattern */}
                  <div className="absolute inset-6 border border-[#E8B32C]/20 rounded-xl">
                    <Image
                      src="/placeholder.svg"
                      alt="First International Investment Conference and Trade Fair on Indigenous Knowledge Intellectual Assets 2025 imagery"
                      fill
                      className="object-cover rounded-lg"
                    />
                  </div>
                </div>

                {/* Diagonal lines - adjusted for smaller size */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-full h-full relative">
                    {/* Primary diagonal lines */}
                    <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-[#7E2518]/25 to-transparent transform rotate-45 origin-left"></div>
                    <div className="absolute top-0 right-0 w-full h-0.5 bg-gradient-to-r from-transparent via-[#7E2518]/25 to-transparent transform -rotate-45 origin-right"></div>

                    {/* Center cross */}
                    <div className="absolute top-1/2 left-1/2 w-0.5 h-full bg-gradient-to-b from-transparent via-[#7E2518]/20 to-transparent transform -translate-x-1/2 -translate-y-1/2"></div>
                    <div className="absolute top-1/2 left-1/2 w-full h-0.5 bg-gradient-to-r from-transparent via-[#7E2518]/20 to-transparent transform -translate-x-1/2 -translate-y-1/2"></div>
                  </div>
                </div>

                {/* Smaller accent shapes */}
                <div className="absolute top-8 right-8 w-12 h-12 bg-gradient-to-br from-[#E8B32C]/15 to-[#C86E36]/15 transform rotate-12 rounded-lg border border-[#E8B32C]/25 shadow-sm hover:scale-105 transition-transform duration-300"></div>

                <div className="absolute bottom-8 left-8 w-10 h-10 bg-gradient-to-br from-[#81B1DB]/15 to-[#159147]/15 transform -rotate-12 rounded-lg border border-[#81B1DB]/25 shadow-sm hover:scale-105 transition-transform duration-300"></div>

                {/* Small floating elements */}
                <div className="absolute top-1/4 right-16 w-2 h-2 bg-[#E8B32C]/40 rounded-full"></div>
                <div className="absolute bottom-1/4 left-16 w-2 h-2 bg-[#81B1DB]/40 rounded-full"></div>
              </div>
            </div>

            {/* Right Side - Content Container (now takes more space) */}
            <div className="lg:col-span-7 relative z-20 lg:-ml-8">
              <div className="bg-white/98 backdrop-blur-xl rounded-3xl p-8 lg:p-12 shadow-xl border border-gray-100/50 relative overflow-hidden">
                {/* Minimal background pattern */}
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-[#7E2518]/3 to-transparent rounded-bl-3xl"></div>

                {/* Content */}
                <div className="relative z-10">
                  {/* Conference Badge */}
                  <div className="inline-flex items-center gap-2 bg-gradient-to-r from-[#7E2518]/8 to-[#159147]/8 px-4 py-2 rounded-full border border-[#7E2518]/15 mb-6">
                    <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
                    <span className="font-myriad text-sm font-medium text-[#7E2518]">
                      IKIA 2025 Investment Conference
                    </span>
                  </div>

                  {/* Main Heading */}
                  <div className="space-y-6 mb-10">
                    <h1 className="font-myriad text-4xl md:text-5xl lg:text-6xl font-bold text-[#7C2313] leading-tight tracking-tight">
                      ABOUT THE INTERNATIONAL INVESTMENT
                      <span className="block bg-clip-text text-[#7C2313]">
                        CONFERENCE AND TRADE FAIR 2025
                      </span>
                    </h1>
                    <div className="w-24 h-1 bg-gradient-to-r from-[#E8B32C] to-[#C86E36] rounded-full"></div>
                    <p
                      className="text-lg md:text-xl text-gray-700 leading-relaxed max-w-lg"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      1<sup>st</sup> INTERNATIONAL INVESTMENT CONFERENCE AND TRADE FAIR ON
                      INDIGENOUS KNOWLEDGE INTELLECTUAL ASSETS 2025 aligned with Kenya Vision 2030,
                      BETA, The Fourth Medium Term Plan (MTP IV), and NMK&apos;s Strategic Plan for
                      sustainable indigenous knowledge development.
                    </p>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 mb-10">
                    <Button className="font-myriad group bg-gradient-to-r from-[#7E2518] to-[#7E2518]/90 text-white px-8 py-4 font-bold transition-all duration-500 shadow-lg hover:shadow-xl hover:shadow-[#7E2518]/20 transform hover:-translate-y-1">
                      <Play className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                      Explore Conference
                      <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>

                    <Button
                      variant="outline"
                      className="group border-2 border-[#159147] text-[#159147] hover:text-[#159147] px-8 py-4 font-bold transition-all duration-500 bg-transparent hover:shadow-lg hover:shadow-[#159147]/20 transform hover:-translate-y-1"
                      asChild
                    >
                      <a href="/downloads/ikia-concept-note.pdf" download>
                        <Download className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                        Concept Note
                      </a>
                    </Button>

                    <Button
                      variant="outline"
                      className="group border-2 border-[#E8B32C] text-[#E8B32C] hover:text-[#E8B32C] px-8 py-4 font-bold transition-all duration-500 bg-transparent hover:shadow-lg hover:shadow-[#E8B32C]/20 transform hover:-translate-y-1"
                    >
                      <Download className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                      Download Brochure
                    </Button>
                  </div>

                  {/* Statistics Cards */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <Card className="group bg-gradient-to-br from-[#159147] to-[#159147]/80 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2 cursor-pointer overflow-hidden relative">
                      <div className="absolute top-0 right-0 w-12 h-12 bg-white/10 rounded-bl-3xl"></div>
                      <CardContent className="p-6 text-center relative z-10">
                        <div
                          className="text-3xl lg:text-4xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300"
                          style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                        >
                          13
                        </div>
                        <div
                          className="text-sm opacity-90 font-medium leading-tight"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          Participating Counties
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="group bg-gradient-to-br from-[#7E2518] to-[#7E2518]/80 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2 cursor-pointer overflow-hidden relative">
                      <div className="absolute top-0 right-0 w-12 h-12 bg-white/10 rounded-bl-3xl"></div>
                      <CardContent className="p-6 text-center relative z-10">
                        <div
                          className="text-3xl lg:text-4xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300"
                          style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                        >
                          40
                        </div>
                        <div
                          className="text-sm opacity-90 font-medium leading-tight"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          Indigenous Knowledge Assets
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="group bg-gradient-to-br from-[#E8B32C] to-[#C86E36] text-white border-0 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2 cursor-pointer overflow-hidden relative">
                      <div className="absolute top-0 right-0 w-12 h-12 bg-white/10 rounded-bl-3xl"></div>
                      <CardContent className="p-6 text-center relative z-10">
                        <div
                          className="text-3xl lg:text-4xl font-bold mb-2 group-hover:scale-110 transition-transform duration-300"
                          style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                        >
                          25
                        </div>
                        <div
                          className="text-sm opacity-90 font-medium leading-tight"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          Indigenous Knowledge Assets
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Additional Info */}
                  <div className="mt-8 pt-6 border-t border-gray-100">
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#E8B32C] rounded-full"></div>
                        <span style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                          November 19-21, 2025
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#81B1DB] rounded-full"></div>
                        <span style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                          Murang&apos;a County
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#159147] rounded-full"></div>
                        <span style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                          1,000+ Attendees
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-[#7E2518]/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-[#7E2518]/50 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Rest of the sections remain the same */}
      {/* Objectives Section */}
      <div className="min-h-screen bg-amber-100 relative overflow-hidden">
        {/* Main Content */}
        <div className="relative z-10 container mx-auto px-6 py-16 lg:py-20">
          {/* Header Section */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-3 mb-8 px-8 py-4 bg-[#149047] rounded-full border border-white/30 shadow-2xl">
              <Sparkles className="w-6 h-6 text-[#E8B32C]" />
              <span className="text-white/90 font-medium tracking-wider text-sm uppercase">
                Objectives of the Investment Conference and Trade Fair
              </span>
              <Lightbulb className="w-6 h-6 text-[#7C2313]" />
            </div>

            <h1 className="text-6xl md:text-8xl text-[#7C2313] bg-clip-text bg-gradient-to-r from-[#159147] via-[#E8B32C] to-[#81B1DB] mb-8 drop-shadow-2xl">
              TRANSFORM
            </h1>
            <h2 className="text-4xl md:text-5xl text-[#159147] bg-clip-text bg-gradient-to-r from-white via-[#81B1DB]/80 to-white">
              The Future Together
            </h2>

            {/* <div className="w-40 h-2 bg-gradient-to-r from-[#159147] via-[#E8B32C] to-[#C86E36] mx-auto mt-10 rounded-full shadow-lg"></div> */}
          </div>

          {/* Objectives Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 max-w-8xl mx-auto">
            {objectives.map((objective) => {
              const IconComponent = objective.icon
              const isActive = activeCard === objective.id

              return (
                <div
                  key={objective.id}
                  className={`group relative transform transition-all duration-700 hover:scale-110 hover:-translate-y-4 cursor-pointer`}
                  onMouseEnter={() => setActiveCard(objective.id)}
                  onMouseLeave={() => setActiveCard(null)}
                >
                  {/* Card Background with Animated Border */}
                  <div
                    className={`absolute inset-0 bg-white border border-gray-200 transition-all duration-500 group-hover:shadow-2xl ${
                      objective.id === 1
                        ? 'group-hover:bg-[#159147]'
                        : objective.id === 2
                          ? 'group-hover:bg-[#7E2518]'
                          : objective.id === 3
                            ? 'group-hover:bg-[#E8B32C]'
                            : objective.id === 4
                              ? 'group-hover:bg-[#C86E36]'
                              : objective.id === 5
                                ? 'group-hover:bg-[#81B1DB]'
                                : 'group-hover:bg-[#159147]'
                    }`}
                  ></div>

                  {/* Animated Border Glow */}
                  <div
                    className={`absolute inset-0 bg-gradient-to-r ${objective.gradient} rounded-3xl opacity-0 group-hover:opacity-30 blur-2xl transition-all duration-700`}
                  ></div>

                  {/* Content */}
                  <div className="relative p-10 h-full flex flex-col">
                    {/* Icon and Number */}
                    <div className="flex items-center justify-between mb-6">
                      <div
                        className={`p-5 bg-gradient-to-br ${objective.gradient} rounded-2xl shadow-2xl transform group-hover:scale-125 group-hover:rotate-6 transition-all duration-700 relative overflow-hidden`}
                      >
                        <div className="absolute inset-0 bg-white/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <IconComponent className="w-10 h-10 text-white relative z-10" />
                      </div>
                      <div
                        className={`text-7xl font-black ${objective.accentColor} opacity-15 group-hover:opacity-100 group-hover:text-white transition-all duration-700 group-hover:scale-110`}
                      >
                        0{objective.id}
                      </div>
                    </div>

                    {/* Title */}
                    <h3
                      className={`text-2xl font-bold text-gray-800 group-hover:text-white mb-6 transition-all duration-700`}
                    >
                      {objective.title}
                    </h3>

                    {/* Description */}
                    <p className="text-gray-600 group-hover:text-white leading-relaxed flex-grow transition-all duration-700 text-base">
                      {objective.description}
                    </p>

                    {/* Progress Bar Animation */}
                    <div className="mt-8 h-2 bg-white/10 rounded-full overflow-hidden relative">
                      <div
                        className={`h-full bg-gradient-to-r ${objective.gradient} rounded-full transform -translate-x-full group-hover:translate-x-0 transition-transform duration-1200 ease-out shadow-lg`}
                      ></div>
                      <div
                        className={`absolute inset-0 bg-gradient-to-r ${objective.gradient} rounded-full opacity-50 blur-sm transform -translate-x-full group-hover:translate-x-0 transition-transform duration-1200 ease-out`}
                      ></div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Strategic Alignment */}
      <section className="py-16 lg:py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
            {/* Left - Strategic Alignment Image */}
            <div className="relative h-[500px] flex items-center justify-center order-2 lg:order-1">
              <div className="relative w-full h-full">
                <Image src="/logo1.png" alt="Strategic Alignment" fill className="object-contain" />
              </div>
            </div>

            {/* Right - Content */}
            <div className="space-y-8 order-1 lg:order-2">
              <div>
                <h2 className="font-myriad text-3xl md:text-4xl font-bold text-[#7E2518] mb-4">
                  STRATEGIC ALIGNMENT
                </h2>
                <p
                  className="text-lg text-gray-700 leading-relaxed"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  The 1<sup>st</sup> IKIA Investment Conference and Trade Fair 2025 is a flagship
                  program under Kenya Vision 2030, creating synergies across multiple strategic
                  frameworks.
                </p>
              </div>

              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <CheckCircle className="w-6 h-6 text-[#7E2518] mt-1 flex-shrink-0" />
                  <div>
                    <strong className="font-myriad text-[#7E2518] text-lg">
                      KENYA VISION 2030
                    </strong>
                    <p
                      className="text-gray-700 mt-1"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Economic & social transformation through innovation and sustainable
                      development
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <CheckCircle className="w-6 h-6 text-[#7E2518] mt-1 flex-shrink-0" />
                  <div>
                    <strong className="font-myriad text-[#7E2518] text-lg">BETA INITIATIVE</strong>
                    <p
                      className="text-gray-700 mt-1"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Sustainable economic empowerment and community-driven development
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <CheckCircle className="w-6 h-6 text-[#7E2518] mt-1 flex-shrink-0" />
                  <div>
                    <strong className="font-myriad text-[#7E2518] text-lg">
                      NMK STRATEGIC PLAN
                    </strong>
                    <p
                      className="text-gray-700 mt-1"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Heritage preservation & innovation through cultural knowledge systems
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <TargetAudience />
    </div>
  )
}
