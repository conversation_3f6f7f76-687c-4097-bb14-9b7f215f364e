'use client'

import { useGetProgramTypesQuery, type ProgramType } from '@/lib/api/programsApi'
import { useGetActiveSpeakersQuery } from '@/lib/api/speakersApi'
import { useGetActiveThematicAreasQuery } from '@/lib/api/thematicAreasApi'

interface AgendaFiltersProps {
  activeDay: string
  setActiveDay: (day: string) => void
  filter: string
  setFilter: (filter: string) => void
  thematic: string
  setThematic: (thematic: string) => void
  topic: string
  setTopic: (topic: string) => void
}

export default function AgendaFilters({
  activeDay,
  setActiveDay,
  filter,
  setFilter,
  thematic,
  setThematic,
  topic,
  setTopic,
}: AgendaFiltersProps) {
  const days = [
    { value: 'Day 1', label: 'Day 1 - Nov 19', date: '2025-11-19' },
    { value: 'Day 2', label: 'Day 2 - Nov 20', date: '2025-11-20' },
    { value: 'Day 3', label: 'Day 3 - Nov 21', date: '2025-11-21' },
  ]

  // Get program types from API
  const { data: programTypesData } = useGetProgramTypesQuery()
  const programTypes = programTypesData?.docs || []

  // Get speakers from API
  const { data: speakersData } = useGetActiveSpeakersQuery({ limit: 0 })
  const speakers = speakersData?.docs || []

  // Get thematic areas from API
  const { data: thematicAreasData } = useGetActiveThematicAreasQuery({ limit: 0 })
  const thematicAreas = thematicAreasData?.docs || []

  return (
    <div className="bg-white py-8 sm:py-12">
      <div className="max-w-7xl mx-auto px-4">
        {/* Filter Options */}
        <div className="flex flex-col sm:flex-row items-center justify-center gap-6 sm:gap-12 mb-8 sm:mb-12">
          <span
            className="text-gray-600 font-medium mb-2 sm:mb-0"
            style={{ fontFamily: 'Myriad Pro Medium, Myriad Pro, Arial, sans-serif' }}
          >
            Filter by:
          </span>
          <div className="flex flex-row gap-2 sm:gap-4 w-full sm:w-auto overflow-x-auto">
            {/* Speaker Filter */}
            <div className="relative w-32 sm:w-48">
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-[#159147] focus:border-[#159147] appearance-none pr-8"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                <option value="all" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                  All Speakers
                </option>
                {speakers.map((speaker) => (
                  <option
                    key={speaker.id}
                    value={speaker.id}
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    {speaker.name}
                  </option>
                ))}
              </select>
            </div>
            {/* Thematic Area Filter */}
            <div className="relative w-32 sm:w-48">
              <select
                value={thematic}
                onChange={(e) => setThematic(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-[#159147] focus:border-[#159147] appearance-none pr-8"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                <option value="all" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                  All Types
                </option>
                {programTypes.map((type: ProgramType) => (
                  <option
                    key={type.id}
                    value={type.id}
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    {type.name}
                  </option>
                ))}
              </select>
            </div>
            {/* Topic Filter */}
            <div className="relative w-32 sm:w-48">
              <select
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-[#159147] focus:border-[#159147] appearance-none pr-8"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                <option value="all" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                  All Topics
                </option>
                {thematicAreas.map((area) => (
                  <option
                    key={area.id}
                    value={area.id}
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    {area.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Day Tabs */}
        <div className="flex flex-wrap justify-center border-b">
          {days.map((day) => (
            <button
              key={day.value}
              onClick={() => setActiveDay(day.value)}
              className={`px-6 sm:px-12 py-3 sm:py-4 font-medium border-b-2 transition-colors
                ${
                  activeDay === day.value
                    ? 'border-black text-black bg-gray-50'
                    : 'border-transparent text-gray-600 hover:text-gray-900'
                }
              `}
              style={{
                fontFamily:
                  activeDay === day.value
                    ? 'Myriad Pro Bold, Myriad Pro, Arial, sans-serif'
                    : 'Myriad Pro Medium, Myriad Pro, Arial, sans-serif',
              }}
            >
              {day.label}
            </button>
          ))}
        </div>

        <p className="text-sm italic pt-4" style={{ color: '#7E2518' }}>
          * Unless otherwise indicated, all times are East African Time (EAT) <br />* Exhibitions
          will run throughout the Conference and Trade Fair period from 8am – 5.30pm daily
        </p>
      </div>
    </div>
  )
}
