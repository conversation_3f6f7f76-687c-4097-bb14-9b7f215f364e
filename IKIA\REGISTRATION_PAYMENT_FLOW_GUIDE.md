# User Registration and Payment Flow Implementation Guide

## Overview
This guide documents the complete implementation of the user registration and payment flow with the following features:
- Citizen registration with delegate package selection
- Complete Pesaflow payment gateway integration with all 15 required parameters
- Real-time payment processing with proper error handling
- Customer data extraction and validation services
- Comprehensive logging and debugging capabilities
- Proper callback URL handling for payment success/failure

## Implementation Components

### 1. Collections Created

#### DelegatePackages Collection
- **Location**: `src/collections/DelegatePackages/index.ts`
- **Purpose**: Define available delegate packages with pricing for citizen registration
- **Key Fields**: name, price, currency, features, description, isActive

#### Invoices Collection
- **Location**: `src/collections/Invoices/index.ts`
- **Purpose**: Store invoice records linked to users and packages
- **Key Fields**: invoice_number, user, package, amount, status, payment_reference, pesaflow_data

#### Enhanced Users Collection
- **Location**: `src/collections/Users/<USER>
- **Added Fields**:
  - Registration context tracking
  - Package status and payment history
  - Customer information (phone, ID number, county)
  - Email verification and authentication

### 2. Services and Utilities

#### HashService
- **Location**: `src/services/HashService.ts`
- **Purpose**: Generate secure hashes for Pesaflow API authentication
- **Features**:
  - Payment status hash generation
  - Checkout hash generation with proper data string formatting
  - Base64 encoding and HMAC-SHA256 hashing

#### CustomerDataService
- **Location**: `src/services/CustomerDataService.ts`
- **Purpose**: Extract and validate customer data from various sources
- **Features**:
  - Extract customer data from invoices and registration forms
  - Phone number formatting (254XXXXXXXXX format)
  - Email and ID number validation
  - Comprehensive error handling for missing data

#### PesaflowPayloadService
- **Location**: `src/services/PesaflowPayloadService.ts`
- **Purpose**: Build complete Pesaflow checkout payloads with all required parameters
- **Features**:
  - Complete 15-parameter payload generation
  - Environment variable validation
  - Customer data integration
  - Payload validation before sending

### 3. API Endpoints

#### Citizen Registration Endpoint
- **Location**: `src/endpoints/citizenRegistration.ts`
- **Endpoint**: `POST /api/citizens/register`
- **Purpose**: Handle complete citizen registration with delegate package selection and payment
- **Features**:
  - User creation with validation
  - Invoice generation with payment references
  - Complete Pesaflow payload generation with all 15 parameters
  - Real-time API calls to Pesaflow with timeout handling
  - Comprehensive error handling (timeout, network, API errors)
  - Detailed logging for debugging

#### Payment Status Query Endpoint
- **Location**: `src/endpoints/pesaflowQueryPaymentStatus.ts`
- **Endpoint**: `GET /api/invoice/payment/status`
- **Purpose**: Query payment status from Pesaflow using invoice references
- **Features**:
  - Real invoice data lookup
  - Complete parameter hash generation
  - 30-second timeout with proper error handling

#### Pesaflow Checkout Endpoint
- **Location**: `src/endpoints/pesaflowCheckout.ts`
- **Endpoint**: `POST /api/checkout`
- **Purpose**: Direct Pesaflow checkout processing
- **Features**:
  - Complete parameter validation
  - 20-second timeout with fallback handling
  - Detailed response logging

## User Experience Flow

### Step 1: Citizen Registration with Package Selection
```
User fills form → Registration → Invoice creation → Pesaflow API call → Real checkout URL
```

**API Call:**
```bash
curl -X POST http://localhost:3000/api/citizens/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone_number": "0700123456",
    "id_number": "12345678",
    "county": "Nairobi",
    "password": "SecurePassword123!",
    "selected_package": "2"
  }'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 9,
      "email": "<EMAIL>",
      "name": "John Doe"
    },
    "invoice": {
      "id": 5,
      "invoice_number": "INV-*************",
      "amount": 35000,
      "currency": "KES"
    },
    "delegate_package": {
      "id": 2,
      "name": "Premium Business Package",
      "price": 35000
    },
    "payment": {
      "checkout_url": "https://test.pesaflow.com/checkout/abc123def456",
      "return_url": "https://yoursite.com/registration/success?payment=completed&invoice=5",
      "bill_ref_number": "REF-*************",
      "api_client_id": "18",
      "service_id": "233285",
      "customer_phone": "************",
      "customer_name": "John Doe"
    }
  },
  "next_step": "redirect_to_payment",
  "checkout_url": "https://test.pesaflow.com/checkout/abc123def456"
}
```

### Step 2: Payment Processing on Pesaflow

The user is redirected to the real Pesaflow checkout URL where they complete payment using:
- M-Pesa STK Push
- Credit/Debit Card
- Bank Transfer
- Other supported payment methods

### Step 3: Payment Status Query

**API Call:**
```bash
curl "http://localhost:3000/api/invoice/payment/status?ref_no=5"
```

**Response:**
```json
{
  "status": 200,
  "description": "Payment status retrieved successfully",
  "data": {
    "invoice": {
      "id": 5,
      "invoice_number": "INV-*************",
      "amount": 35000,
      "currency": "KES",
      "status": "paid",
      "payment_reference": "REF-*************"
    },
    "user": {
      "id": 9,
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "service_package": {
      "id": 2,
      "name": "Premium Business Package",
      "price": 35000
    },
    "pesaflow_status": {
      "status": "PAID",
      "transaction_id": "TXN123456789",
      "payment_method": "MPESA"
    }
  }
}
```

## Complete Pesaflow Parameter Implementation

### All 15 Required Parameters
The system now generates complete Pesaflow payloads with all required parameters:

| Parameter | Source | Example Value |
|-----------|--------|---------------|
| `apiClientID` | `PESAFLOW_API_CLIENT_ID` | "18" |
| `serviceID` | `PESAFLOW_REQUEST_SERVICE_ID` | "233285" |
| `billRefNumber` | Invoice payment_reference | "REF-*************" |
| `billDesc` | Package name + context | "Premium Business Package - Registration Payment" |
| `clientMSISDN` | Customer phone (formatted) | "************" |
| `clientIDNumber` | Customer ID number | "12345678" |
| `clientName` | Customer name | "John Doe" |
| `clientEmail` | Customer email | "<EMAIL>" |
| `notificationURL` | `PESAFLOW_NOTIFICATION_URL` | "https://yoursite.com/api/payment/callback/pesaflow/notification" |
| `pictureURL` | `PESAFLOW_PICTURE_URL` | "https://yoursite.com/logo.png" |
| `callBackURLOnSuccess` | `PESAFLOW_CALLBACK_SUCCESS_URL` | "https://yoursite.com/registration/success?payment=completed&invoice=5" |
| `currency` | Package/invoice currency | "KES" |
| `amountExpected` | Package/invoice amount | "35000" |
| `format` | `PESAFLOW_DEFAULT_FORMAT` | "html" |
| `sendSTK` | `PESAFLOW_SEND_STK` | "true" |
| `secureHash` | Generated by HashService | "base64encodedHash..." |

### Environment Variables Configuration
```env
# Pesaflow API Configuration
PESAFLOW_API_CLIENT_ID=18
PESAFLOW_CLIENT_SECRET=your_secret_key
PESAFLOW_CLIENT_KEY=your_client_key
PESAFLOW_UAT_SERVER_URL=https://test.pesaflow.com

# Pesaflow Service Configuration
PESAFLOW_REQUEST_SERVICE_ID=233285
PESAFLOW_BILL_DESC=Payment for services

# Callback URLs (using ngrok for development)
PESAFLOW_NOTIFICATION_URL=https://cf6496aa612a.ngrok-free.app/api/payment/callback/pesaflow/notification
PESAFLOW_CALLBACK_SUCCESS_URL=https://cf6496aa612a.ngrok-free.app/registration/success?payment=completed
PESAFLOW_CALLBACK_CANCEL_URL=https://cf6496aa612a.ngrok-free.app/register?error=payment_cancelled
PESAFLOW_PICTURE_URL=https://cf6496aa612a.ngrok-free.app/logo.png

# Additional Parameters
PESAFLOW_DEFAULT_FORMAT=html
PESAFLOW_SEND_STK=true
PESAFLOW_DEFAULT_CURRENCY=KES
```

## Testing the Complete Flow

### Prerequisites
1. **Database Setup**: Ensure PostgreSQL is running and collections are created
2. **Sample Data**: Create delegate packages in the admin panel
3. **ngrok Setup**: For testing callbacks, use ngrok to expose localhost

### Test Scenarios

#### Scenario 1: Complete Citizen Registration Flow
1. **Create Sample Delegate Package** (via admin panel):
   - Name: "Premium Business Package"
   - Price: 35000
   - Currency: KES
   - Active: true

2. **Test Citizen Registration**:
```bash
curl -X POST http://localhost:3000/api/citizens/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "phone_number": "0700123456",
    "id_number": "12345678",
    "county": "Nairobi",
    "password": "TestPassword123!",
    "selected_package": "2"
  }'
```

3. **Expected Results**:
   - User created with proper validation
   - Invoice generated with payment reference
   - Real checkout URL returned from Pesaflow
   - Complete payload with all 15 parameters sent to Pesaflow

#### Scenario 2: Payment Status Query
1. **Test Payment Status Query**:
```bash
curl "http://localhost:3000/api/invoice/payment/status?ref_no=5"
```

2. **Expected Results**:
   - Real invoice data retrieved
   - Complete parameter hash generated
   - Pesaflow API called with proper authentication
   - Payment status returned with transaction details

#### Scenario 3: Error Handling Testing
1. **Test Timeout Handling**:
   - Pesaflow API calls timeout after 20 seconds
   - Proper error messages returned (no fallback URLs)
   - User gets clear timeout error with retry instructions

2. **Test Missing Parameters**:
   - System validates all 15 required parameters
   - Returns specific error if any mandatory parameter is missing
   - Detailed logging for debugging

3. **Test Network Errors**:
   - Network connectivity issues return 503 errors
   - Clear error messages with troubleshooting suggestions

#### Scenario 4: Callback URL Testing
1. **Success Callback**:
   - After payment completion, user redirected to `/registration/success?payment=completed&invoice=5`
   - Success page displays payment confirmation and next steps

2. **Cancel Callback**:
   - If payment cancelled, user redirected to `/register?error=payment_cancelled&invoice=5`
   - Error message displayed with retry option

3. **Notification Callback**:
   - Pesaflow sends payment notifications to `/api/payment/callback/pesaflow/notification`
   - System updates invoice status based on payment result

## Error Handling

### Comprehensive Error Scenarios
1. **Pesaflow Timeout (408)**: API calls timeout after 20 seconds
   - Clear error message with retry instructions
   - No fallback URLs - proper error handling

2. **Network Errors (503)**: Connection issues to Pesaflow
   - Service unavailable message
   - Troubleshooting suggestions provided

3. **Missing Parameters (500)**: Incomplete Pesaflow payload
   - Validates all 15 required parameters
   - Specific error listing missing fields

4. **Customer Data Validation (400)**: Invalid customer information
   - Phone number format validation (254XXXXXXXXX)
   - Email format validation
   - Required field validation

5. **Environment Configuration (500)**: Missing environment variables
   - Lists specific missing configuration
   - Setup instructions provided

### Debugging and Logging
1. **Comprehensive Logging**:
   - Complete payload logging with all 15 parameters
   - Response headers and body logging
   - Request/response timing information
   - Error stack traces with context

2. **Parameter Validation**:
   - Pre-flight validation of all required parameters
   - Parameter count verification (should be 15)
   - Hash generation logging (without exposing secrets)

3. **Environment Verification**:
   - All required environment variables checked
   - URL format validation
   - Service configuration verification

## Implementation Status

### ✅ Completed Features
1. **Complete Pesaflow Integration**: All 15 parameters implemented and tested
2. **Real-time Payment Processing**: Direct API calls to Pesaflow with proper timeout handling
3. **Comprehensive Error Handling**: No fallback URLs, proper error messages
4. **Customer Data Services**: Complete data extraction and validation
5. **Hash Generation**: Secure HMAC-SHA256 hash generation for API authentication
6. **Callback URL Handling**: Proper success/cancel/notification URL configuration
7. **Detailed Logging**: Complete request/response logging for debugging

### 🔄 Current Implementation
- **Citizen Registration**: `/api/citizens/register` - Complete registration with payment
- **Payment Status Query**: `/api/invoice/payment/status` - Real-time payment status
- **Pesaflow Checkout**: `/api/checkout` - Direct Pesaflow integration
- **Parameter Validation**: All 15 Pesaflow parameters validated and sent
- **Error Handling**: Timeout, network, and API errors properly handled

### 🎯 Next Steps
1. **Payment Callback Processing**: Handle Pesaflow payment notifications
2. **Invoice Status Updates**: Update invoice status based on payment results
3. **User Dashboard**: Display payment history and status
4. **Admin Panel**: Payment management and monitoring tools
5. **Production Testing**: Test with real Pesaflow production environment

## Security and Best Practices

### ✅ Security Measures Implemented
- **Server-side Processing**: All sensitive operations handled server-side
- **Secure Hash Generation**: HMAC-SHA256 with proper secret management
- **Parameter Validation**: All inputs validated before processing
- **Environment Variable Protection**: Sensitive data stored in environment variables
- **Error Message Sanitization**: No sensitive data exposed in error messages
- **Timeout Protection**: API calls protected with reasonable timeouts

### 🔒 Additional Security Considerations
- **HTTPS Only**: All callback URLs use HTTPS in production
- **Input Sanitization**: All user inputs properly sanitized
- **SQL Injection Protection**: Using Payload CMS ORM for database operations
- **Rate Limiting**: Consider implementing rate limiting for API endpoints
- **Audit Logging**: All payment operations logged for audit purposes
