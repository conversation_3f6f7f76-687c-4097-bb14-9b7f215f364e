import fetch from 'node-fetch'

async function testEventsEndpoint() {
  const baseUrl = 'http://localhost:3001'
  
  console.log('Testing Events Endpoint...\n')
  
  try {
    // Test 1: Get all events
    console.log('1. Testing GET /api/events (all events)')
    const response1 = await fetch(`${baseUrl}/api/events`)
    const data1 = await response1.json()
    
    if (response1.ok) {
      console.log('✅ Success!')
      console.log(`   Total events: ${data1.totalEvents}`)
      console.log(`   Events returned: ${data1.events.length}`)
      console.log(`   Days with events: ${Object.keys(data1.eventsByDay).length}`)
      console.log(`   Event types: ${Object.keys(data1.eventsByType).join(', ')}`)
      console.log(`   Upcoming events: ${data1.upcomingEvents.length}`)
      
      if (data1.events.length > 0) {
        const firstEvent = data1.events[0]
        console.log(`   Sample event: "${firstEvent.title}" on ${firstEvent.formattedDate}`)
        console.log(`   Speakers: ${firstEvent.speakers.map(s => s.name).join(', ') || 'None'}`)
      }
    } else {
      console.log('❌ Failed!')
      console.log('   Response:', data1)
    }
    
    console.log('\n' + '='.repeat(50) + '\n')
    
    // Test 2: Get events by day
    console.log('2. Testing GET /api/events?day=1 (day 1 events)')
    const response2 = await fetch(`${baseUrl}/api/events?day=1`)
    const data2 = await response2.json()
    
    if (response2.ok) {
      console.log('✅ Success!')
      console.log(`   Day 1 events: ${data2.events.length}`)
      data2.events.forEach(event => {
        console.log(`   - ${event.title} (${event.type}) at ${event.formattedTime || 'TBD'}`)
      })
    } else {
      console.log('❌ Failed!')
      console.log('   Response:', data2)
    }
    
    console.log('\n' + '='.repeat(50) + '\n')
    
    // Test 3: Get events by type
    console.log('3. Testing GET /api/events?type=workshop (workshop events)')
    const response3 = await fetch(`${baseUrl}/api/events?type=workshop`)
    const data3 = await response3.json()
    
    if (response3.ok) {
      console.log('✅ Success!')
      console.log(`   Workshop events: ${data3.events.length}`)
      data3.events.forEach(event => {
        console.log(`   - ${event.title} on ${event.dayLabel}`)
        console.log(`     Duration: ${event.duration || 'Unknown'}`)
        console.log(`     Location: ${event.location || 'TBD'}`)
      })
    } else {
      console.log('❌ Failed!')
      console.log('   Response:', data3)
    }
    
    console.log('\n' + '='.repeat(50) + '\n')
    
    // Test 4: Get upcoming events
    console.log('4. Testing GET /api/events?upcoming=true (upcoming events)')
    const response4 = await fetch(`${baseUrl}/api/events?upcoming=true`)
    const data4 = await response4.json()
    
    if (response4.ok) {
      console.log('✅ Success!')
      console.log(`   Upcoming events: ${data4.events.length}`)
      data4.events.forEach(event => {
        console.log(`   - ${event.title} (${event.isUpcoming ? 'Upcoming' : 'Past'})`)
      })
    } else {
      console.log('❌ Failed!')
      console.log('   Response:', data4)
    }
    
    console.log('\n' + '='.repeat(50) + '\n')
    
    // Test 5: Get events with pagination
    console.log('5. Testing GET /api/events?limit=2&page=1 (pagination)')
    const response5 = await fetch(`${baseUrl}/api/events?limit=2&page=1`)
    const data5 = await response5.json()
    
    if (response5.ok) {
      console.log('✅ Success!')
      console.log(`   Events per page: ${data5.events.length}`)
      console.log(`   Total events: ${data5.totalEvents}`)
      data5.events.forEach(event => {
        console.log(`   - ${event.title}`)
      })
    } else {
      console.log('❌ Failed!')
      console.log('   Response:', data5)
    }
    
  } catch (error) {
    console.error('❌ Error testing endpoint:', error.message)
  }
}

// Run the test
testEventsEndpoint()
