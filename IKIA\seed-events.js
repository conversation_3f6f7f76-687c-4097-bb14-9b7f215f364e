import fetch from 'node-fetch'

async function seedEvents() {
  const baseUrl = 'http://localhost:3001'
  
  console.log('Seeding Events Data...\n')
  
  try {
    // First, create speakers
    console.log('Creating speakers...')
    
    const speaker1Response = await fetch(`${baseUrl}/api/speakers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Dr. <PERSON>',
        title: 'Chief Technology Officer',
        company: 'TechCorp Inc.',
        bio: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'Dr. <PERSON> is a renowned expert in artificial intelligence and machine learning with over 15 years of experience in the tech industry.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        slug: 'dr-sarah-johnson',
      }),
    })
    
    const speaker1 = await speaker1Response.json()
    console.log(`✅ Created speaker: ${speaker1.doc?.name || 'Dr. <PERSON>'}`)
    
    const speaker2Response = await fetch(`${baseUrl}/api/speakers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Michael Chen',
        title: 'Senior Software Engineer',
        company: 'DevSolutions',
        bio: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'Michael Chen is a full-stack developer specializing in React, Node.js, and cloud architecture.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        slug: 'michael-chen',
      }),
    })
    
    const speaker2 = await speaker2Response.json()
    console.log(`✅ Created speaker: ${speaker2.doc?.name || 'Michael Chen'}`)
    
    const speaker3Response = await fetch(`${baseUrl}/api/speakers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Emily Rodriguez',
        title: 'UX Design Lead',
        company: 'DesignStudio',
        bio: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'Emily Rodriguez is a user experience designer with a passion for creating intuitive and accessible digital products.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        slug: 'emily-rodriguez',
      }),
    })
    
    const speaker3 = await speaker3Response.json()
    console.log(`✅ Created speaker: ${speaker3.doc?.name || 'Emily Rodriguez'}`)
    
    console.log('\nCreating events...')
    
    // Create events
    const events = [
      {
        title: 'Opening Keynote: The Future of AI',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'Join us for an inspiring keynote about the future of artificial intelligence and its impact on society.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        type: 'keynote',
        date: '2024-03-15',
        startTime: '09:00',
        endTime: '10:00',
        day: 1,
        location: 'Main Auditorium',
        speakers: [speaker1.doc?.id],
      },
      {
        title: 'Building Scalable Web Applications',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'Learn best practices for building scalable web applications using modern frameworks and cloud technologies.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        type: 'workshop',
        date: '2024-03-15',
        startTime: '10:30',
        endTime: '12:00',
        day: 1,
        location: 'Workshop Room A',
        speakers: [speaker2.doc?.id],
      },
      {
        title: 'Design Systems Panel Discussion',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    detail: 0,
                    format: 0,
                    mode: 'normal',
                    style: '',
                    text: 'A panel discussion on creating and maintaining effective design systems in large organizations.',
                    type: 'text',
                    version: 1,
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        type: 'panel',
        date: '2024-03-15',
        startTime: '14:00',
        endTime: '15:30',
        day: 1,
        location: 'Conference Room B',
        speakers: [speaker3.doc?.id, speaker1.doc?.id],
      },
    ]
    
    for (const eventData of events) {
      const eventResponse = await fetch(`${baseUrl}/api/events`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(eventData),
      })
      
      const event = await eventResponse.json()
      console.log(`✅ Created event: ${event.doc?.title || eventData.title}`)
    }
    
    console.log('\n🎉 Events seeded successfully!')
    
  } catch (error) {
    console.error('❌ Error seeding events:', error.message)
  }
}

// Run the seeding
seedEvents()
