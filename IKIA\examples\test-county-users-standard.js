// Test script for standard County Users endpoint
// Run with: node examples/test-county-users-standard.js

const BASE_URL = 'http://localhost:3000/api'

// Test credentials
const ADMIN_EMAIL = '<EMAIL>'
const ADMIN_PASSWORD = 'password123'

let authToken = null
let testCountyId = null
let testUserIds = []

async function login() {
  console.log('🔐 Logging in as admin...')
  
  try {
    const response = await fetch(`${BASE_URL}/users/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD,
      }),
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Login successful!')
      authToken = data.token
      return true
    } else {
      console.log('❌ Login failed:', data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Login error:', error.message)
    return false
  }
}

async function createTestData() {
  console.log('\n🏗️  Creating test data...')
  
  if (!authToken) {
    console.log('❌ No auth token available')
    return false
  }

  // Create test county
  try {
    const countyResponse = await fetch(`${BASE_URL}/counties`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify({
        name: 'Standard Test County',
        code: 'STANDARD-001',
        coordinates: { latitude: -1.5000, longitude: 36.5000 },
        description: 'A test county for standard endpoint testing',
        isActive: true
      }),
    })

    const countyData = await countyResponse.json()
    
    if (countyResponse.ok) {
      testCountyId = countyData.county.id
      console.log(`✅ Created test county: ${countyData.county.name} (ID: ${testCountyId})`)
    } else {
      console.log('❌ Failed to create county:', countyData.error)
      return false
    }
  } catch (error) {
    console.error('❌ County creation error:', error.message)
    return false
  }

  // Create test users
  const users = [
    { email: '<EMAIL>', name: 'Alice Johnson', county: testCountyId },
    { email: '<EMAIL>', name: 'Bob Smith', county: testCountyId },
    { email: '<EMAIL>', name: 'Charlie Brown', county: testCountyId },
    { email: '<EMAIL>', name: 'Diana Prince', county: testCountyId },
    { email: '<EMAIL>', name: 'Eve Wilson', county: testCountyId }
  ]

  for (const userData of users) {
    try {
      const userResponse = await fetch(`${BASE_URL}/users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...userData,
          password: 'password123'
        }),
      })

      const userResult = await userResponse.json()
      
      if (userResponse.ok) {
        testUserIds.push(userResult.doc.id)
        console.log(`✅ Created user: ${userData.name} (ID: ${userResult.doc.id})`)
      } else {
        console.log(`❌ Failed to create user ${userData.name}:`, userResult.message)
      }
    } catch (error) {
      console.error(`❌ User creation error for ${userData.name}:`, error.message)
    }
  }

  return testUserIds.length > 0
}

async function testBasicEndpoint() {
  console.log('\n🔍 Test 1: Basic county users endpoint')
  
  try {
    const response = await fetch(`${BASE_URL}/counties/${testCountyId}/users`)
    const data = await response.json()
    
    if (response.ok) {
      console.log(`✅ Successfully retrieved users from county endpoint`)
      console.log(`County: ${data.county.name} (${data.county.code})`)
      console.log(`Total users: ${data.totalUsers}`)
      console.log(`Users on this page: ${data.users.length}`)
      console.log('Users:')
      data.users.forEach(user => {
        console.log(`  - ${user.name} (${user.email})`)
      })
      return true
    } else {
      console.log('❌ Basic endpoint failed:', data.error)
      return false
    }
  } catch (error) {
    console.error('❌ Basic endpoint error:', error.message)
    return false
  }
}

async function testPagination() {
  console.log('\n📄 Test 2: Pagination')
  
  try {
    // Test first page with limit 2
    const response1 = await fetch(`${BASE_URL}/counties/${testCountyId}/users?limit=2&page=1`)
    const data1 = await response1.json()
    
    if (response1.ok) {
      console.log(`✅ Page 1: ${data1.users.length} users (limit: ${data1.limit})`)
      console.log(`Has next page: ${data1.hasNextPage}`)
      console.log(`Has prev page: ${data1.hasPrevPage}`)
      
      // Test second page
      const response2 = await fetch(`${BASE_URL}/counties/${testCountyId}/users?limit=2&page=2`)
      const data2 = await response2.json()
      
      if (response2.ok) {
        console.log(`✅ Page 2: ${data2.users.length} users (limit: ${data2.limit})`)
        console.log(`Has next page: ${data2.hasNextPage}`)
        console.log(`Has prev page: ${data2.hasPrevPage}`)
        return true
      }
    } else {
      console.log('❌ Pagination failed:', data1.error)
      return false
    }
  } catch (error) {
    console.error('❌ Pagination error:', error.message)
    return false
  }
}

async function testSorting() {
  console.log('\n🔤 Test 3: Sorting')
  
  try {
    // Test sort by name
    const response1 = await fetch(`${BASE_URL}/counties/${testCountyId}/users?sort=name`)
    const data1 = await response1.json()
    
    if (response1.ok) {
      console.log('✅ Sort by name (ascending):')
      data1.users.slice(0, 3).forEach(user => {
        console.log(`  - ${user.name}`)
      })
      
      // Test sort by name descending
      const response2 = await fetch(`${BASE_URL}/counties/${testCountyId}/users?sort=-name`)
      const data2 = await response2.json()
      
      if (response2.ok) {
        console.log('✅ Sort by name (descending):')
        data2.users.slice(0, 3).forEach(user => {
          console.log(`  - ${user.name}`)
        })
        return true
      }
    } else {
      console.log('❌ Sorting failed:', data1.error)
      return false
    }
  } catch (error) {
    console.error('❌ Sorting error:', error.message)
    return false
  }
}

async function testNonExistentCounty() {
  console.log('\n❌ Test 4: Non-existent county')
  
  try {
    const response = await fetch(`${BASE_URL}/counties/999999/users`)
    const data = await response.json()
    
    if (response.status === 404) {
      console.log('✅ Correctly returned 404 for non-existent county')
      console.log(`Error message: ${data.error}`)
      return true
    } else {
      console.log('❌ Should have returned 404 for non-existent county')
      return false
    }
  } catch (error) {
    console.error('❌ Non-existent county test error:', error.message)
    return false
  }
}

async function testCombinedParameters() {
  console.log('\n🔧 Test 5: Combined parameters')
  
  try {
    const response = await fetch(`${BASE_URL}/counties/${testCountyId}/users?limit=3&page=1&sort=email`)
    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Combined parameters (limit=3, page=1, sort=email):')
      console.log(`Total users: ${data.totalUsers}`)
      console.log(`Page: ${data.page}, Limit: ${data.limit}`)
      console.log('Users (sorted by email):')
      data.users.forEach(user => {
        console.log(`  - ${user.name} (${user.email})`)
      })
      return true
    } else {
      console.log('❌ Combined parameters failed:', data.error)
      return false
    }
  } catch (error) {
    console.error('❌ Combined parameters error:', error.message)
    return false
  }
}

async function cleanup() {
  console.log('\n🧹 Cleaning up test data...')
  
  if (!authToken) {
    console.log('❌ No auth token for cleanup')
    return
  }

  // Delete test users
  for (const userId of testUserIds) {
    try {
      await fetch(`${BASE_URL}/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      })
      console.log(`✅ Deleted user ${userId}`)
    } catch (error) {
      console.log(`❌ Failed to delete user ${userId}`)
    }
  }

  // Delete test county
  if (testCountyId) {
    try {
      await fetch(`${BASE_URL}/counties/${testCountyId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      })
      console.log(`✅ Deleted county ${testCountyId}`)
    } catch (error) {
      console.log(`❌ Failed to delete county ${testCountyId}`)
    }
  }
}

async function runStandardEndpointTests() {
  console.log('🧪 Starting Standard County Users Endpoint Tests')
  console.log('=' .repeat(60))
  
  // Login
  const loginSuccess = await login()
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication')
    return
  }

  // Create test data
  const dataCreated = await createTestData()
  if (!dataCreated) {
    console.log('❌ Cannot proceed without test data')
    return
  }

  // Run tests
  await testBasicEndpoint()
  await testPagination()
  await testSorting()
  await testNonExistentCounty()
  await testCombinedParameters()
  
  // Cleanup
  await cleanup()
  
  console.log('\n' + '=' .repeat(60))
  console.log('🏁 Standard county users endpoint tests completed!')
  console.log('\n📋 Summary:')
  console.log('✅ Standard REST endpoint: GET /api/counties/:id/users')
  console.log('✅ Supports pagination with limit and page parameters')
  console.log('✅ Supports sorting with sort parameter')
  console.log('✅ Proper error handling for non-existent counties')
  console.log('✅ Clean, intuitive API following REST conventions')
}

// Run the tests
runStandardEndpointTests().catch(console.error)
