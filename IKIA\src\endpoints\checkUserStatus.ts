import type { PayloadRequest } from 'payload'

/**
 * Endpoint to check user verification status and profile completeness
 */
export const checkUserStatusEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    const { email } = req.body

    if (!email) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'MISSING_EMAIL',
            message: 'Email is required'
          }
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Find user by email
    const users = await req.payload.find({
      collection: 'users',
      where: {
        email: {
          equals: email
        }
      }
    })

    if (users.docs.length === 0) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found'
          }
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    const user = users.docs[0]

    // Check user status
    const status = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      verified: user._verified,
      verificationToken: user._verificationtoken ? 'Present' : 'None',
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      hasPassword: !!user.password,
      profileComplete: !!(user.name && user.role),
      canLogin: true,
      issues: []
    }

    // Check for potential issues
    if (user._verified === false) {
      status.issues.push('Email not verified')
      status.canLogin = false
    }

    if (!user.name) {
      status.issues.push('Name missing')
    }

    if (!user.role) {
      status.issues.push('Role missing')
    }

    if (!user.password) {
      status.issues.push('Password not set')
      status.canLogin = false
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: status
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    )

  } catch (error: any) {
    console.error('Check user status error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          details: error.message
        }
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}
