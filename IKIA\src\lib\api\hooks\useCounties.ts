'use client'

import { useState, useEffect, useCallback } from 'react'
import {
  countiesApi,
  type County,
  type CountiesResponse,
  type CountiesApiParams,
} from '../counties'

export interface UseCountiesOptions extends CountiesApiParams {
  enabled?: boolean
  refetchOnMount?: boolean
}

export interface UseCountiesReturn {
  counties: County[]
  loading: boolean
  error: string | null
  totalCounties: number
  page: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
  refetch: () => Promise<void>
  fetchMore: () => Promise<void>
}

/**
 * Hook for fetching counties with automatic loading states
 */
export function useCounties(options: UseCountiesOptions = {}): UseCountiesReturn {
  const { enabled = true, refetchOnMount = true, ...apiParams } = options

  const [counties, setCounties] = useState<County[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [totalCounties, setTotalCounties] = useState(0)
  const [page, setPage] = useState(apiParams.page || 1)
  const [totalPages, setTotalPages] = useState(0)
  const [hasNextPage, setHasNextPage] = useState(false)
  const [hasPrevPage, setHasPrevPage] = useState(false)

  const fetchCounties = useCallback(
    async (pageNum?: number, append = false) => {
      if (!enabled) return

      try {
        setLoading(true)
        setError(null)

        const params = {
          ...apiParams,
          page: pageNum || page,
        }

        const response = await countiesApi.getCounties(params)

        // Ensure response and counties array exist
        if (response && Array.isArray(response.counties)) {
          if (append) {
            setCounties((prev) => [...prev, ...response.counties])
          } else {
            setCounties(response.counties)
          }

          setTotalCounties(response.totalCounties || 0)
          setPage(response.page || 1)
          setTotalPages(response.totalPages || 0)
          setHasNextPage(response.hasNextPage || false)
          setHasPrevPage(response.hasPrevPage || false)
        } else {
          console.error('Invalid response format:', response)
          setError('Invalid response format from API')
          setCounties([])
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch counties'
        setError(errorMessage)
        console.error('Error fetching counties:', err)
        // Set empty array as fallback
        setCounties([])
        setTotalCounties(0)
        setPage(1)
        setTotalPages(0)
        setHasNextPage(false)
        setHasPrevPage(false)
      } finally {
        setLoading(false)
      }
    },
    [enabled, apiParams, page],
  )

  const refetch = useCallback(async () => {
    await fetchCounties(1, false)
  }, [fetchCounties])

  const fetchMore = useCallback(async () => {
    if (hasNextPage && !loading) {
      await fetchCounties(page + 1, true)
    }
  }, [hasNextPage, loading, page, fetchCounties])

  useEffect(() => {
    if (enabled && refetchOnMount) {
      fetchCounties()
    }
  }, [enabled, refetchOnMount, fetchCounties])

  return {
    counties,
    loading,
    error,
    totalCounties,
    page,
    totalPages,
    hasNextPage,
    hasPrevPage,
    refetch,
    fetchMore,
  }
}

/**
 * Hook for fetching active counties only
 */
export function useActiveCounties(
  options: Omit<UseCountiesOptions, 'active'> = {},
): UseCountiesReturn {
  return useCounties({ ...options, active: true })
}

/**
 * Hook for fetching a single county
 */
export function useCounty(id: string | null, enabled = true) {
  const [county, setCounty] = useState<County | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCounty = useCallback(async () => {
    if (!id || !enabled) return

    try {
      setLoading(true)
      setError(null)

      const response = await countiesApi.getCounty(id)
      setCounty(response.county)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch county'
      setError(errorMessage)
      console.error('Error fetching county:', err)
    } finally {
      setLoading(false)
    }
  }, [id, enabled])

  const refetch = useCallback(async () => {
    await fetchCounty()
  }, [fetchCounty])

  useEffect(() => {
    if (id && enabled) {
      fetchCounty()
    }
  }, [id, enabled, fetchCounty])

  return {
    county,
    loading,
    error,
    refetch,
  }
}

/**
 * Hook for searching counties by name
 */
export function useCountySearch(searchTerm: string, limit = 10, enabled = true) {
  const [counties, setCounties] = useState<County[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const search = useCallback(async () => {
    if (!searchTerm.trim() || !enabled) {
      setCounties([])
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await countiesApi.searchCounties(searchTerm, limit)
      setCounties(response.counties)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to search counties'
      setError(errorMessage)
      console.error('Error searching counties:', err)
    } finally {
      setLoading(false)
    }
  }, [searchTerm, limit, enabled])

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      search()
    }, 300) // Debounce search

    return () => clearTimeout(timeoutId)
  }, [search])

  return {
    counties,
    loading,
    error,
    search,
  }
}
