'use client'

import Image from 'next/image'

interface Category {
  title: string
  description: string
  bulletPoints: string[]
}

interface KnowledgeSectionsProps {
  categories: Category[]
}

export default function KnowledgeSections({ categories }: KnowledgeSectionsProps) {
  return (
    <div className="py-12">
      {/* Large screens: Normal alternating layout */}
      <div className="hidden lg:block space-y-16">
        {categories.map((category, index) => (
          <div key={index} className="max-w-5xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {index % 2 === 0 ? (
                <>
                  {/* Image left, text right */}
                  <div className="flex justify-center">
                    <div className="relative aspect-square w-full max-w-[350px] border border-gray-200 rounded-lg overflow-hidden">
                      <Image
                        src="/placeholder.svg?height=350&width=350"
                        alt={category.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                  </div>
                  <div className="space-y-6">
                    <h2 className="text-2xl md:text-3xl font-semibold text-gray-900">
                      {category.title}
                    </h2>
                    <p className="text-gray-600 text-lg leading-relaxed">{category.description}</p>
                    <ul className="space-y-3">
                      {category.bulletPoints.map((point, pointIndex) => (
                        <li key={pointIndex} className="text-gray-500 flex items-start">
                          <span className="mr-3 mt-2 w-1.5 h-1.5 bg-gray-400 rounded-full flex-shrink-0"></span>
                          <span className="leading-relaxed">{point}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </>
              ) : (
                <>
                  {/* Text left, image right */}
                  <div className="space-y-6">
                    <h2 className="text-2xl md:text-3xl font-semibold text-gray-900">
                      {category.title}
                    </h2>
                    <p className="text-gray-600 text-lg leading-relaxed">{category.description}</p>
                    <ul className="space-y-3">
                      {category.bulletPoints.map((point, pointIndex) => (
                        <li key={pointIndex} className="text-gray-500 flex items-start">
                          <span className="mr-3 mt-2 w-1.5 h-1.5 bg-gray-400 rounded-full flex-shrink-0"></span>
                          <span className="leading-relaxed">{point}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="flex justify-center">
                    <div className="relative aspect-square w-full max-w-[350px] border border-gray-200 rounded-lg overflow-hidden">
                      <Image
                        src="/placeholder.svg?height=350&width=350"
                        alt={category.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Small screens: Alternating image-text pattern across sections */}
      <div className="lg:hidden space-y-8">
        {categories.map((category, index) => (
          <div key={index} className="max-w-lg mx-auto">
            {index % 2 === 0 ? (
              <>
                {/* Show image first for even indices */}
                <div className="flex justify-center mb-6">
                  <div className="relative aspect-square w-full max-w-[300px] border border-gray-200 rounded-lg overflow-hidden">
                    <Image
                      src="/placeholder.svg?height=300&width=300"
                      alt={category.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
                <div className="space-y-4 text-center">
                  <h2 className="text-xl font-semibold text-gray-900">{category.title}</h2>
                  <p className="text-gray-600 leading-relaxed">{category.description}</p>
                  <ul className="space-y-2 text-left">
                    {category.bulletPoints.map((point, pointIndex) => (
                      <li key={pointIndex} className="text-sm text-gray-500 flex items-start">
                        <span className="mr-2 mt-1 w-1 h-1 bg-gray-400 rounded-full flex-shrink-0"></span>
                        <span className="leading-relaxed">{point}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </>
            ) : (
              <>
                {/* Show text first for odd indices */}
                <div className="space-y-4 text-center mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">{category.title}</h2>
                  <p className="text-gray-600 leading-relaxed">{category.description}</p>
                  <ul className="space-y-2 text-left">
                    {category.bulletPoints.map((point, pointIndex) => (
                      <li key={pointIndex} className="text-sm text-gray-500 flex items-start">
                        <span className="mr-2 mt-1 w-1 h-1 bg-gray-400 rounded-full flex-shrink-0"></span>
                        <span className="leading-relaxed">{point}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="flex justify-center">
                  <div className="relative aspect-square w-full max-w-[300px] border border-gray-200 rounded-lg overflow-hidden">
                    <Image
                      src="/placeholder.svg?height=300&width=300"
                      alt={category.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
