import { generatePesaflowHash } from '../utils/pesaflowHash'

/**
 * Common Hash Service for all eCitizen API endpoints
 * Handles server-side hash generation for secure API calls
 */
export class HashService {
  private static instance: HashService
  private apiClientId: string
  private clientSecret: string
  private clientKey: string

  private constructor() {
    const { PESAFLOW_API_CLIENT_ID, PESAFLOW_CLIENT_SECRET, PESAFLOW_CLIENT_KEY } = process.env

    if (!PESAFLOW_API_CLIENT_ID || !PESAFLOW_CLIENT_SECRET || !PESAFLOW_CLIENT_KEY) {
      throw new Error('Missing required Pesaflow configuration')
    }

    this.apiClientId = PESAFLOW_API_CLIENT_ID
    this.clientSecret = PESAFLOW_CLIENT_SECRET
    this.clientKey = PESAFLOW_CLIENT_KEY
  }

  public static getInstance(): HashService {
    if (!HashService.instance) {
      HashService.instance = new HashService()
    }
    return HashService.instance
  }

  /**
   * Generate hash for payment validation
   * data_string = api_client_id + ref_no + amount + secret
   */
  public generatePaymentValidationHash(
    ref_no: string,
    amount: string,
  ): {
    hash: string
    api_client_id: string
  } {
    const dataString = `${this.apiClientId}${ref_no}${amount}${this.clientSecret}`
    const hash = generatePesaflowHash(dataString, this.clientKey)

    console.log('Generated payment validation hash:', {
      dataString: `${this.apiClientId}${ref_no}${amount}[SECRET]`,
      hash,
    })

    return {
      hash,
      api_client_id: this.apiClientId,
    }
  }

  /**
   * Generate hash for payment confirmation
   * data_string = api_client_id + ref_no + amount + currency + gateway_transaction_id + gateway_transaction_date + customer_name + customer_account_number + secret
   */
  public generatePaymentConfirmationHash(
    ref_no: string,
    amount: string,
    currency: string,
    gateway_transaction_id: string,
    gateway_transaction_date: string,
    customer_name: string,
    customer_account_number: string,
  ): {
    hash: string
    api_client_id: string
  } {
    const dataString = `${this.apiClientId}${ref_no}${amount}${currency}${gateway_transaction_id}${gateway_transaction_date}${customer_name}${customer_account_number}${this.clientSecret}`
    const hash = generatePesaflowHash(dataString, this.clientKey)

    console.log('Generated payment confirmation hash:', {
      dataString: `${this.apiClientId}${ref_no}${amount}${currency}${gateway_transaction_id}${gateway_transaction_date}${customer_name}${customer_account_number}[SECRET]`,
      hash,
    })

    return {
      hash,
      api_client_id: this.apiClientId,
    }
  }

  /**
   * Generate hash for payment status query
   * data_string = api_client_id + amountExpected + serviceID + clientIDNumber + currency + billRefNumber + billDesc + clientName + secret
   */
  public generatePaymentStatusHash(
    ref_no: string,
    amountExpected: string,
    serviceID: string,
    clientIDNumber: string,
    currency: string,
    billDesc: string,
    clientName: string,
  ): {
    hash: string
    api_client_id: string
  } {
    const dataString = `${this.apiClientId}${amountExpected}${serviceID}${clientIDNumber}${currency}${ref_no}${billDesc}${clientName}${this.clientSecret}`
    const hash = generatePesaflowHash(dataString, this.clientKey)

    console.log('Generated payment status hash:', {
      dataString: `${this.apiClientId}${amountExpected}${serviceID}${clientIDNumber}${currency}${ref_no}${billDesc}${clientName}[SECRET]`,
      hash,
    })

    return {
      hash,
      api_client_id: this.apiClientId,
    }
  }

  /**
   * Generate hash for checkout
   * data_string = api_client_id + ref_no + amount + secret
   */
  public generateCheckoutHash(
    ref_no: string,
    amount: string,
  ): {
    hash: string
    api_client_id: string
  } {
    const dataString = `${this.apiClientId}${ref_no}${amount}${this.clientSecret}`
    const hash = generatePesaflowHash(dataString, this.clientKey)

    console.log('Generated checkout hash:', {
      dataString: `${this.apiClientId}${ref_no}${amount}[SECRET]`,
      hash,
    })

    return {
      hash,
      api_client_id: this.apiClientId,
    }
  }

  /**
   * Verify incoming hash for IPN
   * data_string = client_invoice_ref + invoice_number + amount_paid + payment_date + secret
   */
  public verifyIPNHash(
    client_invoice_ref: string,
    invoice_number: string,
    amount_paid: string,
    payment_date: string,
    received_hash: string,
  ): boolean {
    const expectedDataString = `${client_invoice_ref}${invoice_number}${amount_paid}${payment_date}${this.clientSecret}`
    const expectedHash = generatePesaflowHash(expectedDataString, this.clientKey)

    const isValid = received_hash === expectedHash

    console.log('IPN hash verification:', {
      dataString: `${client_invoice_ref}${invoice_number}${amount_paid}${payment_date}[SECRET]`,
      received: received_hash,
      expected: expectedHash,
      valid: isValid,
    })

    return isValid
  }

  /**
   * Get API client ID
   */
  public getApiClientId(): string {
    return this.apiClientId
  }

  /**
   * Get client secret (for internal use only)
   */
  private getClientSecret(): string {
    return this.clientSecret
  }

  /**
   * Get client key (for internal use only)
   */
  private getClientKey(): string {
    return this.clientKey
  }
}
