import type { PayloadRequest } from 'payload'
import crypto from 'crypto'
import { PesaflowPayloadService } from '../services/PesaflowPayloadService'
import { EmailService } from '../utils/emailService'

interface CitizenRegistrationData {
  // Personal Information
  name: string
  email: string
  phone_number: string
  id_number: string
  county: string

  // Service Package Selection
  selected_package: string

  // Optional fields
  business_type?: string
  registration_purpose?: string
}

interface PesaflowCheckoutData {
  client_invoice_ref: string
  invoice_number: string
  amount: string
  currency: string
  customer_email: string
  customer_phone: string
  customer_name: string
  description: string
  notificationURL: string
  returnURL: string
  cancelURL: string
}

/**
 * Comprehensive citizen registration endpoint with integrated payment processing
 * POST /api/citizens/register
 */
export const citizenRegistrationEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('Citizen registration request received:', {
      headers: Object.fromEntries(req.headers.entries()),
      timestamp: new Date().toISOString(),
    })

    // Parse registration data
    let registrationData: CitizenRegistrationData
    try {
      registrationData = (await req.json()) as CitizenRegistrationData
    } catch (error) {
      console.error('Failed to parse registration data:', error)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid registration data format',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    console.log('Processing registration for:', {
      email: registrationData.email,
      name: registrationData.name,
      selected_package: registrationData.selected_package,
    })

    // Validate required fields
    const requiredFields = [
      'name',
      'email',
      'phone_number',
      'id_number',
      'county',
      'selected_package',
    ]
    const missingFields = requiredFields.filter(
      (field) => !registrationData[field as keyof CitizenRegistrationData],
    )

    if (missingFields.length > 0) {
      return new Response(
        JSON.stringify({
          success: false,
          error: `Missing required fields: ${missingFields.join(', ')}`,
          missing_fields: missingFields,
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Check if user already exists
    const existingUser = await req.payload.find({
      collection: 'users',
      where: {
        or: [
          { email: { equals: registrationData.email } },
          { id_number: { equals: registrationData.id_number } },
        ],
      },
      limit: 1,
    })

    if (existingUser.docs.length > 0) {
      const existingField =
        existingUser.docs[0].email === registrationData.email ? 'email' : 'ID number'
      return new Response(
        JSON.stringify({
          success: false,
          error: `User with this ${existingField} already exists`,
          existing_field: existingField,
        }),
        {
          status: 409,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Validate county exists
    try {
      const county = await req.payload.findByID({
        collection: 'counties',
        id: registrationData.county,
      })

      if (!county) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Invalid county selected',
            available_counties: 'Please use county IDs: 13 (Nairobi), 14 (Mombasa), or 15 (Kisumu)',
          }),
          {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid county selected',
          available_counties: 'Please use county IDs: 13 (Nairobi), 14 (Mombasa), or 15 (Kisumu)',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Validate selected delegate package
    const delegatePackage = await req.payload.findByID({
      collection: 'delegatepackages',
      id: registrationData.selected_package,
    })

    if (!delegatePackage || !delegatePackage.isActive) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid or inactive delegate package selected',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    console.log('Selected package validated:', {
      id: delegatePackage.id,
      name: delegatePackage.name,
      price: delegatePackage.price,
      currency: delegatePackage.currency,
    })

    // Generate 6-character password
    const emailService = new EmailService(req.payload)
    const generatedPassword = emailService.generate6CharPassword()

    // Create user account
    const newUser = await req.payload.create({
      collection: 'users',
      data: {
        name: registrationData.name,
        email: registrationData.email,
        password: generatedPassword,
        phone_number: registrationData.phone_number,
        id_number: registrationData.id_number,
        county: registrationData.county,
        role: 'citizen', // Set role as citizen by default
        _verified: false, // User needs to verify email
        selected_package: null, // Will be set after successful payment
        package_status: 'none', // Will be activated after payment
        package_expiry: null,
        business_type: registrationData.business_type || null,
        registration_purpose: registrationData.registration_purpose || null,
        registration_context: {
          created_during_package_flow: true,
          temporary_password_sent: false,
          initial_package_invoice: null, // Will be set after invoice creation
        },
        payment_profile: {
          preferred_currency: delegatePackage.currency,
          daily_limit: 50000,
          monthly_limit: 500000,
        },
        statistics: {
          total_payments: 0,
          total_amount_paid: 0,
          last_payment_date: null,
        },
      },
    })

    console.log('User created successfully:', {
      id: newUser.id,
      email: newUser.email,
      name: newUser.name,
    })

    // Generate unique payment reference
    const timestamp = Date.now()
    const randomSuffix = crypto.randomBytes(4).toString('hex').toUpperCase()
    const paymentReference = `REG-${timestamp}-${randomSuffix}`

    // Create invoice for selected package
    const invoice = await req.payload.create({
      collection: 'invoices',
      data: {
        user: newUser.id,
        package: delegatePackage.id,
        amount: delegatePackage.price,
        currency: delegatePackage.currency,
        status: 'pending',
        payment_reference: paymentReference,
        due_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
        customer_info: {
          name: registrationData.name,
          email: registrationData.email,
          phone: registrationData.phone_number,
          id_number: registrationData.id_number,
        },
        registration_context: {
          is_registration_payment: true,
          user_created_during_flow: true,
          temporary_password_sent: false,
        },
        notes: `Registration payment for ${delegatePackage.name} - New citizen account`,
      },
    })

    console.log('Invoice created successfully:', {
      id: invoice.id,
      invoice_number: invoice.invoice_number,
      amount: invoice.amount,
      currency: invoice.currency,
    })

    // Update user with initial package invoice reference
    await req.payload.update({
      collection: 'users',
      id: newUser.id,
      data: {
        registration_context: {
          ...newUser.registration_context,
          initial_package_invoice: invoice.id,
        },
      },
    })

    // Initialize Pesaflow payload service
    let pesaflowService: PesaflowPayloadService
    let pesaflowPayload
    try {
      pesaflowService = new PesaflowPayloadService(req)

      // Build complete payload with all required parameters
      pesaflowPayload = pesaflowService.buildPayloadFromRegistration(
        registrationData,
        delegatePackage,
        invoice,
        `&invoice=${invoice.id}`, // Success URL suffix
      )

      // Validate payload before sending
      pesaflowService.validatePayload(pesaflowPayload)
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Pesaflow payload generation failed',
          message: error instanceof Error ? error.message : 'Unknown error',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Actually call Pesaflow API to get the real checkout URL
    const pesaflowApiUrl = pesaflowService.getCheckoutUrl()
    let pesaflowResponse
    let actualCheckoutURL

    console.log('🚀 Sending COMPLETE Pesaflow payload with ALL 15 parameters:', {
      url: pesaflowApiUrl,
      payload: pesaflowPayload, // Log the complete payload
      parameterCount: Object.keys(pesaflowPayload).length,
    })

    // Verify all mandatory parameters are present
    const requiredParams = [
      'apiClientID',
      'serviceID',
      'billRefNumber',
      'billDesc',
      'clientMSISDN',
      'clientIDNumber',
      'clientName',
      'clientEmail',
      'notificationURL',
      'callBackURLOnSuccess',
      'currency',
      'amountExpected',
      'secureHash',
    ]

    const missingParams = requiredParams.filter(
      (param) => !pesaflowPayload[param as keyof typeof pesaflowPayload],
    )

    if (missingParams.length > 0) {
      console.error('❌ Missing mandatory parameters:', missingParams)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Incomplete Pesaflow payload',
          missing_parameters: missingParams,
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    console.log('✅ All mandatory parameters present. Payload ready for Pesaflow API.')

    try {
      // Add timeout for the checkout request (reduced to 20 seconds)
      const CHECKOUT_TIMEOUT = 20000 // 20 seconds
      const controller = new AbortController()
      const timeoutId = setTimeout(() => {
        controller.abort()
      }, CHECKOUT_TIMEOUT)

      console.log('📤 Making POST request to Pesaflow:', {
        url: pesaflowApiUrl,
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        payloadSize: JSON.stringify(pesaflowPayload).length,
        timeout: `${CHECKOUT_TIMEOUT / 1000}s`,
      })

      const startTime = Date.now()
      const response = await fetch(pesaflowApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json, text/html, */*',
          'User-Agent': 'IKIA-Payment-System/1.0',
        },
        body: JSON.stringify(pesaflowPayload),
        signal: controller.signal,
      })

      clearTimeout(timeoutId)
      const responseTime = Date.now() - startTime
      console.log(`📡 Pesaflow response received in ${responseTime}ms, status: ${response.status}`)

      // Log response headers for debugging
      console.log('📋 Pesaflow response headers:', {
        'content-type': response.headers.get('content-type'),
        'content-length': response.headers.get('content-length'),
        location: response.headers.get('location'),
        'set-cookie': response.headers.get('set-cookie'),
        'cache-control': response.headers.get('cache-control'),
        server: response.headers.get('server'),
      })

      // Handle different response types
      const contentType = response.headers.get('content-type')

      if (!contentType?.includes('application/json')) {
        // Handle non-JSON responses (redirects, HTML, etc.)
        console.log('📄 Non-JSON response detected, content-type:', contentType)

        // Try to read response body for logging
        try {
          const responseText = await response.text()
          const preview = responseText.substring(0, 500).replace(/\s+/g, ' ').trim()
          console.log(
            '📄 Response body preview:',
            preview + (responseText.length > 500 ? '...' : ''),
          )

          // Store full response for debugging
          pesaflowResponse = {
            status: response.status,
            statusText: response.statusText,
            contentType: contentType,
            bodyPreview: preview,
            fullBodyLength: responseText.length,
          } as Record<string, unknown>
        } catch (textError) {
          console.warn('⚠️ Could not read response body:', textError)
          pesaflowResponse = {
            status: response.status,
            statusText: response.statusText,
            contentType: contentType,
            error: 'Could not read response body',
          }
        }

        if (response.ok || (response.status >= 300 && response.status < 400)) {
          const location = response.headers.get('location')
          if (location) {
            actualCheckoutURL = location
            pesaflowResponse.redirect_url = location
            console.log('🔄 Redirect URL found:', location)
          } else {
            // If no location header, use the response URL or fallback
            actualCheckoutURL =
              response.url || `${pesaflowApiUrl}?ref=${pesaflowPayload.billRefNumber}`
            pesaflowResponse.checkout_url = actualCheckoutURL
            console.log('🔗 Using response URL or fallback:', actualCheckoutURL)
          }
        } else {
          throw new Error(`Pesaflow API returned ${response.status}: ${response.statusText}`)
        }
      } else {
        // Handle JSON responses
        console.log('📄 JSON response detected')
        pesaflowResponse = await response.json()

        console.log('📋 Pesaflow JSON response:', {
          ...pesaflowResponse,
          // Truncate long fields for readability
          ...(pesaflowResponse.html_content && {
            html_content: `[HTML_CONTENT_${pesaflowResponse.html_content.length}_CHARS]`,
          }),
        })

        if (!response.ok) {
          throw new Error(`Pesaflow API error: ${pesaflowResponse.message || response.statusText}`)
        }

        // Extract checkout URL from JSON response
        actualCheckoutURL =
          pesaflowResponse.invoice_link ||
          pesaflowResponse.checkout_url ||
          pesaflowResponse.redirect_url ||
          pesaflowResponse.payment_url ||
          `${pesaflowApiUrl}?ref=${pesaflowPayload.billRefNumber}`

        console.log('🔗 Extracted checkout URL from JSON:', actualCheckoutURL)
      }

      console.log('✅ Pesaflow checkout URL received:', actualCheckoutURL)
    } catch (error) {
      console.error('🚨 Pesaflow API call failed:', error)

      // Handle timeout specifically
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('⏱️ Pesaflow API timed out after 20 seconds')
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Payment Gateway Timeout',
            message:
              'The payment gateway is taking too long to respond. This may indicate server issues or high traffic. Please try again in a few minutes.',
            code: 'PESAFLOW_TIMEOUT',
            timeout: '20 seconds',
            details: {
              url: pesaflowApiUrl,
              payload_size: JSON.stringify(pesaflowPayload).length,
              user_id: newUser.id,
              invoice_id: invoice.id,
            },
          }),
          {
            status: 408,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }

      // Handle network errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        console.error('🌐 Network error connecting to Pesaflow:', error.message)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Payment Gateway Unavailable',
            message:
              'Unable to connect to the payment gateway. The service may be temporarily unavailable.',
            code: 'PESAFLOW_NETWORK_ERROR',
            details: {
              url: pesaflowApiUrl,
              error_message: error.message,
              user_id: newUser.id,
              invoice_id: invoice.id,
            },
          }),
          {
            status: 503,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }

      // Handle other API errors
      console.error('💥 Pesaflow API error:', error)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Payment Gateway Error',
          message:
            'An error occurred while processing your payment request. Please try again later.',
          code: 'PESAFLOW_API_ERROR',
          details: {
            error_type: error instanceof Error ? error.constructor.name : 'Unknown',
            error_message: error instanceof Error ? error.message : 'Unknown error',
            user_id: newUser.id,
            invoice_id: invoice.id,
          },
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Update invoice with complete Pesaflow data
    await req.payload.update({
      collection: 'invoices',
      id: invoice.id,
      data: {
        pesaflow_data: {
          checkout_url: actualCheckoutURL,
          bill_ref_number: pesaflowPayload.billRefNumber,
          gateway_response: pesaflowResponse,
          last_gateway_sync: new Date().toISOString(),
        },
      },
    })

    console.log('Registration workflow completed successfully:', {
      user_id: newUser.id,
      invoice_id: invoice.id,
      checkout_url: actualCheckoutURL,
    })

    // Send welcome email with generated password
    try {
      const loginUrl = process.env.PAYLOAD_PUBLIC_SERVER_URL
        ? `${process.env.PAYLOAD_PUBLIC_SERVER_URL}/admin/login`
        : 'http://localhost:3000/admin/login'

      const emailSent = await emailService.sendWelcomeEmailWithCredentials({
        user: {
          name: registrationData.name,
          email: registrationData.email,
        },
        credentials: {
          email: registrationData.email,
          temporaryPassword: generatedPassword,
        },
        package: {
          name: delegatePackage.name,
          price: delegatePackage.price,
          currency: delegatePackage.currency,
        },
        loginUrl,
        supportEmail: process.env.FROM_EMAIL || '<EMAIL>',
      })

      if (emailSent) {
        console.log('✅ Welcome email sent successfully to:', registrationData.email)
      } else {
        console.warn('⚠️ Failed to send welcome email to:', registrationData.email)
      }
    } catch (emailError) {
      console.error('❌ Error sending welcome email:', emailError)
      // Don't fail the registration if email fails
    }

    // Return success response with checkout URL
    return new Response(
      JSON.stringify({
        success: true,
        message:
          'Registration completed successfully. Your login credentials have been sent to your email. Redirecting to payment...',
        data: {
          user: {
            id: newUser.id,
            name: newUser.name,
            email: newUser.email,
            role: newUser.role,
          },
          invoice: {
            id: invoice.id,
            invoice_number: invoice.invoice_number,
            amount: invoice.amount,
            currency: invoice.currency,
            payment_reference: paymentReference,
          },
          delegate_package: {
            id: delegatePackage.id,
            name: delegatePackage.name,
            price: delegatePackage.price,
            currency: delegatePackage.currency,
          },
          payment: {
            checkout_url: actualCheckoutURL,
            return_url: pesaflowPayload.callBackURLOnSuccess,
            bill_ref_number: pesaflowPayload.billRefNumber,
            api_client_id: pesaflowPayload.apiClientID,
            service_id: pesaflowPayload.serviceID,
            customer_phone: pesaflowPayload.clientMSISDN,
            customer_name: pesaflowPayload.clientName,
            pesaflow_response: pesaflowResponse,
          },
        },
        next_step: 'redirect_to_payment',
        checkout_url: actualCheckoutURL,
      }),
      {
        status: 201,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  } catch (error: any) {
    console.error('Citizen registration error:', error)

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Registration failed due to server error',
        message: error.message,
        timestamp: new Date().toISOString(),
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}

/**
 * Get registration status endpoint
 * GET /api/citizens/registration-status/:invoiceId
 */
export const getRegistrationStatusEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    const invoiceId = req.routeParams?.invoiceId

    if (!invoiceId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invoice ID is required',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get invoice with relationships
    const invoice = await req.payload.findByID({
      collection: 'invoices',
      id: invoiceId,
      depth: 2,
    })

    if (!invoice) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invoice not found',
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get related notification if exists
    const notifications = await req.payload.find({
      collection: 'pesaflow-notifications',
      where: {
        invoice: { equals: invoiceId },
      },
      limit: 1,
      sort: '-createdAt',
    })

    const latestNotification = notifications.docs[0] || null

    return new Response(
      JSON.stringify({
        success: true,
        data: {
          invoice: {
            id: invoice.id,
            invoice_number: invoice.invoice_number,
            status: invoice.status,
            amount: invoice.amount,
            currency: invoice.currency,
            payment_reference: invoice.payment_reference,
          },
          user: {
            id: invoice.user.id,
            name: invoice.user.name,
            email: invoice.user.email,
            package_status: invoice.user.package_status,
            _verified: invoice.user._verified,
          },
          service_package: {
            id: invoice.package.id,
            name: invoice.package.name,
            price: invoice.package.price,
          },
          payment_notification: latestNotification
            ? {
                id: latestNotification.id,
                processing_status: latestNotification.processing_status,
                payment_channel: latestNotification.payment_channel,
                processed_at: latestNotification.processed_at,
              }
            : null,
        },
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  } catch (error: any) {
    console.error('Registration status check error:', error)

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Failed to check registration status',
        message: error.message,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}
