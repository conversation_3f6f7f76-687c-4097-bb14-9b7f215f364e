# Pesaflow Payment Integration Setup

## Environment Variables

Add the following environment variable to your `.env` file:

```bash
# Pesaflow Configuration
PESAFLOW_SECRET_KEY=your_pesaflow_secret_key_here
```

## Pesaflow Notification Endpoint

### URL Configuration
Configure this URL as your `notificationURL` in Pesaflow checkout requests:

```
https://your-domain.com/api/payment/callback/pesaflow/notification
```

### Webhook Configuration
- **Method**: POST
- **Content-Type**: application/json
- **Authentication**: Secure hash verification using PESAFLOW_SECRET_KEY

## Expected Payload Structure

The endpoint expects the exact Pesaflow notification payload structure:

```json
{
  "payment_channel": "M-Pesa",
  "client_invoice_ref": "SS6489db2e1fdfd",
  "payment_reference": [
    {
      "payment_reference": "ABCDEF-925",
      "payment_date": "2023-06-14T15:33:16",
      "inserted_at": "2023-06-14T15:33:16",
      "currency": "KES",
      "amount": "102.00"
    }
  ],
  "currency": "KES",
  "amount_paid": "102.00",
  "invoice_amount": "102.00",
  "status": "settled",
  "invoice_number": "ABCDEF",
  "payment_date": "2023-06-14 15:33:16Z",
  "last_payment_amount": "102.00",
  "secure_hash": "NTU4NzEzNzhiOTI1N2NlODY3YWYzYjVhYjQ4MzNiNDYzY2M3MzQwYmNlZDc4ZDJlZjg3ZDZkOTQ5ZjUyM2EzNQ=="
}
```

## Field Mapping

| Pesaflow Field | Our System Field | Description |
|----------------|------------------|-------------|
| `client_invoice_ref` | `invoice_id` | Used to find related invoice |
| `payment_reference[0].payment_reference` | `provider_reference` | Unique payment reference |
| `status: "settled"` | `callback_type: "payment_success"` | Payment status mapping |
| `amount_paid` | `amount` | Payment amount |
| `currency` | `currency` | Payment currency |
| `secure_hash` | Used for verification | Security validation |

## Invoice Matching Strategy

The system uses multiple strategies to find the related invoice:

1. **By Invoice Number**: Matches `invoice_number` field
2. **By Client Reference**: Matches `client_invoice_ref` in `payment_reference` field
3. **By ID**: If `client_invoice_ref` looks like a database ID
4. **By Amount & Currency**: Matches pending invoices with same amount and currency

## Security Features

- **Secure Hash Verification**: Validates incoming notifications using SHA256 + Base64
- **IP Validation**: Can be configured to only accept requests from Pesaflow IPs
- **Duplicate Prevention**: Prevents processing the same notification twice
- **Audit Trail**: Complete logging of all notification processing

## Testing

### Test Notification
```bash
curl -X POST http://localhost:3000/api/payment/callback/pesaflow/notification \
  -H "Content-Type: application/json" \
  -d '{
    "payment_channel": "M-Pesa",
    "client_invoice_ref": "TEST123",
    "payment_reference": [{
      "payment_reference": "TEST-REF-001",
      "payment_date": "2024-08-01T10:00:00",
      "inserted_at": "2024-08-01T10:00:00",
      "currency": "KES",
      "amount": "100.00"
    }],
    "currency": "KES",
    "amount_paid": "100.00",
    "invoice_amount": "100.00",
    "status": "settled",
    "invoice_number": "INV-TEST-001",
    "payment_date": "2024-08-01 10:00:00Z",
    "last_payment_amount": "100.00",
    "secure_hash": "test_hash_here"
  }'
```

## Production Checklist

- [ ] Set `PESAFLOW_SECRET_KEY` environment variable
- [ ] Configure notification URL in Pesaflow dashboard
- [ ] Test with Pesaflow sandbox environment
- [ ] Verify secure hash validation is working
- [ ] Monitor callback processing logs
- [ ] Set up alerts for failed notifications

## Troubleshooting

### Common Issues

1. **Invalid Secure Hash**: Check `PESAFLOW_SECRET_KEY` configuration
2. **Invoice Not Found**: Verify `client_invoice_ref` matches your invoice system
3. **Duplicate Notifications**: System automatically handles duplicates
4. **Processing Failures**: Check logs for detailed error messages

### Debug Mode

Set environment variable for detailed logging:
```bash
DEBUG_PESAFLOW=true
```
