import type { PayloadRequest } from 'payload'
import { HashService } from '../utils/pesaflowHash'
import { OptimizedIPNProcessor } from '../services/OptimizedIPNProcessor'

// Types for the eCitizen Instant Payment Notification (IPN) webhook
export const pesaflowIPNEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('IPN webhook endpoint called')
    console.log('Request body:', req.body)
    console.log('Request body type:', typeof req.body)

    // Parse request body (handle ReadableStream)
    let parsedBody: any
    if (req.body instanceof ReadableStream) {
      const reader = req.body.getReader()
      const chunks: Uint8Array[] = []
      let done = false

      while (!done) {
        const { value, done: streamDone } = await reader.read()
        done = streamDone
        if (value) {
          chunks.push(value)
        }
      }

      const bodyText = new TextDecoder().decode(
        new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0)).map((_, i) => {
          let offset = 0
          for (const chunk of chunks) {
            if (i < offset + chunk.length) {
              return chunk[i - offset]
            }
            offset += chunk.length
          }
          return 0
        }),
      )

      try {
        parsedBody = JSON.parse(bodyText)
      } catch (parseError) {
        console.error('JSON parse error:', parseError)
        return new Response(
          JSON.stringify({
            status: 400,
            description: 'Invalid JSON in request body',
          }),
          {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }
    } else if (typeof req.body === 'object' && req.body !== null) {
      parsedBody = req.body
    } else {
      return new Response(
        JSON.stringify({
          status: 400,
          description: 'Invalid request body format',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    console.log('Parsed body:', parsedBody)

    const {
      payment_channel,
      client_invoice_ref,
      payment_reference,
      currency,
      amount_paid,
      invoice_amount,
      status,
      invoice_number,
      payment_date,
      last_payment_amount,
      secure_hash,
    } = parsedBody

    // Validate required fields (secure_hash comes from Pesaflow in the webhook)
    const missingFields = [
      !client_invoice_ref && 'client_invoice_ref',
      !invoice_number && 'invoice_number',
      !amount_paid && 'amount_paid',
      !payment_date && 'payment_date',
      !secure_hash && 'secure_hash',
    ].filter(Boolean)

    if (missingFields.length > 0) {
      console.error('IPN missing fields:', missingFields)
      return new Response(
        JSON.stringify({
          status: 400,
          description: 'Missing required fields',
          missingFields,
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Use HashService to verify the hash that came from Pesaflow
    let hashService: HashService
    try {
      hashService = HashService.getInstance()
    } catch (error) {
      console.error('HashService initialization failed:', error)
      return new Response(
        JSON.stringify({
          status: 500,
          description: 'Internal Error - Missing configuration',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Verify the hash that Pesaflow sent us in the webhook
    // This ensures the webhook is actually from Pesaflow and not a malicious actor
    const isValidHash = hashService.verifyIPNHash(
      client_invoice_ref,
      invoice_number,
      amount_paid,
      payment_date,
      secure_hash,
    )

    if (!isValidHash) {
      console.error('IPN Hash verification failed - webhook may not be from Pesaflow')
      return new Response(
        JSON.stringify({
          status: 401,
          description: 'Unauthorized - Invalid secure hash',
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    console.log('IPN hash verification successful - webhook authenticated from Pesaflow')

    // Choose processing method based on environment variable
    const useOptimizedProcessor = process.env.USE_OPTIMIZED_IPN_PROCESSOR === 'true'

    let processingResult

    if (useOptimizedProcessor) {
      // Use optimized processor for high-volume scenarios
      console.log('🚀 Using optimized IPN processor')
      processingResult = await OptimizedIPNProcessor.processIPN(req.payload, {
        payment_channel,
        client_invoice_ref,
        payment_reference,
        currency,
        amount_paid,
        invoice_amount,
        status,
        invoice_number,
        payment_date,
        last_payment_amount,
        secure_hash,
      })
    } else {
      // Use standard processor for normal scenarios
      console.log('📋 Using standard IPN processor')
      processingResult = await processPaymentNotification(req.payload, {
        payment_channel,
        client_invoice_ref,
        payment_reference,
        currency,
        amount_paid,
        invoice_amount,
        status,
        invoice_number,
        payment_date,
        last_payment_amount,
        secure_hash,
      })
    }

    if (processingResult.success) {
      console.log('IPN processed successfully:', processingResult)
      return new Response(
        JSON.stringify({
          status: 200,
          description: 'IPN processed successfully',
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    } else {
      console.error('IPN processing failed:', processingResult.error)
      return new Response(
        JSON.stringify({
          status: 500,
          description: 'Failed to process IPN',
          error: processingResult.error,
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }
  } catch (error) {
    console.error('IPN endpoint error:', error)
    return new Response(
      JSON.stringify({
        status: 500,
        description: 'Internal Error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}

// Helper function to process payment notification and save to database
async function processPaymentNotification(payload: any, ipnData: any) {
  try {
    console.log('Processing IPN data and saving to database:', ipnData)

    // Prepare notification data for database
    const notificationData = {
      payment_channel: ipnData.payment_channel,
      client_invoice_ref: ipnData.client_invoice_ref,
      payment_reference: ipnData.payment_reference || `IPN-${Date.now()}`, // Generate if missing
      payment_date: new Date(ipnData.payment_date).toISOString(),
      inserted_at: new Date().toISOString(), // Current timestamp
      currency: ipnData.currency,
      amount_paid: parseFloat(ipnData.amount_paid),
      invoice_amount: parseFloat(ipnData.invoice_amount),
      last_payment_amount: parseFloat(ipnData.last_payment_amount || ipnData.amount_paid),
      status: ipnData.status,
      invoice_number: ipnData.invoice_number,
      secure_hash: ipnData.secure_hash,
      // Processing metadata
      processing_status: 'processed' as
        | 'pending'
        | 'processed'
        | 'failed'
        | 'duplicate'
        | 'invalid',
      processed_at: new Date().toISOString(),
      processing_notes: `IPN processed successfully for ${ipnData.status} payment`,
      hash_verified: true, // Already verified before reaching this point
      // Raw payload for debugging
      raw_payload: ipnData,
    }

    // Save to PesaflowNotifications collection
    console.log('Saving IPN notification to database...')
    const notificationRecord = await payload.create({
      collection: 'pesaflow-notifications',
      data: notificationData,
    })

    console.log('✅ IPN notification saved to database:', {
      id: notificationRecord.id,
      payment_reference: notificationRecord.payment_reference,
      status: notificationRecord.status,
      amount_paid: notificationRecord.amount_paid,
    })

    // Process payment based on status and update invoice
    await updateInvoiceFromIPN(payload, ipnData, notificationRecord.id)

    switch (ipnData.status) {
      case 'settled':
        console.log(`✅ Payment fully settled for invoice ${ipnData.invoice_number}`)
        break
      case 'partial':
        console.log(`⚠️ Partial payment received for invoice ${ipnData.invoice_number}`)
        break
      case 'pending':
        console.log(`⏳ Payment pending for invoice ${ipnData.invoice_number}`)
        break
      case 'failed':
        console.log(`❌ Payment failed for invoice ${ipnData.invoice_number}`)
        break
      case 'cancelled':
        console.log(`🚫 Payment cancelled for invoice ${ipnData.invoice_number}`)
        break
      default:
        console.warn(`⚠️ Unknown payment status: ${ipnData.status}`)
    }

    return {
      success: true,
      notificationId: notificationRecord.id,
      transactionId: ipnData.invoice_number,
      processedAt: new Date().toISOString(),
      status: ipnData.status,
      amount: ipnData.amount_paid,
    }
  } catch (error) {
    console.error('❌ Error processing IPN:', error)

    // Try to save failed processing attempt
    try {
      await payload.create({
        collection: 'pesaflow-notifications',
        data: {
          payment_channel: ipnData.payment_channel || 'unknown',
          client_invoice_ref: ipnData.client_invoice_ref || 'unknown',
          payment_reference: ipnData.payment_reference || `FAILED-${Date.now()}`,
          payment_date: ipnData.payment_date
            ? new Date(ipnData.payment_date).toISOString()
            : new Date().toISOString(),
          inserted_at: new Date().toISOString(),
          currency: ipnData.currency || 'KES',
          amount_paid: parseFloat(ipnData.amount_paid || '0'),
          invoice_amount: parseFloat(ipnData.invoice_amount || '0'),
          last_payment_amount: parseFloat(
            ipnData.last_payment_amount || ipnData.amount_paid || '0',
          ),
          status: ipnData.status || 'failed',
          invoice_number: ipnData.invoice_number || 'unknown',
          secure_hash: ipnData.secure_hash || 'missing',
          processing_status: 'failed' as
            | 'pending'
            | 'processed'
            | 'failed'
            | 'duplicate'
            | 'invalid',
          processed_at: new Date().toISOString(),
          processing_notes: `IPN processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          hash_verified: false,
          raw_payload: ipnData,
        },
      })
      console.log('⚠️ Failed IPN attempt saved to database for debugging')
    } catch (saveError) {
      console.error('❌ Failed to save failed IPN attempt:', saveError)
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      processedAt: new Date().toISOString(),
    }
  }
}

// Helper function to update invoice based on IPN data with optimizations
async function updateInvoiceFromIPN(payload: any, ipnData: any, notificationId: number) {
  const startTime = Date.now()

  try {
    // Use a unique key for this IPN to prevent duplicate processing
    const ipnKey = `${ipnData.invoice_number}-${ipnData.payment_reference || notificationId}-${ipnData.amount_paid}`

    // Find the invoice by invoice_number (optimized query)
    const invoices = await payload.find({
      collection: 'invoices',
      where: {
        invoice_number: {
          equals: ipnData.invoice_number,
        },
      },
      limit: 1,
    })

    if (!invoices.docs || invoices.docs.length === 0) {
      console.warn(`⚠️ Invoice not found: ${ipnData.invoice_number}`)
      return { success: false, error: 'Invoice not found' }
    }

    const invoice = invoices.docs[0]
    console.log(`📋 Found invoice: ${invoice.id} - ${invoice.invoice_number}`)

    // Calculate payment amounts
    const currentAmountPaid = invoice.payment_summary?.amount_paid || 0
    const newPaymentAmount = parseFloat(ipnData.amount_paid)
    const totalAmountPaid = currentAmountPaid + newPaymentAmount
    const invoiceAmount = invoice.amount
    const amountRemaining = Math.max(0, invoiceAmount - totalAmountPaid)
    const paymentCount = (invoice.payment_summary?.payment_count || 0) + 1

    // Determine new status based on payment
    let newStatus = invoice.status
    if (ipnData.status === 'settled') {
      if (totalAmountPaid >= invoiceAmount) {
        newStatus = 'settled' // Fully paid
      } else {
        newStatus = 'partial' // Partially paid
      }
    } else if (ipnData.status === 'failed') {
      newStatus = 'failed'
    } else if (ipnData.status === 'cancelled') {
      newStatus = 'cancelled'
    } else if (ipnData.status === 'pending') {
      newStatus = 'processing'
    }

    console.log(
      `💰 Payment calculation: ${currentAmountPaid} + ${newPaymentAmount} = ${totalAmountPaid} (Invoice: ${invoiceAmount})`,
    )
    console.log(`📊 New status: ${invoice.status} → ${newStatus}`)

    // Update the invoice
    const updatedInvoice = await payload.update({
      collection: 'invoices',
      id: invoice.id,
      data: {
        status: newStatus,
        payment_summary: {
          amount_paid: totalAmountPaid,
          amount_remaining: amountRemaining,
          payment_count: paymentCount,
          last_payment_date: new Date(ipnData.payment_date).toISOString(),
        },
        // Add payment notes
        notes:
          `${invoice.notes || ''}\n[${new Date().toISOString()}] IPN Payment: ${ipnData.amount_paid} ${ipnData.currency} via ${ipnData.payment_channel} (Ref: ${ipnData.payment_reference || 'N/A'}, Notification: ${notificationId})`.trim(),
      },
    })

    console.log(`✅ Invoice updated successfully: ${updatedInvoice.invoice_number}`)
    console.log(
      `📊 Payment summary: ${totalAmountPaid}/${invoiceAmount} ${invoice.currency} (${paymentCount} payments)`,
    )

    return {
      success: true,
      invoice_id: invoice.id,
      old_status: invoice.status,
      new_status: newStatus,
      amount_paid: newPaymentAmount,
      total_paid: totalAmountPaid,
      remaining: amountRemaining,
    }
  } catch (error) {
    console.error(`❌ Error updating invoice from IPN:`, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}
