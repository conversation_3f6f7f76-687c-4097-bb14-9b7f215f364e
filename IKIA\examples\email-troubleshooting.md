# Email Configuration Troubleshooting

## ✅ **Current Status**

Your environment variables are correctly configured and being read by the application:
- ✅ SMTP_HOST: smtp.gmail.com
- ✅ SMTP_PORT: 587
- ✅ SMTP_SECURE: false
- ✅ SMTP_USER: Set
- ✅ SMTP_PASS: Set
- ✅ FROM_EMAIL: app.dukalink.com
- ✅ FROM_NAME: IKIa

## ❌ **Current Issue**

Gmail authentication is failing with error:
```
Invalid login: 535-5.7.8 Username and Password not accepted
```

## 🔧 **Solutions**

### Solution 1: **Use Gmail App Password (Recommended)**

1. **Enable 2-Factor Authentication** on your Gmail account:
   - Go to [Google Account Security](https://myaccount.google.com/security)
   - Enable 2-Step Verification

2. **Generate App Password**:
   - Go to [App Passwords](https://myaccount.google.com/apppasswords)
   - Select "Mail" as the app
   - Copy the 16-character password (e.g., `abcd efgh ijkl mnop`)

3. **Update your .env file**:
   ```env
   SMTP_PASS=abcdefghijklmnop  # Use the 16-character app password (no spaces)
   ```

### Solution 2: **Use a Different Email Provider**

If Gmail is causing issues, try these alternatives:

#### **SendGrid (Recommended for Production)**
```env
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME="Your App Name"
```

#### **Mailgun**
```env
SMTP_HOST=smtp.mailgun.org
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-mailgun-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Your App Name"
```

#### **Outlook/Hotmail**
```env
SMTP_HOST=smtp-mail.outlook.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Your App Name"
```

### Solution 3: **Fix Gmail FROM_EMAIL**

Your `FROM_EMAIL` is set to `app.dukalink.com` but you're using Gmail SMTP. Gmail requires the FROM_EMAIL to match your Gmail account or be an alias you've verified.

**Update your .env:**
```env
FROM_EMAIL=<EMAIL>  # Must match your Gmail account
```

## 🧪 **Test Your Configuration**

After making changes, test with:

```bash
# Test the configuration
node debug-email-config.cjs

# Test forgot password endpoint
curl -X POST http://localhost:3000/api/users/forgot-password \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'
```

## 🔧 **Updated Payload Configuration**

I've updated your Payload config to use `transportOptions` instead of `transport` which should resolve the "transport.verify is not a function" error.

## 📋 **Complete Working .env Example**

```env
# Database
DATABASE_URI=your-database-url

# Payload
PAYLOAD_SECRET=your-secret-key

# Gmail Configuration (Option 1)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-16-char-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Your App Name"

# OR SendGrid Configuration (Option 2)
# SMTP_HOST=smtp.sendgrid.net
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=apikey
# SMTP_PASS=your-sendgrid-api-key
# FROM_EMAIL=<EMAIL>
# FROM_NAME="Your App Name"
```

## 🚀 **Next Steps**

1. **Choose one of the solutions above**
2. **Update your .env file** with the correct credentials
3. **Restart your server** to load new environment variables
4. **Test the forgot password endpoint**

## ⚠️ **Important Notes**

- **Gmail App Passwords**: Required when using 2FA (which is recommended)
- **FROM_EMAIL**: Must match your SMTP provider's requirements
- **Restart Required**: Always restart your server after changing .env
- **Security**: Never commit .env files to version control

## 🔍 **Debug Commands**

```bash
# Check if environment variables are loaded
node debug-email-config.cjs

# Test forgot password
curl -X POST http://localhost:3000/api/users/forgot-password \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'

# Check server logs for email errors
# Look for "Email sent successfully" or error messages
```

The most likely solution is to use a Gmail App Password instead of your regular password. Let me know which solution you'd like to try!
