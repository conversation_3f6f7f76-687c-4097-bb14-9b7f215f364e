# Email Configuration for Payload CMS

## ✅ **Email Adapter Installed & Configured**

I've installed `@payloadcms/email-nodemailer` and configured your Payload CMS to use email. Now you need to add environment variables to enable email functionality.

## 📧 **Environment Variables Setup**

Add these variables to your `.env` file:

### Option 1: **Gmail SMTP (Recommended for Development)**

```env
# Gmail SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Your App Name"
```

**🔑 Gmail App Password Setup:**
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Enable 2-Factor Authentication
3. Go to "Security" → "App passwords"
4. Generate a new app password for "Mail"
5. Use the 16-character password in `SMTP_PASS`

### Option 2: **Other Email Providers**

```env
# Generic SMTP Configuration
SMTP_HOST=your-smtp-host.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Your App Name"
```

### Option 3: **SendGrid**

```env
# SendGrid Configuration
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME="Your App Name"
```

### Option 4: **Mailgun**

```env
# Mailgun Configuration
SMTP_HOST=smtp.mailgun.org
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-mailgun-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Your App Name"
```

## 🧪 **Test Email Configuration**

After adding environment variables, restart your server and test:

```bash
# Test forgot password (should now send email)
curl -X POST http://localhost:3000/api/users/forgot-password \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

**Expected Response:**
```json
{
  "message": "Password reset email sent successfully"
}
```

## 📨 **Email Templates**

Payload CMS automatically sends emails for:

1. **Password Reset** - When user requests forgot password
2. **Email Verification** - If email verification is enabled
3. **Account Unlock** - If account gets locked

### Default Email Content:

**Password Reset Email:**
- Subject: "Reset Your Password"
- Contains a reset link with token
- Link format: `http://yoursite.com/reset-password?token=RESET_TOKEN`

## 🎨 **Custom Email Templates (Optional)**

You can customize email templates by adding them to your Payload config:

```typescript
// In payload.config.ts
export default buildConfig({
  email: nodemailerAdapter({
    defaultFromAddress: process.env.FROM_EMAIL || '<EMAIL>',
    defaultFromName: process.env.FROM_NAME || 'Your App',
    transport: {
      // ... transport config
    },
  }),
  // Custom email templates
  collections: [
    {
      slug: 'users',
      auth: {
        forgotPassword: {
          generateEmailHTML: ({ token, user }) => {
            return `
              <h1>Reset Your Password</h1>
              <p>Hello ${user.name || user.email},</p>
              <p>Click the link below to reset your password:</p>
              <a href="${process.env.PAYLOAD_PUBLIC_SERVER_URL}/reset-password?token=${token}">
                Reset Password
              </a>
              <p>This link will expire in 1 hour.</p>
            `
          },
          generateEmailSubject: ({ user }) => {
            return `Reset your password, ${user.name || user.email}`
          },
        },
      },
      // ... other config
    },
  ],
})
```

## 🔧 **Troubleshooting**

### Common Issues:

1. **"Email attempted without being configured"**
   - ✅ **Fixed!** - Email is now configured
   - Make sure environment variables are set
   - Restart your server after adding env vars

2. **SMTP Authentication Failed**
   - Check username/password
   - For Gmail, use App Password, not regular password
   - Verify SMTP host and port

3. **Connection Timeout**
   - Check firewall settings
   - Try different SMTP ports (25, 465, 587)
   - Verify SMTP_SECURE setting

4. **Emails Not Received**
   - Check spam folder
   - Verify FROM_EMAIL is valid
   - Test with different email providers

### Debug Email Issues:

Add this to see detailed email logs:

```env
# Add to .env for debugging
DEBUG=nodemailer*
```

## 🔐 **Security Best Practices**

1. **Use App Passwords** - Never use your main email password
2. **Environment Variables** - Keep credentials in .env, not in code
3. **HTTPS in Production** - Always use HTTPS for password reset links
4. **Rate Limiting** - Implement rate limiting for forgot password endpoint
5. **Token Expiration** - Keep reset token expiration short (1-2 hours)

## 📋 **Complete .env Example**

```env
# Database
DATABASE_URI=postgresql://username:password@localhost:5432/database

# Payload
PAYLOAD_SECRET=your-secret-key
PAYLOAD_PUBLIC_SERVER_URL=http://localhost:3000

# Email Configuration (Choose one)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-16-char-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME="Your App Name"
```

## ✅ **Next Steps**

1. **Add environment variables** to your `.env` file
2. **Restart your server** to load new config
3. **Test forgot password** endpoint
4. **Check your email** for the reset message
5. **Customize email templates** if needed

Now your forgot password functionality should work properly! 🎉
