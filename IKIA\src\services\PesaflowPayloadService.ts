import type { PayloadRequest } from 'payload'
import { HashService } from '../utils/pesaflowHash'
import { CustomerDataService, type ExtractedCustomerData } from './CustomerDataService'

/**
 * Complete Pesaflow checkout payload interface matching their API specification
 */
export interface PesaflowCheckoutPayload {
  apiClientID: string
  serviceID: string
  billRefNumber: string
  billDesc: string
  clientMSISDN: string
  clientIDNumber: string
  clientName: string
  clientEmail: string
  notificationURL: string
  pictureURL?: string
  callBackURLOnSuccess: string
  currency: string
  amountExpected: string
  format?: string
  sendSTK?: string
  secureHash: string
}

/**
 * Service to build complete Pesaflow checkout payloads with all required parameters
 */
export class PesaflowPayloadService {
  private req: PayloadRequest
  private hashService: HashService
  private customerDataService: CustomerDataService

  constructor(req: PayloadRequest) {
    this.req = req
    this.hashService = HashService.getInstance()
    this.customerDataService = new CustomerDataService(req)
  }

  /**
   * Build complete Pesaflow checkout payload from invoice ID
   */
  async buildPayloadFromInvoice(
    invoiceId: string | number,
    successUrlSuffix?: string,
    cancelUrlSuffix?: string,
  ): Promise<PesaflowCheckoutPayload> {
    // Extract customer data from invoice
    const customerData = await this.customerDataService.extractFromInvoice(invoiceId)

    // Get environment variables
    const envVars = this.getEnvironmentVariables()

    // Build URLs with suffixes
    const callBackURLOnSuccess = successUrlSuffix
      ? `${envVars.PESAFLOW_CALLBACK_SUCCESS_URL}${successUrlSuffix}`
      : envVars.PESAFLOW_CALLBACK_SUCCESS_URL

    // Generate hash using complete Pesaflow parameters
    const { hash, api_client_id } = this.hashService.generateCheckoutHash(
      customerData.amountExpected,
      envVars.PESAFLOW_REQUEST_SERVICE_ID,
      customerData.clientIDNumber,
      customerData.currency,
      customerData.billRefNumber,
      customerData.billDesc,
      customerData.clientName,
    )

    // Build complete payload
    const payload: PesaflowCheckoutPayload = {
      apiClientID: api_client_id,
      serviceID: envVars.PESAFLOW_REQUEST_SERVICE_ID,
      billRefNumber: customerData.billRefNumber,
      billDesc: customerData.billDesc,
      clientMSISDN: customerData.clientMSISDN,
      clientIDNumber: customerData.clientIDNumber,
      clientName: customerData.clientName,
      clientEmail: customerData.clientEmail,
      notificationURL: envVars.PESAFLOW_NOTIFICATION_URL,
      pictureURL: envVars.PESAFLOW_PICTURE_URL || undefined,
      callBackURLOnSuccess,
      currency: customerData.currency,
      amountExpected: customerData.amountExpected,
      format: envVars.PESAFLOW_DEFAULT_FORMAT || 'html',
      sendSTK: envVars.PESAFLOW_SEND_STK || 'true',
      secureHash: hash,
    }

    console.log('✅ Built COMPLETE Pesaflow payload from invoice:', {
      ...payload,
      secureHash: payload.secureHash ? '[HASH_PRESENT]' : '[NO_HASH]', // Hide actual hash for security
      parameterCount: Object.keys(payload).length,
      allParameters: Object.keys(payload),
    })

    return payload
  }

  /**
   * Build complete Pesaflow checkout payload from registration data
   */
  buildPayloadFromRegistration(
    registrationData: any,
    packageData: any,
    invoiceData: any,
    successUrlSuffix?: string,
  ): PesaflowCheckoutPayload {
    // Extract customer data from registration
    const customerData = this.customerDataService.extractFromRegistration(
      registrationData,
      packageData,
      invoiceData,
    )

    // Get environment variables
    const envVars = this.getEnvironmentVariables()

    // Build URLs with suffixes
    const callBackURLOnSuccess = successUrlSuffix
      ? `${envVars.PESAFLOW_CALLBACK_SUCCESS_URL}${successUrlSuffix}`
      : envVars.PESAFLOW_CALLBACK_SUCCESS_URL

    // Generate hash using complete Pesaflow parameters
    const { hash, api_client_id } = this.hashService.generateCheckoutHash(
      customerData.amountExpected,
      envVars.PESAFLOW_REQUEST_SERVICE_ID,
      customerData.clientIDNumber,
      customerData.currency,
      customerData.billRefNumber,
      customerData.billDesc,
      customerData.clientName,
    )

    // Build complete payload
    const payload: PesaflowCheckoutPayload = {
      apiClientID: api_client_id,
      serviceID: envVars.PESAFLOW_REQUEST_SERVICE_ID,
      billRefNumber: customerData.billRefNumber,
      billDesc: customerData.billDesc,
      clientMSISDN: customerData.clientMSISDN,
      clientIDNumber: customerData.clientIDNumber,
      clientName: customerData.clientName,
      clientEmail: customerData.clientEmail,
      notificationURL: envVars.PESAFLOW_NOTIFICATION_URL,
      pictureURL: envVars.PESAFLOW_PICTURE_URL || undefined,
      callBackURLOnSuccess,
      currency: customerData.currency,
      amountExpected: customerData.amountExpected,
      format: envVars.PESAFLOW_DEFAULT_FORMAT || 'html',
      sendSTK: envVars.PESAFLOW_SEND_STK || 'true',
      secureHash: hash,
    }

    console.log('✅ Built COMPLETE Pesaflow payload from registration:', {
      ...payload,
      secureHash: payload.secureHash ? '[HASH_PRESENT]' : '[NO_HASH]', // Hide actual hash for security
      parameterCount: Object.keys(payload).length,
      allParameters: Object.keys(payload),
    })

    return payload
  }

  /**
   * Get and validate all required environment variables
   */
  private getEnvironmentVariables() {
    const {
      PESAFLOW_REQUEST_SERVICE_ID,
      PESAFLOW_BILL_DESC,
      PESAFLOW_NOTIFICATION_URL,
      PESAFLOW_CALLBACK_SUCCESS_URL,
      PESAFLOW_CALLBACK_CANCEL_URL,
      PESAFLOW_PICTURE_URL,
      PESAFLOW_DEFAULT_FORMAT,
      PESAFLOW_SEND_STK,
    } = process.env

    // Validate required environment variables
    const missingVars = [
      !PESAFLOW_REQUEST_SERVICE_ID && 'PESAFLOW_REQUEST_SERVICE_ID',
      !PESAFLOW_NOTIFICATION_URL && 'PESAFLOW_NOTIFICATION_URL',
      !PESAFLOW_CALLBACK_SUCCESS_URL && 'PESAFLOW_CALLBACK_SUCCESS_URL',
    ].filter(Boolean)

    if (missingVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`)
    }

    return {
      PESAFLOW_REQUEST_SERVICE_ID: PESAFLOW_REQUEST_SERVICE_ID!,
      PESAFLOW_BILL_DESC: PESAFLOW_BILL_DESC || 'Payment for services',
      PESAFLOW_NOTIFICATION_URL: PESAFLOW_NOTIFICATION_URL!,
      PESAFLOW_CALLBACK_SUCCESS_URL: PESAFLOW_CALLBACK_SUCCESS_URL!,
      PESAFLOW_CALLBACK_CANCEL_URL: PESAFLOW_CALLBACK_CANCEL_URL || '',
      PESAFLOW_PICTURE_URL: PESAFLOW_PICTURE_URL || '',
      PESAFLOW_DEFAULT_FORMAT: PESAFLOW_DEFAULT_FORMAT || 'html',
      PESAFLOW_SEND_STK: PESAFLOW_SEND_STK || 'true',
    }
  }

  /**
   * Get the Pesaflow checkout URL
   */
  getCheckoutUrl(): string {
    const { PESAFLOW_UAT_SERVER_URL } = process.env
    if (!PESAFLOW_UAT_SERVER_URL) {
      throw new Error('PESAFLOW_UAT_SERVER_URL not configured')
    }
    return PESAFLOW_UAT_SERVER_URL
  }

  /**
   * Validate payload before sending to Pesaflow
   */
  validatePayload(payload: PesaflowCheckoutPayload): void {
    const requiredFields = [
      'apiClientID',
      'serviceID',
      'billRefNumber',
      'billDesc',
      'clientMSISDN',
      'clientIDNumber',
      'clientName',
      'clientEmail',
      'notificationURL',
      'callBackURLOnSuccess',
      'currency',
      'amountExpected',
      'secureHash',
    ]

    const missingFields = requiredFields.filter(
      (field) => !payload[field as keyof PesaflowCheckoutPayload],
    )

    if (missingFields.length > 0) {
      throw new Error(`Missing required payload fields: ${missingFields.join(', ')}`)
    }

    // Validate phone number format
    if (!payload.clientMSISDN.match(/^254\d{9}$/)) {
      console.warn(`Invalid phone number format: ${payload.clientMSISDN}. Expected: 254XXXXXXXXX`)
    }

    // Validate email format
    if (!payload.clientEmail.includes('@')) {
      throw new Error(`Invalid email format: ${payload.clientEmail}`)
    }

    // Validate amount
    const amount = parseFloat(payload.amountExpected)
    if (isNaN(amount) || amount <= 0) {
      throw new Error(`Invalid amount: ${payload.amountExpected}`)
    }
  }
}

export default PesaflowPayloadService
