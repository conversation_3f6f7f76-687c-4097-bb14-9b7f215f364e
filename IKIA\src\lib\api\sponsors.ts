import { getPayload } from 'payload'
import config from '@payload-config'

export interface SponsorData {
  id: string
  name: string
  logo: {
    url: string
    alt: string
  }
  description?: string
  website?: string
  tier?: 'title' | 'platinum' | 'gold' | 'silver' | 'bronze'
  category: 'financial' | 'service' | 'media' | 'special'
  hasDetailsPage?: boolean
  featured?: boolean
  displayOrder?: number
  slug?: string
  additionalLogos?: Array<{
    name: string
    logo: {
      url: string
      alt: string
    }
  }>
  sponsorshipPackage?: {
    id: string
    name: string
    tier?: string
    price: string
  }
}

export interface SponsorshipPackageData {
  id: string
  name: string
  price: string
  priceAmount?: number
  tier?: 'title' | 'platinum' | 'gold' | 'silver' | 'bronze' | 'special'
  category: 'financial' | 'service' | 'media' | 'special'
  maxPartners: number
  currentPartners: number
  description?: any // Rich text content
  benefits?: Array<{ benefit: string }>
  isInKind?: boolean
  featured?: boolean
  available?: boolean
  displayOrder?: number
  slug?: string
  prospectusDocument?: {
    url: string
    filename: string
  }
}

/**
 * Fetch all sponsors from Payload CMS
 */
export async function getSponsors(): Promise<SponsorData[]> {
  try {
    const payload = await getPayload({ config })
    
    const sponsors = await payload.find({
      collection: 'sponsors',
      depth: 2, // Populate relationships
      sort: 'displayOrder',
      limit: 100, // Adjust as needed
    })

    return sponsors.docs.map(transformSponsorData)
  } catch (error) {
    console.error('Error fetching sponsors:', error)
    return []
  }
}

/**
 * Fetch sponsors by tier
 */
export async function getSponsorsByTier(tier: string): Promise<SponsorData[]> {
  try {
    const payload = await getPayload({ config })
    
    const sponsors = await payload.find({
      collection: 'sponsors',
      where: {
        tier: {
          equals: tier,
        },
      },
      depth: 2,
      sort: 'displayOrder',
      limit: 100,
    })

    return sponsors.docs.map(transformSponsorData)
  } catch (error) {
    console.error(`Error fetching sponsors for tier ${tier}:`, error)
    return []
  }
}

/**
 * Fetch sponsors by category
 */
export async function getSponsorsByCategory(category: string): Promise<SponsorData[]> {
  try {
    const payload = await getPayload({ config })
    
    const sponsors = await payload.find({
      collection: 'sponsors',
      where: {
        category: {
          equals: category,
        },
      },
      depth: 2,
      sort: 'displayOrder',
      limit: 100,
    })

    return sponsors.docs.map(transformSponsorData)
  } catch (error) {
    console.error(`Error fetching sponsors for category ${category}:`, error)
    return []
  }
}

/**
 * Fetch a single sponsor by slug
 */
export async function getSponsorBySlug(slug: string): Promise<SponsorData | null> {
  try {
    const payload = await getPayload({ config })
    
    const sponsors = await payload.find({
      collection: 'sponsors',
      where: {
        slug: {
          equals: slug,
        },
      },
      depth: 2,
      limit: 1,
    })

    if (sponsors.docs.length === 0) {
      return null
    }

    return transformSponsorData(sponsors.docs[0])
  } catch (error) {
    console.error(`Error fetching sponsor with slug ${slug}:`, error)
    return null
  }
}

/**
 * Fetch all sponsorship packages
 */
export async function getSponsorshipPackages(): Promise<SponsorshipPackageData[]> {
  try {
    const payload = await getPayload({ config })
    
    const packages = await payload.find({
      collection: 'sponsorship-packages',
      where: {
        available: {
          equals: true,
        },
      },
      depth: 1,
      sort: 'displayOrder',
      limit: 100,
    })

    return packages.docs.map(transformSponsorshipPackageData)
  } catch (error) {
    console.error('Error fetching sponsorship packages:', error)
    return []
  }
}

/**
 * Transform raw Payload sponsor data to our interface
 */
function transformSponsorData(sponsor: any): SponsorData {
  return {
    id: sponsor.id,
    name: sponsor.name,
    logo: {
      url: sponsor.logo?.url || '',
      alt: sponsor.logo?.alt || sponsor.name,
    },
    description: sponsor.description,
    website: sponsor.website,
    tier: sponsor.tier,
    category: sponsor.category,
    hasDetailsPage: sponsor.hasDetailsPage,
    featured: sponsor.featured,
    displayOrder: sponsor.displayOrder,
    slug: sponsor.slug,
    additionalLogos: sponsor.additionalLogos?.map((item: any) => ({
      name: item.name,
      logo: {
        url: item.logo?.url || '',
        alt: item.logo?.alt || item.name,
      },
    })) || [],
    sponsorshipPackage: sponsor.sponsorshipPackage ? {
      id: sponsor.sponsorshipPackage.id,
      name: sponsor.sponsorshipPackage.name,
      tier: sponsor.sponsorshipPackage.tier,
      price: sponsor.sponsorshipPackage.price,
    } : undefined,
  }
}

/**
 * Transform raw Payload sponsorship package data to our interface
 */
function transformSponsorshipPackageData(pkg: any): SponsorshipPackageData {
  return {
    id: pkg.id,
    name: pkg.name,
    price: pkg.price,
    priceAmount: pkg.priceAmount,
    tier: pkg.tier,
    category: pkg.category,
    maxPartners: pkg.maxPartners,
    currentPartners: pkg.currentPartners,
    description: pkg.description,
    benefits: pkg.benefits || [],
    isInKind: pkg.isInKind,
    featured: pkg.featured,
    available: pkg.available,
    displayOrder: pkg.displayOrder,
    slug: pkg.slug,
    prospectusDocument: pkg.prospectusDocument ? {
      url: pkg.prospectusDocument.url,
      filename: pkg.prospectusDocument.filename,
    } : undefined,
  }
}
