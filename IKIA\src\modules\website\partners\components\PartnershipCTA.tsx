'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { ArrowRight, Users, Building, Globe, Heart } from 'lucide-react'
import Link from 'next/link'

export default function PartnershipCTA() {
  return (
    <section className="py-16 lg:py-20 section-bg-secondary">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-[#7E2518]/8 px-4 py-2 border border-[#7E2518]/15 mb-6">
              <div className="w-2 h-2 bg-[#E8B32C] animate-pulse"></div>
              <span className="text-sm font-medium text-[#7E2518] font-['Myriad_Pro',Arial,sans-serif]">
                Join Our Network
              </span>
            </div>

            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#7E2518] mb-6 font-['Myriad_Pro',Arial,sans-serif]">
              Become a<span className="block text-[#159147]">Strategic Partner</span>
            </h2>

            <div className="w-24 h-1 bg-[#E8B32C] mx-auto mb-6"></div>

            <p className="text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto font-['Myriad_Pro',Arial,sans-serif]">
              Join our growing network of organizations committed to preserving and commercializing
              indigenous knowledge. Together, we can create lasting impact for communities across
              Kenya and beyond.
            </p>
          </div>

          {/* Partnership Benefits */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <div className="text-center">
              <div className="w-16 h-16 bg-[#7E2518] flex items-center justify-center mx-auto mb-4">
                <Building className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-bold text-[#7E2518] mb-2 font-['Myriad_Pro',Arial,sans-serif]">
                Brand Visibility
              </h3>
              <p className="text-gray-600 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                Showcase your commitment to indigenous knowledge preservation
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[#159147] flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-bold text-[#7E2518] mb-2 font-['Myriad_Pro',Arial,sans-serif]">
                Network Access
              </h3>
              <p className="text-gray-600 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                Connect with like-minded organizations and community leaders
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[#E8B32C] flex items-center justify-center mx-auto mb-4">
                <Globe className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-bold text-[#7E2518] mb-2 font-['Myriad_Pro',Arial,sans-serif]">
                Global Impact
              </h3>
              <p className="text-gray-600 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                Be part of a movement that spans across continents
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[#81B1DB] flex items-center justify-center mx-auto mb-4">
                <Heart className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-bold text-[#7E2518] mb-2 font-['Myriad_Pro',Arial,sans-serif]">
                Community Impact
              </h3>
              <p className="text-gray-600 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                Directly support indigenous communities and their heritage
              </p>
            </div>
          </div>

          {/* CTA Section */}
          <div className="bg-white main-shadow border border-gray-100 p-8 text-center">
            <h3 className="text-2xl font-bold text-[#7E2518] mb-4 font-['Myriad_Pro',Arial,sans-serif]">
              Ready to Make a Difference?
            </h3>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto font-['Myriad_Pro',Arial,sans-serif]">
              Whether you&apos;re an organization, institution, or community group, we welcome
              partnerships that align with our mission of preserving and promoting indigenous
              knowledge.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                asChild
                className="bg-[#7E2518] hover:bg-[#159147] text-white border-0 font-bold transition-all duration-300 px-8 py-3 main-shadow font-['Myriad_Pro',Arial,sans-serif]"
              >
                <Link href="/partners-sponsors#inquiry" className="flex items-center gap-2">
                  Become a Partner
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>

              <Button
                asChild
                variant="outline"
                className="border-2 border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white font-bold transition-all duration-300 px-8 py-3 main-shadow font-['Myriad_Pro',Arial,sans-serif]"
              >
                <Link href="/contact" className="flex items-center gap-2">
                  Contact Us
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
