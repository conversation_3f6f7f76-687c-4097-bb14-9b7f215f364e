'use client'

import React from 'react'
import { QRCodeSVG } from 'qrcode.react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Calendar, MapPin, User, Building, Mail, Phone } from 'lucide-react'

interface ConferenceBadgeProps {
  registrationData: {
    firstName: string
    lastName: string
    email: string
    phone?: string
    organization: string
    position: string
    registrationType: string
    selectedPackage: string
    registrationId: string
  }
  className?: string
}

function ConferenceBadge({ registrationData, className = '' }: ConferenceBadgeProps) {
  const {
    firstName,
    lastName,
    email,
    phone,
    organization,
    position,
    registrationType,
    selectedPackage,
    registrationId
  } = registrationData

  const fullName = `${firstName} ${lastName}`
  
  // Generate QR code data
  const qrData = JSON.stringify({
    id: registrationId,
    name: fullName,
    email: email,
    type: registrationType,
    package: selectedPackage,
    timestamp: Date.now()
  })

  // Get badge color based on registration type
  const getBadgeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'vip':
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600'
      case 'sponsor':
        return 'bg-gradient-to-r from-purple-500 to-purple-700'
      case 'exhibitor':
        return 'bg-gradient-to-r from-blue-500 to-blue-700'
      case 'delegate':
        return 'bg-gradient-to-r from-green-500 to-green-700'
      default:
        return 'bg-gradient-to-r from-gray-500 to-gray-700'
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type.toLowerCase()) {
      case 'vip':
        return 'VIP'
      case 'sponsor':
        return 'SPONSOR'
      case 'exhibitor':
        return 'EXHIBITOR'
      case 'delegate':
        return 'DELEGATE'
      default:
        return type.toUpperCase()
    }
  }

  return (
    <div className={`max-w-md mx-auto ${className}`}>
      <Card className="overflow-hidden shadow-2xl border-2 border-gray-200">
        {/* Header with IKIA branding */}
        <div className={`${getBadgeColor(registrationType)} text-white p-4 text-center relative`}>
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10">
            <h1 className="text-xl font-bold mb-1" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
              IKIA CONFERENCE 2024
            </h1>
            <p className="text-sm opacity-90">Indigenous Knowledge & Intellectual Assets</p>
            <Badge 
              variant="secondary" 
              className="mt-2 bg-white/20 text-white border-white/30 font-bold"
            >
              {getTypeLabel(registrationType)}
            </Badge>
          </div>
        </div>

        <CardContent className="p-6 space-y-4">
          {/* Attendee Information */}
          <div className="text-center border-b border-gray-200 pb-4">
            <h2 className="text-2xl font-bold text-gray-900 mb-1" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
              {fullName}
            </h2>
            <p className="text-lg text-gray-600 mb-1">{position}</p>
            <p className="text-base text-gray-500">{organization}</p>
          </div>

          {/* Contact Information */}
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2 text-gray-600">
              <Mail className="w-4 h-4 flex-shrink-0" />
              <span className="truncate">{email}</span>
            </div>
            {phone && (
              <div className="flex items-center gap-2 text-gray-600">
                <Phone className="w-4 h-4 flex-shrink-0" />
                <span>{phone}</span>
              </div>
            )}
          </div>

          {/* Conference Details */}
          <div className="bg-gray-50 p-3 space-y-2 text-sm">
            <div className="flex items-center gap-2 text-gray-700">
              <Calendar className="w-4 h-4 flex-shrink-0" />
              <span>December 15-17, 2024</span>
            </div>
            <div className="flex items-center gap-2 text-gray-700">
              <MapPin className="w-4 h-4 flex-shrink-0" />
              <span>Nairobi, Kenya</span>
            </div>
            <div className="flex items-center gap-2 text-gray-700">
              <Building className="w-4 h-4 flex-shrink-0" />
              <span>Package: {selectedPackage}</span>
            </div>
          </div>

          {/* QR Code */}
          <div className="flex justify-center pt-4">
            <div className="bg-white p-3 border border-gray-200">
              <QRCodeSVG
                value={qrData}
                size={120}
                level="M"
                includeMargin={false}
                fgColor="#000000"
                bgColor="#ffffff"
              />
            </div>
          </div>

          {/* Registration ID */}
          <div className="text-center pt-2">
            <p className="text-xs text-gray-500 font-mono">
              ID: {registrationId}
            </p>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 p-3 text-xs text-blue-800 border border-blue-200">
            <p className="font-semibold mb-1">Important Instructions:</p>
            <ul className="space-y-1 text-blue-700">
              <li>• Present this badge at registration desk</li>
              <li>• Keep QR code visible for scanning</li>
              <li>• Badge must be worn at all times during the conference</li>
              <li>• Contact support if you encounter any issues</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ConferenceBadge
export { ConferenceBadge }
