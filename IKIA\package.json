{"name": "my-project", "version": "1.0.0", "description": "Website template for Payload", "license": "MIT", "type": "module", "scripts": {"build": "NODE_OPTIONS=--no-deprecation next build", "build:prod": "NODE_OPTIONS=--no-deprecation next build", "postbuild": "next-sitemap --config next-sitemap.config.cjs", "dev": "NODE_OPTIONS='--max-old-space-size=2048 --expose-gc --no-deprecation' next dev", "dev:prod": "NODE_OPTIONS=--no-deprecation rm -rf .next && pnpm build && pnpm start", "generate:importmap": "NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "NODE_OPTIONS=--no-deprecation payload generate:types", "ii": "NODE_OPTIONS=--no-deprecation pnpm --ignore-workspace install", "lint": "NODE_OPTIONS=--no-deprecation next lint", "lint:fix": "NODE_OPTIONS=--no-deprecation next lint --fix", "payload": "NODE_OPTIONS=--no-deprecation payload", "seed": "NODE_OPTIONS=--no-deprecation tsx ./src/endpoints/seed/seed-script.ts", "reinstall": "NODE_OPTIONS=--no-deprecation rm -rf node_modules && rm pnpm-lock.yaml && pnpm --ignore-workspace install", "start": "NODE_OPTIONS=--no-deprecation next start", "start:prod": "NODE_ENV=production NODE_OPTIONS=--no-deprecation next start -p 3000", "start:passenger": "NODE_ENV=production NODE_OPTIONS=--no-deprecation node app.js", "test": "pnpm run test:int && pnpm run test:e2e", "test:e2e": "NODE_OPTIONS=\"--no-deprecation --no-experimental-strip-types\" pnpm exec playwright test --config=playwright.config.ts", "test:int": "NODE_OPTIONS=--no-deprecation vitest run --config ./vitest.config.mts", "create-indexes": "node scripts/create-indexes.js", "check-indexes": "node scripts/check-index-performance.js", "validate-env": "node scripts/validate-env.js", "check-versions": "node scripts/check-package-versions.js", "pm2:start": "pm2 start ecosystem.config.cjs --env production", "pm2:stop": "pm2 stop ecosystem.config.cjs", "pm2:restart": "bash scripts/restart-pm2.sh hard", "pm2:restart:soft": "bash scripts/restart-pm2.sh soft", "pm2:restart:quick": "bash scripts/restart-pm2.sh quick", "pm2:reload": "pm2 reload ecosystem.config.cjs", "pm2:delete": "pm2 delete ecosystem.config.cjs", "pm2:logs": "pm2 logs ikia-conference", "pm2:monit": "pm2 monit", "pm2:status": "pm2 status", "deploy": "bash scripts/deploy.sh deploy", "deploy:restart": "bash scripts/deploy.sh restart", "deploy:production": "pm2 deploy ecosystem.config.cjs production", "deploy:staging": "pm2 deploy ecosystem.config.cjs staging", "deploy:setup": "pm2 deploy ecosystem.config.cjs production setup", "build:production": "NODE_ENV=production NODE_OPTIONS=--no-deprecation next build", "start:cluster": "pm2 start ecosystem.config.cjs --env production", "health-check": "node scripts/health-check.js", "db:update-config": "bash scripts/update-database-config.sh update", "db:test": "bash scripts/update-database-config.sh test", "db:setup": "bash scripts/setup-database.sh setup"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@lexical/code": "0.28.0", "@lexical/link": "0.28.0", "@lexical/list": "0.28.0", "@lexical/mark": "0.28.0", "@lexical/react": "0.28.0", "@lexical/rich-text": "0.28.0", "@lexical/selection": "0.28.0", "@lexical/table": "0.28.0", "@lexical/utils": "0.28.0", "@payloadcms/admin-bar": "3.48.0", "@payloadcms/db-postgres": "3.48.0", "@payloadcms/email-nodemailer": "3.48.0", "@payloadcms/live-preview-react": "3.48.0", "@payloadcms/next": "3.48.0", "@payloadcms/payload-cloud": "3.48.0", "@payloadcms/plugin-form-builder": "3.48.0", "@payloadcms/plugin-nested-docs": "3.48.0", "@payloadcms/plugin-redirects": "3.48.0", "@payloadcms/plugin-search": "3.48.0", "@payloadcms/plugin-seo": "3.48.0", "@payloadcms/richtext-lexical": "3.48.0", "@payloadcms/storage-vercel-blob": "3.48.0", "@payloadcms/ui": "3.48.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.8.2", "@types/qrcode": "^1.5.5", "@types/react-simple-maps": "^3.0.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "16.4.7", "geist": "^1.3.0", "graphql": "^16.8.2", "html2canvas": "^1.4.1", "lucide-react": "^0.378.0", "next": "15.3.3", "next-sitemap": "^4.2.3", "next-themes": "^0.4.6", "node-fetch": "^2.6.7", "payload": "3.48.0", "prism-react-renderer": "^2.3.1", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "19.1.0", "react-custom-google-translate": "^1.0.32", "react-dom": "19.1.0", "react-hook-form": "7.45.4", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-simple-maps": "^3.0.0", "sharp": "0.34.2", "sonner": "^2.0.6", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.9"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@playwright/test": "1.50.0", "@tailwindcss/typography": "^0.5.13", "@testing-library/react": "16.3.0", "@types/escape-html": "^1.0.2", "@types/html2canvas": "^1.0.0", "@types/node": "22.5.4", "@types/qrcode.react": "^3.0.0", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "@vitejs/plugin-react": "4.5.2", "autoprefixer": "^10.4.21", "copyfiles": "^2.4.1", "eslint": "^9.16.0", "eslint-config-next": "15.3.0", "jsdom": "26.1.0", "playwright": "1.50.0", "playwright-core": "1.50.0", "postcss": "^8.4.38", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "typescript": "5.7.3", "vite-tsconfig-paths": "5.1.4", "vitest": "3.2.3"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp", "esbuild", "unrs-resolver"]}}