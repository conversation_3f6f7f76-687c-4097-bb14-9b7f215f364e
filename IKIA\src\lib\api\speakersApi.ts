import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

// Types for Speakers
export interface Speaker {
  id: number
  name: string
  title?: string
  company?: string
  bio?: string
  photo?: {
    id: number
    url: string
    alt?: string
    width?: number
    height?: number
  }
  slug?: string
  updatedAt: string
  createdAt: string
}

export interface SpeakersResponse {
  docs: Speaker[]
  totalDocs: number
  limit: number
  totalPages: number
  page: number
  pagingCounter: number
  hasPrevPage: boolean
  hasNextPage: boolean
  prevPage?: number | null
  nextPage?: number | null
}

export interface SpeakersQueryParams {
  limit?: number
  page?: number
  sort?: string
  where?: {
    name?: {
      contains?: string
    }
  }
}

type SpeakersCategoryGroupResponse = {
  keynote: PaginatedSpeakers
  plenary: PaginatedSpeakers
  others?: PaginatedSpeakers // optional if it's not always present
}

type PaginatedSpeakers = {
  docs: Speaker[]
  totalDocs: number
  totalPages: number
  page: number
  limit: number
  hasNextPage: boolean
  hasPrevPage: boolean
  nextPage: number | null
  prevPage: number | null
  pagingCounter: number
}

export const speakersApi = createApi({
  reducerPath: 'speakersApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  tagTypes: ['Speaker'],
  endpoints: (builder) => ({
    // Get all speakers with optional filtering
    getSpeakers: builder.query<SpeakersResponse, SpeakersQueryParams | void>({
      query: (params) => {
        const searchParams = new URLSearchParams()

        // Handle the case where params might be void
        if (!params) {
          return 'speakers?limit=0&sort=name'
        }

        // Add basic params
        if (params.limit) searchParams.append('limit', params.limit.toString())
        if (params.page) searchParams.append('page', params.page.toString())
        if (params.sort) searchParams.append('sort', params.sort)

        // Add where conditions directly as URL parameters
        if (params.where) {
          // Handle name search
          if (params.where.name?.contains) {
            searchParams.append('where[name][contains]', params.where.name.contains)
          }
        }

        return `speakers?${searchParams.toString()}`
      },
      providesTags: ['Speaker'],
    }),

    // Get active speakers only (for dropdowns)
    getActiveSpeakers: builder.query<SpeakersResponse, { limit?: number }>({
      query: ({ limit = 0 } = {}) => `speakers?limit=${limit}&sort=name`,
      providesTags: ['Speaker'],
    }),

    // Get single speaker by ID
    getSpeaker: builder.query<Speaker, string | number>({
      query: (id) => `speakers/${id}`,
      providesTags: ['Speaker'],
    }),

    // Get single speaker by slug
    getSpeakerBySlug: builder.query<Speaker, string>({
      query: (slug) => `speakers?where[slug][equals]=${slug}&limit=1`,
      transformResponse: (response: SpeakersResponse) => response.docs[0],
      providesTags: ['Speaker'],
    }),

    // Get grouped speakers
    getSpeakersByCategory: builder.query<SpeakersCategoryGroupResponse, void>({
      query: () => `speakers/group`,
      providesTags: ['Speaker'],
    }),
  }),
})

export const {
  useGetSpeakersQuery,
  useGetActiveSpeakersQuery,
  useGetSpeakerQuery,
  useGetSpeakerBySlugQuery,
  useGetSpeakersByCategoryQuery,
} = speakersApi
