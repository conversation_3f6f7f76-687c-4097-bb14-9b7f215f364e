import type { PayloadRequest } from 'payload'

interface UpdateUserRoleRequest {
  email: string
  role: 'citizen' | 'business' | 'admin' | 'payment_processor'
}

/**
 * Endpoint to update a user's role
 * This is useful for promoting users to admin or changing roles
 */
export const updateUserRoleEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('Update user role endpoint called')
    console.log('Request body:', req.body)

    const { email, role } = req.body as UpdateUserRoleRequest

    // Validate required fields
    if (!email || !role) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'MISSING_REQUIRED_FIELDS',
            message: 'Email and role are required',
            details: { email: !!email, role: !!role }
          }
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Validate role
    const validRoles = ['citizen', 'business', 'admin', 'payment_processor']
    if (!validRoles.includes(role)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'INVALID_ROLE',
            message: `Role must be one of: ${validRoles.join(', ')}`,
            details: { providedRole: role, validRoles }
          }
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Check if request is from an admin (for security)
    // For initial setup, we'll allow this without authentication
    // In production, you should add proper authentication
    const isInitialSetup = !req.user // No user means this might be initial setup
    const isAdmin = req.user?.role === 'admin'

    if (!isInitialSetup && !isAdmin) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'Only admins can update user roles'
          }
        }),
        {
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Find the user by email
    const users = await req.payload.find({
      collection: 'users',
      where: {
        email: {
          equals: email
        }
      }
    })

    if (users.docs.length === 0) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: `User with email ${email} not found`
          }
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    const user = users.docs[0]

    // Check if role is already the same
    if (user.role === role) {
      return new Response(
        JSON.stringify({
          success: true,
          message: `User already has role: ${role}`,
          data: {
            userId: user.id,
            email: user.email,
            name: user.name,
            currentRole: user.role,
            unchanged: true
          }
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Update the user's role
    const updatedUser = await req.payload.update({
      collection: 'users',
      id: user.id,
      data: {
        role: role
      }
    })

    console.log(`User role updated: ${email} -> ${role}`)

    return new Response(
      JSON.stringify({
        success: true,
        message: `User role updated successfully`,
        data: {
          userId: updatedUser.id,
          email: updatedUser.email,
          name: updatedUser.name,
          previousRole: user.role,
          newRole: updatedUser.role,
          updatedAt: updatedUser.updatedAt,
          canAccessAdmin: updatedUser.role === 'admin'
        }
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Update user role endpoint error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          details: error instanceof Error ? error.message : 'Unknown error'
        }
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}
