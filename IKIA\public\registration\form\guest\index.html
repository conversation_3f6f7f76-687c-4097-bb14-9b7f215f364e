<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IKIA Citizen Registration - Complete Your Registration</title>
    <style>
        /* Import IKIA Fonts */
        @font-face {
            font-family: 'ACQUIRE';
            src: url('/assets/fonts/Aquire-BW0ox.otf') format('opentype');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'ACQUIRE';
            src: url('/assets/fonts/AquireBold-8Ma60.otf') format('opentype');
            font-weight: 700;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Myriad Pro';
            src: url('/assets/fonts/MYRIADPRO-REGULAR.OTF') format('opentype');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Myriad Pro';
            src: url('/assets/fonts/MYRIADPRO-SEMIBOLD.OTF') format('opentype');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }

        /* IKIA Color Variables */
        :root {
            --ikia-deep-brown: #7E2518;
            --ikia-green: #159147;
            --ikia-yellow-ochre: #E8B32C;
            --ikia-sienna: #C85E36;
            --ikia-blue: #81B1DB;
            --ikia-cream: #FFF8E3;
            --main-shadow: 0px 4px 20px 2px rgba(126, 37, 24, 0.08);
            --card-shadow: 0px 2px 12px 1px rgba(126, 37, 24, 0.06);
            --hover-shadow: 0px 8px 32px 4px rgba(126, 37, 24, 0.12);
            --focus-shadow: 0px 0px 0px 3px rgba(21, 145, 71, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Myriad Pro', 'Inter', system-ui, sans-serif;
            background: linear-gradient(135deg, var(--ikia-cream) 0%, #ffffff 50%, var(--ikia-cream) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        /* Heritage Background Pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20% 20%, rgba(126, 37, 24, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(21, 145, 71, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(232, 179, 44, 0.02) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .registration-container {
            background: white;
            border-radius: 20px;
            box-shadow: var(--main-shadow);
            max-width: 900px;
            width: 100%;
            overflow: hidden;
            border: 1px solid rgba(126, 37, 24, 0.1);
        }

        .header {
            background: linear-gradient(135deg, var(--ikia-deep-brown) 0%, var(--ikia-sienna) 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        /* Heritage Elements in Header */
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background-image:
                radial-gradient(circle at 30% 30%, rgba(232, 179, 44, 0.1) 0%, transparent 30%),
                radial-gradient(circle at 70% 70%, rgba(21, 145, 71, 0.1) 0%, transparent 30%);
            animation: heritage-rotate 20s linear infinite;
            pointer-events: none;
        }

        @keyframes heritage-rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .header h1 {
            font-family: 'ACQUIRE', 'Arial Black', Impact, sans-serif;
            font-size: 2.8em;
            margin-bottom: 15px;
            font-weight: 700;
            position: relative;
            z-index: 2;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.95;
            position: relative;
            z-index: 2;
            font-weight: 400;
        }
        
        .form-container {
            padding: 50px 40px;
            background: linear-gradient(to bottom, #ffffff 0%, var(--ikia-cream) 100%);
        }

        .form-section {
            margin-bottom: 35px;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(126, 37, 24, 0.05);
            transition: all 0.3s ease;
        }

        .form-section:hover {
            box-shadow: var(--hover-shadow);
            transform: translateY(-2px);
        }

        .form-section h3 {
            color: var(--ikia-deep-brown);
            margin-bottom: 20px;
            font-size: 1.4em;
            font-weight: 600;
            font-family: 'ACQUIRE', 'Arial Black', Impact, sans-serif;
            border-bottom: 3px solid var(--ikia-green);
            padding-bottom: 8px;
            position: relative;
        }

        .form-section h3::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--ikia-yellow-ochre);
            border-radius: 2px;
        }

        .form-row {
            display: flex;
            gap: 25px;
            margin-bottom: 25px;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--ikia-deep-brown);
            font-size: 0.95em;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid rgba(126, 37, 24, 0.15);
            border-radius: 10px;
            font-size: 16px;
            font-family: 'Myriad Pro', sans-serif;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--ikia-green);
            box-shadow: var(--focus-shadow);
            background: #fefefe;
        }

        .form-group input:hover,
        .form-group select:hover,
        .form-group textarea:hover {
            border-color: var(--ikia-sienna);
        }

        .form-group.error input,
        .form-group.error select {
            border-color: var(--ikia-sienna);
            background: rgba(200, 94, 54, 0.05);
        }

        .error-message {
            color: var(--ikia-sienna);
            font-size: 0.9em;
            margin-top: 6px;
            display: none;
            font-weight: 500;
        }

        .form-group.error .error-message {
            display: block;
        }
        
        .package-selection {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }

        .package-card {
            border: 2px solid rgba(126, 37, 24, 0.15);
            border-radius: 15px;
            padding: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            background: white;
            box-shadow: var(--card-shadow);
        }

        .package-card:hover {
            border-color: var(--ikia-green);
            transform: translateY(-3px);
            box-shadow: var(--hover-shadow);
        }

        .package-card.selected {
            border-color: var(--ikia-green);
            background: linear-gradient(135deg, rgba(21, 145, 71, 0.05) 0%, rgba(232, 179, 44, 0.05) 100%);
            box-shadow: var(--hover-shadow);
        }

        .package-card.selected::before {
            content: '✓';
            position: absolute;
            top: 15px;
            right: 15px;
            width: 30px;
            height: 30px;
            background: var(--ikia-green);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
        }

        .package-card input[type="radio"] {
            position: absolute;
            opacity: 0;
        }

        .package-name {
            font-size: 1.3em;
            font-weight: 700;
            color: var(--ikia-deep-brown);
            margin-bottom: 12px;
            font-family: 'ACQUIRE', 'Arial Black', Impact, sans-serif;
        }

        .package-price {
            font-size: 1.6em;
            color: var(--ikia-green);
            font-weight: bold;
            margin-bottom: 15px;
            font-family: 'ACQUIRE', sans-serif;
        }

        .package-features {
            list-style: none;
            margin-bottom: 15px;
        }

        .package-features li {
            padding: 5px 0;
            color: var(--ikia-deep-brown);
            font-weight: 500;
        }

        .package-features li:before {
            content: "✓";
            color: var(--ikia-green);
            font-weight: bold;
            margin-right: 10px;
            font-size: 1.1em;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, var(--ikia-deep-brown) 0%, var(--ikia-sienna) 100%);
            color: white;
            padding: 18px 40px;
            border: none;
            border-radius: 12px;
            font-size: 1.1em;
            font-weight: 600;
            font-family: 'ACQUIRE', 'Arial Black', Impact, sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 25px;
            box-shadow: var(--card-shadow);
            position: relative;
            overflow: hidden;
        }

        .submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .submit-btn:hover::before {
            left: 100%;
        }

        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: var(--hover-shadow);
            background: linear-gradient(135deg, var(--ikia-sienna) 0%, var(--ikia-deep-brown) 100%);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            background: #ccc;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: var(--card-shadow);
            margin-top: 20px;
        }

        .spinner {
            border: 4px solid rgba(126, 37, 24, 0.1);
            border-top: 4px solid var(--ikia-green);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 18px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            border-left: 5px solid;
            font-weight: 500;
            box-shadow: var(--card-shadow);
        }

        .alert.error {
            background: rgba(200, 94, 54, 0.1);
            color: var(--ikia-sienna);
            border-left-color: var(--ikia-sienna);
        }

        .alert.success {
            background: rgba(21, 145, 71, 0.1);
            color: var(--ikia-green);
            border-left-color: var(--ikia-green);
        }

        .alert.hidden {
            display: none;
        }



        .strength-weak { color: var(--ikia-sienna); }
        .strength-medium { color: var(--ikia-yellow-ochre); }
        .strength-strong { color: var(--ikia-green); }
        
        /* Heritage Dots Animation */
        .heritage-dot {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: heritage-pulse 3s ease-in-out infinite;
        }

        .heritage-dot-primary {
            background: var(--ikia-deep-brown);
            top: 20px;
            right: 30px;
        }

        .heritage-dot-secondary {
            background: var(--ikia-green);
            top: 50px;
            right: 60px;
            animation-delay: 1s;
        }

        .heritage-dot-accent {
            background: var(--ikia-yellow-ochre);
            top: 80px;
            right: 40px;
            animation-delay: 2s;
        }

        @keyframes heritage-pulse {
            0%, 100% {
                opacity: 0.6;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 15px;
            }

            .package-selection {
                grid-template-columns: 1fr;
            }

            .form-container {
                padding: 30px 20px;
            }

            .form-section {
                padding: 20px;
                margin-bottom: 25px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2.2em;
            }

            .header p {
                font-size: 1.1em;
            }

            .registration-container {
                margin: 10px;
                border-radius: 15px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.8em;
            }

            .form-section h3 {
                font-size: 1.2em;
            }

            .package-card {
                padding: 20px;
            }

            .submit-btn {
                padding: 16px 30px;
                font-size: 1em;
            }
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <div class="header">
            <!-- Heritage Dots -->
            <div class="heritage-dot heritage-dot-primary"></div>
            <div class="heritage-dot heritage-dot-secondary"></div>
            <div class="heritage-dot heritage-dot-accent"></div>

            <h1>🇰🇪 IKIA</h1>
            <p>Investment Kenya Investment Authority - Complete Your Registration</p>
            <p style="font-size: 0.95em; margin-top: 8px; opacity: 0.85;">
                <em>"Empowering Indigenous Knowledge & Innovation"</em>
            </p>
        </div>
        
        <div class="form-container">
            <div class="alert error hidden" id="alertMessage"></div>
            
            <form id="registrationForm">
                <!-- Personal Information Section -->
                <div class="form-section">
                    <h3>👤 Personal Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Full Name *</label>
                            <input type="text" id="name" name="name" required placeholder="Enter your full name">
                            <div class="error-message">Please enter your full name</div>
                        </div>
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                            <div class="error-message">Please enter a valid email address</div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="phone_number">Phone Number *</label>
                            <input type="tel" id="phone_number" name="phone_number" required placeholder="254712345678">
                            <div class="error-message">Please enter a valid phone number (254XXXXXXXXX)</div>
                        </div>
                        <div class="form-group">
                            <label for="id_number">National ID Number *</label>
                            <input type="text" id="id_number" name="id_number" required placeholder="12345678">
                            <div class="error-message">Please enter a valid ID number (7-8 digits)</div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="county">County *</label>
                            <select id="county" name="county" required>
                                <option value="">Loading counties...</option>
                            </select>
                            <div class="error-message">Please select your county</div>
                        </div>
                    </div>
                </div>
                
                <!-- Account Information Section -->
                <div class="form-section">
                    <h3>🔐 Account Information</h3>
                    <div class="info-box" style="background-color: #e3f2fd; border: 1px solid #2196f3; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
                        <div style="display: flex; align-items: flex-start; gap: 12px;">
                            <div style="width: 20px; height: 20px; background-color: #2196f3; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0; margin-top: 2px;">
                                <span style="color: white; font-size: 12px; font-weight: bold;">ℹ</span>
                            </div>
                            <div>
                                <h4 style="margin: 0 0 8px 0; color: #1976d2; font-weight: 600;">Automatic Password Generation</h4>
                                <p style="margin: 0; color: #1565c0; font-size: 14px; line-height: 1.4;">
                                    A secure 6-character password will be automatically generated and sent to your email address after registration.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Business Information Section -->
                <div class="form-section">
                    <h3>🏢 Business Information (Optional)</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="business_type">Business Type</label>
                            <select id="business_type" name="business_type">
                                <option value="">Select business type</option>
                                <option value="Technology Startup">Technology Startup</option>
                                <option value="Manufacturing">Manufacturing</option>
                                <option value="Agriculture">Agriculture</option>
                                <option value="Services">Services</option>
                                <option value="Retail">Retail</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="registration_purpose">Registration Purpose</label>
                            <input type="text" id="registration_purpose" name="registration_purpose" placeholder="e.g., Business Registration, Investment Facilitation">
                        </div>
                    </div>
                </div>
                
                <!-- Service Package Selection -->
                <div class="form-section">
                    <h3>📦 Select Service Package *</h3>
                    <div class="package-selection" id="packageSelection">
                        <!-- Packages will be loaded dynamically -->
                        <div style="text-align: center; padding: 20px; color: #666;">
                            Loading service packages...
                        </div>
                    </div>
                    <div class="error-message" id="packageError" style="display: none;">Please select a service package</div>
                </div>
                
                <button type="submit" class="submit-btn" id="submitBtn">
                    Complete Registration & Proceed to Payment
                </button>
            </form>
            
            <div class="loading hidden" id="loadingDiv">
                <div class="spinner"></div>
                <p>Creating your account and preparing payment...</p>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let availablePackages = [];
        let availableCounties = [];

        // Initialize the form
        document.addEventListener('DOMContentLoaded', function() {
            loadCounties();
            loadServicePackages();
            setupFormValidation();
        });

        // Load counties from API
        async function loadCounties() {
            try {
                const response = await fetch('/api/counties');
                const result = await response.json();
                availableCounties = result.docs || result;

                const countySelect = document.getElementById('county');
                countySelect.innerHTML = '<option value="">Select your county</option>';

                availableCounties.forEach(county => {
                    const option = document.createElement('option');
                    option.value = county.id;
                    option.textContent = county.name;
                    countySelect.appendChild(option);
                });

                console.log('Counties loaded:', availableCounties.length);
            } catch (error) {
                console.error('Failed to load counties:', error);
                showAlert('Failed to load counties. Please refresh the page.', 'error');
            }
        }

        // Load service packages from API
        async function loadServicePackages() {
            try {
                const response = await fetch('/api/packages');
                const result = await response.json();
                availablePackages = result.docs || result;

                const packageSelection = document.getElementById('packageSelection');
                packageSelection.innerHTML = '';

                availablePackages.forEach(pkg => {
                    if (pkg.isActive) {
                        const packageCard = createPackageCard(pkg);
                        packageSelection.appendChild(packageCard);
                    }
                });

                console.log('Service packages loaded:', availablePackages.length);
            } catch (error) {
                console.error('Failed to load service packages:', error);
                showAlert('Failed to load service packages. Please refresh the page.', 'error');
            }
        }

        // Create package card element
        function createPackageCard(pkg) {
            const card = document.createElement('div');
            card.className = 'package-card';
            card.onclick = () => selectPackage(pkg.id);

            const features = pkg.features || [];
            const featuresList = features.map(f => `<li>${f.feature}</li>`).join('');

            card.innerHTML = `
                <input type="radio" name="selected_package" value="${pkg.id}" id="package-${pkg.id}" required>
                <div class="package-name">${pkg.name}</div>
                <div class="package-price">${pkg.currency} ${pkg.price.toLocaleString()}</div>
                <ul class="package-features">
                    ${featuresList}
                </ul>
                <small>${pkg.description}</small>
            `;

            return card;
        }

        // Select package
        function selectPackage(packageId) {
            // Remove selected class from all cards
            document.querySelectorAll('.package-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Add selected class to clicked card
            event.currentTarget.classList.add('selected');

            // Check the radio button
            document.getElementById(`package-${packageId}`).checked = true;

            // Clear package error
            document.getElementById('packageError').style.display = 'none';
        }

        // Setup form validation
        function setupFormValidation() {
            const form = document.getElementById('registrationForm');
            const inputs = form.querySelectorAll('input, select');

            inputs.forEach(input => {
                input.addEventListener('blur', validateField);
                input.addEventListener('input', clearFieldError);
            });

            form.addEventListener('submit', handleFormSubmit);
        }

        // Validate individual field
        function validateField(event) {
            const field = event.target;
            const fieldGroup = field.closest('.form-group');
            let isValid = true;
            let errorMessage = '';

            switch (field.name) {
                case 'name':
                    if (!field.value.trim()) {
                        isValid = false;
                        errorMessage = 'Please enter your full name';
                    }
                    break;

                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(field.value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid email address';
                    }
                    break;

                case 'phone_number':
                    const phoneRegex = /^254\d{9}$/;
                    if (!phoneRegex.test(field.value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid phone number (254XXXXXXXXX)';
                    }
                    break;

                case 'id_number':
                    const idRegex = /^\d{7,8}$/;
                    if (!idRegex.test(field.value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid ID number (7-8 digits)';
                    }
                    break;

                case 'county':
                    if (!field.value) {
                        isValid = false;
                        errorMessage = 'Please select your county';
                    }
                    break;


            }

            if (fieldGroup) {
                if (isValid) {
                    fieldGroup.classList.remove('error');
                } else {
                    fieldGroup.classList.add('error');
                    const errorDiv = fieldGroup.querySelector('.error-message');
                    if (errorDiv) {
                        errorDiv.textContent = errorMessage;
                    }
                }
            }

            return isValid;
        }

        // Clear field error
        function clearFieldError(event) {
            const fieldGroup = event.target.closest('.form-group');
            if (fieldGroup) {
                fieldGroup.classList.remove('error');
            }
        }



        // Handle form submission
        async function handleFormSubmit(event) {
            event.preventDefault();

            // Validate all fields
            const form = event.target;
            const inputs = form.querySelectorAll('input[required], select[required]');
            let isFormValid = true;

            inputs.forEach(input => {
                if (!validateField({ target: input })) {
                    isFormValid = false;
                }
            });

            // Check if package is selected
            const selectedPackage = form.querySelector('input[name="selected_package"]:checked');
            if (!selectedPackage) {
                document.getElementById('packageError').style.display = 'block';
                isFormValid = false;
            }

            if (!isFormValid) {
                showAlert('Please correct the errors above and try again.', 'error');
                return;
            }

            // Show loading state
            const submitBtn = document.getElementById('submitBtn');
            const loadingDiv = document.getElementById('loadingDiv');

            submitBtn.disabled = true;
            submitBtn.textContent = 'Processing...';
            loadingDiv.classList.remove('hidden');

            try {
                // Collect form data
                const formData = new FormData(form);
                const registrationData = Object.fromEntries(formData.entries());



                console.log('Submitting registration:', registrationData);

                // Submit registration
                const response = await fetch('/api/citizens/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(registrationData)
                });

                const result = await response.json();

                if (result.success) {
                    // Registration successful, redirect to payment
                    console.log('Registration successful:', result);
                    showAlert('Registration successful! Your login credentials have been sent to your email. Redirecting to payment gateway...', 'success');

                    // Redirect to payment after a short delay
                    setTimeout(() => {
                        window.location.href = result.checkout_url;
                    }, 2000);

                } else {
                    throw new Error(result.error || 'Registration failed');
                }

            } catch (error) {
                console.error('Registration error:', error);
                showAlert(error.message, 'error');

                // Reset form state
                submitBtn.disabled = false;
                submitBtn.textContent = 'Complete Registration & Proceed to Payment';
                loadingDiv.classList.add('hidden');
            }
        }

        // Show alert message
        function showAlert(message, type = 'error') {
            const alertDiv = document.getElementById('alertMessage');
            alertDiv.textContent = message;
            alertDiv.className = `alert ${type}`;
            alertDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

            if (type === 'success') {
                setTimeout(() => {
                    alertDiv.classList.add('hidden');
                }, 5000);
            }
        }
    </script>
</body>
</html>
