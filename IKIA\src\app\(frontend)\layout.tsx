import type { <PERSON>ada<PERSON> } from 'next'

import { cn } from '@/utilities/ui'
import { GeistMono } from 'geist/font/mono'
import { GeistSans } from 'geist/font/sans'
import React from 'react'
import { AdminBar } from '@/components/AdminBar'
import { Providers } from '@/providers'
import { InitTheme } from '@/providers/Theme/InitTheme'
import { mergeOpenGraph } from '@/utilities/mergeOpenGraph'
import { draftMode } from 'next/headers'
import { IkiaNavbar } from '@/components/ikia-navbar'
import Footer from '@/components/footer'
import { BackToTopButton } from '@/components/ui/BackToTopButton'
import { SkipNavigation } from '@/components/accessibility/SkipNavigation'
import { IkiaTranslateProvider } from '@/components/translation/IkiaTranslateProvider'

import './globals.css'
import { getServerSideURL } from '@/utilities/getURL'

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const { isEnabled } = await draftMode()

  return (
    <html className={cn(GeistSans.variable, GeistMono.variable)} lang="en" suppressHydrationWarning>
      <head>
        <InitTheme />
        <link href="/favicon.ico" rel="icon" sizes="32x32" />
        <link href="/favicon.svg" rel="icon" type="image/svg+xml" />
      </head>
      <body>
        <Providers>
          <IkiaTranslateProvider>
            <SkipNavigation />
            <div className="min-h-screen flex flex-col">
              <AdminBar
                adminBarProps={{
                  preview: isEnabled,
                }}
              />

              <IkiaNavbar />
              <main id="main-content" className="flex-1" tabIndex={-1}>
                {children}
              </main>
              <Footer />
              <BackToTopButton />
            </div>
          </IkiaTranslateProvider>
        </Providers>
      </body>
    </html>
  )
}

export const metadata: Metadata = {
  metadataBase: new URL(getServerSideURL()),
  openGraph: mergeOpenGraph(),
  twitter: {
    card: 'summary_large_image',
    creator: '@payloadcms',
  },
}
