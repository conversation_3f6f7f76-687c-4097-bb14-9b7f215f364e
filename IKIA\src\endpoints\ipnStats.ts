import type { PayloadRequest } from 'payload'
import { OptimizedIPNProcessor } from '../services/OptimizedIPNProcessor'

/**
 * IPN Performance Statistics Endpoint
 * GET /api/payment/ipn/stats
 */
export const ipnStatsEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    // Get processing statistics
    const processorStats = OptimizedIPNProcessor.getStats()
    
    // Get recent IPN notifications from database
    const recentNotifications = await req.payload.find({
      collection: 'pesaflow-notifications',
      limit: 10,
      sort: '-createdAt',
      select: {
        id: true,
        status: true,
        amount_paid: true,
        processing_status: true,
        processed_at: true,
        createdAt: true,
      },
    })
    
    // Calculate processing time statistics
    const processingTimes = recentNotifications.docs
      .filter(doc => doc.processed_at && doc.createdAt)
      .map(doc => {
        const created = new Date(doc.createdAt).getTime()
        const processed = new Date(doc.processed_at).getTime()
        return processed - created
      })
    
    const avgProcessingTime = processingTimes.length > 0 
      ? processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length 
      : 0
    
    const maxProcessingTime = processingTimes.length > 0 
      ? Math.max(...processingTimes) 
      : 0
    
    // Get status distribution
    const statusCounts = recentNotifications.docs.reduce((acc, doc) => {
      acc[doc.status] = (acc[doc.status] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    const stats = {
      timestamp: new Date().toISOString(),
      processor: {
        optimized_enabled: process.env.USE_OPTIMIZED_IPN_PROCESSOR === 'true',
        currently_processing: processorStats.currentlyProcessing,
        recently_processed: processorStats.recentlyProcessed,
        cache_size: processorStats.cacheSize,
      },
      performance: {
        avg_processing_time_ms: Math.round(avgProcessingTime),
        max_processing_time_ms: maxProcessingTime,
        recent_notifications_count: recentNotifications.docs.length,
        total_processing_samples: processingTimes.length,
      },
      status_distribution: statusCounts,
      recent_notifications: recentNotifications.docs.map(doc => ({
        id: doc.id,
        status: doc.status,
        amount_paid: doc.amount_paid,
        processing_status: doc.processing_status,
        created_at: doc.createdAt,
        processed_at: doc.processed_at,
      })),
      recommendations: generatePerformanceRecommendations(processorStats, avgProcessingTime),
    }
    
    return new Response(JSON.stringify(stats, null, 2), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('Error getting IPN stats:', error)
    return new Response(
      JSON.stringify({
        error: 'Failed to get IPN statistics',
        message: error instanceof Error ? error.message : 'Unknown error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}

/**
 * Generate performance recommendations based on current stats
 */
function generatePerformanceRecommendations(
  processorStats: any, 
  avgProcessingTime: number
): string[] {
  const recommendations: string[] = []
  
  // High concurrency recommendations
  if (processorStats.currentlyProcessing > 10) {
    recommendations.push('High concurrency detected. Consider enabling optimized processor.')
  }
  
  // Performance recommendations
  if (avgProcessingTime > 1000) {
    recommendations.push('Average processing time is high (>1s). Consider database indexing.')
  }
  
  if (avgProcessingTime > 5000) {
    recommendations.push('Very high processing time (>5s). Check database performance and connection pool.')
  }
  
  // Cache recommendations
  if (processorStats.cacheSize > 1000) {
    recommendations.push('Large cache size detected. Consider implementing cache cleanup.')
  }
  
  // Optimization recommendations
  if (process.env.USE_OPTIMIZED_IPN_PROCESSOR !== 'true') {
    recommendations.push('Consider enabling optimized IPN processor for better performance.')
  }
  
  if (recommendations.length === 0) {
    recommendations.push('Performance looks good! No immediate optimizations needed.')
  }
  
  return recommendations
}

/**
 * Clear IPN processor cache endpoint
 * POST /api/payment/ipn/clear-cache
 */
export const clearIPNCacheEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    const beforeStats = OptimizedIPNProcessor.getStats()
    
    OptimizedIPNProcessor.clearCache()
    
    const afterStats = OptimizedIPNProcessor.getStats()
    
    return new Response(
      JSON.stringify({
        success: true,
        message: 'IPN processor cache cleared successfully',
        before: beforeStats,
        after: afterStats,
        cleared_at: new Date().toISOString(),
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  } catch (error) {
    console.error('Error clearing IPN cache:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Failed to clear IPN cache',
        message: error instanceof Error ? error.message : 'Unknown error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}
