'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import {
  Search,
  ChevronLeft,
  ChevronRight,
  Phone,
  Mail,
  Loader2,
  TriangleAlert,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { useGetNewsMediaQuery } from '@/lib/api/newsMediaApi'
import {
  HeadlinesSlider,
  PressReleaseSlider,
  AllPressReleases,
  LatestNewsTopics,
  BlogSlider,
  NewsFeed,
  PopularTopics,
  PressReleaseRecent,
  LatestBlogPosts,
  UpcomingEvents,
  EventsSlider,
  AllEvents,
} from '@/modules/website/news-media/components'
import { NewsItem } from '@/modules/website/news-media/types'

function NewsMediaContent() {
  const [activeTab, setActiveTab] = useState('news-feed')

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sub Navigation */}
      <div
        className=" border-gray-200"
        style={{
          fontFamily: 'Myriad Pro, Arial, sans-serif',
          backgroundColor: 'rgba(20, 144, 71, 0.65',
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex flex-col lg:flex-row  items-center justify-center space-x-8 py-4">
            <button
              onClick={() => setActiveTab('news-feed')}
              className={`font-medium pb-2 transition-colors ${
                activeTab === 'news-feed'
                  ? 'text-[#7E2518] border-b-2 text-[#7E2518]'
                  : 'text-gray-600 hover:text-[#7E2518]'
              }`}
            >
              News feed
            </button>
            <button
              onClick={() => setActiveTab('press-releases')}
              className={`font-medium pb-2 transition-colors ${
                activeTab === 'press-releases'
                  ? 'text-[#7E2518] border-b-2 text-[#7E2518]'
                  : 'text-gray-600 hover:text-[#7E2518]'
              }`}
            >
              Press releases
            </button>
            <button
              onClick={() => setActiveTab('blogs')}
              className={`font-medium pb-2 transition-colors ${
                activeTab === 'blogs'
                  ? 'text-[#7E2518] border-b-2 text-[#7E2518]'
                  : 'text-gray-600 hover:text-[#7E2518]'
              }`}
            >
              Blogs
            </button>
            <button
              onClick={() => setActiveTab('events-updates')}
              className={`font-medium pb-2 transition-colors ${
                activeTab === 'events-updates'
                  ? 'text-[#7E2518] border-b-2 text-[#7E2518]'
                  : 'text-gray-600 hover:text-[#7E2518]'
              }`}
            >
              Events updates
            </button>
          </nav>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'news-feed' && <NewsFeedContent />}
        {activeTab === 'press-releases' && <PressReleasesContent />}
        {activeTab === 'blogs' && <BlogsContent />}
        {activeTab === 'events-updates' && <EventsUpdatesContent />}

        {/* Featured Banner Slider - Show on all tabs */}
        <div className="mb-12">
          <SummitSlider />
        </div>

        {/* Newsletter Section - Show on all tabs */}
        {/* <div className="bg-white rounded-lg p-8 mb-12 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Join Our Newsletter</h3>
          <p className="text-gray-600 mb-6">
            Stay updated with the latest business news and insights
          </p>
          <div className="max-w-md mx-auto flex space-x-4">
            <Input type="email" placeholder="Enter your email address" className="flex-1" />
            <Button className="bg-red-600 hover:bg-red-700">Subscribe</Button>
          </div>
        </div> */}
      </div>
    </div>
  )
}

export default function NewsMediaPage() {
  return <NewsMediaContent />
}

function SummitSlider() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  const summits = [
    {
      title: 'Join Our Upcoming Business Summit',
      description: 'Connect with industry leaders and expand your network',
      image: '/placeholder.svg?height=300&width=800',
      bgColor: 'from-red-600 to-red-800',
    },
    {
      title: 'Entrepreneurship Masterclass Series',
      description: 'Learn from successful entrepreneurs and scale your business',
      image: '/placeholder.svg?height=300&width=800',
      bgColor: 'from-blue-600 to-blue-800',
    },
    {
      title: 'Investment Opportunities Forum',
      description: 'Discover the latest investment trends and opportunities',
      image: '/placeholder.svg?height=300&width=800',
      bgColor: 'from-green-600 to-green-800',
    },
    {
      title: 'Digital Transformation Workshop',
      description: 'Transform your business with cutting-edge technology',
      image: '/placeholder.svg?height=300&width=800',
      bgColor: 'from-purple-600 to-purple-800',
    },
  ]

  const nextSlide = () => {
    if (!isAnimating) {
      setIsAnimating(true)
      setCurrentSlide((prev) => (prev + 1) % summits.length)
      setTimeout(() => setIsAnimating(false), 1200)
    }
  }

  const prevSlide = () => {
    if (!isAnimating) {
      setIsAnimating(true)
      setCurrentSlide((prev) => (prev - 1 + summits.length) % summits.length)
      setTimeout(() => setIsAnimating(false), 1200)
    }
  }

  const goToSlide = (index: number) => {
    if (!isAnimating && index !== currentSlide) {
      setIsAnimating(true)
      setCurrentSlide(index)
      setTimeout(() => setIsAnimating(false), 1200)
    }
  }

  useEffect(() => {
    const timer = setInterval(() => {
      nextSlide()
    }, 7000)
    return () => clearInterval(timer)
  }, [])

  return (
    <Card className="overflow-hidden">
      <div className="relative h-80">
        {summits.map((summit, index) => {
          const isActive = index === currentSlide
          const isPrev = index === (currentSlide - 1 + summits.length) % summits.length
          const isNext = index === (currentSlide + 1) % summits.length

          let slideClass = 'absolute inset-0 transition-all duration-1200 ease-in-out'

          if (isActive) {
            slideClass += ' opacity-100 transform translate-x-0 scale-100'
          } else if (isNext) {
            slideClass += ' opacity-0 transform translate-x-full scale-95'
          } else if (isPrev) {
            slideClass += ' opacity-0 transform -translate-x-full scale-95'
          } else {
            slideClass += ' opacity-0 transform translate-x-full scale-95'
          }

          return (
            <div key={index} className={slideClass}>
              <div className={`absolute inset-0 bg-gradient-to-r ${summit.bgColor}`} />
              <Image
                src={summit.image || '/placeholder.svg'}
                alt={summit.title}
                fill
                className="object-cover opacity-30 transition-all duration-1200 ease-out"
              />
              <div className="absolute inset-0 flex items-center justify-between px-8">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white hover:bg-white/20 z-10 transition-all duration-300 hover:scale-110"
                  onClick={prevSlide}
                  disabled={isAnimating}
                >
                  <ChevronLeft className="w-6 h-6" />
                </Button>
                <div
                  className={`text-center text-white transform transition-all duration-1000 delay-200 ${
                    isActive
                      ? 'translate-y-0 opacity-100 scale-100'
                      : 'translate-y-4 opacity-0 scale-95'
                  }`}
                >
                  <h3 className="text-3xl font-bold mb-3">{summit.title}</h3>
                  <p className="text-gray-100 text-lg">{summit.description}</p>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white hover:bg-white/20 z-10 transition-all duration-300 hover:scale-110"
                  onClick={nextSlide}
                  disabled={isAnimating}
                >
                  <ChevronRight className="w-6 h-6" />
                </Button>
              </div>
            </div>
          )
        })}

        {/* Enhanced slide indicators */}
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-3">
          {summits.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              disabled={isAnimating}
              className={`relative overflow-hidden transition-all duration-500 ${
                index === currentSlide
                  ? 'w-8 h-2 bg-white rounded-full'
                  : 'w-2 h-2 bg-white/50 hover:bg-white/70 rounded-full'
              }`}
            >
              {index === currentSlide && (
                <div className="absolute inset-0 bg-white/80 rounded-full animate-pulse" />
              )}
            </button>
          ))}
        </div>
      </div>
    </Card>
  )
}

function NewsFeedContent() {
  const { data, isLoading, error } = useGetNewsMediaQuery({
    where: {
      category: { equals: 'news-feed' },
    },
  })

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen text-black">
        <Loader2 className="h-16 w-16 animate-spin text-palette-green" />
        <p className="mt-4 text-lg font-medium">Loading news feed...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen text-gray-700 p-4 text-center">
        <TriangleAlert className="h-24 w-24 text-palette-red mb-6 animate-pulse" />
        <h1 className="text-4xl font-bold mb-4">Something went wrong!</h1>
        <p className="text-lg text-gray-300 max-w-md mb-8">
          We&apos;re sorry, but an unexpected error occurred. Please try again later or go back to
          the homepage.
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <Button
            onClick={() => {}}
            className="font-semibold text-sm transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02] transform hover:-translate-y-0.5 rounded-lg px-8 py-4 bg-palette-green hover:bg-palette-green/90 text-black"
          >
            Try Again
          </Button>
          <Button
            asChild
            className="font-semibold text-sm transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02] transform hover:-translate-y-0.5 rounded-lg px-8 py-4 bg-transparent border-2 border-palette-blue text-palette-blue hover:bg-palette-blue hover:text-black"
          >
            <Link href="/">Go Home</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
        {/* Headlines Section */}
        <div className="lg:col-span-2">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Headlines</h2>
          <div className="ml-4">
            <HeadlinesSlider />
          </div>
        </div>

        {/* Latest Topics Section */}
        <LatestNewsTopics />
      </div>

      <NewsFeed />
    </>
  )
}

function PressReleasesContent() {
  const { data, isLoading, error } = useGetNewsMediaQuery({
    where: {
      category: 'press-releases',
    },
  })

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen text-black">
        <Loader2 className="h-16 w-16 animate-spin text-palette-green" />
        <p className="mt-4 text-lg font-medium">Loading press releases...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen text-gray-700 p-4 text-center">
        <TriangleAlert className="h-24 w-24 text-palette-red mb-6 animate-pulse" />
        <h1 className="text-4xl font-bold mb-4">Something went wrong!</h1>
        <p className="text-lg text-gray-300 max-w-md mb-8">
          We&apos;re sorry, but an unexpected error occurred. Please try again later or go back to
          the homepage.
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <Button
            onClick={() => {}}
            className="font-semibold text-sm transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02] transform hover:-translate-y-0.5 rounded-lg px-8 py-4 bg-palette-green hover:bg-palette-green/90 text-white"
          >
            Try Again
          </Button>
          <Button
            asChild
            className="font-semibold text-sm transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02] transform hover:-translate-y-0.5 rounded-lg px-8 py-4 bg-transparent border-2 border-palette-blue text-palette-blue hover:bg-palette-blue hover:text-white"
          >
            <Link href="/">Go Home</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
        {/* Featured Press Release */}
        <div className="lg:col-span-2">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Featured Press Release</h2>
          <div className="ml-4">
            <PressReleaseSlider />
          </div>
        </div>

        {/* Recent Press Releases */}
        <PressReleaseRecent />
      </div>

      <AllPressReleases />
    </>
  )
}

function BlogsContent() {
  const { data, isLoading, error } = useGetNewsMediaQuery({
    where: {
      category: 'blogs',
    },
  })

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen text-black">
        <Loader2 className="h-16 w-16 animate-spin text-palette-green" />
        <p className="mt-4 text-lg font-medium">Loading blogs...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen text-gray-700 p-4 text-center">
        <TriangleAlert className="h-24 w-24 text-palette-red mb-6 animate-pulse" />
        <h1 className="text-4xl font-bold mb-4">Something went wrong!</h1>
        <p className="text-lg text-gray-300 max-w-md mb-8">
          We&apos;re sorry, but an unexpected error occurred. Please try again later or go back to
          the homepage.
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <Button
            onClick={() => {}}
            className="font-semibold text-sm transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02] transform hover:-translate-y-0.5 rounded-lg px-8 py-4 bg-palette-green hover:bg-palette-green/90 text-white"
          >
            Try Again
          </Button>
          <Button
            asChild
            className="font-semibold text-sm transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02] transform hover:-translate-y-0.5 rounded-lg px-8 py-4 bg-transparent border-2 border-palette-blue text-palette-blue hover:bg-palette-blue hover:text-white"
          >
            <Link href="/">Go Home</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
        {/* Featured Blog Post */}
        <div className="lg:col-span-2">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Featured Blog Post</h2>
          <div className="ml-4">
            <BlogSlider />
          </div>
        </div>

        {/* Popular Topics */}
        <PopularTopics />
      </div>

      <LatestBlogPosts />
    </>
  )
}

function EventsUpdatesContent() {
  const { data, isLoading, error } = useGetNewsMediaQuery({
    where: {
      category: 'event-updates',
    },
  })

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen text-black">
        <Loader2 className="h-16 w-16 animate-spin text-palette-green" />
        <p className="mt-4 text-lg font-medium">Loading event updates...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen text-gray-700 p-4 text-center">
        <TriangleAlert className="h-24 w-24 text-palette-red mb-6 animate-pulse" />
        <h1 className="text-4xl font-bold mb-4">Something went wrong!</h1>
        <p className="text-lg text-gray-300 max-w-md mb-8">
          We&apos;re sorry, but an unexpected error occurred. Please try again later or go back to
          the homepage.
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <Button
            onClick={() => {}}
            className="font-semibold text-sm transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02] transform hover:-translate-y-0.5 rounded-lg px-8 py-4 bg-palette-green hover:bg-palette-green/90 text-white"
          >
            Try Again
          </Button>
          <Button
            asChild
            className="font-semibold text-sm transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02] transform hover:-translate-y-0.5 rounded-lg px-8 py-4 bg-transparent border-2 border-palette-blue text-palette-blue hover:bg-palette-blue hover:text-white"
          >
            <Link href="/">Go Home</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
        {/* Featured Event */}
        <div className="lg:col-span-2">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Featured Event</h2>
          <div className="ml-4">
            <EventsSlider />
          </div>
        </div>

        {/* Upcoming Events */}
        <UpcomingEvents />
      </div>

      <AllEvents />
    </>
  )
}
