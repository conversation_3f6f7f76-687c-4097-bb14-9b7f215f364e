'use client'

import type React from 'react'
import { useState } from 'react'
import { User, Building, Lightbulb, CheckCircle, Heart, Send } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { useCreatePartnershipMatchingMutation } from '@/lib/api/partnershipMatchingApi'

const formSteps = [
  { id: 1, title: 'Partnership Type', icon: User },
  { id: 2, title: 'Contact Details', icon: Building },
  { id: 3, title: 'Project Information', icon: Lightbulb },
  { id: 4, title: 'Review & Submit', icon: CheckCircle },
]

export default function PartnershipForm() {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    userType: 'ik-holder',
    lookingFor: 'investor',
    fullName: '',
    email: '',
    phone: '',
    organization: '',
    areaOfInterest: '',
    investmentRange: '',
    timeline: '',
    experience: '',
    message: '',
  })

  const [createPartnershipMatching, { isLoading, isSuccess, error }] = useCreatePartnershipMatchingMutation()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (currentStep < formSteps.length) {
      setCurrentStep(currentStep + 1)
    } else {
      try {
        await createPartnershipMatching({
          ...formData,
          projectDescription: formData.message // Map message to projectDescription for API
        }).unwrap()
        // Reset form on success
        setFormData({
          userType: 'ik-holder',
          lookingFor: 'investor',
          fullName: '',
          email: '',
          phone: '',
          organization: '',
          areaOfInterest: '',
          investmentRange: '',
          timeline: '',
          experience: '',
          message: '',
        })
      } catch (err) {
        console.error('Failed to submit partnership matching:', err)
      }
    }
  }

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  const goToStep = (step: number) => {
    setCurrentStep(step)
  }

  // Show success message if form was submitted successfully
  if (isSuccess) {
    return (
      <section className="py-20 bg-background">
        <div className="max-w-5xl mx-auto px-6">
          <div className="bg-white border border-gray-200 shadow-2xl overflow-hidden">
            <div className="bg-[#159147] p-8 text-white text-center">
              <h2 className="text-3xl font-bold mb-4" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                Partnership Request Submitted!
              </h2>
              <p className="text-lg" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                Thank you for your interest. We'll review your information and connect you with suitable partners soon.
              </p>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-background">
      <div className="max-w-5xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2
            className="font-myriad font-bold text-sm uppercase tracking-wider text-muted-foreground mb-4"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            PARTNERSHIP MATCHING
          </h2>
          <h3
            className="font-myriad font-bold text-3xl lg:text-4xl text-primary mb-6"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            Partnership Matching
          </h3>
          <p
            className="font-myriad text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            Connect with the right partners through our intelligent matching system. Tell us about
            your goals and we'll help you find the perfect collaboration opportunities.
          </p>
        </div>

        {/* Enhanced Form Header */}
        <div className="bg-gradient-to-r from-[#7E2518] to-[#C86E36] text-white p-8">
          <h3
            className="text-2xl md:text-3xl font-bold text-center mb-8"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            PARTNERSHIP MATCH MAKING FORM
          </h3>

          {/* Progress Steps */}
          <div className="flex items-center justify-between max-w-2xl mx-auto">
            {formSteps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <button
                  onClick={() => goToStep(step.id)}
                  className={`w-12 h-12 flex items-center justify-center transition-all duration-300 ${
                    currentStep >= step.id
                      ? 'bg-white text-[#7E2518] shadow-lg'
                      : 'bg-white/20 text-white/70 hover:bg-white/30'
                  }`}
                >
                  <step.icon className="w-6 h-6" />
                </button>
                {index < formSteps.length - 1 && (
                  <div
                    className={`w-8 md:w-16 h-1 mx-2 transition-all duration-300 ${
                      currentStep > step.id ? 'bg-white' : 'bg-white/30'
                    }`}
                  ></div>
                )}
              </div>
            ))}
          </div>

          <div className="text-center mt-4">
            <p className="text-white/90" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
              Step {currentStep} of {formSteps.length}: {formSteps[currentStep - 1]?.title}
            </p>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white border border-gray-200 shadow-2xl overflow-hidden">
          <form onSubmit={handleSubmit} className="p-8 lg:p-12">
            {/* Step 1: Partnership Type */}
            {currentStep === 1 && (
              <div className="space-y-8">
                <div className="text-center mb-8">
                  <User className="w-16 h-16 text-[#7E2518] mx-auto mb-4" />
                  <h4
                    className="text-2xl font-bold text-[#7E2518] mb-2"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    Tell us about yourself
                  </h4>
                  <p
                    className="text-gray-600"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    Help us understand your role and what you're looking for
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-8">
                  {/* I am a */}
                  <div className="bg-gray-50 p-6">
                    <label
                      className="block text-lg font-bold text-[#7E2518] mb-4"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      I am a:
                    </label>
                    <div className="space-y-3">
                      {[
                        { value: 'ik-holder', label: 'Indigenous Knowledge Holder' },
                        { value: 'investor', label: 'Investor' },
                        { value: 'entrepreneur', label: 'Entrepreneur' },
                        { value: 'researcher', label: 'Researcher' },
                      ].map((option) => (
                        <label
                          key={option.value}
                          className="flex items-center p-3 bg-white border-2 border-transparent hover:border-[#7E2518]/20 cursor-pointer transition-all"
                        >
                          <input
                            type="radio"
                            name="userType"
                            value={option.value}
                            checked={formData.userType === option.value}
                            onChange={handleChange}
                            className="sr-only"
                          />
                          <div
                            className={`w-5 h-5 border-2 mr-3 flex items-center justify-center ${
                              formData.userType === option.value
                                ? 'border-[#7E2518] bg-[#7E2518]'
                                : 'border-gray-300'
                            }`}
                          >
                            {formData.userType === option.value && (
                              <div className="w-2 h-2 bg-white"></div>
                            )}
                          </div>
                          <span className="text-2xl mr-3">{option.icon}</span>
                          <span
                            className="text-gray-700 font-medium"
                            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                          >
                            {option.label}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* I am looking for */}
                  <div className="bg-gray-50 p-6">
                    <label
                      className="block text-lg font-bold text-[#7E2518] mb-4"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      I am looking for:
                    </label>
                    <div className="space-y-3">
                      {[
                        { value: 'investor', label: 'Investment Partner' },
                        { value: 'knowledge-holder', label: 'Knowledge Holder' },
                        { value: 'business-partner', label: 'Business Partner' },
                        { value: 'mentor', label: 'Mentor/Advisor' },
                      ].map((option) => (
                        <label
                          key={option.value}
                          className="flex items-center p-3 bg-white border-2 border-transparent hover:border-[#7E2518]/20 cursor-pointer transition-all"
                        >
                          <input
                            type="radio"
                            name="lookingFor"
                            value={option.value}
                            checked={formData.lookingFor === option.value}
                            onChange={handleChange}
                            className="sr-only"
                          />
                          <div
                            className={`w-5 h-5 border-2 mr-3 flex items-center justify-center ${
                              formData.lookingFor === option.value
                                ? 'border-[#159147] bg-[#159147]'
                                : 'border-gray-300'
                            }`}
                          >
                            {formData.lookingFor === option.value && (
                              <div className="w-2 h-2 bg-white"></div>
                            )}
                          </div>
                          <span className="text-2xl mr-3">{option.icon}</span>
                          <span
                            className="text-gray-700 font-medium"
                            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                          >
                            {option.label}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Contact Details */}
            {currentStep === 2 && (
              <div className="space-y-8">
                <div className="text-center mb-8">
                  <Building className="w-16 h-16 text-[#7E2518] mx-auto mb-4" />
                  <h4
                    className="text-2xl font-bold text-[#7E2518] mb-2"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    Contact Information
                  </h4>
                  <p
                    className="text-gray-600"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    Provide your contact details so we can connect you with the right partners
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label
                      htmlFor="fullName"
                      className="block text-sm font-bold text-[#7E2518] mb-2"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="fullName"
                      name="fullName"
                      required
                      value={formData.fullName}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border-2 border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
                      placeholder="Enter your full name"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-bold text-[#7E2518] mb-2"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border-2 border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
                      placeholder="Enter your email address"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="phone"
                      className="block text-sm font-bold text-[#7E2518] mb-2"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border-2 border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
                      placeholder="Enter your phone number"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="organization"
                      className="block text-sm font-bold text-[#7E2518] mb-2"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Organization
                    </label>
                    <input
                      type="text"
                      id="organization"
                      name="organization"
                      value={formData.organization}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border-2 border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
                      placeholder="Enter your organization name"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Project Information */}
            {currentStep === 3 && (
              <div className="space-y-8">
                <div className="text-center mb-8">
                  <Lightbulb className="w-16 h-16 text-[#7E2518] mx-auto mb-4" />
                  <h4
                    className="text-2xl font-bold text-[#7E2518] mb-2"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    Project Details
                  </h4>
                  <p
                    className="text-gray-600"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    Tell us about your interests and project requirements
                  </p>
                </div>

                <div className="space-y-6">
                  <div>
                    <label
                      htmlFor="areaOfInterest"
                      className="block text-sm font-bold text-[#7E2518] mb-2"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Area of Interest *
                    </label>
                    <select
                      id="areaOfInterest"
                      name="areaOfInterest"
                      required
                      value={formData.areaOfInterest}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border-2 border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      <option value="">Select your area of interest</option>
                      <option value="agriculture">Traditional Agriculture</option>
                      <option value="medicine">Traditional Medicine</option>
                      <option value="crafts">Traditional Crafts</option>
                      <option value="tourism">Cultural Tourism</option>
                      <option value="technology">Technology Integration</option>
                      <option value="education">Education & Research</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label
                        htmlFor="investmentRange"
                        className="block text-sm font-bold text-[#7E2518] mb-2"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        Investment Range (USD)
                      </label>
                      <select
                        id="investmentRange"
                        name="investmentRange"
                        value={formData.investmentRange}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border-2 border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        <option value="">Select investment range</option>
                        <option value="under-50k">Under $50,000</option>
                        <option value="50k-100k">$50,000 - $100,000</option>
                        <option value="100k-500k">$100,000 - $500,000</option>
                        <option value="500k-1m">$500,000 - $1,000,000</option>
                        <option value="over-1m">Over $1,000,000</option>
                      </select>
                    </div>

                    <div>
                      <label
                        htmlFor="timeline"
                        className="block text-sm font-bold text-[#7E2518] mb-2"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        Project Timeline
                      </label>
                      <select
                        id="timeline"
                        name="timeline"
                        value={formData.timeline}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border-2 border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        <option value="">Select timeline</option>
                        <option value="immediate">Immediate (0-3 months)</option>
                        <option value="short-term">Short-term (3-12 months)</option>
                        <option value="medium-term">Medium-term (1-3 years)</option>
                        <option value="long-term">Long-term (3+ years)</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="experience"
                      className="block text-sm font-bold text-[#7E2518] mb-2"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Relevant Experience
                    </label>
                    <textarea
                      id="experience"
                      name="experience"
                      rows={4}
                      value={formData.experience}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border-2 border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors resize-none"
                      placeholder="Describe your relevant experience, previous projects, or expertise..."
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="message"
                      className="block text-sm font-bold text-[#7E2518] mb-2"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Project Description & Goals *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      required
                      rows={6}
                      value={formData.message}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border-2 border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors resize-none"
                      placeholder="Tell us about your partnership interests, investment goals, project vision, or how you'd like to collaborate..."
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Review & Submit */}
            {currentStep === 4 && (
              <div className="space-y-8">
                <div className="text-center mb-8">
                  <CheckCircle className="w-16 h-16 text-[#159147] mx-auto mb-4" />
                  <h4
                    className="text-2xl font-bold text-[#7E2518] mb-2"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    Review Your Information
                  </h4>
                  <p
                    className="text-gray-600"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    Please review your details before submitting your partnership request
                  </p>
                </div>

                <div className="bg-gray-50 p-6 space-y-4">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h5
                        className="font-bold text-[#7E2518] mb-2"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        Partnership Type
                      </h5>
                      <p
                        className="text-gray-700"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        I am a: <span className="font-semibold">{formData.userType}</span>
                      </p>
                      <p
                        className="text-gray-700"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        Looking for: <span className="font-semibold">{formData.lookingFor}</span>
                      </p>
                    </div>
                    <div>
                      <h5
                        className="font-bold text-[#7E2518] mb-2"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        Contact Information
                      </h5>
                      <p
                        className="text-gray-700"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        {formData.fullName}
                      </p>
                      <p
                        className="text-gray-700"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        {formData.email}
                      </p>
                      {formData.organization && (
                        <p
                          className="text-gray-700"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {formData.organization}
                        </p>
                      )}
                    </div>
                  </div>
                  <div>
                    <h5
                      className="font-bold text-[#7E2518] mb-2"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Project Details
                    </h5>
                    <p
                      className="text-gray-700"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Area: <span className="font-semibold">{formData.areaOfInterest}</span>
                    </p>
                    {formData.investmentRange && (
                      <p
                        className="text-gray-700"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        Investment Range:{' '}
                        <span className="font-semibold">{formData.investmentRange}</span>
                      </p>
                    )}
                    {formData.timeline && (
                      <p
                        className="text-gray-700"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        Timeline: <span className="font-semibold">{formData.timeline}</span>
                      </p>
                    )}
                  </div>
                  {formData.message && (
                    <div>
                      <h5
                        className="font-bold text-[#7E2518] mb-2"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        Message
                      </h5>
                      <p
                        className="text-gray-700 text-sm"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        {formData.message}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex items-center justify-between mt-12 pt-8 border-t border-gray-200">
              {currentStep > 1 && (
                <Button
                  type="button"
                  onClick={() => setCurrentStep(currentStep - 1)}
                  variant="outline"
                  className="border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white bg-transparent font-bold px-8 py-3"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  Previous Step
                </Button>
              )}

              <div className="flex-1"></div>

              <Button
                type="submit"
                disabled={isLoading}
                className={`font-bold px-8 py-3 ${
                  currentStep === formSteps.length
                    ? 'bg-[#159147] hover:bg-[#159147]/90 text-white disabled:opacity-50'
                    : 'bg-[#7E2518] hover:bg-[#6B1F14] text-white'
                }`}
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                {currentStep === formSteps.length ? (
                  <>
                    <Send className="w-5 h-5 mr-2" />
                    {isLoading ? 'Submitting...' : 'Submit Partnership Request'}
                  </>
                ) : (
                  'Next Step'
                )}
              </Button>

              {error && (
                <div className="text-red-600 text-sm mt-2">
                  Failed to submit partnership request. Please try again.
                </div>
              )}
            </div>
          </form>
        </div>

        {/* Additional Information */}
        <div className="mt-12 grid md:grid-cols-3 gap-6">
          <div className="bg-[#159147]/10 border border-[#159147]/20 p-6 text-center">
            <Heart className="w-8 h-8 text-[#159147] mx-auto mb-3" />
            <h4
              className="font-bold text-[#159147] mb-2"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Personalized Matching
            </h4>
            <p
              className="text-gray-700 text-sm"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Our AI-powered system matches you with compatible partners based on your goals and
              values.
            </p>
          </div>
          <div className="bg-[#7E2518]/10 border border-[#7E2518]/20 p-6 text-center">
            <CheckCircle className="w-8 h-8 text-[#7E2518] mx-auto mb-3" />
            <h4
              className="font-bold text-[#7E2518] mb-2"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Verified Partners
            </h4>
            <p
              className="text-gray-700 text-sm"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              All partners go through our comprehensive verification process to ensure authenticity
              and credibility.
            </p>
          </div>
          <div className="bg-[#E8B32C]/10 border border-[#E8B32C]/20 p-6 text-center">
            <Building className="w-8 h-8 text-[#C86E36] mx-auto mb-3" />
            <h4
              className="font-bold text-[#C86E36] mb-2"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Ongoing Support
            </h4>
            <p
              className="text-gray-700 text-sm"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Our team provides continuous support throughout your partnership journey to ensure
              success.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
