module.exports = {
  apps: [
    {
      name: 'ikia-conference',
      script: 'npm',
      args: 'start',
      cwd: '/home/<USER>/public_html',
      interpreter: 'none',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        NODE_OPTIONS: '--no-deprecation'
      },
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true
    }
  ]
};
