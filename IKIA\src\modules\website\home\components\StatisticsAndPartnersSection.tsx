'use client'

import { MapPin, Users, Briefcase, FileText } from 'lucide-react'
import Image from 'next/image'

// Statistics data
const statisticsData = [
  {
    number: '13',
    label: 'COUNTIES',
    icon: MapPin,
  },
  {
    number: '1,000+',
    label: 'ATTENDEES',
    icon: Users,
  },
  {
    number: '25+',
    label: 'INVESTMENT OPPORTUNITIES',
    icon: Briefcase,
  },
  {
    number: '40+',
    label: 'INDIGENOUS KNOWLEDGE ASSETS',
    icon: FileText,
  },
]

// Partners data
const partnersData = [
  {
    name: 'Kenya Emblem',
    logo: '/assets/partners/kenya-emblem.png',
  },
  {
    name: 'Museums of Kenya',
    logo: '/assets/partners/museums-logo.png',
  },
  {
    name: 'NPI',
    logo: '/assets/partners/npi-logo.png',
  },
  {
    name: 'Kenya Vision 2030',
    logo: '/assets/partners/vision-2030-no-bg.png',
  },
  {
    name: 'Council of Governors',
    logo: '/assets/partners/council-governs.png',
  },
]

export default function StatisticsAndPartnersSection() {
  return (
    <section className="relative">
      {/* Statistics Card Strip - Hovering over hero */}
      <div className="relative -mt-24 z-20">
        <div className="max-w-7xl mx-auto px-6">
          {/* Single Statistics Card Strip */}
          <div className="main-shadow bg-[#FFF8E3] p-8 lg:p-12">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 relative divide-y md:divide-y-0 md:divide-x lg:divide-x-0 divide-[#E5B11F]">
              {statisticsData.map((stat, index) => {
                const IconComponent = stat.icon
                return (
                  <div key={index} className="relative">
                    {/* Slanted Separator for Large Screens */}
                    {index < statisticsData.length - 1 && (
                      <div
                        className="absolute right-0 top-1/2 w-px h-32 bg-[#E5B11F] hidden lg:block"
                        style={{
                          transform: 'translateY(-50%) rotate(15deg)',
                          transformOrigin: 'center',
                        }}
                      />
                    )}

                    {/* Statistics Content - Number/Label Left, Icon Right */}
                    <div className="flex items-center justify-between px-6 lg:px-8 py-4">
                      {/* Left Side - Number and Label */}
                      <div className="flex-1">
                        <h3
                          className="text-2xl lg:text-3xl font-normal text-[#7E2518] mb-1"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {stat.number}
                        </h3>
                        <p
                          className="text-xs font-semibold text-[#A0503A] uppercase tracking-wide leading-tight"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {stat.label}
                        </p>
                      </div>

                      {/* Right Side - Icon */}
                      <div className="ml-4">
                        <div className="w-12 h-12 bg-[#7E2518] rounded-full flex items-center justify-center">
                          <IconComponent className="w-6 h-6 text-white" />
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Partners Section - Negative margin to eliminate white space */}
      <div className="bg-[#FFF8E3] pt-80 md:pt-40 pb-20 -mt-80 md:-mt-28">
        <div className="max-w-4xl mx-auto px-6 pt-20 md:pt-0">
          {/* Partners Grid */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 lg:gap-6 items-center justify-items-center">
            {partnersData.map((partner, index) => (
              <div key={index} className="group transition-all duration-300 hover:scale-105">
                <div className="w-28 h-24 flex items-center justify-center">
                  <Image
                    src={partner.logo}
                    alt={partner.name}
                    width={224}
                    height={112}
                    className="max-w-full max-h-full object-contain transition-all duration-300 group-hover:brightness-110"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
