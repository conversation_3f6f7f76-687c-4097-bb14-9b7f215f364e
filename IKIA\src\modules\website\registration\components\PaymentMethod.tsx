"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CreditCard, 
  Smartphone, 
  Building2, 
  DollarSign,
  Shield,
  CheckCircle,
  Info
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface PaymentMethodProps {
  amount: string | number
  onPaymentComplete: (paymentData: any) => void
  className?: string
  currency?: string
  registrationType?: string
}

export function PaymentMethod({
  amount,
  onPaymentComplete,
  className = "",
  currency = "KES",
  registrationType = "registration"
}: PaymentMethodProps) {
  const [selectedMethod, setSelectedMethod] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [paymentData, setPaymentData] = useState({
    mpesaPhone: '',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardName: '',
    bankAccount: '',
    bankName: '',
    paypalEmail: ''
  })

  const paymentMethods = [
    {
      id: 'mpesa',
      name: 'M-Pesa',
      description: 'Pay with M-Pesa mobile money',
      icon: Smartphone,
      popular: true
    },
    {
      id: 'card',
      name: 'Credit/Debit Card',
      description: 'Visa, Mastercard, American Express',
      icon: CreditCard,
      popular: false
    },
    {
      id: 'bank',
      name: 'Bank Transfer',
      description: 'Direct bank transfer',
      icon: Building2,
      popular: false
    }
  ]

  const handleInputChange = (field: string, value: string) => {
    setPaymentData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const processPayment = async () => {
    setIsProcessing(true)
    
    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const paymentResult = {
        method: selectedMethod,
        amount: amount,
        currency: currency,
        transactionId: `TXN_${Date.now()}`,
        status: 'completed',
        timestamp: new Date().toISOString(),
        ...paymentData
      }
      
      onPaymentComplete(paymentResult)
    } catch (error) {
      console.error('Payment failed:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const formatAmount = (amount: string | number) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(numAmount)
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <DollarSign className="w-5 h-5" />
          <span>Payment Information</span>
        </CardTitle>
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="text-lg font-medium">Total Amount:</span>
            <span className="text-2xl font-bold text-primary">{formatAmount(amount)}</span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Payment Method Selection */}
        <div className="space-y-4">
          <Label className="text-base font-medium">Select Payment Method</Label>
          <RadioGroup value={selectedMethod} onValueChange={setSelectedMethod}>
            {paymentMethods.map((method) => {
              const IconComponent = method.icon
              return (
                <div key={method.id} className="flex items-center space-x-3">
                  <RadioGroupItem value={method.id} id={method.id} />
                  <Label 
                    htmlFor={method.id} 
                    className="flex items-center space-x-3 cursor-pointer flex-1 p-3 border rounded-lg hover:bg-gray-50"
                  >
                    <IconComponent className="w-5 h-5 text-gray-600" />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{method.name}</span>
                        {method.popular && (
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                            Popular
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">{method.description}</p>
                    </div>
                  </Label>
                </div>
              )
            })}
          </RadioGroup>
        </div>

        {/* Payment Details Forms */}
        {selectedMethod === 'mpesa' && (
          <div className="space-y-4 p-4 border rounded-lg bg-green-50">
            <h4 className="font-medium flex items-center space-x-2">
              <Smartphone className="w-4 h-4" />
              <span>M-Pesa Payment Details</span>
            </h4>
            <div>
              <Label htmlFor="mpesa-phone">M-Pesa Phone Number</Label>
              <Input
                id="mpesa-phone"
                type="tel"
                placeholder="254XXXXXXXXX"
                value={paymentData.mpesaPhone}
                onChange={(e) => handleInputChange('mpesaPhone', e.target.value)}
              />
            </div>
            <Alert>
              <Info className="w-4 h-4" />
              <AlertDescription>
                You will receive an M-Pesa prompt on your phone to complete the payment.
              </AlertDescription>
            </Alert>
          </div>
        )}

        {selectedMethod === 'card' && (
          <div className="space-y-4 p-4 border rounded-lg bg-blue-50">
            <h4 className="font-medium flex items-center space-x-2">
              <CreditCard className="w-4 h-4" />
              <span>Card Payment Details</span>
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="card-name">Cardholder Name</Label>
                <Input
                  id="card-name"
                  placeholder="John Doe"
                  value={paymentData.cardName}
                  onChange={(e) => handleInputChange('cardName', e.target.value)}
                />
              </div>
              <div className="md:col-span-2">
                <Label htmlFor="card-number">Card Number</Label>
                <Input
                  id="card-number"
                  placeholder="1234 5678 9012 3456"
                  value={paymentData.cardNumber}
                  onChange={(e) => handleInputChange('cardNumber', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="expiry">Expiry Date</Label>
                <Input
                  id="expiry"
                  placeholder="MM/YY"
                  value={paymentData.expiryDate}
                  onChange={(e) => handleInputChange('expiryDate', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="cvv">CVV</Label>
                <Input
                  id="cvv"
                  placeholder="123"
                  value={paymentData.cvv}
                  onChange={(e) => handleInputChange('cvv', e.target.value)}
                />
              </div>
            </div>
          </div>
        )}

        {selectedMethod === 'bank' && (
          <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
            <h4 className="font-medium flex items-center space-x-2">
              <Building2 className="w-4 h-4" />
              <span>Bank Transfer Details</span>
            </h4>
            <Alert>
              <Info className="w-4 h-4" />
              <AlertDescription>
                Bank transfer details will be provided after registration. Payment must be completed within 24 hours.
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Security Notice */}
        <div className="flex items-center space-x-2 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
          <Shield className="w-4 h-4" />
          <span>Your payment information is secure and encrypted</span>
        </div>

        {/* Process Payment Button */}
        {selectedMethod && (
          <Button 
            onClick={processPayment} 
            disabled={isProcessing}
            className="w-full"
            size="lg"
          >
            {isProcessing ? (
              <>Processing Payment...</>
            ) : (
              <>
                <DollarSign className="w-4 h-4 mr-2" />
                Pay {formatAmount(amount)}
              </>
            )}
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
