import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'

export const Sponsors: CollectionConfig = {
  slug: 'sponsors',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'tier', 'category', 'sponsorshipPackage'],
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Sponsor Name',
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
      required: true,
      label: 'Sponsor Logo',
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Description',
    },
    {
      name: 'website',
      type: 'text',
      label: 'Website URL',
      validate: (val) => {
        if (val && !val.match(/^https?:\/\/.+/)) {
          return 'Please enter a valid URL starting with http:// or https://'
        }
        return true
      },
    },
    {
      name: 'tier',
      type: 'select',
      label: 'Sponsorship Tier',
      options: [
        { label: 'Title Sponsor', value: 'title' },
        { label: 'Platinum', value: 'platinum' },
        { label: 'Gold', value: 'gold' },
        { label: 'Silver', value: 'silver' },
        { label: 'Bronze', value: 'bronze' },
      ],
    },
    {
      name: 'category',
      type: 'select',
      label: 'Sponsor Category',
      required: true,
      options: [
        { label: 'Financial', value: 'financial' },
        { label: 'Service', value: 'service' },
        { label: 'Media', value: 'media' },
        { label: 'Special', value: 'special' },
      ],
    },
    {
      name: 'sponsorshipPackage',
      type: 'relationship',
      relationTo: 'sponsorship-packages',
      label: 'Sponsorship Package',
    },
    {
      name: 'hasDetailsPage',
      type: 'checkbox',
      label: 'Has Details Page',
      defaultValue: false,
      admin: {
        description: 'Enable if this sponsor should have a dedicated details page',
      },
    },
    {
      name: 'additionalLogos',
      type: 'array',
      label: 'Additional Logos',
      admin: {
        description: 'Additional logos for sub-brands or services (e.g., M-Pesa for Safaricom)',
      },
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
          label: 'Logo Name',
        },
        {
          name: 'logo',
          type: 'upload',
          relationTo: 'media',
          required: true,
          label: 'Logo Image',
        },
      ],
    },
    {
      name: 'featured',
      type: 'checkbox',
      label: 'Featured Sponsor',
      defaultValue: false,
      admin: {
        description: 'Mark as featured to highlight in special sections',
      },
    },
    {
      name: 'displayOrder',
      type: 'number',
      label: 'Display Order',
      admin: {
        description: 'Lower numbers appear first. Leave empty for automatic ordering.',
      },
    },
    ...slugField(),
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Auto-generate slug if not provided
        if (!data.slug && data.name) {
          data.slug = data.name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
        }
        return data
      },
    ],
  },
}

export default Sponsors
