import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
//import { Separator } from '@/components/ui/separator'
import { ArrowLeft, ExternalLink, Mail, Phone, MapPin, Globe } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

export default function SponsorDetails() {
  // Mock sponsor data - in a real app, this would come from props or API
  const sponsor = {
    id: '1',
    name: '<PERSON>',
    organization: 'TechCorp Solutions',
    tier: 'Platinum',
    profileImage: '/placeholder.svg?height=200&width=200&text=JS',
    about:
      '<PERSON> is the CEO and founder of TechCorp Solutions, a leading technology company specializing in innovative software solutions for enterprise clients. With over 15 years of experience in the tech industry, <PERSON> has been instrumental in driving digital transformation initiatives across various sectors. His passion for technology and commitment to excellence has made TechCorp Solutions a trusted partner for businesses worldwide.',
    email: '<EMAIL>',
    phone: '+****************',
    website: 'https://techcorp.com',
    location: 'San Francisco, CA',
    sponsorshipAmount: '$50,000',
    eventName: 'Tech Innovation Summit 2024',
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Back Button */}
        <div className="mb-6">
          <Button variant="ghost" className="gap-2" asChild>
            <Link href="/sponsors">
              <ArrowLeft className="h-4 w-4" />
              Back to Sponsors
            </Link>
          </Button>
        </div>

        {/* Main Content Card */}
        <Card className="shadow-lg">
          <CardHeader className="text-center pb-6">
            <div className="flex flex-col items-center space-y-4">
              {/* Profile Image */}
              <div className="relative">
                <Image
                  src={sponsor.profileImage || '/placeholder.svg'}
                  alt={`${sponsor.name} profile`}
                  width={200}
                  height={200}
                  className="rounded-full border-4 border-white shadow-lg"
                />
                <Badge className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-yellow-400 to-yellow-600 text-black font-semibold">
                  {sponsor.tier} Sponsor
                </Badge>
              </div>

              {/* Name and Organization */}
              <div className="text-center space-y-2">
                <h1 className="text-3xl font-bold text-gray-900">{sponsor.name}</h1>
                <p className="text-xl text-gray-600 font-medium">{sponsor.organization}</p>
                <p className="text-sm text-gray-500">Sponsor for {sponsor.eventName}</p>
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-8">
            {/* About Sections */}
            <div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">About {sponsor.name}</h2>

              {/* Company Overview */}
              <div className="mb-8">
                <div className="flex flex-col lg:flex-row gap-6 items-start">
                  <div className="lg:w-1/3">
                    <Image
                      src="/placeholder.svg?height=200&width=300&text=Company+Overview"
                      alt="Company Overview"
                      width={300}
                      height={200}
                      className="rounded-lg shadow-md w-full"
                    />
                  </div>
                  <div className="lg:w-2/3">
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Company Overview</h3>
                    <p className="text-gray-700 leading-relaxed">
                      TechCorp Solutions is a leading technology company specializing in innovative
                      software solutions for enterprise clients. Founded in 2008, the company has
                      grown from a small startup to a global organization serving Fortune 500
                      companies across various industries.
                    </p>
                  </div>
                </div>
              </div>

              {/* Notable Contributions */}
              <div className="mb-8">
                <div className="flex flex-col lg:flex-row-reverse gap-6 items-start">
                  <div className="lg:w-1/3">
                    <Image
                      src="/placeholder.svg?height=200&width=300&text=Innovation+Award"
                      alt="Notable Contributions"
                      width={300}
                      height={200}
                      className="rounded-lg shadow-md w-full"
                    />
                  </div>
                  <div className="lg:w-2/3">
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">
                      Notable Contributions
                    </h3>
                    <p className="text-gray-700 leading-relaxed mb-3">
                      John has been instrumental in driving digital transformation initiatives
                      across various sectors. His key contributions include:
                    </p>
                    <ul className="text-gray-700 space-y-2">
                      <li className="flex items-start gap-2">
                        <span className="text-blue-600 mt-1">•</span>
                        <span>
                          Led the development of AI-powered analytics platform used by 200+
                          companies
                        </span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-blue-600 mt-1">•</span>
                        <span>
                          Pioneered sustainable tech practices, reducing carbon footprint by 40%
                        </span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="text-blue-600 mt-1">•</span>
                        <span>
                          Mentored over 50 startup founders through TechCorp's incubator program
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Awards & Recognition */}
              <div className="mb-8">
                <div className="flex flex-col lg:flex-row gap-6 items-start">
                  <div className="lg:w-1/3">
                    <Image
                      src="/placeholder.svg?height=200&width=300&text=Awards+Trophy"
                      alt="Awards and Recognition"
                      width={300}
                      height={200}
                      className="rounded-lg shadow-md w-full"
                    />
                  </div>
                  <div className="lg:w-2/3">
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">
                      Awards & Recognition
                    </h3>
                    <div className="space-y-3">
                      <div className="border-l-4 border-blue-500 pl-4">
                        <h4 className="font-semibold text-gray-900">
                          Tech Innovator of the Year 2023
                        </h4>
                        <p className="text-gray-600 text-sm">Silicon Valley Tech Awards</p>
                      </div>
                      <div className="border-l-4 border-green-500 pl-4">
                        <h4 className="font-semibold text-gray-900">
                          Sustainability Leadership Award 2022
                        </h4>
                        <p className="text-gray-600 text-sm">Green Tech Initiative</p>
                      </div>
                      <div className="border-l-4 border-purple-500 pl-4">
                        <h4 className="font-semibold text-gray-900">Forbes 40 Under 40 2021</h4>
                        <p className="text-gray-600 text-sm">Technology Category</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Projects */}
              <div className="mb-8">
                <div className="flex flex-col lg:flex-row-reverse gap-6 items-start">
                  <div className="lg:w-1/3">
                    <Image
                      src="/placeholder.svg?height=200&width=300&text=Project+Dashboard"
                      alt="Recent Projects"
                      width={300}
                      height={200}
                      className="rounded-lg shadow-md w-full"
                    />
                  </div>
                  <div className="lg:w-2/3">
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Recent Projects</h3>
                    <div className="space-y-4">
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h4 className="font-semibold text-gray-900 mb-2">
                          Smart City Infrastructure Platform
                        </h4>
                        <p className="text-gray-700 text-sm">
                          Developed an integrated IoT platform for city management, deployed in 15
                          major cities worldwide, improving efficiency by 35%.
                        </p>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <h4 className="font-semibold text-gray-900 mb-2">
                          Healthcare AI Assistant
                        </h4>
                        <p className="text-gray-700 text-sm">
                          Created an AI-powered diagnostic tool that assists healthcare
                          professionals, reducing diagnosis time by 50%.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Community Impact */}
              <div className="mb-8">
                <div className="flex flex-col lg:flex-row gap-6 items-start">
                  <div className="lg:w-1/3">
                    <Image
                      src="/placeholder.svg?height=200&width=300&text=Community+Impact"
                      alt="Community Impact"
                      width={300}
                      height={200}
                      className="rounded-lg shadow-md w-full"
                    />
                  </div>
                  <div className="lg:w-2/3">
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Community Impact</h3>
                    <p className="text-gray-700 leading-relaxed mb-3">
                      Beyond business success, John is committed to giving back to the community
                      through various initiatives:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">$2M+</div>
                        <div className="text-sm text-gray-600">Donated to Education</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">500+</div>
                        <div className="text-sm text-gray-600">Students Mentored</div>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">25</div>
                        <div className="text-sm text-gray-600">Scholarships Funded</div>
                      </div>
                      <div className="text-center p-4 bg-orange-50 rounded-lg">
                        <div className="text-2xl font-bold text-orange-600">10</div>
                        <div className="text-sm text-gray-600">Tech Bootcamps Sponsored</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* <Separator /> */}

            {/* Contact Information */}
            <div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">Contact Information</h2>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <a
                        href={`mailto:${sponsor.email}`}
                        className="text-blue-600 hover:text-blue-800 font-medium"
                      >
                        {sponsor.email}
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Phone className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Phone</p>
                      <a
                        href={`tel:${sponsor.phone}`}
                        className="text-blue-600 hover:text-blue-800 font-medium"
                      >
                        {sponsor.phone}
                      </a>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Globe className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Website</p>
                      <a
                        href={sponsor.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 font-medium flex items-center gap-1"
                      >
                        {sponsor.website.replace('https://', '')}
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <MapPin className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm text-gray-500">Location</p>
                      <p className="text-gray-900 font-medium">{sponsor.location}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* <Separator /> */}

            {/* Sponsorship Details */}
            <div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">Sponsorship Details</h2>
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Sponsorship Tier</p>
                    <Badge variant="secondary" className="text-lg px-4 py-2">
                      {sponsor.tier}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Contribution</p>
                    <p className="text-2xl font-bold text-green-600">{sponsor.sponsorshipAmount}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6">
              <Button className="flex-1" asChild>
                <a href={`mailto:${sponsor.email}`}>
                  <Mail className="h-4 w-4 mr-2" />
                  Contact Sponsor
                </a>
              </Button>
              <Button variant="outline" className="flex-1 bg-transparent" asChild>
                <a href={sponsor.website} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Visit Website
                </a>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
