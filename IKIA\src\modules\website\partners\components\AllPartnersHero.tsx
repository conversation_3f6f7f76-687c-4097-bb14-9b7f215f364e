'use client'

import { But<PERSON> } from '@/components/ui/button'
import { ArrowLeft, Users, Building, Globe } from 'lucide-react'
import Link from 'next/link'
import { useGetPartnersQuery } from '@/lib/api/partnersApi'
import { useMemo } from 'react'

export default function AllPartnersHero() {
  const { data, isLoading, error } = useGetPartnersQuery({ limit: 0 }, {
    refetchOnMountOrArgChange: true,
  })

  const stats = useMemo(() => {
    if (!data?.partners) return { strategic: 0, community: 0, countries: 0, total: 0 }

    const partners = data.partners
    const total = partners.length

    const strategic = partners.filter(p =>
      p.category?.includes('strategic') || p.sponsorshipTier === 'platinum' || p.sponsorshipTier === 'gold'
    ).length

    const community = partners.filter(p =>
      p.category?.includes('community') || p.category?.includes('local')
    ).length

    const uniqueCountries = new Set(
      partners
        .map(p => p.contact?.location?.country)
        .filter(Boolean)
    ).size

    return { strategic, community, countries: uniqueCountries, total }
  }, [data])

  return (
    <section className="py-16 lg:py-20 section-bg-secondary">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="lg:pr-8">
            {/* Breadcrumb */}
            <div className="inline-flex items-center gap-2 bg-[#7E2518]/8 px-4 py-2 border border-[#7E2518]/15 mb-6">
              <div className="w-2 h-2 bg-[#E8B32C] animate-pulse"></div>
              <span className="text-sm font-medium text-[#7E2518] font-['Myriad_Pro',Arial,sans-serif]">
                All Partners
              </span>
            </div>

            <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight text-[#7E2518] font-['Myriad_Pro',Arial,sans-serif]">
              Our Strategic
              <span className="block text-[#E8B32C]">Partners</span>
            </h1>

            <div className="w-24 h-1 bg-[#E8B32C] mb-6"></div>

            <p className="text-xl mb-8 text-gray-700 max-w-lg leading-relaxed font-['Myriad_Pro',Arial,sans-serif]">
              Discover all the organizations, institutions, and communities collaborating with us to
              advance indigenous knowledge preservation and commercialization.
            </p>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                asChild
                className="bg-[#7E2518] hover:bg-[#159147] text-white border-0 font-bold transition-all duration-300 main-shadow px-8 font-['Myriad_Pro',Arial,sans-serif]"
              >
                <Link href="/partners-sponsors" className="flex items-center gap-2">
                  <ArrowLeft className="w-5 h-5" />
                  Back to Partners & Sponsors
                </Link>
              </Button>
            </div>
          </div>

          {/* Right Content - Stats */}
          <div className="lg:pl-8">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div className="bg-white main-shadow border border-gray-100 p-6 text-center">
                <div className="w-12 h-12 bg-[#7E2518] flex items-center justify-center mx-auto mb-4">
                  <Building className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-[#7E2518] mb-2 font-['Myriad_Pro',Arial,sans-serif]">
                  {isLoading ? (
                    <div className="w-12 h-6 bg-gray-200 animate-pulse rounded mx-auto"></div>
                  ) : error ? (
                    '0'
                  ) : (
                    `${stats.strategic}+`
                  )}
                </h3>
                <p className="text-gray-600 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                  Strategic Partners
                </p>
              </div>

              <div className="bg-white main-shadow border border-gray-100 p-6 text-center">
                <div className="w-12 h-12 bg-[#159147] flex items-center justify-center mx-auto mb-4">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-[#7E2518] mb-2 font-['Myriad_Pro',Arial,sans-serif]">
                  {isLoading ? (
                    <div className="w-12 h-6 bg-gray-200 animate-pulse rounded mx-auto"></div>
                  ) : error ? (
                    '0'
                  ) : (
                    `${stats.community}+`
                  )}
                </h3>
                <p className="text-gray-600 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                  Community Partners
                </p>
              </div>

              <div className="bg-white main-shadow border border-gray-100 p-6 text-center sm:col-span-2">
                <div className="w-12 h-12 bg-[#E8B32C] flex items-center justify-center mx-auto mb-4">
                  <Globe className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-[#7E2518] mb-2 font-['Myriad_Pro',Arial,sans-serif]">
                  {isLoading ? (
                    <div className="w-12 h-6 bg-gray-200 animate-pulse rounded mx-auto"></div>
                  ) : error ? (
                    '0'
                  ) : (
                    `${stats.countries}+`
                  )}
                </h3>
                <p className="text-gray-600 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                  Countries Represented
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
