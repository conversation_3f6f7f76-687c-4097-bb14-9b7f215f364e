import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'

export const DelegatePackages: CollectionConfig = {
  slug: 'delegatepackages',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone, // Public access for package selection
    update: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'price', 'currency', 'isActive', 'displayOrder'],
    group: 'Registration',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Package Name',
      admin: {
        description: 'Name of the delegate package (e.g., Basic Package, Premium Package)',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
      label: 'Package Description',
      admin: {
        description: 'Detailed description of what this package includes',
      },
    },
    {
      name: 'price',
      type: 'number',
      required: true,
      label: 'Price',
      admin: {
        description: 'Package price in the specified currency',
      },
    },
    {
      name: 'currency',
      type: 'select',
      required: true,
      defaultValue: 'KES',
      options: [
        { label: 'Kenyan Shilling (KES)', value: 'KES' },
        { label: 'US Dollar (USD)', value: 'USD' },
        { label: 'Euro (EUR)', value: 'EUR' },
        { label: 'British Pound (GBP)', value: 'GBP' },
      ],
      admin: {
        description: 'Currency for the package price',
      },
    },
    {
      name: 'duration',
      type: 'text',
      label: 'Duration',
      admin: {
        description: 'Package duration (e.g., "3 days", "1 week", "One-time")',
      },
    },
    {
      name: 'packageType',
      type: 'select',
      required: true,
      defaultValue: 'delegate',
      options: [
        { label: 'Delegate Registration', value: 'delegate' },
        { label: 'Exhibition Package', value: 'exhibition' },
        { label: 'Sponsorship Package', value: 'sponsorship' },
        { label: 'Special Event', value: 'special' },
      ],
      admin: {
        description: 'Type of package for categorization',
      },
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      label: 'Active',
      admin: {
        description: 'Whether this package is available for selection',
      },
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      defaultValue: false,
      label: 'Featured',
      admin: {
        description: 'Whether this package should be highlighted as featured',
      },
    },
    {
      name: 'displayOrder',
      type: 'number',
      defaultValue: 0,
      label: 'Display Order',
      admin: {
        description: 'Order in which packages are displayed (lower numbers first)',
      },
    },
    {
      name: 'features',
      type: 'array',
      label: 'Package Features',
      fields: [
        {
          name: 'feature',
          type: 'text',
          required: true,
          label: 'Feature Description',
        },
        {
          name: 'included',
          type: 'checkbox',
          defaultValue: true,
          label: 'Included',
          admin: {
            description: 'Whether this feature is included or excluded',
          },
        },
        {
          name: 'additionalCost',
          type: 'number',
          label: 'Additional Cost',
          admin: {
            description: 'Additional cost for this feature (if not included)',
          },
        },
      ],
      admin: {
        description: 'List of features included in this package',
      },
    },
    {
      name: 'benefits',
      type: 'array',
      label: 'Package Benefits',
      fields: [
        {
          name: 'benefit',
          type: 'text',
          required: true,
          label: 'Benefit Description',
        },
        {
          name: 'icon',
          type: 'text',
          label: 'Icon Name',
          admin: {
            description: 'Icon name for display (e.g., "check", "star", "gift")',
          },
        },
      ],
      admin: {
        description: 'Key benefits of this package',
      },
    },
    {
      name: 'limitations',
      type: 'array',
      label: 'Package Limitations',
      fields: [
        {
          name: 'limitation',
          type: 'text',
          required: true,
          label: 'Limitation Description',
        },
      ],
      admin: {
        description: 'Any limitations or restrictions for this package',
      },
    },
    {
      name: 'targetAudience',
      type: 'array',
      label: 'Target Audience',
      fields: [
        {
          name: 'audience',
          type: 'select',
          options: [
            { label: 'Individual Delegates', value: 'individual' },
            { label: 'Corporate Groups', value: 'corporate' },
            { label: 'Students', value: 'students' },
            { label: 'Researchers', value: 'researchers' },
            { label: 'Government Officials', value: 'government' },
            { label: 'NGO Representatives', value: 'ngo' },
            { label: 'Media Personnel', value: 'media' },
            { label: 'International Delegates', value: 'international' },
          ],
        },
      ],
      admin: {
        description: 'Who this package is designed for',
      },
    },
    {
      name: 'inclusions',
      type: 'group',
      label: 'Package Inclusions',
      fields: [
        {
          name: 'conferenceAccess',
          type: 'checkbox',
          defaultValue: true,
          label: 'Conference Access',
        },
        {
          name: 'meals',
          type: 'select',
          options: [
            { label: 'No Meals', value: 'none' },
            { label: 'Lunch Only', value: 'lunch' },
            { label: 'All Meals', value: 'all' },
            { label: 'Refreshments Only', value: 'refreshments' },
          ],
          defaultValue: 'refreshments',
          label: 'Meals Included',
        },
        {
          name: 'materials',
          type: 'checkbox',
          defaultValue: true,
          label: 'Conference Materials',
        },
        {
          name: 'certificate',
          type: 'checkbox',
          defaultValue: true,
          label: 'Certificate of Attendance',
        },
        {
          name: 'networking',
          type: 'checkbox',
          defaultValue: false,
          label: 'Networking Events',
        },
        {
          name: 'accommodation',
          type: 'checkbox',
          defaultValue: false,
          label: 'Accommodation',
        },
        {
          name: 'transport',
          type: 'checkbox',
          defaultValue: false,
          label: 'Transport',
        },
      ],
      admin: {
        description: 'What is included in this package',
      },
    },
    {
      name: 'pricing',
      type: 'group',
      label: 'Pricing Details',
      fields: [
        {
          name: 'earlyBirdPrice',
          type: 'number',
          label: 'Early Bird Price',
          admin: {
            description: 'Discounted price for early registrations',
          },
        },
        {
          name: 'earlyBirdDeadline',
          type: 'date',
          label: 'Early Bird Deadline',
          admin: {
            description: 'Last date for early bird pricing',
          },
        },
        {
          name: 'groupDiscountThreshold',
          type: 'number',
          label: 'Group Discount Threshold',
          admin: {
            description: 'Minimum number of people for group discount',
          },
        },
        {
          name: 'groupDiscountPercentage',
          type: 'number',
          label: 'Group Discount Percentage',
          admin: {
            description: 'Percentage discount for group registrations',
          },
        },
      ],
      admin: {
        description: 'Advanced pricing options',
      },
    },
    {
      name: 'availability',
      type: 'group',
      label: 'Availability Settings',
      fields: [
        {
          name: 'maxCapacity',
          type: 'number',
          label: 'Maximum Capacity',
          admin: {
            description: 'Maximum number of people who can register for this package',
          },
        },
        {
          name: 'currentRegistrations',
          type: 'number',
          defaultValue: 0,
          label: 'Current Registrations',
          admin: {
            description: 'Number of people currently registered (auto-updated)',
            readOnly: true,
          },
        },
        {
          name: 'registrationDeadline',
          type: 'date',
          label: 'Registration Deadline',
          admin: {
            description: 'Last date for registration',
          },
        },
      ],
      admin: {
        description: 'Package availability and capacity settings',
      },
    },
    {
      name: 'metadata',
      type: 'group',
      label: 'Package Metadata',
      fields: [
        {
          name: 'tags',
          type: 'array',
          fields: [
            {
              name: 'tag',
              type: 'text',
              required: true,
            },
          ],
          admin: {
            description: 'Tags for categorization and search',
          },
        },
        {
          name: 'internalNotes',
          type: 'textarea',
          label: 'Internal Notes',
          admin: {
            description: 'Internal notes for admin use (not visible to users)',
          },
        },
      ],
      admin: {
        description: 'Additional metadata for this package',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Ensure display order is set
        if (typeof data.displayOrder !== 'number') {
          data.displayOrder = 0
        }
        return data
      },
    ],
    afterChange: [
      ({ doc, operation }) => {
        if (operation === 'create') {
          console.log(`New delegate package created: ${doc.name}`)
        }
      },
    ],
  },
}
