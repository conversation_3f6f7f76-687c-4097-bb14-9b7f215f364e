import type { PayloadRequest } from 'payload'

/**
 * Get comprehensive user data with registration info, active package, invoices, and payments
 * GET /api/users/user-data/:userId
 */
export const getUserProfileEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('🎯 User data endpoint called! v2')
    console.log('Route params:', req.routeParams)

    const userId = req.routeParams?.userId

    if (!userId) {
      console.log('❌ No userId provided')
      return new Response(
        JSON.stringify({
          success: false,
          error: 'User ID is required',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    console.log('✅ Fetching user data for ID:', userId)

    // Get user with full details
    const user = await req.payload.findByID({
      collection: 'users',
      id: userId,
      depth: 2,
    })

    if (!user) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'User not found',
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get user's active package details
    let activePackage = null
    if (user.selected_package && user.package_status === 'active') {
      try {
        const packageData = await req.payload.findByID({
          collection: 'service-packages',
          id: user.selected_package,
          depth: 1,
        })

        if (packageData) {
          const expiryDate = user.package_expiry ? new Date(user.package_expiry) : null
          const daysRemaining = expiryDate
            ? Math.max(0, Math.ceil((expiryDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24)))
            : null

          activePackage = {
            id: packageData.id,
            name: packageData.name,
            description: packageData.description,
            price: packageData.price,
            currency: packageData.currency,
            features: packageData.features || [],
            duration: packageData.duration,
            packageType: packageData.packageType,
            isActive: packageData.isActive,
            expires_at: user.package_expiry,
            days_remaining: daysRemaining,
          }
        }
      } catch (error) {
        console.error('Error fetching active package:', error)
      }
    }

    // Get all user invoices
    const invoicesResponse = await req.payload.find({
      collection: 'invoices',
      where: {
        user: { equals: userId },
      },
      depth: 2,
      sort: '-createdAt',
      limit: 100,
    })

    const invoices = invoicesResponse.docs.map((invoice: any) => ({
      id: invoice.id,
      invoice_number: invoice.invoice_number,
      amount: invoice.amount,
      currency: invoice.currency,
      status: invoice.status,
      payment_reference: invoice.payment_reference,
      due_date: invoice.due_date,
      paid_at: invoice.paid_at || null,
      payment_method: invoice.payment_method || null,
      package: invoice.package
        ? {
            id: invoice.package.id,
            name: invoice.package.name,
            price: invoice.package.price,
          }
        : null,
      payment_summary: invoice.payment_summary,
      registration_context: invoice.registration_context,
      createdAt: invoice.createdAt,
      updatedAt: invoice.updatedAt,
    }))

    // Get all payment notifications for this user
    const paymentsResponse = await req.payload.find({
      collection: 'pesaflow-notifications',
      where: {
        user: { equals: userId },
      },
      depth: 1,
      sort: '-createdAt',
      limit: 100,
    })

    const payments = paymentsResponse.docs.map((payment: any) => ({
      id: payment.id,
      payment_channel: payment.payment_channel,
      payment_reference: payment.payment_reference,
      amount_paid: payment.amount_paid,
      currency: payment.currency,
      status: payment.status,
      payment_date: payment.payment_date,
      processing_status: payment.processing_status,
      invoice: payment.invoice
        ? {
            id: payment.invoice.id,
            invoice_number: payment.invoice.invoice_number,
            amount: payment.invoice.amount,
          }
        : null,
      createdAt: payment.createdAt,
    }))

    // Calculate summary statistics
    const totalInvoices = invoices.length
    const totalAmountInvoiced = invoices.reduce((sum, inv) => sum + inv.amount, 0)
    const totalAmountPaid = payments
      .filter((p) => p.status === 'settled')
      .reduce((sum, payment) => sum + payment.amount_paid, 0)
    const pendingAmount = invoices
      .filter((inv) => inv.status === 'pending')
      .reduce((sum, inv) => sum + inv.amount, 0)
    const activePackages = user.package_status === 'active' ? 1 : 0
    const registrationDate = user.createdAt
    const lastPaymentDate =
      payments.length > 0
        ? payments.find((p) => p.status === 'settled')?.payment_date || null
        : null
    const accountAgeDays = Math.floor(
      (Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24),
    )

    const summary = {
      total_invoices: totalInvoices,
      total_amount_invoiced: totalAmountInvoiced,
      total_amount_paid: totalAmountPaid,
      pending_amount: pendingAmount,
      active_packages: activePackages,
      registration_date: registrationDate,
      last_payment_date: lastPaymentDate,
      account_age_days: accountAgeDays,
    }

    console.log('✅ User data compiled successfully:', {
      user_id: userId,
      total_invoices: totalInvoices,
      total_payments: payments.length,
      active_package: activePackage?.name || 'None',
    })

    const response = {
      success: true,
      data: {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          phone_number: user.phone_number,
          id_number: user.id_number,
          county: user.county,
          package_status: user.package_status,
          package_expiry: user.package_expiry,
          _verified: user._verified,
          registration_context: user.registration_context,
          payment_profile: user.payment_profile,
          statistics: user.statistics,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
        active_package: activePackage,
        invoices,
        payments,
        summary,
      },
    }

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error: any) {
    console.error('❌ User data fetch error:', error)

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Failed to fetch user data',
        message: error.message,
        timestamp: new Date().toISOString(),
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}

/**
 * Get current authenticated user's data
 * GET /api/users/me/user-data
 */
export const getCurrentUserProfileEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('🎯 Current user data endpoint called!')

    // Check if user is authenticated
    if (!req.user) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Authentication required',
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    console.log('✅ Authenticated user ID:', req.user.id)

    // Use the authenticated user's ID
    req.routeParams = { userId: String(req.user.id) }

    // Call the main profile endpoint
    return await getUserProfileEndpoint(req)
  } catch (error: any) {
    console.error('❌ Current user data fetch error:', error)

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Failed to fetch current user data',
        message: error.message,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}
