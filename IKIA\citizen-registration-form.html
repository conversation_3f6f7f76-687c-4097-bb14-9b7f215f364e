<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IKIA Citizen Registration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .registration-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .form-container {
            padding: 40px;
        }
        
        .form-section {
            margin-bottom: 30px;
        }
        
        .form-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            flex: 1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .package-selection {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        
        .package-card {
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .package-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .package-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        }
        
        .package-card input[type="radio"] {
            position: absolute;
            opacity: 0;
        }
        
        .package-name {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .package-price {
            font-size: 1.5em;
            color: #667eea;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .package-features {
            list-style: none;
            margin-bottom: 15px;
        }
        
        .package-features li {
            padding: 3px 0;
            color: #666;
        }
        
        .package-features li:before {
            content: "✓";
            color: #4CAF50;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #c62828;
            display: none;
        }
        
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
            
            .package-selection {
                grid-template-columns: 1fr;
            }
            
            .form-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <div class="header">
            <h1>🇰🇪 IKIA</h1>
            <p>Investment Kenya Investment Authority - Citizen Registration</p>
        </div>
        
        <div class="form-container">
            <div class="error-message" id="errorMessage"></div>
            
            <form id="registrationForm">
                <!-- Personal Information Section -->
                <div class="form-section">
                    <h3>👤 Personal Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Full Name *</label>
                            <input type="text" id="name" name="name" required placeholder="Enter your full name">
                        </div>
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="phone_number">Phone Number *</label>
                            <input type="tel" id="phone_number" name="phone_number" required placeholder="254712345678">
                        </div>
                        <div class="form-group">
                            <label for="id_number">National ID Number *</label>
                            <input type="text" id="id_number" name="id_number" required placeholder="12345678">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="county">County *</label>
                            <select id="county" name="county" required>
                                <option value="">Select your county</option>
                                <option value="13">Nairobi</option>
                                <option value="14">Mombasa</option>
                                <option value="15">Kisumu</option>
                                <!-- More counties would be loaded dynamically -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="password">Password *</label>
                            <input type="password" id="password" name="password" required placeholder="Create a secure password">
                        </div>
                    </div>
                </div>
                
                <!-- Business Information Section -->
                <div class="form-section">
                    <h3>🏢 Business Information (Optional)</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="business_type">Business Type</label>
                            <select id="business_type" name="business_type">
                                <option value="">Select business type</option>
                                <option value="Technology Startup">Technology Startup</option>
                                <option value="Manufacturing">Manufacturing</option>
                                <option value="Agriculture">Agriculture</option>
                                <option value="Services">Services</option>
                                <option value="Retail">Retail</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="registration_purpose">Registration Purpose</label>
                            <input type="text" id="registration_purpose" name="registration_purpose" placeholder="e.g., Business Registration, Investment Facilitation">
                        </div>
                    </div>
                </div>
                
                <!-- Service Package Selection -->
                <div class="form-section">
                    <h3>📦 Select Service Package *</h3>
                    <div class="package-selection">
                        <div class="package-card" onclick="selectPackage('basic')">
                            <input type="radio" name="selected_package" value="basic" id="package-basic" required>
                            <div class="package-name">Basic Registration</div>
                            <div class="package-price">KES 5,000</div>
                            <ul class="package-features">
                                <li>Business name registration</li>
                                <li>Basic compliance check</li>
                                <li>Email support</li>
                            </ul>
                            <small>Perfect for small businesses and startups</small>
                        </div>
                        
                        <div class="package-card" onclick="selectPackage('premium')">
                            <input type="radio" name="selected_package" value="premium" id="package-premium" required>
                            <div class="package-name">Premium Business Package</div>
                            <div class="package-price">KES 15,000</div>
                            <ul class="package-features">
                                <li>Business registration</li>
                                <li>Tax registration</li>
                                <li>Compliance consultation</li>
                                <li>Priority support</li>
                                <li>Legal document templates</li>
                            </ul>
                            <small>Comprehensive business setup</small>
                        </div>
                        
                        <div class="package-card" onclick="selectPackage('enterprise')">
                            <input type="radio" name="selected_package" value="enterprise" id="package-enterprise" required>
                            <div class="package-name">Enterprise Solution</div>
                            <div class="package-price">KES 50,000</div>
                            <ul class="package-features">
                                <li>Complete business setup</li>
                                <li>Regulatory compliance</li>
                                <li>Dedicated account manager</li>
                                <li>Custom legal documentation</li>
                                <li>Ongoing support</li>
                                <li>Investment facilitation</li>
                            </ul>
                            <small>Full-service enterprise package</small>
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="submit-btn" id="submitBtn">
                    Register & Proceed to Payment
                </button>
            </form>
            
            <div class="loading" id="loadingDiv">
                <div class="spinner"></div>
                <p>Creating your account and preparing payment...</p>
            </div>
        </div>
    </div>

    <script>
        // Package selection functionality
        function selectPackage(packageType) {
            // Remove selected class from all cards
            document.querySelectorAll('.package-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selected class to clicked card
            event.currentTarget.classList.add('selected');
            
            // Check the radio button
            document.getElementById(`package-${packageType}`).checked = true;
        }

        // Form submission
        document.getElementById('registrationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const loadingDiv = document.getElementById('loadingDiv');
            const errorMessage = document.getElementById('errorMessage');
            
            // Hide error message
            errorMessage.style.display = 'none';
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.textContent = 'Processing...';
            loadingDiv.style.display = 'block';
            
            try {
                // Collect form data
                const formData = new FormData(this);
                const registrationData = Object.fromEntries(formData.entries());
                
                // Map package selection to actual package IDs (these would come from API in real app)
                const packageMapping = {
                    'basic': '7',    // Basic Registration package ID
                    'premium': '9',  // Premium Business Package ID
                    'enterprise': '8' // Enterprise Solution package ID
                };
                
                registrationData.selected_package = packageMapping[registrationData.selected_package];
                
                console.log('Submitting registration:', registrationData);
                
                // Submit registration
                const response = await fetch('/api/citizens/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(registrationData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // Registration successful, redirect to payment
                    console.log('Registration successful:', result);
                    alert('Registration successful! Redirecting to payment gateway...');
                    
                    // In a real application, you would redirect to the checkout URL
                    // window.location.href = result.checkout_url;
                    
                    // For demo purposes, show success message
                    document.body.innerHTML = `
                        <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                            <h1 style="color: #4CAF50;">✅ Registration Successful!</h1>
                            <p>Account created for: <strong>${result.data.user.name}</strong></p>
                            <p>Invoice: <strong>${result.data.invoice.invoice_number}</strong></p>
                            <p>Amount: <strong>${result.data.invoice.currency} ${result.data.invoice.amount}</strong></p>
                            <p>Package: <strong>${result.data.service_package.name}</strong></p>
                            <br>
                            <p><strong>Next Steps:</strong></p>
                            <p>1. Complete payment via Pesaflow</p>
                            <p>2. Verify your email address</p>
                            <p>3. Access your premium features</p>
                            <br>
                            <a href="${result.checkout_url}" style="background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px;">
                                Proceed to Payment
                            </a>
                        </div>
                    `;
                } else {
                    throw new Error(result.error || 'Registration failed');
                }
                
            } catch (error) {
                console.error('Registration error:', error);
                errorMessage.textContent = error.message;
                errorMessage.style.display = 'block';
                
                // Reset form state
                submitBtn.disabled = false;
                submitBtn.textContent = 'Register & Proceed to Payment';
                loadingDiv.style.display = 'none';
            }
        });

        // Load counties dynamically
        async function loadCounties() {
            try {
                const response = await fetch('/api/counties');
                const result = await response.json();
                const counties = result.docs || result;

                const countySelect = document.getElementById('county');
                // Clear existing options except the first one
                while (countySelect.children.length > 1) {
                    countySelect.removeChild(countySelect.lastChild);
                }

                counties.forEach(county => {
                    const option = document.createElement('option');
                    option.value = county.id;
                    option.textContent = county.name;
                    countySelect.appendChild(option);
                });

                console.log('Counties loaded:', counties.length);
            } catch (error) {
                console.error('Failed to load counties:', error);
                // Fallback to hardcoded values if API fails
                console.log('Using fallback county values');
            }
        }

        // Load counties on page load
        loadCounties();
    </script>
</body>
</html>
