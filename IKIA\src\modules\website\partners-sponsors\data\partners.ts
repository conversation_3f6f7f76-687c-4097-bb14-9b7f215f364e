import type { Partner, Sponsor } from '../types'

export const partners: Partner[] = [
  {
    id: 'nmk',
    name: 'National Museums of Kenya',
    logo: '/assets/partners/nmk-logo.png',
    description:
      "State corporation mandated to collect, preserve, study, and present Kenya's cultural and natural heritage.",
    website: 'https://museums.or.ke',
    type: 'strategic',
    featured: true,
  },
  {
    id: 'kipi',
    name: 'Kenya Industrial Property Institute',
    logo: '/assets/partners/kipi-logo.png',
    description: 'Government agency responsible for industrial property rights in Kenya.',
    website: 'https://kipi.go.ke',
    type: 'strategic',
    featured: true,
  },
  {
    id: 'kenyatta-university',
    name: 'Kenyatta University',
    logo: '/assets/partners/ku-logo.png',
    description:
      'Leading public university in Kenya with strong research focus on indigenous knowledge.',
    website: 'https://ku.ac.ke',
    type: 'implementation',
    featured: true,
  },
  {
    id: 'unesco',
    name: 'UNESCO Kenya',
    logo: '/assets/partners/unesco-logo.png',
    description: 'United Nations Educational, Scientific and Cultural Organization Kenya office.',
    website: 'https://unesco.org',
    type: 'strategic',
    featured: true,
  },
  {
    id: 'wipo',
    name: 'World Intellectual Property Organization',
    logo: '/assets/partners/wipo-logo.png',
    description:
      'Global forum for intellectual property services, policy, information and cooperation.',
    website: 'https://wipo.int',
    type: 'strategic',
    featured: true,
  },
  {
    id: 'african-union',
    name: 'African Union',
    logo: '/assets/partners/au-logo.png',
    description:
      'Continental body consisting of 55 member states promoting African unity and development.',
    website: 'https://au.int',
    type: 'strategic',
  },
  {
    id: 'kbc',
    name: 'Kenya Broadcasting Corporation',
    logo: '/assets/partners/kbc-logo.png',
    description: "Kenya's national broadcaster providing comprehensive media coverage.",
    website: 'https://kbc.co.ke',
    type: 'media',
  },
  {
    id: 'nation-media',
    name: 'Nation Media Group',
    logo: '/assets/partners/nation-logo.png',
    description: 'Leading media house in East and Central Africa.',
    website: 'https://nationmedia.com',
    type: 'media',
  },
  {
    id: 'standard-group',
    name: 'Standard Group',
    logo: '/assets/partners/standard-logo.png',
    description: 'Multimedia company with strong presence in Kenya and East Africa.',
    website: 'https://standardmedia.co.ke',
    type: 'media',
  },
  {
    id: 'community-networks',
    name: 'Indigenous Community Networks',
    logo: '/assets/partners/community-logo.png',
    description: 'Network of indigenous communities across Kenya preserving traditional knowledge.',
    type: 'community',
  },
]

export const sponsors: Sponsor[] = [
  {
    id: 'safaricom',
    name: 'Safaricom PLC',
    logo: '/assets/sponsors/safaricom-logo.png',
    description: 'Leading telecommunications company in Kenya driving digital transformation.',
    website: 'https://safaricom.co.ke',
    packageId: 'title-package',
    category: 'financial',
    tier: 'title',
    hasDetailsPage: true,
    additionalLogos: [
      {
        name: 'M-Pesa',
        logo: '/assets/sponsors/mpesa-logo.png',
      },
    ],
  },
  {
    id: 'equity-bank',
    name: 'Equity Bank',
    logo: '/assets/sponsors/equity-logo.png',
    description: 'Pan-African financial services group committed to socio-economic development.',
    website: 'https://equitybank.co.ke',
    packageId: 'gold-package',
    category: 'financial',
    tier: 'gold',
    hasDetailsPage: true,
  },
  {
    id: 'kenyatta-university-sponsor',
    name: 'Kenyatta University',
    logo: '/assets/sponsors/ku-logo.png',
    description: 'Premier university supporting indigenous knowledge research and development.',
    website: 'https://ku.ac.ke',
    packageId: 'silver-package',
    category: 'financial',
    tier: 'silver',
  },
  {
    id: 'thika-greens',
    name: 'Thika Greens Golf Resort',
    logo: '/assets/sponsors/thika-greens-logo.png',
    description: 'Premier conference venue providing world-class facilities and hospitality.',
    website: 'https://thikagreens.com',
    packageId: 'venue-partner',
    category: 'service',
  },
  {
    id: 'heritage-catering',
    name: 'Heritage Catering Services',
    logo: '/assets/sponsors/heritage-catering-logo.png',
    description: 'Specialized catering service focusing on indigenous and traditional cuisine.',
    packageId: 'catering-partner',
    category: 'service',
  },
  {
    id: 'citizen-tv',
    name: 'Citizen TV',
    logo: '/assets/sponsors/citizen-logo.png',
    description: 'Leading television station providing comprehensive conference coverage.',
    website: 'https://citizentv.co.ke',
    packageId: 'media-partner',
    category: 'media',
  },
  {
    id: 'ktn-news',
    name: 'KTN News',
    logo: '/assets/sponsors/ktn-logo.png',
    description: 'Premier news channel offering extensive conference reporting.',
    website: 'https://ktnnews.co.ke',
    packageId: 'media-partner',
    category: 'media',
  },
]
