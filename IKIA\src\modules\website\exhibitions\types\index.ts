export interface IKIAAsset {
  id: number
  title: string
  description: string
  slug: string
  categories: {
    id: string
    name: string
  }[]
  tags: {
    id: string
    name: string
  }[]
  location: string
  documentedBy: string
  yearDocumented: number
  investmentPotential: string
  featured: boolean
  image: IKIAAssetImage
  updatedAt: string
  createdAt: string
}

export interface IKIAAssetImage {
  id: number
  alt: string | null
  caption: string | null
  updatedAt: string
  createdAt: string
  url: string
  thumbnailURL: string | null
  filename: string
  mimeType: string
  filesize: number
  width: number
  height: number
  focalX: number
  focalY: number
  sizes: {
    thumbnail?: IKIAImageSize | null
    square?: IKIAImageSize | null
    small?: IKIAImageSize | null
    medium?: IKIAImageSize | null
    large?: IKIAImageSize | null
    xlarge?: IKIAImageSize | null
    og?: IKIAImageSize | null
  }
}

export interface IKIAImageSize {
  url: string | null
  width: number | null
  height: number | null
  mimeType: string | null
  filesize: number | null
  filename: string | null
}
