import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { serializeParams } from '@/lib/serialize'
import type { User } from '@/modules/website/invest/types'

export interface UsersResponse {
  docs: User[]
  totalDocs: number
  limit: number
  totalPages: number
  page: number
  pagingCounter: number
  hasPrevPage: boolean
  hasNextPage: boolean
  prevPage?: number | null
  nextPage?: number | null
}

export interface UsersQueryParams {
  limit?: number
  page?: number
  sort?: string
  where?: {
    userType?: {
      equals?: string
    }
    featured?: {
      equals?: boolean
    }
    verified?: {
      equals?: boolean
    }
    county?: {
      equals?: number | string
    }
    name?: {
      contains?: string
    }
    organization?: {
      contains?: string
    }
    focus?: {
      contains?: string
    }
    rating?: {
      greater_than_equal?: number
    }
  }
}

export const usersApi = createApi({
  reducerPath: 'usersApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  tagTypes: ['User'],
  endpoints: (builder) => ({
    // Get users with flexible filtering
    getUsers: builder.query<UsersResponse, UsersQueryParams | void>({
      query: (params) => {
        const queryParams = params || {}
        const queryString = serializeParams(queryParams)
        return `users?${queryString.toString()}`
      },
      providesTags: ['User'],
    }),

    // Get featured users by type
    getFeaturedUsers: builder.query<UsersResponse, { userType?: string; limit?: number }>({
      query: ({ userType, limit = 6 }) => {
        const params: UsersQueryParams = {
          where: { featured: { equals: true } },
          limit,
          sort: '-createdAt',
        }
        
        if (userType) {
          params.where!.userType = { equals: userType }
        }
        
        const queryString = serializeParams(params)
        return `users?${queryString.toString()}`
      },
      providesTags: ['User'],
    }),

    // Get users by type (investors, exhibitors, etc.)
    getUsersByType: builder.query<UsersResponse, { userType: string; limit?: number; page?: number }>({
      query: ({ userType, limit = 50, page = 1 }) => {
        const params: UsersQueryParams = {
          where: { userType: { equals: userType } },
          limit,
          page,
          sort: '-createdAt',
        }
        
        const queryString = serializeParams(params)
        return `users?${queryString.toString()}`
      },
      providesTags: ['User'],
    }),

    // Get verified users
    getVerifiedUsers: builder.query<UsersResponse, { userType?: string; limit?: number }>({
      query: ({ userType, limit = 50 }) => {
        const params: UsersQueryParams = {
          where: { verified: { equals: true } },
          limit,
          sort: '-rating',
        }
        
        if (userType) {
          params.where!.userType = { equals: userType }
        }
        
        const queryString = serializeParams(params)
        return `users?${queryString.toString()}`
      },
      providesTags: ['User'],
    }),

    // Get top-rated users
    getTopRatedUsers: builder.query<UsersResponse, { userType?: string; minRating?: number; limit?: number }>({
      query: ({ userType, minRating = 4.8, limit = 50 }) => {
        const params: UsersQueryParams = {
          where: { rating: { greater_than_equal: minRating } },
          limit,
          sort: '-rating',
        }
        
        if (userType) {
          params.where!.userType = { equals: userType }
        }
        
        const queryString = serializeParams(params)
        return `users?${queryString.toString()}`
      },
      providesTags: ['User'],
    }),

    // Search users
    searchUsers: builder.query<UsersResponse, { 
      searchTerm: string
      userType?: string
      limit?: number 
    }>({
      query: ({ searchTerm, userType, limit = 50 }) => {
        const params: UsersQueryParams = {
          where: {
            name: { contains: searchTerm },
          },
          limit,
          sort: '-rating',
        }
        
        if (userType) {
          params.where!.userType = { equals: userType }
        }
        
        const queryString = serializeParams(params)
        return `users?${queryString.toString()}`
      },
      providesTags: ['User'],
    }),

    // Get single user by ID
    getUser: builder.query<User, string | number>({
      query: (id) => `users/${id}`,
      providesTags: ['User'],
    }),
  }),
})

export const {
  useGetUsersQuery,
  useGetFeaturedUsersQuery,
  useGetUsersByTypeQuery,
  useGetVerifiedUsersQuery,
  useGetTopRatedUsersQuery,
  useSearchUsersQuery,
  useGetUserQuery,
} = usersApi
