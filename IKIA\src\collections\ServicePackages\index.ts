import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'

export const Packages: CollectionConfig = {
  slug: 'packages',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone, // Public access for package selection
    update: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'price', 'currency', 'isActive', 'displayOrder'],
    group: 'Packages',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Package Name',
      admin: {
        description: 'Name of the service package (e.g., Basic Package, Premium Package)',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
      label: 'Package Description',
      admin: {
        description: 'Brief description of what this package includes',
      },
    },
    {
      name: 'price',
      type: 'number',
      required: true,
      min: 0,
      label: 'Package Price',
      admin: {
        description: 'Price of the package in the specified currency',
      },
    },
    {
      name: 'currency',
      type: 'select',
      options: [
        { label: 'Kenyan Shilling (KES)', value: 'KES' },
        { label: 'US Dollar (USD)', value: 'USD' },
      ],
      defaultValue: 'KES',
      required: true,
    },
    {
      name: 'features',
      type: 'array',
      label: 'Package Features',
      fields: [
        {
          name: 'feature',
          type: 'text',
          required: true,
          label: 'Feature Description',
        },
        {
          name: 'included',
          type: 'checkbox',
          defaultValue: true,
          label: 'Included in Package',
        },
      ],
      admin: {
        description: 'List of features included in this package',
      },
    },
    {
      name: 'packageType',
      type: 'select',
      options: [
        { label: 'Basic', value: 'basic' },
        { label: 'Premium', value: 'premium' },
        { label: 'Enterprise', value: 'enterprise' },
        { label: 'Custom', value: 'custom' },
      ],
      defaultValue: 'basic',
      required: true,
      label: 'Package Type',
    },
    {
      name: 'duration',
      type: 'group',
      label: 'Package Duration',
      fields: [
        {
          name: 'value',
          type: 'number',
          required: true,
          min: 1,
          defaultValue: 12,
          label: 'Duration Value',
        },
        {
          name: 'unit',
          type: 'select',
          options: [
            { label: 'Days', value: 'days' },
            { label: 'Months', value: 'months' },
            { label: 'Years', value: 'years' },
          ],
          defaultValue: 'months',
          required: true,
          label: 'Duration Unit',
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      label: 'Active Package',
      admin: {
        description: 'Whether this package is available for selection',
      },
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      defaultValue: false,
      label: 'Featured Package',
      admin: {
        description: 'Highlight this package as recommended or popular',
      },
    },
    {
      name: 'displayOrder',
      type: 'number',
      label: 'Display Order',
      admin: {
        description: 'Order in which packages are displayed (lower numbers first)',
      },
    },
    {
      name: 'maxUsers',
      type: 'number',
      label: 'Maximum Users',
      admin: {
        description: 'Maximum number of users allowed for this package (optional)',
      },
    },
    {
      name: 'setupFee',
      type: 'number',
      min: 0,
      defaultValue: 0,
      label: 'Setup Fee',
      admin: {
        description: 'One-time setup fee (if applicable)',
      },
    },
    {
      name: 'discountPercentage',
      type: 'number',
      min: 0,
      max: 100,
      label: 'Discount Percentage',
      admin: {
        description: 'Discount percentage for promotional pricing',
      },
    },
    {
      name: 'validUntil',
      type: 'date',
      label: 'Valid Until',
      admin: {
        description: 'Expiry date for this package (optional)',
      },
    },
    {
      name: 'metadata',
      type: 'json',
      label: 'Additional Metadata',
      admin: {
        description: 'Additional package configuration data',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Calculate discounted price if discount is applied
        if (data.discountPercentage && data.price) {
          data.discountedPrice = data.price * (1 - data.discountPercentage / 100)
        }
      },
    ],
    afterChange: [
      ({ doc, operation }) => {
        if (operation === 'create') {
          console.log(`New service package created: ${doc.name} - ${doc.currency} ${doc.price}`)
        }
      },
    ],
  },
  timestamps: true,
}

export default Packages
