'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState } from 'react'
import {
  ChevronDown,
  Users,
  Building,
  FileText,
  Newspaper,
  CalendarRange,
  MicVocal,
  Info,
} from 'lucide-react'
import InvestDropdown from '../InvestDropdown'

interface NavItem {
  title: string
  href: string
}

interface IkiaNavMenuProps {
  className?: string
  isMobile?: boolean
  isScrolled?: boolean
  onItemClick?: () => void
}

const navigationItems: NavItem[] = [
  { title: 'HOME', href: '/' },
  { title: 'EXHIBITIONS', href: '/exhibitions' },
  { title: 'CONTACT', href: '/contact' },
]

const aboutItems = [
  { title: 'About Conference', href: '/about', icon: Info },
  { title: 'Programs & Agenda', href: '/programs', icon: CalendarRange },
  { title: 'Speakers', href: '/speakers', icon: MicVocal },
]

const businessItems = [
  { title: 'Partners', href: '/partners', icon: Users },
  { title: 'Sponsors', href: '/sponsors', icon: Building },
]

const resourceItems = [
  { title: 'Resources', href: '/resources', icon: FileText },
  { title: 'News & Media', href: '/news-media', icon: Newspaper },
]

// Business Dropdown Component
const BusinessDropdown: React.FC<{ isScrolled: boolean }> = ({ isScrolled }) => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div
      className="relative"
      onMouseEnter={() => setIsOpen(true)}
      onMouseLeave={() => setIsOpen(false)}
    >
      <div
        className={`relative font-myriad font-semibold text-xs transition-colors duration-300 group cursor-pointer ${
          isScrolled ? 'text-foreground hover:text-primary' : 'text-white hover:text-accent'
        }`}
      >
        <span className="relative z-10 flex items-center gap-1">
          BUSINESS
          <ChevronDown
            className={`w-3 h-3 transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}
          />
        </span>
        <div
          className={`absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full`}
        />
      </div>

      <div
        className={`absolute top-full left-0 mt-2 w-64 bg-background shadow-2xl border border-border overflow-hidden transition-all duration-300 z-50 ${
          isOpen
            ? 'opacity-100 visible transform translate-y-0'
            : 'opacity-0 invisible transform -translate-y-2'
        }`}
      >
        <div className="py-2">
          {businessItems.map((item, index) => (
            <Link
              key={index}
              href={item.href}
              className="block px-6 py-4 hover:bg-muted transition-colors duration-200 group"
            >
              <div className="flex items-start gap-4">
                <div className="p-2 bg-muted group-hover:bg-muted/80 transition-colors text-primary">
                  <item.icon className="w-5 h-5" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-foreground group-hover:text-primary transition-colors font-myriad">
                    {item.title}
                  </h4>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}

// Resources Dropdown Component
const ResourcesDropdown: React.FC<{ isScrolled: boolean }> = ({ isScrolled }) => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div
      className="relative"
      onMouseEnter={() => setIsOpen(true)}
      onMouseLeave={() => setIsOpen(false)}
    >
      <div
        className={`relative font-myriad font-semibold text-xs transition-colors duration-300 group cursor-pointer ${
          isScrolled ? 'text-foreground hover:text-primary' : 'text-white hover:text-accent'
        }`}
      >
        <span className="relative z-10 flex items-center gap-1">
          RESOURCES
          <ChevronDown
            className={`w-3 h-3 transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}
          />
        </span>
        <div
          className={`absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full`}
        />
      </div>

      <div
        className={`absolute top-full left-0 mt-2 w-64 bg-background shadow-2xl border border-border overflow-hidden transition-all duration-300 z-50 ${
          isOpen
            ? 'opacity-100 visible transform translate-y-0'
            : 'opacity-0 invisible transform -translate-y-2'
        }`}
      >
        <div className="py-2">
          {resourceItems.map((item, index) => (
            <Link
              key={index}
              href={item.href}
              className="block px-6 py-4 hover:bg-muted transition-colors duration-200 group"
            >
              <div className="flex items-start gap-4">
                <div className="p-2 bg-muted group-hover:bg-muted/80 transition-colors text-primary">
                  <item.icon className="w-5 h-5" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-foreground group-hover:text-primary transition-colors font-myriad">
                    {item.title}
                  </h4>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}

const AboutDropdown: React.FC<{ isScrolled: boolean }> = ({ isScrolled }) => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div
      className="relative"
      onMouseEnter={() => setIsOpen(true)}
      onMouseLeave={() => setIsOpen(false)}
    >
      <div
        className={`relative font-myriad font-semibold text-xs transition-colors duration-300 group cursor-pointer ${
          isScrolled ? 'text-foreground hover:text-primary' : 'text-white hover:text-accent'
        }`}
      >
        <span className="relative z-10 flex items-center gap-1">
          ABOUT
          <ChevronDown
            className={`w-3 h-3 transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}
          />
        </span>
        <div
          className={`absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full`}
        />
      </div>

      <div
        className={`absolute top-full left-0 mt-2 w-64 bg-background shadow-2xl border border-border overflow-hidden transition-all duration-300 z-50 ${
          isOpen
            ? 'opacity-100 visible transform translate-y-0'
            : 'opacity-0 invisible transform -translate-y-2'
        }`}
      >
        <div className="py-2">
          {aboutItems.map((item, index) => (
            <Link
              key={index}
              href={item.href}
              className="block px-6 py-4 hover:bg-muted transition-colors duration-200 group"
            >
              <div className="flex items-start gap-4">
                <div className="p-2 bg-muted group-hover:bg-muted/80 transition-colors text-primary">
                  <item.icon className="w-5 h-5" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-foreground group-hover:text-primary transition-colors font-myriad">
                    {item.title}
                  </h4>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}

export const IkiaNavMenu: React.FC<IkiaNavMenuProps> = ({
  className = '',
  isMobile = false,
  isScrolled = false,
  onItemClick,
}) => {
  const pathname = usePathname()
  const baseClasses = isMobile ? 'flex flex-col space-y-3' : 'hidden lg:flex items-center space-x-4'

  return (
    <nav className={`${baseClasses} ${className}`}>
      {/* Render main navigation items */}
      {/* Render main navigation items */}
      {navigationItems.map((item) => {
        const isActive = pathname === item.href

        return (
          <div key={item.title} className={isMobile ? 'w-full' : 'flex items-center space-x-4'}>
            <Link
              href={item.href}
              className={`
          font-myriad font-semibold transition-all duration-300 relative group
          ${
            isMobile
              ? isScrolled
                ? 'block w-full px-4 py-3 text-foreground hover:text-primary hover:bg-muted rounded-lg text-sm'
                : 'block w-full px-4 py-3 text-white hover:text-accent hover:bg-white/10 rounded-lg text-sm'
              : isScrolled
                ? 'text-xs text-foreground hover:text-primary'
                : 'text-xs text-white hover:text-accent'
          }
          ${isActive && isMobile ? (isScrolled ? 'bg-muted text-primary' : 'bg-white/10 text-accent') : ''}
          ${isActive && !isMobile ? 'text-primary' : ''}
        `}
              onClick={onItemClick}
            >
              {item.title}
              {!isMobile && (
                <div
                  className={`
              absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5
              bg-primary transition-all duration-300 group-hover:w-full
              ${isActive ? 'w-full' : ''}
            `}
                />
              )}
            </Link>

            {/* Insert AboutDropdown right after HOME */}
            {!isMobile && item.title === 'HOME' && <AboutDropdown isScrolled={isScrolled} />}
          </div>
        )
      })}

      {/* Business Dropdown */}
      {!isMobile && <BusinessDropdown isScrolled={isScrolled} />}

      {/* Invest Dropdown */}
      {!isMobile && (
        <div className="relative">
          <InvestDropdown isScrolled={isScrolled} />
        </div>
      )}

      {/* Resources Dropdown */}
      {!isMobile && <ResourcesDropdown isScrolled={isScrolled} />}

      {/* Mobile Business section */}
      {isMobile && (
        <div className="space-y-2">
          <div className="text-xs font-myriad font-semibold text-accent uppercase tracking-wider">
            About
          </div>
          {aboutItems.map((item) => (
            <Link
              key={item.title}
              href={item.href}
              className="block text-xs font-myriad font-semibold text-white hover:text-accent py-1 pl-4"
              onClick={onItemClick}
            >
              {item.title}
            </Link>
          ))}
        </div>
      )}

      {/* Mobile Business section */}
      {isMobile && (
        <div className="space-y-2">
          <div className="text-xs font-myriad font-semibold text-accent uppercase tracking-wider">
            Business
          </div>
          {businessItems.map((item) => (
            <Link
              key={item.title}
              href={item.href}
              className="block text-xs font-myriad font-semibold text-white hover:text-accent py-1 pl-4"
              onClick={onItemClick}
            >
              {item.title}
            </Link>
          ))}
        </div>
      )}

      {/* Mobile Invest section */}
      {isMobile && (
        <div className="space-y-2">
          <div className="text-xs font-myriad font-semibold text-accent uppercase tracking-wider">
            Invest
          </div>
          <Link
            href="/invest"
            className="block text-xs font-myriad font-semibold text-white hover:text-accent py-1 pl-4"
            onClick={onItemClick}
          >
            Investor Hub
          </Link>
          <Link
            href="/invest/success-stories"
            className="block text-xs font-myriad font-semibold text-white hover:text-accent py-1 pl-4"
            onClick={onItemClick}
          >
            Success Stories
          </Link>
        </div>
      )}

      {/* Mobile Resources section */}
      {isMobile && (
        <div className="space-y-2">
          <div className="text-xs font-myriad font-medium text-accent uppercase tracking-wider">
            Resources
          </div>
          {resourceItems.map((item) => (
            <Link
              key={item.title}
              href={item.href}
              className="block text-xs font-myriad font-semibold text-white hover:text-accent py-1 pl-4"
              onClick={onItemClick}
            >
              {item.title}
            </Link>
          ))}
        </div>
      )}
    </nav>
  )
}
