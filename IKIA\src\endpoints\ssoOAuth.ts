import type { PayloadRequest } from 'payload'

// OAuth Authorization endpoint - redirects to eCitizen
export const ssoAuthorizeEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('SSO Authorization endpoint called')
    console.log('Request query:', req.query)

    const {
      redirect_uri,
      client_id,
      response_type,
      scope,
      state,
      code_challenge,
      code_challenge_method,
    } = req.query as any

    // Validate required parameters
    const missingFields = [
      !redirect_uri && 'redirect_uri',
      !client_id && 'client_id',
      !response_type && 'response_type',
      !scope && 'scope',
      !state && 'state',
    ].filter(Boolean)

    if (missingFields.length > 0) {
      return new Response(
        JSON.stringify({
          error: 'invalid_request',
          error_description: `Missing required parameters: ${missingFields.join(', ')}`,
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Validate response_type
    if (response_type !== 'code') {
      return new Response(
        JSON.stringify({
          error: 'unsupported_response_type',
          error_description: 'Only authorization code flow is supported',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get environment variables
    const { ECITIZEN_CLIENT_ID: expectedClientId, ECITIZEN_AUTHORIZATION_URL: authUrl } =
      process.env

    // Validate required environment variables
    if (!authUrl) {
      return new Response(
        JSON.stringify({
          error: 'server_error',
          error_description: 'Missing ECITIZEN_AUTHORIZATION_URL configuration',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Verify client_id if configured
    if (expectedClientId && client_id !== expectedClientId) {
      return new Response(
        JSON.stringify({
          error: 'invalid_client',
          error_description: 'Invalid client_id',
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Build authorization URL
    const authParams = new URLSearchParams({
      client_id,
      redirect_uri,
      response_type,
      scope,
      state,
    })

    // Add PKCE parameters if provided
    if (code_challenge && code_challenge_method) {
      authParams.append('code_challenge', code_challenge)
      authParams.append('code_challenge_method', code_challenge_method)
    }

    const authorizationUrl = `${authUrl}?${authParams.toString()}`

    console.log('Redirecting to eCitizen authorization:', authorizationUrl)

    // Return redirect response
    return new Response(null, {
      status: 302,
      headers: {
        Location: authorizationUrl,
      },
    })
  } catch (error) {
    console.error('SSO Authorization error:', error)
    return new Response(
      JSON.stringify({
        error: 'server_error',
        error_description: 'Internal server error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}

// OAuth Access Token endpoint
export const ssoAccessTokenEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('SSO Access Token endpoint called')
    console.log('Request body:', req.body)

    const { client_id, client_secret, grant_type, code, redirect_uri, code_verifier } =
      req.body as any

    // Validate required parameters
    const missingFields = [
      !client_id && 'client_id',
      !grant_type && 'grant_type',
      !code && 'code',
      !redirect_uri && 'redirect_uri',
    ].filter(Boolean)

    if (missingFields.length > 0) {
      return new Response(
        JSON.stringify({
          error: 'invalid_request',
          error_description: `Missing required parameters: ${missingFields.join(', ')}`,
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Validate grant_type
    if (grant_type !== 'authorization_code') {
      return new Response(
        JSON.stringify({
          error: 'unsupported_grant_type',
          error_description: 'Only authorization_code grant type is supported',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get environment variables
    const {
      ECITIZEN_CLIENT_ID: expectedClientId,
      ECITIZEN_CLIENT_SECRET: expectedClientSecret,
      ECITIZEN_TOKEN_URL: tokenUrl,
    } = process.env

    // Validate required environment variables
    if (!tokenUrl) {
      return new Response(
        JSON.stringify({
          error: 'server_error',
          error_description: 'Missing ECITIZEN_TOKEN_URL configuration',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Verify client credentials
    if (expectedClientId && client_id !== expectedClientId) {
      return new Response(
        JSON.stringify({
          error: 'invalid_client',
          error_description: 'Invalid client_id',
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    if (expectedClientSecret && client_secret !== expectedClientSecret) {
      return new Response(
        JSON.stringify({
          error: 'invalid_client',
          error_description: 'Invalid client_secret',
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Exchange authorization code for access token with eCitizen
    try {
      const tokenParams = new URLSearchParams({
        client_id,
        client_secret: client_secret || expectedClientSecret || '',
        grant_type,
        code,
        redirect_uri,
      })

      if (code_verifier) {
        tokenParams.append('code_verifier', code_verifier)
      }

      console.log('Exchanging code for token with eCitizen')

      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: tokenParams.toString(),
      })

      const tokenData = await response.json()

      if (!response.ok) {
        console.error('eCitizen token exchange failed:', tokenData)
        return new Response(JSON.stringify(tokenData), {
          status: response.status,
          headers: { 'Content-Type': 'application/json' },
        })
      }

      console.log('Token exchange successful')
      return new Response(JSON.stringify(tokenData), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      })
    } catch (error) {
      console.error('Token exchange error:', error)
      return new Response(
        JSON.stringify({
          error: 'server_error',
          error_description: 'Failed to exchange authorization code',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }
  } catch (error) {
    console.error('SSO Access Token error:', error)
    return new Response(
      JSON.stringify({
        error: 'server_error',
        error_description: 'Internal server error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}

// Token Introspection endpoint
export const ssoTokenIntrospectionEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('SSO Token Introspection endpoint called')
    console.log('Request body:', req.body)

    const { token } = req.body as any

    if (!token) {
      return new Response(
        JSON.stringify({
          error: 'invalid_request',
          error_description: 'Missing token parameter',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get environment variables
    const { ECITIZEN_INTROSPECTION_URL: introspectionUrl } = process.env

    // Validate required environment variables
    if (!introspectionUrl) {
      return new Response(
        JSON.stringify({
          error: 'server_error',
          error_description: 'Missing ECITIZEN_INTROSPECTION_URL configuration',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    try {
      console.log('Introspecting token with eCitizen')

      const response = await fetch(introspectionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({ token }).toString(),
      })

      const introspectionData = await response.json()

      return new Response(JSON.stringify(introspectionData), {
        status: response.status,
        headers: { 'Content-Type': 'application/json' },
      })
    } catch (error) {
      console.error('Token introspection error:', error)
      return new Response(
        JSON.stringify({
          error: 'server_error',
          error_description: 'Failed to introspect token',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }
  } catch (error) {
    console.error('SSO Token Introspection error:', error)
    return new Response(
      JSON.stringify({
        error: 'server_error',
        error_description: 'Internal server error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}

// User Info endpoint
export const ssoUserInfoEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('SSO User Info endpoint called')
    console.log('Request query:', req.query)

    const { access_token } = req.query as any

    if (!access_token) {
      return new Response(
        JSON.stringify({
          error: 'invalid_request',
          error_description: 'Missing access_token parameter',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get environment variables
    const { ECITIZEN_USERINFO_URL: userinfoUrl } = process.env

    // Validate required environment variables
    if (!userinfoUrl) {
      return new Response(
        JSON.stringify({
          error: 'server_error',
          error_description: 'Missing ECITIZEN_USERINFO_URL configuration',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    try {
      console.log('Fetching user info from eCitizen')

      const response = await fetch(`${userinfoUrl}?access_token=${access_token}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const userInfoData = await response.json()

      return new Response(JSON.stringify(userInfoData), {
        status: response.status,
        headers: { 'Content-Type': 'application/json' },
      })
    } catch (error) {
      console.error('User info fetch error:', error)
      return new Response(
        JSON.stringify({
          error: 'server_error',
          error_description: 'Failed to fetch user information',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }
  } catch (error) {
    console.error('SSO User Info error:', error)
    return new Response(
      JSON.stringify({
        error: 'server_error',
        error_description: 'Internal server error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}
