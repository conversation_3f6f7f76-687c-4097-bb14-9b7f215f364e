"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Users, 
  Crown, 
  User, 
  Plus, 
  Minus, 
  Info,
  CheckCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { SponsorshipTier } from '@/modules/website/registration/lib/registration-data'

interface TicketAllocationProps {
  sponsorshipTier: SponsorshipTier
  onAllocationChange: (allocation: TicketAllocation) => void
  className?: string
}

export interface TicketAllocation {
  vipTickets: TicketDetails[]
  delegateTickets: TicketDetails[]
  totalAllocated: number
  isComplete: boolean
}

export interface TicketDetails {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  position?: string
}

export function TicketAllocation({ 
  sponsorshipTier, 
  onAllocationChange, 
  className = "" 
}: TicketAllocationProps) {
  const [vipTickets, setVipTickets] = useState<TicketDetails[]>([])
  const [delegateTickets, setDelegateTickets] = useState<TicketDetails[]>([])

  const maxVipTickets = sponsorshipTier.vipTickets
  const maxDelegateTickets = sponsorshipTier.delegateTickets - sponsorshipTier.vipTickets
  const totalTickets = sponsorshipTier.delegateTickets

  useEffect(() => {
    const allocation: TicketAllocation = {
      vipTickets,
      delegateTickets,
      totalAllocated: vipTickets.length + delegateTickets.length,
      isComplete: (vipTickets.length + delegateTickets.length) === totalTickets &&
                  vipTickets.every(ticket => ticket.firstName && ticket.lastName && ticket.email) &&
                  delegateTickets.every(ticket => ticket.firstName && ticket.lastName && ticket.email)
    }
    onAllocationChange(allocation)
  }, [vipTickets, delegateTickets, totalTickets, onAllocationChange])

  const createEmptyTicket = (): TicketDetails => ({
    id: `ticket-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    position: ''
  })

  const addVipTicket = () => {
    if (vipTickets.length < maxVipTickets) {
      setVipTickets([...vipTickets, createEmptyTicket()])
    }
  }

  const addDelegateTicket = () => {
    if (delegateTickets.length < maxDelegateTickets) {
      setDelegateTickets([...delegateTickets, createEmptyTicket()])
    }
  }

  const removeVipTicket = (index: number) => {
    setVipTickets(vipTickets.filter((_, i) => i !== index))
  }

  const removeDelegateTicket = (index: number) => {
    setDelegateTickets(delegateTickets.filter((_, i) => i !== index))
  }

  const updateVipTicket = (index: number, field: keyof TicketDetails, value: string) => {
    const updated = [...vipTickets]
    updated[index] = { ...updated[index], [field]: value }
    setVipTickets(updated)
  }

  const updateDelegateTicket = (index: number, field: keyof TicketDetails, value: string) => {
    const updated = [...delegateTickets]
    updated[index] = { ...updated[index], [field]: value }
    setDelegateTickets(updated)
  }

  const renderTicketForm = (
    ticket: TicketDetails, 
    index: number, 
    type: 'vip' | 'delegate',
    onUpdate: (index: number, field: keyof TicketDetails, value: string) => void,
    onRemove: (index: number) => void
  ) => (
    <div key={ticket.id} className="p-4 border rounded-lg space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium flex items-center space-x-2">
          {type === 'vip' ? <Crown className="w-4 h-4 text-yellow-600" /> : <User className="w-4 h-4" />}
          <span>{type === 'vip' ? 'VIP' : 'Delegate'} Ticket #{index + 1}</span>
        </h4>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onRemove(index)}
          className="text-red-600 hover:text-red-700"
        >
          <Minus className="w-4 h-4" />
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor={`${type}-${index}-firstName`}>First Name *</Label>
          <Input
            id={`${type}-${index}-firstName`}
            value={ticket.firstName}
            onChange={(e) => onUpdate(index, 'firstName', e.target.value)}
            placeholder="Enter first name"
          />
        </div>
        <div>
          <Label htmlFor={`${type}-${index}-lastName`}>Last Name *</Label>
          <Input
            id={`${type}-${index}-lastName`}
            value={ticket.lastName}
            onChange={(e) => onUpdate(index, 'lastName', e.target.value)}
            placeholder="Enter last name"
          />
        </div>
        <div>
          <Label htmlFor={`${type}-${index}-email`}>Email *</Label>
          <Input
            id={`${type}-${index}-email`}
            type="email"
            value={ticket.email}
            onChange={(e) => onUpdate(index, 'email', e.target.value)}
            placeholder="Enter email address"
          />
        </div>
        <div>
          <Label htmlFor={`${type}-${index}-phone`}>Phone</Label>
          <Input
            id={`${type}-${index}-phone`}
            type="tel"
            value={ticket.phone}
            onChange={(e) => onUpdate(index, 'phone', e.target.value)}
            placeholder="Enter phone number"
          />
        </div>
        <div className="md:col-span-2">
          <Label htmlFor={`${type}-${index}-position`}>Position/Title</Label>
          <Input
            id={`${type}-${index}-position`}
            value={ticket.position || ''}
            onChange={(e) => onUpdate(index, 'position', e.target.value)}
            placeholder="Enter position or job title"
          />
        </div>
      </div>
    </div>
  )

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Users className="w-5 h-5" />
          <span>Ticket Allocation</span>
        </CardTitle>
        <div className="flex items-center space-x-4">
          <Badge variant="outline" className="flex items-center space-x-1">
            <Crown className="w-3 h-3" />
            <span>VIP: {vipTickets.length}/{maxVipTickets}</span>
          </Badge>
          <Badge variant="outline" className="flex items-center space-x-1">
            <User className="w-3 h-3" />
            <span>Delegate: {delegateTickets.length}/{maxDelegateTickets}</span>
          </Badge>
          <Badge variant="outline">
            Total: {vipTickets.length + delegateTickets.length}/{totalTickets}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Allocation Info */}
        <Alert>
          <Info className="w-4 h-4" />
          <AlertDescription>
            Your {sponsorshipTier.name} sponsorship includes {maxVipTickets} VIP tickets and {maxDelegateTickets} delegate tickets. 
            Please provide details for each attendee.
          </AlertDescription>
        </Alert>

        {/* VIP Tickets Section */}
        {maxVipTickets > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold flex items-center space-x-2">
                <Crown className="w-5 h-5 text-yellow-600" />
                <span>VIP Tickets ({vipTickets.length}/{maxVipTickets})</span>
              </h3>
              {vipTickets.length < maxVipTickets && (
                <Button onClick={addVipTicket} variant="outline" size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Add VIP Ticket
                </Button>
              )}
            </div>
            
            <div className="space-y-4">
              {vipTickets.map((ticket, index) => 
                renderTicketForm(ticket, index, 'vip', updateVipTicket, removeVipTicket)
              )}
            </div>
          </div>
        )}

        {/* Delegate Tickets Section */}
        {maxDelegateTickets > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold flex items-center space-x-2">
                <User className="w-5 h-5" />
                <span>Delegate Tickets ({delegateTickets.length}/{maxDelegateTickets})</span>
              </h3>
              {delegateTickets.length < maxDelegateTickets && (
                <Button onClick={addDelegateTicket} variant="outline" size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Delegate Ticket
                </Button>
              )}
            </div>
            
            <div className="space-y-4">
              {delegateTickets.map((ticket, index) => 
                renderTicketForm(ticket, index, 'delegate', updateDelegateTicket, removeDelegateTicket)
              )}
            </div>
          </div>
        )}

        {/* Completion Status */}
        {(vipTickets.length + delegateTickets.length) === totalTickets && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="w-4 h-4 text-green-600" />
            <AlertDescription className="text-green-800">
              All tickets have been allocated. Please review the details before proceeding.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
