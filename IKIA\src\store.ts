import { configureStore } from '@reduxjs/toolkit'
import { setupListeners } from '@reduxjs/toolkit/query'

import { targetAudienceApi } from './lib/api/targetAudienceApi'
import { programsApi } from './lib/api/programsApi'
import { speakersApi } from './lib/api/speakersApi'
import { thematicAreasApi } from './lib/api/thematicAreasApi'
import { partnersApi } from './lib/api/partnersApi'
import { ikiaAssetApi } from './lib/api/ikiaAssetApi'
import { enquiryApi } from './lib/api/enquiryApi'
import { newsMediaApi } from './lib/api/newsMediaApi'
import { partnershipMatchingApi } from './lib/api/partnershipMatchingApi'
import { successStoriesApi } from './lib/api/successStoriesApi'
import { usersApi } from './lib/api/usersApi'

export const store = configureStore({
  reducer: {
    [targetAudienceApi.reducerPath]: targetAudienceApi.reducer,
    [programsApi.reducerPath]: programsApi.reducer,
    [speakersApi.reducerPath]: speakersApi.reducer,
    [thematicAreasApi.reducerPath]: thematicAreasApi.reducer,
    [partnersApi.reducerPath]: partnersApi.reducer,
    [ikiaAssetApi.reducerPath]: ikiaAssetApi.reducer,
    [enquiryApi.reducerPath]: enquiryApi.reducer,
    [newsMediaApi.reducerPath]: newsMediaApi.reducer,
    [partnershipMatchingApi.reducerPath]: partnershipMatchingApi.reducer,
    [successStoriesApi.reducerPath]: successStoriesApi.reducer,
    [usersApi.reducerPath]: usersApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE', 'persist/REGISTER'],
      },
    }).concat([
      targetAudienceApi.middleware,
      programsApi.middleware,
      speakersApi.middleware,
      thematicAreasApi.middleware,
      partnersApi.middleware,
      ikiaAssetApi.middleware,
      enquiryApi.middleware,
      newsMediaApi.middleware,
      partnershipMatchingApi.middleware,
      successStoriesApi.middleware,
      usersApi.middleware,
    ]),
})

// Enable automatic refetching behaviors (e.g. on window refocus)
setupListeners(store.dispatch)

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
