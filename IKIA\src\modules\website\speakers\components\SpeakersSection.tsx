import SpeakerCard from './SpeakersCard'

const speakers = [
  {
    id: '1',
    name: '<PERSON>',
    title: 'Founder of FormM',
    type: 'main',
    imageUrl: '/placeholder.svg?height=128&width=128&text=TM',
    social: {
      twitter: '#',
      facebook: '#',
      linkedin: '#',
      instagram: '#',
    },
  },
  {
    id: '2',
    name: '<PERSON>',
    title: 'Product Lead',
    type: 'main',
    imageUrl: '/placeholder.svg?height=128&width=128&text=TP',
    social: {
      twitter: '#',
      facebook: '#',
      linkedin: '#',
      instagram: '#',
    },
  },
  {
    id: '3',
    name: '<PERSON>',
    title: 'CTO',
    type: 'main',
    imageUrl: '/placeholder.svg?height=128&width=128&text=SC',
    social: {
      twitter: '#',
      facebook: '#',
      linkedin: '#',
      instagram: '#',
    },
  },
  {
    id: '4',
    name: '<PERSON>',
    title: 'Data Science Director',
    type: 'main',
    imageUrl: '/placeholder.svg?height=128&width=128&text=RG',
    social: {
      twitter: '#',
      facebook: '#',
      linkedin: '#',
      instagram: '#',
    },
  },
  {
    id: '5',
    name: '<PERSON>',
    title: 'Product Designer',
    type: 'keynote',
    imageUrl: '/placeholder.svg?height=128&width=128&text=AH',
    social: {
      twitter: '#',
      facebook: '#',
      linkedin: '#',
      instagram: '#',
    },
  },
  {
    id: '6',
    name: 'John May',
    title: 'Founder',
    type: 'keynote',
    imageUrl: '/placeholder.svg?height=128&width=128&text=JM',
    social: {
      twitter: '#',
      facebook: '#',
      linkedin: '#',
      instagram: '#',
    },
  },
  {
    id: '7',
    name: 'Lisa Wang',
    title: 'Head of Product',
    type: 'keynote',
    imageUrl: '/placeholder.svg?height=128&width=128&text=LW',
    social: {
      twitter: '#',
      facebook: '#',
      linkedin: '#',
      instagram: '#',
    },
  },
  {
    id: '8',
    name: 'Alex Kim',
    title: 'Blockchain Developer',
    type: 'keynote',
    imageUrl: '/placeholder.svg?height=128&width=128&text=AK',
    social: {
      twitter: '#',
      facebook: '#',
      linkedin: '#',
      instagram: '#',
    },
  },
]

export default function SpeakersSection() {
  const mainSpeakers = speakers.filter((s) => s.type === 'main')
  const keynoteSpeakers = speakers.filter((s) => s.type === 'keynote')

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Main Speakers Section */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-ikia-green-500 inline-block pb-2 border-b-2 border-ikia-green-500">
            MAIN SPEAKERS
          </h2>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {mainSpeakers.map((speaker) => (
            <SpeakerCard key={speaker.id} speaker={speaker} />
          ))}
        </div>

        {/* Keynote Speakers Section */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-ikia-yellow-500 inline-block pb-2 border-b-2 border-ikia-yellow-500">
            KEYNOTE SPEAKERS
          </h2>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {keynoteSpeakers.map((speaker) => (
            <SpeakerCard key={speaker.id} speaker={speaker} />
          ))}
        </div>
      </div>
    </section>
  )
}
