// Frontend API client for counties
export interface County {
  id: string
  name: string
  code: string
  coordinates: {
    latitude: number
    longitude: number
  }
  description?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface CountiesResponse {
  counties: County[]
  totalCounties: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// Payload CMS response format
export interface PayloadCountiesResponse {
  docs: County[]
  totalDocs: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
  nextPage?: number
  prevPage?: number
}

export interface CountyResponse {
  county: County
}

// Payload CMS single county response format
export interface PayloadCountyResponse extends County {
  // Payload returns the county object directly
}

export interface CountiesApiParams {
  name?: string
  code?: string
  active?: boolean
  near_lat?: number
  near_lng?: number
  radius?: number
  limit?: number
  page?: number
  sort?: string
}

class CountiesApi {
  private baseUrl: string
  private static instance: CountiesApi

  private constructor() {
    // Use the current domain for API calls
    // Payload custom endpoints are available at /api/[endpoint-path]
    this.baseUrl =
      typeof window !== 'undefined'
        ? `${window.location.origin}/api`
        : process.env.NEXT_PUBLIC_SITE_URL
          ? `${process.env.NEXT_PUBLIC_SITE_URL}/api`
          : 'http://localhost:3000/api'
  }

  static getInstance(): CountiesApi {
    if (!CountiesApi.instance) {
      CountiesApi.instance = new CountiesApi()
    }
    return CountiesApi.instance
  }

  /**
   * Fetch all counties with optional filtering
   */
  async getCounties(params: CountiesApiParams = {}): Promise<CountiesResponse> {
    try {
      const searchParams = new URLSearchParams()

      // Add parameters to search params
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, value.toString())
        }
      })

      const url = `${this.baseUrl}/counties${searchParams.toString() ? `?${searchParams.toString()}` : ''}`

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      // Handle different response formats
      if (data && typeof data === 'object') {
        // Check if it's our custom endpoint format (expected)
        if ('counties' in data && Array.isArray(data.counties)) {
          return data as CountiesResponse
        }

        // Check if it's a Payload CMS collection response format (fallback)
        if ('docs' in data && Array.isArray(data.docs)) {
          const payloadResponse = data as PayloadCountiesResponse
          const transformedResponse: CountiesResponse = {
            counties: payloadResponse.docs || [],
            totalCounties: payloadResponse.totalDocs || 0,
            page: payloadResponse.page || 1,
            limit: payloadResponse.limit || 50,
            totalPages: payloadResponse.totalPages || 0,
            hasNextPage: payloadResponse.hasNextPage || false,
            hasPrevPage: payloadResponse.hasPrevPage || false,
          }
          return transformedResponse
        }

        // Check if it's a direct array (simple format)
        if (Array.isArray(data)) {
          return {
            counties: data as County[],
            totalCounties: data.length,
            page: 1,
            limit: data.length,
            totalPages: 1,
            hasNextPage: false,
            hasPrevPage: false,
          }
        }
      }

      // If we can't recognize the format, throw an error
      throw new Error(
        `Unexpected response format. Expected object with 'counties' or 'docs' property, got: ${typeof data}`,
      )
    } catch (error) {
      console.error('Error fetching counties:', error)
      throw error
    }
  }

  /**
   * Fetch active counties only
   */
  async getActiveCounties(
    params: Omit<CountiesApiParams, 'active'> = {},
  ): Promise<CountiesResponse> {
    return this.getCounties({ ...params, active: true })
  }

  /**
   * Fetch a single county by ID or code
   */
  async getCounty(id: string): Promise<CountyResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/counties/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      // Transform Payload response to our expected format
      if (data && typeof data === 'object') {
        // Check if it's already wrapped in our format
        if ('county' in data) {
          return data as CountyResponse
        }

        // If it's a direct county object from Payload, wrap it
        if ('id' in data && 'name' in data) {
          return { county: data as County }
        }
      }

      throw new Error(`Unexpected county response format: ${JSON.stringify(data)}`)
    } catch (error) {
      console.error('Error fetching county:', error)
      throw error
    }
  }

  /**
   * Search counties by name
   */
  async searchCounties(name: string, limit: number = 10): Promise<CountiesResponse> {
    return this.getCounties({ name, limit, active: true })
  }

  /**
   * Get counties near a specific location
   */
  async getCountiesNear(
    latitude: number,
    longitude: number,
    radius: number = 100,
    limit: number = 10,
  ): Promise<CountiesResponse> {
    return this.getCounties({
      near_lat: latitude,
      near_lng: longitude,
      radius,
      limit,
      active: true,
    })
  }

  /**
   * Get counties within a bounding box
   */
  async getCountiesInBounds(
    north: number,
    south: number,
    east: number,
    west: number,
  ): Promise<{
    counties: County[]
    totalCounties: number
    bounds: { north: number; south: number; east: number; west: number }
  }> {
    try {
      const searchParams = new URLSearchParams({
        north: north.toString(),
        south: south.toString(),
        east: east.toString(),
        west: west.toString(),
      })

      const response = await fetch(`${this.baseUrl}/counties/bounds?${searchParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Error fetching counties in bounds:', error)
      throw error
    }
  }
}

// Export a singleton instance
export const countiesApi = CountiesApi.getInstance()

// Export the class for custom instances if needed
export default CountiesApi
