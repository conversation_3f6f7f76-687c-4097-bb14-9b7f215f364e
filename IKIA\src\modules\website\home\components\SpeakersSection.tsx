'use client'

import { SkewedContainer } from '@/components/ui/SkewedContainer'
import { IkiaActionButtons } from '@/components/ikia-navbar'
import { speakersButtons } from '../data/buttonConfigs'
import { useGetSpeakersByCategoryQuery } from '@/lib/api/speakersApi'
import { SpeakerCardSkeleton, SpeakerCard } from '@/modules/website/speakers/components'

export default function SpeakersSection() {
  const { data, isLoading, error } = useGetSpeakersByCategoryQuery()

  return (
    <section className="py-20 bg-[#FFF8E3]">
      <div className="max-w-7xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-4">
            <SkewedContainer variant="outlined" size="sm">
              SPEAKERS
            </SkewedContainer>
          </div>
          <h3 className="font-myriad font-bold text-3xl lg:text-4xl text-black mb-6">
            Who&apos;s Speaking?
          </h3>
          <p className="font-myriad text-lg text-gray-700 max-w-3xl mx-auto">
            Distinguished experts, thought leaders, and practitioners sharing insights on indigenous
            knowledge commercialization and sustainable investment opportunities.
          </p>
        </div>
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          {/* Keynote Speakers Section */}
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-ikia-green-500 inline-block pb-2 border-b-2 border-ikia-green-500">
              KEYNOTE SPEAKER
            </h2>
          </div>
          <div className="flex flex-wrap justify-center gap-8 mb-16">
            {isLoading ? (
              <div className="w-full sm:w-1/2 lg:w-1/4 flex justify-center">
                <SpeakerCardSkeleton size="w-56 h-56" />
              </div>
            ) : (
              data?.keynote.docs?.map((speaker) => (
                <div key={speaker.id} className="w-full sm:w-1/2 lg:w-1/4 flex justify-center">
                  <SpeakerCard key={speaker.id} speaker={speaker} size="w-56 h-56" />
                </div>
              ))
            )}
          </div>

          {/* Plenary Speakers Section */}
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-ikia-yellow-500 inline-block pb-2 border-b-2 border-ikia-yellow-500">
              PLENARY SPEAKERS
            </h2>
          </div>
          <div className="flex flex-wrap justify-center gap-8 mb-16">
            {isLoading
              ? [...Array(6)].map((_, index) => (
                  <div key={index} className="w-full sm:w-1/2 lg:w-1/4 flex justify-center">
                    <SpeakerCardSkeleton key={index} size="w-44 h-44" />
                  </div>
                ))
              : data?.plenary.docs?.map((speaker) => (
                  <div key={speaker.id} className="w-full sm:w-1/2 lg:w-1/4 flex justify-center">
                    <SpeakerCard key={speaker.id} speaker={speaker} size="w-44 h-44" />
                  </div>
                ))}
          </div>
        </div>

        {/* View All */}
        <div className="text-center">
          <IkiaActionButtons
            buttons={speakersButtons}
            size="large"
            showOnMobile={true}
            layout="horizontal"
            className="justify-center"
          />
        </div>
      </div>
    </section>
  )
}
