import type { PayloadRequest } from 'payload'

interface TransformedPartner {
  id: string
  name: string
  company?: string
  about?: string
  sponsorshipTier?: string
  logo?: string
  eventsSponsored?: Array<{
    id: string
    title: string
    date: string
  }>
  notableContributions?: string[]
  awardsRecognitions?: string[]
  recentProjects?: string[]
  impact?: string[]
  contact: {
    emails: string[]
    phones: string[]
    location: {
      city?: string | null
      country?: string | null
      address?: string | null
    }
  }
}

interface PartnersResponse {
  partners: TransformedPartner[]
  totalPartners: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

const transformMedia = (media: any): string | undefined => {
  if (!media || typeof media === 'string') return undefined
  return media.url || `/api/media/file/${media.filename}`
}

const transformPartner = (partner: any): TransformedPartner => ({
  id: partner.id,
  name: partner.name,
  company: partner.company,
  about: partner.about,
  sponsorshipTier: partner.sponsorshipTier,
  logo: transformMedia(partner.logo),
  eventsSponsored: Array.isArray(partner.eventsSponsored)
    ? partner.eventsSponsored.map((event: any) => ({
        id: event.id,
        title: event.title,
        date: event.date,
      }))
    : [],
  notableContributions: Array.isArray(partner.notableContributions)
    ? partner.notableContributions.map((c: any) => c.contribution).filter(Boolean)
    : [],
  awardsRecognitions: Array.isArray(partner.awardsRecognitions)
    ? partner.awardsRecognitions.map((a: any) => a.award).filter(Boolean)
    : [],
  recentProjects: Array.isArray(partner.recentProjects)
    ? partner.recentProjects.map((p: any) => p.project).filter(Boolean)
    : [],
  impact: Array.isArray(partner.impact)
    ? partner.impact.map((i: any) => i.impactItem).filter(Boolean)
    : [],
  contact: {
    emails: partner.contact?.emails?.map((e: any) => e.email).filter(Boolean) || [],
    phones: partner.contact?.phones?.map((p: any) => p.phone).filter(Boolean) || [],
    location: {
      city: partner.contact?.location?.city || null,
      country: partner.contact?.location?.country || null,
      address: partner.contact?.location?.address || null,
    },
  },
})

// --- Main Handler ---
export const partnersHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const { page = '1', limit = '50', sort = 'name', search } = req.query as Record<string, string>

    const where: any = {}
    if (search) {
      where.name = { contains: search }
    }

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = parseInt(limit) || 50

    const partnersResult = await payload.find({
      collection: 'partners',
      where,
      page: parsedPage,
      limit: parsedLimit,
      sort: sort as any,
      depth: 2, // Include events data
      select: {
        name: true,
        company: true,
        logo: true,
        sponsorshipTier: true,
        eventsSponsored: true,
        about: true,
        notableContributions: true,
        awardsRecognitions: true,
        recentProjects: true,
        impact: true,
        contact: true,
        // Explicitly exclude sensitive fields
        // internalNotes: false,
      },
    })

    const transformed = partnersResult.docs.map(transformPartner)

    const response: PartnersResponse = {
      partners: transformed,
      totalPartners: partnersResult.totalDocs,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(partnersResult.totalDocs / parseInt(limit)),
      hasNextPage: parseInt(page) < Math.ceil(partnersResult.totalDocs / parseInt(limit)),
      hasPrevPage: parseInt(page) > 1,
    }

    res.status(200).json(response)
  } catch (error) {
    console.error('Error in partners endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message:
        process.env.NODE_ENV === 'development'
          ? error instanceof Error
            ? error.message
            : 'Unknown error'
          : 'Something went wrong',
    })
  }
}
