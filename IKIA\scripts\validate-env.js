#!/usr/bin/env node

/**
 * Environment Variables Validation Script
 * Validates that all required environment variables are set for eCitizen API endpoints
 *
 * Usage: node scripts/validate-env.js
 */

import { config } from 'dotenv'
config()

const REQUIRED_VARIABLES = {
  'Core Application': ['PAYLOAD_SECRET', 'DATABASE_URL', 'NEXT_PUBLIC_SERVER_URL'],
  'Pesaflow Payment Integration (HashService)': [
    'PESAFLOW_API_CLIENT_ID',
    'PESAFLOW_CLIENT_SECRET',
    'PESAFLOW_CLIENT_KEY',
    'PESAFLOW_UAT_SERVER_URL',
  ],
  'Pesaflow Checkout Configuration': [
    'PESAFLOW_REQUEST_SERVICE_ID',
    'PESAFLOW_BILL_DESC',
    'PESAFLOW_NOTIFICATION_URL',
    'PESAFLOW_CALLBACK_SUCCESS_URL',
  ],
  'eCitizen SSO Integration': [
    'ECITIZEN_CLIENT_ID',
    'ECITIZEN_CLIENT_SECRET',
    'ECITIZEN_AUTHORIZATION_URL',
    'ECITIZEN_TOKEN_URL',
    'ECITIZEN_INTROSPECTION_URL',
    'ECITIZEN_USERINFO_URL',
  ],
  'eCitizen USSD Integration': ['ECITIZEN_MERCHANT_KEY', 'ECITIZEN_MERCHANT_SECRET'],
}

const OPTIONAL_VARIABLES = {
  'Email Configuration': [
    'SMTP_HOST',
    'SMTP_PORT',
    'SMTP_USER',
    'SMTP_PASS',
    'FROM_EMAIL',
    'FROM_NAME',
  ],
  'IPN Optimization Settings': [
    'USE_OPTIMIZED_IPN_PROCESSOR',
    'IPN_CACHE_DURATION_MINUTES',
    'IPN_MAX_CONCURRENT_PROCESSING',
    'IPN_ENABLE_PERFORMANCE_LOGGING',
  ],
  'Database Optimization': [
    'DATABASE_MAX_CONNECTIONS',
    'DATABASE_IDLE_TIMEOUT',
    'DATABASE_ACQUIRE_TIMEOUT',
    'DATABASE_CONNECTION_POOL_MIN',
    'DATABASE_CONNECTION_POOL_MAX',
  ],
  'Performance Monitoring': [
    'ENABLE_REQUEST_LOGGING',
    'ENABLE_PERFORMANCE_METRICS',
    'LOG_LEVEL',
    'REQUEST_TIMEOUT_MS',
  ],
  'Rate Limiting': [
    'ENABLE_RATE_LIMITING',
    'RATE_LIMIT_WINDOW_MS',
    'RATE_LIMIT_MAX_REQUESTS',
    'RATE_LIMIT_IPN_MAX_REQUESTS',
  ],
  'Optional Configuration': [
    'CRON_SECRET',
    'PREVIEW_SECRET',
    'PESAFLOW_PICTURE_URL',
    'VERCEL_PROJECT_PRODUCTION_URL',
  ],
}

function validateEnvironmentVariables() {
  console.log('🔍 ENVIRONMENT VARIABLES VALIDATION')
  console.log('='.repeat(60))

  let hasErrors = false
  let hasWarnings = false

  // Check required variables
  console.log('\n📋 REQUIRED VARIABLES:')
  for (const [category, variables] of Object.entries(REQUIRED_VARIABLES)) {
    console.log(`\n${category}:`)

    for (const variable of variables) {
      const value = process.env[variable]
      if (!value || value.trim() === '') {
        console.log(`  ❌ ${variable}: MISSING`)
        hasErrors = true
      } else if (value.includes('your_') || value.includes('yourdomain.com')) {
        console.log(`  ⚠️  ${variable}: PLACEHOLDER VALUE`)
        hasWarnings = true
      } else {
        console.log(`  ✅ ${variable}: SET`)
      }
    }
  }

  // Check optional variables
  console.log('\n📋 OPTIONAL VARIABLES:')
  for (const [category, variables] of Object.entries(OPTIONAL_VARIABLES)) {
    console.log(`\n${category}:`)

    for (const variable of variables) {
      const value = process.env[variable]
      if (!value || value.trim() === '') {
        console.log(`  ⚪ ${variable}: NOT SET`)
      } else if (value.includes('your_') || value.includes('yourdomain.com')) {
        console.log(`  ⚠️  ${variable}: PLACEHOLDER VALUE`)
      } else {
        console.log(`  ✅ ${variable}: SET`)
      }
    }
  }

  // Summary
  console.log('\n' + '='.repeat(60))
  console.log('📊 VALIDATION SUMMARY:')

  if (hasErrors) {
    console.log('❌ ERRORS: Missing required environment variables')
    console.log('   Please set all required variables before running the application')
  } else {
    console.log('✅ SUCCESS: All required environment variables are set')
  }

  if (hasWarnings) {
    console.log('⚠️  WARNINGS: Some variables have placeholder values')
    console.log('   Replace placeholder values with actual credentials for production')
  }

  console.log('\n🔧 ENDPOINT REQUIREMENTS:')
  console.log('  Payment Validation: PESAFLOW_* variables')
  console.log('  Payment Confirmation: PESAFLOW_* variables')
  console.log('  Payment Status Query: PESAFLOW_* variables')
  console.log('  IPN Webhook: PESAFLOW_CLIENT_SECRET, PESAFLOW_CLIENT_KEY')
  console.log('  Checkout: PESAFLOW_* + PESAFLOW_REQUEST_SERVICE_ID, PESAFLOW_BILL_DESC')
  console.log('  SSO OAuth: ECITIZEN_* variables')
  console.log('  USSD Integration: ECITIZEN_MERCHANT_* variables')

  console.log('\n📝 NEXT STEPS:')
  if (hasErrors) {
    console.log('  1. Copy .env.example to .env (if not done)')
    console.log('  2. Replace placeholder values with actual credentials')
    console.log('  3. Run this script again to validate')
    console.log('  4. Start the application: npm run dev')
    process.exit(1)
  } else {
    console.log('  1. Replace any placeholder values with production credentials')
    console.log('  2. Start the application: npm run dev')
    console.log('  3. Test endpoints using the API documentation')
    process.exit(0)
  }
}

// Run validation
validateEnvironmentVariables()
