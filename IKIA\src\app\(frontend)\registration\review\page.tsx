'use client'

import type React from 'react'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { CheckCircle, CreditCard, FileText, Shield } from 'lucide-react'
import { loadFormDataFromStorage } from '@/modules/website/registration/lib/registration-utils'
import {
  delegatePackages,
  exhibitorPackages,
  vipPackage,
  allSponsorshipTiers,
} from '@/modules/website/registration/lib/registration-data'

export default function RegistrationReview() {
  const router = useRouter()
  const [agreements, setAgreements] = useState({
    terms: false,
    privacy: false,
    marketing: false,
  })
  const [registrationData, setRegistrationData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [isProcessingPayment, setIsProcessingPayment] = useState(false)

  useEffect(() => {
    // Load registration data from localStorage
    const loadRegistrationData = () => {
      try {
        // Try to load from different possible keys
        let data = loadFormDataFromStorage('ikia-registration')

        // If not found, try investment intent key
        if (!data) {
          data = loadFormDataFromStorage('ikia-investment-intent')
        }

        if (data) {
          setRegistrationData(data)
        } else {
          // Fallback to mock data if no real data found
          setRegistrationData({
            registrationType: 'Conference Delegate',
            selectedPackage: 'standard',
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '+254 700 000 000',
            organization: 'Example University',
            country: 'Kenya',
            thematicAreas: [
              'Traditional Foods & Nutrition',
              'Indigenous Technologies & Innovations',
            ],
            experience: '6-10 years',
          })
        }
      } catch (error) {
        console.error('Error loading registration data:', error)
        // Set fallback data
        setRegistrationData({
          registrationType: 'Conference Delegate',
          selectedPackage: 'standard',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+254 700 000 000',
          organization: 'Example University',
          country: 'Kenya',
        })
      } finally {
        setLoading(false)
      }
    }

    loadRegistrationData()
  }, [])

  // Helper function to get package details and pricing
  const getPackageInfo = () => {
    if (!registrationData) return { name: 'Unknown Package', price: 'KES 0' }

    const { registrationType, selectedPackage, selectedTier } = registrationData

    // Handle different registration types
    switch (registrationType?.toLowerCase()) {
      case 'delegate':
      case 'conference delegate':
        const delegatePackage = delegatePackages.find((pkg) => pkg.id === selectedPackage)
        return {
          name: delegatePackage?.name || 'Standard Package',
          price: delegatePackage?.price || 'KES 25,000',
        }

      case 'exhibitor':
        const exhibitorPackage = exhibitorPackages.find((pkg) => pkg.id === selectedPackage)
        return {
          name: exhibitorPackage?.name || 'Basic Booth',
          price: exhibitorPackage?.price || 'KES 50,000',
        }

      case 'vip':
        return {
          name: vipPackage.name,
          price: vipPackage.price,
        }

      case 'sponsor':
        const sponsorTier = allSponsorshipTiers.find((tier) => tier.id === selectedTier)
        return {
          name: sponsorTier?.name || 'Sponsorship Package',
          price: sponsorTier?.price || 'KES 100,000',
        }

      case 'investment intent':
      case 'investor':
        return {
          name: 'Investment Intent Registration',
          price: 'Free',
        }

      default:
        return {
          name: 'Registration Package',
          price: 'KES 0',
        }
    }
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading registration data...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!registrationData) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">No Registration Data Found</h1>
          <p className="text-gray-600 mb-6">Please complete your registration first.</p>
          <Button onClick={() => router.push('/registration')}>Go to Registration</Button>
        </div>
      </div>
    )
  }

  const packageInfo = getPackageInfo()

  const handleAgreementChange = (field: string, checked: boolean) => {
    setAgreements((prev) => ({
      ...prev,
      [field]: checked,
    }))
  }

  const canProceed = agreements.terms && agreements.privacy

  const handleSubmit = () => {
    if (canProceed && !isProcessingPayment) {
      const registrationType = registrationData.registrationType?.toLowerCase()

      // Free registration types (no payment required)
      if (registrationType === 'investment intent' || registrationType === 'investor') {
        router.push('/registration/success?type=investment-intent')
        return
      }

      // Paid registration types - simulate payment process
      const packageInfo = getPackageInfo()
      const isPaidRegistration =
        packageInfo.price !== 'Free' && packageInfo.price !== 'In-kind contribution'

      if (isPaidRegistration) {
        // Start payment processing
        setIsProcessingPayment(true)

        // Simulate payment processing
        setTimeout(() => {
          setIsProcessingPayment(false)
          // After successful payment, redirect to success page
          router.push('/registration/success?payment=completed')
        }, 3000) // 3 second delay to simulate payment processing
      } else {
        // For any other free registrations
        router.push('/registration/success')
      }
    }
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="mb-8">
        <button onClick={() => router.back()}>Back to Form</button>

        <div className="flex items-center space-x-4 mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center">
            <FileText className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1
              className="text-3xl font-bold text-gray-900"
              style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
            >
              Review Your Registration
            </h1>
            <p className="text-blue-600 font-semibold">
              Please review your information before proceeding to payment
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-8">
        {/* Registration Summary */}
        <Card>
          <CardHeader>
            <CardTitle
              className="text-xl font-bold flex items-center space-x-2"
              style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
            >
              <CheckCircle className="w-6 h-6 text-green-600" />
              <span>Registration Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Registration Type</h3>
                <p className="text-gray-700 capitalize">
                  {registrationData.registrationType || 'Conference Delegate'}
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Selected Package</h3>
                <p className="text-gray-700">{packageInfo.name}</p>
              </div>
            </div>

            {registrationData.isGroupRegistration && (
              <div className="border-t pt-4">
                <h3 className="font-semibold text-gray-900 mb-2">Group Registration</h3>
                <p className="text-gray-700">
                  {registrationData.groupMembers ? registrationData.groupMembers.length + 1 : 1}{' '}
                  members
                </p>
              </div>
            )}

            <div className="border-t pt-4">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold text-gray-900">Total Amount</span>
                <span className="text-2xl font-bold text-green-600">{packageInfo.price}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle
              className="text-xl font-bold"
              style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
            >
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Full Name</h3>
                <p className="text-gray-700">
                  {registrationData.title ? `${registrationData.title} ` : ''}
                  {registrationData.firstName} {registrationData.lastName}
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Email</h3>
                <p className="text-gray-700">{registrationData.email}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Phone</h3>
                <p className="text-gray-700">{registrationData.phone}</p>
              </div>
              {registrationData.organization && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Organization</h3>
                  <p className="text-gray-700">{registrationData.organization}</p>
                </div>
              )}
              {registrationData.position && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Position</h3>
                  <p className="text-gray-700">{registrationData.position}</p>
                </div>
              )}
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Country</h3>
                <p className="text-gray-700">{registrationData.country}</p>
              </div>
              {registrationData.city && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">City</h3>
                  <p className="text-gray-700">{registrationData.city}</p>
                </div>
              )}
              {registrationData.experience && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Experience</h3>
                  <p className="text-gray-700">{registrationData.experience}</p>
                </div>
              )}
            </div>

            {/* Thematic Areas for Delegates */}
            {registrationData.thematicAreas && registrationData.thematicAreas.length > 0 && (
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Areas of Interest</h3>
                <div className="flex flex-wrap gap-2">
                  {registrationData.thematicAreas.map((area: string, index: number) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm"
                    >
                      {area}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Investment Areas for Investors */}
            {registrationData.investmentAreas && registrationData.investmentAreas.length > 0 && (
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Investment Areas of Interest</h3>
                <div className="flex flex-wrap gap-2">
                  {registrationData.investmentAreas.map((area: string, index: number) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                    >
                      {area}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Investment Types for Investors */}
            {registrationData.investmentTypes && registrationData.investmentTypes.length > 0 && (
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Preferred Investment Types</h3>
                <div className="flex flex-wrap gap-2">
                  {registrationData.investmentTypes.map((type: string, index: number) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm"
                    >
                      {type}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Additional Information based on registration type */}
            {registrationData.registrationType?.toLowerCase() === 'sponsor' &&
              registrationData.companyName && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Company Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Company Name</p>
                      <p className="text-gray-700">{registrationData.companyName}</p>
                    </div>
                    {registrationData.website && (
                      <div>
                        <p className="text-sm text-gray-600">Website</p>
                        <p className="text-gray-700">{registrationData.website}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

            {registrationData.registrationType?.toLowerCase() === 'vip' &&
              registrationData.vipCategory && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">VIP Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">VIP Category</p>
                      <p className="text-gray-700">{registrationData.vipCategory}</p>
                    </div>
                    {registrationData.invitationReference && (
                      <div>
                        <p className="text-sm text-gray-600">Invitation Reference</p>
                        <p className="text-gray-700">{registrationData.invitationReference}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

            {registrationData.registrationType?.toLowerCase() === 'exhibitor' && (
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Exhibition Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {registrationData.companyName && (
                    <div>
                      <p className="text-sm text-gray-600">Company/Business Name</p>
                      <p className="text-gray-700">{registrationData.companyName}</p>
                    </div>
                  )}
                  {registrationData.businessType && (
                    <div>
                      <p className="text-sm text-gray-600">Business Type</p>
                      <p className="text-gray-700">{registrationData.businessType}</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Terms and Agreements */}
        <Card>
          <CardHeader>
            <CardTitle
              className="text-xl font-bold flex items-center space-x-2"
              style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
            >
              <Shield className="w-6 h-6 text-blue-600" />
              <span>Terms and Agreements</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <Checkbox
                  id="terms"
                  checked={agreements.terms}
                  onCheckedChange={(checked) => handleAgreementChange('terms', checked as boolean)}
                />
                <div className="flex-1">
                  <Label htmlFor="terms" className="text-sm font-medium">
                    I agree to the Terms and Conditions *
                  </Label>
                  <p className="text-xs text-gray-600 mt-1">
                    By checking this box, you agree to our terms of service and conference policies.
                    <a href="#" className="text-blue-600 hover:underline ml-1">
                      Read full terms
                    </a>
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Checkbox
                  id="privacy"
                  checked={agreements.privacy}
                  onCheckedChange={(checked) =>
                    handleAgreementChange('privacy', checked as boolean)
                  }
                />
                <div className="flex-1">
                  <Label htmlFor="privacy" className="text-sm font-medium">
                    I agree to the Privacy Policy *
                  </Label>
                  <p className="text-xs text-gray-600 mt-1">
                    We will handle your personal data in accordance with our privacy policy.
                    <a href="#" className="text-blue-600 hover:underline ml-1">
                      Read privacy policy
                    </a>
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Checkbox
                  id="marketing"
                  checked={agreements.marketing}
                  onCheckedChange={(checked) =>
                    handleAgreementChange('marketing', checked as boolean)
                  }
                />
                <div className="flex-1">
                  <Label htmlFor="marketing" className="text-sm font-medium">
                    I consent to receive marketing communications
                  </Label>
                  <p className="text-xs text-gray-600 mt-1">
                    Receive updates about future events and opportunities (optional).
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Section */}
        <Card>
          <CardHeader>
            <CardTitle
              className="text-xl font-bold flex items-center space-x-2"
              style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
            >
              <CreditCard className="w-6 h-6 text-green-600" />
              <span>Payment Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-blue-900 mb-2">Payment Methods Accepted</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• M-Pesa (Kenya)</li>
                <li>• Bank Transfer</li>
                <li>• Credit/Debit Cards (Visa, Mastercard)</li>
                <li>• PayPal</li>
              </ul>
            </div>

            <div className="text-center">
              {isProcessingPayment ? (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <div>
                      <p className="text-blue-900 font-semibold">Processing your payment...</p>
                      <p className="text-blue-700 text-sm">
                        Please wait while we securely process your registration payment.
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-gray-600 mb-4">
                  You will be redirected to our secure payment gateway to complete your
                  registration.
                </p>
              )}

              <Button
                onClick={handleSubmit}
                disabled={!canProceed || isProcessingPayment}
                className="px-12 py-3 text-lg font-semibold"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                {isProcessingPayment ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Processing Payment...</span>
                  </div>
                ) : (
                  `Proceed to Payment (${getPackageInfo().price})`
                )}
              </Button>

              {!canProceed && (
                <p className="text-red-600 text-sm mt-2">
                  Please accept the required terms and conditions to proceed.
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
