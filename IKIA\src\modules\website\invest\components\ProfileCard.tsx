'use client'

import React from 'react'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import {
  MapPin,
  Star,
  Award,
  Building,
  TrendingUp,
  Users,
  Lightbulb,
  ExternalLink,
} from 'lucide-react'
import { ProfileCardProps, isInvestorProfile } from '../types'

// Helper function to get profile image URL
const getProfileImageUrl = (profile: ProfileCardProps['profile'] | any): string => {
  // Handle legacy image string
  if (typeof profile.image === 'string' && profile.image) {
    return profile.image
  }

  // Handle API profileImage object
  if (typeof profile.profileImage === 'object' && profile.profileImage !== null) {
    return (
      profile.profileImage.sizes?.medium?.url ||
      profile.profileImage.sizes?.small?.url ||
      profile.profileImage.url ||
      '/placeholder.svg?height=300&width=300&text=Profile'
    )
  }

  // Fallback placeholder
  return '/placeholder.svg?height=300&width=300&text=Profile'
}

export default function ProfileCard({ profile, onClick }: ProfileCardProps) {
  const isInvestor = isInvestorProfile(profile)
  const imageUrl = getProfileImageUrl(profile)

  return (
    <div className="bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 overflow-hidden group">
      {/* Profile Image */}
      <div className="relative w-full h-64 overflow-hidden">
        <Image
          src={imageUrl}
          alt={profile.name}
          width={300}
          height={300}
          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
        />
        {profile.verified && (
          <div className="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 text-xs font-medium flex items-center gap-1">
            <Award className="w-3 h-3" />
            Verified
          </div>
        )}
        <div className="absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm px-2 py-1 text-xs font-medium">
          {profile.badge}
        </div>
      </div>

      {/* Profile Information */}
      <div className="p-6 space-y-4">
        {/* Name and Title */}
        <div>
          <h3 className="text-xl font-bold text-gray-900 mb-1 group-hover:text-[#7E2518] transition-colors">
            {profile.name}
          </h3>
          <p className="text-gray-600 text-sm">{profile.title}</p>
        </div>

        {/* Organization and Location */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-gray-600 text-sm">
            <Building className="w-4 h-4 flex-shrink-0" />
            <span className="truncate">{profile.organization}</span>
          </div>
          <div className="flex items-center gap-2 text-gray-600 text-sm">
            <MapPin className="w-4 h-4 flex-shrink-0" />
            <span className="truncate">{profile.location}</span>
          </div>
        </div>

        {/* Focus Area */}
        <div className="bg-gray-50 p-3">
          <div className="flex items-center gap-2 mb-1">
            <Lightbulb className="w-4 h-4 text-yellow-500 flex-shrink-0" />
            <span className="text-sm font-medium text-gray-900">Focus</span>
          </div>
          <p className="text-sm text-gray-700 line-clamp-2">{profile.focus}</p>
        </div>

        {/* Type-specific metrics */}
        <div className="grid grid-cols-2 gap-3">
          {isInvestor ? (
            <>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <TrendingUp className="w-4 h-4 text-blue-500" />
                </div>
                <p className="text-xs text-gray-600 mb-1">Investment</p>
                <p className="text-sm font-semibold text-gray-900">{profile.investment}</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Users className="w-4 h-4 text-green-500" />
                </div>
                <p className="text-xs text-gray-600 mb-1">Projects</p>
                <p className="text-sm font-semibold text-gray-900">{profile.projects}</p>
              </div>
            </>
          ) : (
            <>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Award className="w-4 h-4 text-purple-500" />
                </div>
                <p className="text-xs text-gray-600 mb-1">Achievement</p>
                <p className="text-sm font-semibold text-gray-900 line-clamp-1">
                  {profile.achievement}
                </p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 mb-1">
                  <Users className="w-4 h-4 text-orange-500" />
                </div>
                <p className="text-xs text-gray-600 mb-1">Projects</p>
                <p className="text-sm font-semibold text-gray-900">{profile.projects}</p>
              </div>
            </>
          )}
        </div>

        {/* Rating */}
        <div className="flex items-center justify-between pt-2 border-t border-gray-100">
          <div className="flex items-center gap-1">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`w-4 h-4 ${
                  i < Math.floor(profile.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <span className="text-sm font-semibold text-gray-900">{profile.rating}</span>
        </div>

        {/* View Full Profile Button */}
        <Button
          onClick={onClick}
          className="w-full mt-4 bg-[#7E2518] hover:bg-[#7E2518]/90 text-white"
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          View Full Profile
        </Button>
      </div>
    </div>
  )
}
