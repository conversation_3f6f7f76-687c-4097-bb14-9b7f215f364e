import { Card, CardContent } from '@/components/ui/card'
import { IkiaActionButtons } from '@/components/ikia-navbar'
import { Lightbulb, Target, TrendingUp, Users, MapPin, FileText, CheckCircle } from 'lucide-react'
import { aboutButtons } from '../data/buttonConfigs'

export default function AboutSection() {
  return (
    <section className="py-20 bg-muted/30">
      <div className="max-w-7xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="font-acquire font-bold text-3xl lg:text-4xl text-primary mb-4">
            About IKIA Conference
          </h2>
          <p className="font-myriad text-lg text-muted-foreground max-w-3xl mx-auto">
            Discover Kenya&apos;s Indigenous Knowledge Intellectual Assets and their potential for
            sustainable economic development and investment opportunities.
          </p>
        </div>

        {/* Main Content - Two Column Layout */}
        <div className="flex flex-col lg:flex-row gap-16 mb-16">
          {/* Left Column - Content */}
          <div className="lg:w-2/3 space-y-8">
            {/* What is IKIA */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3 mb-4">
                <Lightbulb className="w-6 h-6 text-secondary" />
                <h3 className="font-acquire font-bold text-2xl text-primary">
                  What are Indigenous Knowledge Intellectual Assets?
                </h3>
              </div>
              <p className="font-myriad text-base text-muted-foreground leading-relaxed">
                Indigenous Knowledge Intellectual Assets (IKIAs) represent Kenya&apos;s rich
                heritage of traditional knowledge, practices, and innovations developed by local
                communities over generations. These assets encompass traditional medicine,
                agricultural practices, cultural heritage, and sustainable technologies that hold
                immense potential for modern economic development.
              </p>
            </div>

            {/* Conference Purpose */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3 mb-4">
                <Target className="w-6 h-6 text-accent" />
                <h3 className="font-acquire font-bold text-2xl text-primary">
                  Conference Objectives
                </h3>
              </div>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-secondary mt-0.5 flex-shrink-0" />
                  <p className="font-myriad text-base text-muted-foreground">
                    Create investment opportunities in Kenya&apos;s natural products industry
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-secondary mt-0.5 flex-shrink-0" />
                  <p className="font-myriad text-base text-muted-foreground">
                    Bridge traditional knowledge with modern business practices
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-secondary mt-0.5 flex-shrink-0" />
                  <p className="font-myriad text-base text-muted-foreground">
                    Support sustainable economic development aligned with Vision 2030
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-secondary mt-0.5 flex-shrink-0" />
                  <p className="font-myriad text-base text-muted-foreground">
                    Foster partnerships between communities, investors, and institutions
                  </p>
                </div>
              </div>
            </div>

            {/* Vision 2030 Alignment */}
            <div className="bg-secondary/10 border-l-4 border-secondary p-6 rounded-r-lg">
              <div className="flex items-center space-x-3 mb-3">
                <TrendingUp className="w-5 h-5 text-secondary" />
                <h4 className="font-acquire font-bold text-lg text-primary">
                  Vision 2030 Flagship Program
                </h4>
              </div>
              <p className="font-myriad text-sm text-muted-foreground">
                This conference is a flagship initiative under Kenya&apos;s Vision 2030 development
                blueprint, specifically supporting the Natural Products Industry Initiative to
                achieve double-digit GDP growth through indigenous knowledge commercialization.
              </p>
            </div>
          </div>

          {/* Right Column - Statistics Cards */}
          <div className="lg:w-1/3">
            <div className="space-y-6">
              {/* Statistics Header */}
              <h3 className="font-acquire font-bold text-xl text-primary text-center lg:text-left">
                Conference Impact
              </h3>

              {/* Statistics Cards */}
              <div className="space-y-4">
                <Card className="border-primary/20 hover:border-primary/40 transition-colors">
                  <CardContent className="p-6 text-center">
                    <MapPin className="w-8 h-8 text-secondary mx-auto mb-3" />
                    <div className="font-acquire font-bold text-3xl text-primary mb-2">13</div>
                    <div className="font-myriad text-sm text-muted-foreground">
                      Counties Covered
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-secondary/20 hover:border-secondary/40 transition-colors">
                  <CardContent className="p-6 text-center">
                    <FileText className="w-8 h-8 text-accent mx-auto mb-3" />
                    <div className="font-acquire font-bold text-3xl text-primary mb-2">100+</div>
                    <div className="font-myriad text-sm text-muted-foreground">
                      IKIAs Documented
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-accent/20 hover:border-accent/40 transition-colors">
                  <CardContent className="p-6 text-center">
                    <Users className="w-8 h-8 text-primary mx-auto mb-3" />
                    <div className="font-acquire font-bold text-3xl text-primary mb-2">500+</div>
                    <div className="font-myriad text-sm text-muted-foreground">
                      Expected Participants
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-primary/20 hover:border-primary/40 transition-colors bg-primary/5">
                  <CardContent className="p-6 text-center">
                    <CheckCircle className="w-8 h-8 text-secondary mx-auto mb-3" />
                    <div className="font-acquire font-bold text-lg text-primary mb-2">
                      Investment Ready
                    </div>
                    <div className="font-myriad text-sm text-muted-foreground">
                      Commercialization Opportunities
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-secondary/10 to-primary/10 rounded-lg p-8 border border-secondary/20">
            <h3 className="font-acquire font-bold text-2xl text-primary mb-4">
              Join Kenya&apos;s Indigenous Knowledge Revolution
            </h3>
            <p className="font-myriad text-base text-muted-foreground mb-6 max-w-2xl mx-auto">
              Be part of this historic opportunity to unlock the economic potential of Kenya&apos;s
              traditional knowledge and contribute to sustainable development.
            </p>
            <IkiaActionButtons
              buttons={aboutButtons}
              size="large"
              showOnMobile={true}
              layout="horizontal"
              className="justify-center"
            />
          </div>
        </div>
      </div>
    </section>
  )
}
