// storage-adapter-import-placeholder
import { vercelBlobStorage } from '@payloadcms/storage-vercel-blob'
import { postgresAdapter } from '@payloadcms/db-postgres'
import { nodemailerAdapter } from '@payloadcms/email-nodemailer'

import sharp from 'sharp' // sharp-import
import path from 'path'
import { buildConfig, PayloadRequest } from 'payload'
import { fileURLToPath } from 'url'

import { Categories } from './collections/Categories'
import { Media } from './collections/Media'
import { Pages } from './collections/Pages'
import { Posts } from './collections/Posts'
import { NewsItem } from './collections/News'
import { TargetAudience } from './collections/target-audience'
import { IKIAAsset } from './collections/ikia-assets'
import { Users } from './collections/Users'
import Invoices from './collections/Invoices'
import { Footer } from './Footer/config'
import { Header } from './Header/config'
import { plugins } from './plugins'
import { defaultLexical } from '@/fields/defaultLexical'
import { getServerSideURL } from './utilities/getURL'
import Events from './collections/Events'
import Speakers from './collections/Speakers'
import Counties from './collections/Counties'
import Programs from './collections/Programs'
import ProgramTypes from './collections/ProgramTypes'
import ThematicAreas from './collections/ThematicAreas'
import Sponsors from './collections/Sponsors'
import SponsorshipPackages from './collections/SponsorshipPackages'
import { eventsHandler } from './endpoints/events'
import { speakersHandler } from './endpoints/speakers'
import Partners from './collections/Partners'
import { partnersHandler } from '@/endpoints/partners'
import {
  countiesHandler,
  countyByIdHandler,
  countiesInBoundsHandler,
  createCountyHandler,
  updateCountyHandler,
  deleteCountyHandler,
  countyUsersHandler,
} from './endpoints/counties'
import { pesaflowCheckoutEndpoint } from './endpoints/pesaflowCheckout'
import { pesaflowValidatePaymentEndpoint } from './endpoints/pesaflowValidatePayment'
import { pesaflowConfirmPaymentEndpoint } from './endpoints/pesaflowConfirmPayment'
import { pesaflowQueryPaymentStatusEndpoint } from './endpoints/pesaflowQueryPaymentStatus'
import { pesaflowIPNEndpoint } from './endpoints/pesaflowIPN'
import { ipnStatsEndpoint, clearIPNCacheEndpoint } from './endpoints/ipnStats'
import {
  ssoAuthorizeEndpoint,
  ssoAccessTokenEndpoint,
  ssoTokenIntrospectionEndpoint,
  ssoUserInfoEndpoint,
} from './endpoints/ssoOAuth'
import { ussdIntegrationEndpoint } from './endpoints/ussdIntegration'
import { packageRegistrationEndpoint } from './endpoints/packageRegistration'
import { enhancedPaymentEndpoint } from './endpoints/enhancedPayment'
import { seedTestDataEndpoint } from './endpoints/seedTestData'
import { updateUserRoleEndpoint } from './endpoints/updateUserRole'
import { debugLoginEndpoint } from './endpoints/debugLogin'
import { checkUserStatusEndpoint } from './endpoints/checkUserStatus'
import { verifyUserEndpoint } from './endpoints/verifyUser'
import { healthCheckEndpoint, simpleHealthCheckEndpoint } from './endpoints/health'

import { pesaflowNotificationEndpoint } from './endpoints/pesaflowNotification'
import {
  citizenRegistrationEndpoint,
  getRegistrationStatusEndpoint,
} from './endpoints/citizenRegistration'
import { getUserProfileEndpoint, getCurrentUserProfileEndpoint } from './endpoints/userProfile'
import { exhibitorRegistrationEndpoint } from './endpoints/exhibitorRegistration'
import { Enquiry } from './collections/Enquiry'
import { PesaflowNotifications } from './collections/PesaflowNotifications'
import { Exhibitors } from './collections/Exhibitors'
import { DelegatePackages } from './collections/DelegatePackages'

import { seed } from './endpoints/seed'
import PartnershipMatching from './collections/PartnershipMatching'
import SuccessStories from './collections/SuccessStories'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  email: process.env.SMTP_HOST
    ? nodemailerAdapter({
        defaultFromAddress: process.env.FROM_EMAIL || '<EMAIL>',
        defaultFromName: process.env.FROM_NAME || 'Your App',
        transportOptions: {
          host: process.env.SMTP_HOST,
          port: parseInt(process.env.SMTP_PORT || '587', 10),
          secure: process.env.SMTP_SECURE === 'true', // true for 465, false for 587
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS,
          },
        },
      })
    : undefined,
  admin: {
    components: {
      // The `BeforeLogin` component renders a message that you see while logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below.
      beforeLogin: ['@/components/BeforeLogin'],
      // The `BeforeDashboard` component renders the 'welcome' block that you see after logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below.
      beforeDashboard: ['@/components/BeforeDashboard'],
    },
    importMap: {
      baseDir: path.resolve(dirname),
    },
    user: Users.slug,
    livePreview: {
      breakpoints: [
        {
          label: 'Mobile',
          name: 'mobile',
          width: 375,
          height: 667,
        },
        {
          label: 'Tablet',
          name: 'tablet',
          width: 768,
          height: 1024,
        },
        {
          label: 'Desktop',
          name: 'desktop',
          width: 1440,
          height: 900,
        },
      ],
    },
  },
  // This config helps us configure global or default features that the other editors can inherit
  editor: defaultLexical,
  db: postgresAdapter({
    pool: process.env.DATABASE_URL
      ? {
          // Use connection string if available (for production/cloud)
          connectionString: process.env.DATABASE_URL,
          // Connection pool optimization settings
          max: parseInt(process.env.DATABASE_CONNECTION_POOL_MAX || '20', 10),
          min: parseInt(process.env.DATABASE_CONNECTION_POOL_MIN || '5', 10),
          idleTimeoutMillis: parseInt(process.env.DATABASE_IDLE_TIMEOUT || '30000', 10),
          connectionTimeoutMillis: parseInt(process.env.DATABASE_ACQUIRE_TIMEOUT || '60000', 10),
          // Query timeout settings
          statement_timeout: 30000, // 30 seconds
          query_timeout: 30000, // 30 seconds
          // SSL configuration
          ssl:
            process.env.DATABASE_URL?.includes('neon.tech') ||
            process.env.DATABASE_URL?.includes('amazonaws.com')
              ? { rejectUnauthorized: false }
              : false,
        }
      : process.env.DATABASE_URI
        ? {
            // Fallback to DATABASE_URI for backward compatibility
            connectionString: process.env.DATABASE_URI,
            max: parseInt(process.env.DATABASE_CONNECTION_POOL_MAX || '20', 10),
            min: parseInt(process.env.DATABASE_CONNECTION_POOL_MIN || '5', 10),
            idleTimeoutMillis: parseInt(process.env.DATABASE_IDLE_TIMEOUT || '30000', 10),
            connectionTimeoutMillis: parseInt(process.env.DATABASE_ACQUIRE_TIMEOUT || '60000', 10),
            statement_timeout: 30000,
            query_timeout: 30000,
            ssl: process.env.DATABASE_URI?.includes('neon.tech')
              ? { rejectUnauthorized: false }
              : false,
          }
        : {
            // Use individual parameters (for cPanel/shared hosting)
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT || '5432', 10),
            database: process.env.DB_NAME || '',
            user: process.env.DB_USER || '',
            password: process.env.DB_PASSWORD || '',
            // Connection pool optimization settings
            max: parseInt(process.env.DATABASE_CONNECTION_POOL_MAX || '20', 10),
            min: parseInt(process.env.DATABASE_CONNECTION_POOL_MIN || '5', 10),
            idleTimeoutMillis: parseInt(process.env.DATABASE_IDLE_TIMEOUT || '30000', 10),
            connectionTimeoutMillis: parseInt(process.env.DATABASE_ACQUIRE_TIMEOUT || '60000', 10),
            // Query timeout settings
            statement_timeout: 30000, // 30 seconds
            query_timeout: 30000, // 30 seconds
            // SSL configuration (usually false for cPanel)
            ssl: false,
          },
  }),
  collections: [
    Pages,
    Posts,
    Media,
    Categories,
    Users,
    Events,
    Speakers,
    Counties,
    Programs,
    ProgramTypes,
    ThematicAreas,
    TargetAudience,
    Partners,
    Sponsors,
    SponsorshipPackages,
    IKIAAsset,
    Enquiry,
    DelegatePackages,
    Invoices,
    PesaflowNotifications,
    Exhibitors,
    NewsItem,
    PartnershipMatching,
    SuccessStories,
  ],
  // collections: [Pages, Posts, Media, Categories, Users, Events, Speakers, Counties, TargetAudience, Enquiry],
  cors: [getServerSideURL()].filter(Boolean),
  globals: [Header, Footer],
  endpoints: [
    // Health check endpoints
    {
      path: '/health',
      method: 'get',
      handler: healthCheckEndpoint as any,
    },
    {
      path: '/health/simple',
      method: 'get',
      handler: simpleHealthCheckEndpoint as any,
    },
    {
      path: '/events',
      method: 'get',
      handler: eventsHandler as any,
    },
    // Counties CRUD endpoints
    {
      path: '/counties',
      method: 'get',
      handler: countiesHandler as any,
    },
    {
      path: '/counties',
      method: 'post',
      handler: createCountyHandler as any,
    },
    {
      path: '/counties/:id',
      method: 'get',
      handler: countyByIdHandler as any,
    },
    {
      path: '/counties/:id',
      method: 'put',
      handler: updateCountyHandler as any,
    },
    {
      path: '/counties/:id',
      method: 'delete',
      handler: deleteCountyHandler as any,
    },
    {
      path: '/counties/bounds',
      method: 'get',
      handler: countiesInBoundsHandler as any,
    },
    {
      path: '/counties/:id/users',
      method: 'get',
      handler: countyUsersHandler as any,
    },
    {
      path: '/checkout',
      method: 'post',
      handler: pesaflowCheckoutEndpoint as any,
    },
    {
      path: '/payment/validate',
      method: 'post',
      handler: pesaflowValidatePaymentEndpoint,
    },
    {
      path: '/payment/confirm',
      method: 'post',
      handler: pesaflowConfirmPaymentEndpoint as any,
    },
    {
      path: '/invoice/payment/status',
      method: 'get',
      handler: pesaflowQueryPaymentStatusEndpoint as any,
    },
    // IPN Webhook
    {
      path: '/payment/ipn',
      method: 'post',
      handler: pesaflowIPNEndpoint as any,
    },
    {
      path: '/payment/ipn/stats',
      method: 'get',
      handler: ipnStatsEndpoint as any,
    },
    {
      path: '/payment/ipn/clear-cache',
      method: 'post',
      handler: clearIPNCacheEndpoint as any,
    },
    // SSO OAuth Endpoints
    {
      path: '/oauth/authorize',
      method: 'get',
      handler: ssoAuthorizeEndpoint as any,
    },
    {
      path: '/oauth/access-token',
      method: 'post',
      handler: ssoAccessTokenEndpoint as any,
    },
    {
      path: '/oauth/token/introspect',
      method: 'post',
      handler: ssoTokenIntrospectionEndpoint as any,
    },
    {
      path: '/userinfo',
      method: 'get',
      handler: ssoUserInfoEndpoint as any,
    },
    // USSD Integration
    {
      path: '/ussd/validate',
      method: 'post',
      handler: ussdIntegrationEndpoint as any,
    },
    {
      path: '/speakers',
      method: 'get',
      handler: speakersHandler as any,
    },
    {
      path: '/partners',
      method: 'get',
      handler: partnersHandler as any,
    },
    // Package Registration and Payment Endpoints
    {
      path: '/package-registration',
      method: 'post',
      handler: packageRegistrationEndpoint as any,
    },
    {
      path: '/enhanced-payment',
      method: 'post',
      handler: enhancedPaymentEndpoint as any,
    },
    // Test data seeding endpoint
    {
      path: '/seed-test-data',
      method: 'post',
      handler: seedTestDataEndpoint as any,
    },
    // User role management endpoint
    {
      path: '/update-user-role',
      method: 'post',
      handler: updateUserRoleEndpoint as any,
    },
    // Debug login endpoint to catch verification errors
    {
      path: '/debug-login',
      method: 'post',
      handler: debugLoginEndpoint as any,
    },
    // Check user status endpoint
    {
      path: '/check-user-status',
      method: 'post',
      handler: checkUserStatusEndpoint as any,
    },
    // Verify user endpoint (for debugging)
    {
      path: '/verify-user',
      method: 'post',
      handler: verifyUserEndpoint as any,
    },

    // Dedicated Pesaflow notification endpoint for successful payments
    {
      path: '/payment/callback/pesaflow/notification',
      method: 'post',
      handler: pesaflowNotificationEndpoint as any,
    },
    // Citizen registration with integrated payment processing
    {
      path: '/citizens/register',
      method: 'post',
      handler: citizenRegistrationEndpoint as any,
    },
    // Exhibitor registration with integrated payment processing
    {
      path: '/exhibitors/register',
      method: 'post',
      handler: exhibitorRegistrationEndpoint as any,
    },
    // Registration status check endpoint
    {
      path: '/citizens/registration-status/:invoiceId',
      method: 'get',
      handler: getRegistrationStatusEndpoint as any,
    },
    // User data endpoints
    {
      path: '/userdata/:userId',
      method: 'get',
      handler: getUserProfileEndpoint as any,
    },
    // Current authenticated user data
    {
      path: '/userdata/me',
      method: 'get',
      handler: getCurrentUserProfileEndpoint as any,
    },
    // Payload seed endpoint for admin dashboard
    {
      path: '/seed',
      method: 'post',
      handler: async (req: any) => {
        try {
          // Check if user is authenticated (optional security check)
          if (!req.user) {
            return new Response(
              JSON.stringify({
                success: false,
                error: 'Authentication required to seed database',
              }),
              {
                status: 401,
                headers: { 'Content-Type': 'application/json' },
              },
            )
          }

          req.payload.logger.info('Manual seed requested by user:', req.user.email)
          await seed({ payload: req.payload, req })

          return new Response(
            JSON.stringify({
              success: true,
              message: 'Database seeded successfully',
              timestamp: new Date().toISOString(),
            }),
            {
              status: 200,
              headers: { 'Content-Type': 'application/json' },
            },
          )
        } catch (error: any) {
          console.error('Seed error:', error)
          req.payload.logger.error('Seed error:', error)
          return new Response(
            JSON.stringify({
              success: false,
              error: error.message,
              timestamp: new Date().toISOString(),
            }),
            {
              status: 500,
              headers: { 'Content-Type': 'application/json' },
            },
          )
        }
      },
    },
  ],
  onInit: async (payload) => {
    // Check if database is empty and seed if needed
    const existingUsers = await payload.find({
      collection: 'users',
      limit: 1,
    })

    // Only seed if no users exist (first time setup)
    if (existingUsers.docs.length === 0) {
      payload.logger.info('Database appears to be empty, running initial seed...')
      try {
        await seed({ payload, req: {} as any })
        payload.logger.info('Initial seed completed successfully')
      } catch (error) {
        payload.logger.error('Error during initial seed:', error)
      }
    }
  },
  plugins: [
    ...plugins,
    // storage-adapter-placeholder
    // Only enable Vercel Blob storage if valid token is provided
    ...(process.env.BLOB_READ_WRITE_TOKEN &&
    process.env.BLOB_READ_WRITE_TOKEN.startsWith('vercel_blob_rw_')
      ? [
          vercelBlobStorage({
            collections: {
              media: true, // Enable for media collection
            },
            token: process.env.BLOB_READ_WRITE_TOKEN,
          }),
        ]
      : []),
  ],
  secret: process.env.PAYLOAD_SECRET,
  sharp,
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  jobs: {
    access: {
      run: ({ req }: { req: PayloadRequest }): boolean => {
        // Allow logged in users to execute this endpoint (default)
        if (req.user) return true

        // If there is no logged in user, then check
        // for the Vercel Cron secret to be present as an
        // Authorization header:
        const authHeader = req.headers.get('authorization')
        return authHeader === `Bearer ${process.env.CRON_SECRET}`
      },
    },
    tasks: [],
  },
})
