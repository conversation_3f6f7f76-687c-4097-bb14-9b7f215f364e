import { TrendingUp, Globe, Users, Award } from 'lucide-react'

export default function InvestHero() {
  return (
    <section className="relative min-h-[125vh] overflow-hidden pt-12">
      {/* Background Image with Overlays */}
      <div className="absolute inset-0">
        {/* Background Video */}
        <video
          autoPlay
          muted
          loop
          playsInline
          className="absolute inset-0 w-full h-full object-cover"
        >
          <source src="/landing-page-video.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* Dark Brown Overlay - reduced opacity for lighter background */}
        <div className="absolute inset-0 bg-[#5A1A10]/40"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 py-16">
        {/* Main Hero Container */}
        <div className="flex flex-col items-center justify-center min-h-[70vh] gap-8">
          {/* White Container Background for Text Content */}
          <div className="bg-white p-8 md:p-12 shadow-2xl max-w-4xl w-full text-center">
            {/* Enhanced Badge */}
            <div className="flex justify-center mb-6">
              <span
                className="bg-[#E8B32C] text-[#7E2518] font-bold px-4 py-2 text-sm shadow-lg border border-[#7E2518]/20 flex items-center space-x-2 uppercase"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                <Award className="w-5 h-5" />
                <span>IKIA Investment Platform</span>
                <div className="w-2 h-2 bg-[#159147] animate-pulse"></div>
              </span>
            </div>

            {/* Enhanced Main Title */}
            <div className="mb-8">
              <h1
                className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#7E2518] leading-tight mb-4"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                Unlocking Investment Opportunities In Kenya's Indigenous Wealth: A New Growth Area
                Of The Economy
              </h1>
              <p
                className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                Connect with verified investors and Indigenous Knowledge holders to create
                sustainable partnerships that preserve culture while generating meaningful returns.
              </p>
            </div>

            {/* Enhanced Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="/registration/form/investor">
                <button
                  className="bg-[#7E2518] hover:bg-[#5A1A10] text-white font-bold px-8 py-4 text-base transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center justify-center space-x-2"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  <TrendingUp className="w-5 h-5" />
                  <span>Register Now</span>
                </button>
              </a>

              <a href="/invest/success-stories">
                <button
                  className="bg-[#159147] hover:bg-[#0F6B35] text-white font-bold px-8 py-4 text-base transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center justify-center space-x-2"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  <Award className="w-5 h-5" />
                  <span>Explore Opportunities</span>
                </button>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Decorative Line */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#E8B32C] via-[#C86E36] to-[#159147]"></div>
    </section>
  )
}
