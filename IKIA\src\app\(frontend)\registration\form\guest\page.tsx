'use client'

import type React from 'react'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ArrowLeft, Users, ChevronRight, ChevronLeft } from 'lucide-react'
import { StepIndicator } from '@/modules/website/registration/components/StepIndicator'

// Define steps for citizen registration
const citizenSteps = [
  {
    id: 'personal-details',
    number: 1,
    title: 'Personal Details',
    description: 'Basic information and package selection',
  },
  {
    id: 'review-payment',
    number: 2,
    title: 'Review & Payment',
    description: 'Confirm details and complete payment',
  },
]

// Business types for the form
const businessTypes = [
  'Technology Startup',
  'Manufacturing',
  'Agriculture',
  'Services',
  'Retail',
  'Other',
]

interface County {
  id: string
  name: string
}

interface ServicePackage {
  id: string
  name: string
  description: string
  price: number
  currency: string
  features: Array<{ feature: string }>
  isActive: boolean
}

export default function GuestRegistrationPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [counties, setCounties] = useState<County[]>([])
  const [servicePackages, setServicePackages] = useState<ServicePackage[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone_number: '',
    id_number: '',
    county: '',
    business_type: '',
    registration_purpose: '',
    selected_package: '',
  })

  // Load counties and service packages on component mount
  useEffect(() => {
    loadCounties()
    loadServicePackages()
  }, [])

  const loadCounties = async () => {
    try {
      const response = await fetch('/api/counties')
      const result = await response.json()
      setCounties(result.docs || result)
    } catch (error) {
      console.error('Failed to load counties:', error)
      setError('Failed to load counties. Please refresh the page.')
    }
  }

  const loadServicePackages = async () => {
    try {
      const response = await fetch('/api/delegatepackages')
      const result = await response.json()
      // Transform delegate packages to match expected format
      const packages = (result.docs || result)
        .filter((pkg: ServicePackage) => pkg.isActive)
        .map((pkg: ServicePackage) => ({
          ...pkg,
          // Ensure price is a number for proper formatting
          price: typeof pkg.price === 'string' ? parseFloat(pkg.price) : pkg.price,
          // Add duration if not present
          duration: pkg.duration || 'One-time',
          // Ensure features is an array
          features: pkg.features || [],
        }))
      setServicePackages(packages)
    } catch (error) {
      console.error('Failed to load delegate packages:', error)
      setError('Failed to load delegate packages. Please refresh the page.')
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
    // Clear errors when user starts typing
    if (error) setError('')
  }

  const validateForm = () => {
    if (!formData.name.trim()) return 'Please enter your full name'
    if (!formData.email.trim()) return 'Please enter your email address'
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email))
      return 'Please enter a valid email address'
    if (!formData.phone_number.trim()) return 'Please enter your phone number'
    if (!/^254\d{9}$/.test(formData.phone_number))
      return 'Please enter a valid phone number (254XXXXXXXXX)'
    if (!formData.id_number.trim()) return 'Please enter your ID number'
    if (!/^\d{7,8}$/.test(formData.id_number)) return 'Please enter a valid ID number (7-8 digits)'
    if (!formData.county) return 'Please select your county'
    if (!formData.selected_package) return 'Please select a service package'
    return null
  }

  const nextStep = () => {
    const validationError = validateForm()
    if (validationError) {
      setError(validationError)
      return
    }

    if (currentStep < citizenSteps.length) {
      setCompletedSteps((prev) => [...prev, currentStep])
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const validationError = validateForm()
    if (validationError) {
      setError(validationError)
      return
    }

    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/citizens/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (result.success) {
        setSuccess(
          'Registration successful! Your login credentials have been sent to your email. Redirecting to payment gateway...',
        )

        // Redirect to payment after a short delay
        setTimeout(() => {
          window.location.href = result.checkout_url
        }, 2000)
      } else {
        throw new Error(result.error || 'Registration failed')
      }
    } catch (error: any) {
      console.error('Registration error:', error)
      setError(error.message || 'Registration failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8 font-myriad">
      {/* Enhanced Header with Homepage Design */}
      <div className="mb-12">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-8 font-myriad hover:bg-primary/10 text-primary"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Registration Types
        </Button>

        {/* Hero Section matching homepage style */}
        <div className="text-center mb-8 relative">
          <div className="relative bg-gradient-to-br from-background via-card to-muted/20 p-8 md:p-12 border border-border overflow-hidden main-shadow">
            {/* Heritage Elements */}
            <div className="absolute top-6 left-8 heritage-dot heritage-dot-secondary"></div>
            <div
              className="absolute top-12 right-12 heritage-dot heritage-dot-accent"
              style={{ animationDelay: '1s' }}
            ></div>
            <div
              className="absolute bottom-8 left-12 heritage-dot heritage-dot-primary"
              style={{ animationDelay: '2s' }}
            ></div>

            <div className="relative z-10">
              {/* Icon */}
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-secondary to-secondary/80 mb-6 shadow-lg">
                <Users className="w-8 h-8 text-secondary-foreground" />
              </div>

              <h1 className="text-3xl md:text-5xl font-bold text-foreground mb-4 font-myriad">
                Citizen <span className="text-secondary">Registration</span>
              </h1>

              <p className="text-base md:text-lg text-muted-foreground mb-4 font-myriad">
                Join the <span className="text-secondary font-semibold">IKIA Community</span> and
                access <span className="text-accent font-semibold">Premium Services</span>
              </p>

              <p className="text-muted-foreground text-sm italic font-myriad">
                &ldquo;Empowering <span className="text-secondary">Indigenous Knowledge</span> and{' '}
                <span className="text-primary">Innovation</span>&rdquo;
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Step Indicator */}
      <StepIndicator
        steps={citizenSteps}
        currentStep={currentStep}
        completedSteps={completedSteps}
        className="mb-8"
      />

      {/* Error/Success Messages */}
      {error && (
        <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
          <p className="text-destructive font-medium">{error}</p>
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-800 font-medium">{success}</p>
        </div>
      )}

      {/* Step Content */}
      <div className="space-y-8">
        {/* Step 1: Personal Details & Package Selection */}
        {currentStep === 1 && (
          <>
            {/* Personal Information */}
            <Card className="form-card main-shadow hover-shadow">
              <CardHeader>
                <CardTitle className="text-xl font-bold font-myriad text-primary">
                  👤 Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 font-myriad">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="name" className="font-myriad font-semibold text-primary">
                      Full Name *
                    </Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      required
                      className="form-input focus-shadow"
                      placeholder="Enter your full name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email" className="font-myriad font-semibold text-primary">
                      Email Address *
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                      className="form-input focus-shadow"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label
                      htmlFor="phone_number"
                      className="font-myriad font-semibold text-primary"
                    >
                      Phone Number *
                    </Label>
                    <Input
                      id="phone_number"
                      value={formData.phone_number}
                      onChange={(e) => handleInputChange('phone_number', e.target.value)}
                      required
                      className="form-input focus-shadow"
                      placeholder="254712345678"
                    />
                  </div>
                  <div>
                    <Label htmlFor="id_number" className="font-myriad font-semibold text-primary">
                      National ID Number *
                    </Label>
                    <Input
                      id="id_number"
                      value={formData.id_number}
                      onChange={(e) => handleInputChange('id_number', e.target.value)}
                      required
                      className="form-input focus-shadow"
                      placeholder="12345678"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="county" className="font-myriad font-semibold text-primary">
                      County *
                    </Label>
                    <Select
                      value={formData.county}
                      onValueChange={(value) => handleInputChange('county', value)}
                    >
                      <SelectTrigger className="form-input focus-shadow">
                        <SelectValue placeholder="Select your county" />
                      </SelectTrigger>
                      <SelectContent>
                        {counties.map((county) => (
                          <SelectItem key={county.id} value={county.id}>
                            {county.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Account Information */}
            <Card className="form-card main-shadow hover-shadow">
              <CardHeader>
                <CardTitle className="text-xl font-bold font-myriad text-primary">
                  🔐 Account Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 font-myriad">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">ℹ</span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-blue-800">Automatic Password Generation</h4>
                      <p className="text-sm text-blue-700 mt-1">
                        A secure 6-character password will be automatically generated and sent to
                        your email address after registration.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Business Information (Optional) */}
            <Card className="form-card main-shadow hover-shadow">
              <CardHeader>
                <CardTitle className="text-xl font-bold font-myriad text-primary">
                  🏢 Business Information (Optional)
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 font-myriad">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label
                      htmlFor="business_type"
                      className="font-myriad font-semibold text-primary"
                    >
                      Business Type
                    </Label>
                    <Select
                      value={formData.business_type}
                      onValueChange={(value) => handleInputChange('business_type', value)}
                    >
                      <SelectTrigger className="form-input focus-shadow">
                        <SelectValue placeholder="Select business type" />
                      </SelectTrigger>
                      <SelectContent>
                        {businessTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label
                      htmlFor="registration_purpose"
                      className="font-myriad font-semibold text-primary"
                    >
                      Registration Purpose
                    </Label>
                    <Input
                      id="registration_purpose"
                      value={formData.registration_purpose}
                      onChange={(e) => handleInputChange('registration_purpose', e.target.value)}
                      className="form-input focus-shadow"
                      placeholder="e.g., Business Registration, Investment Facilitation"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Service Package Selection */}
            <Card className="form-card main-shadow hover-shadow">
              <CardHeader>
                <CardTitle className="text-xl font-bold font-myriad text-primary">
                  📦 Select Service Package *
                </CardTitle>
              </CardHeader>
              <CardContent className="font-myriad">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {servicePackages.map((pkg) => (
                    <Card
                      key={pkg.id}
                      className={`relative cursor-pointer transition-all duration-300 hover:shadow-lg ${
                        formData.selected_package === pkg.id
                          ? 'ring-2 ring-primary shadow-lg bg-primary/5'
                          : 'hover:shadow-md'
                      }`}
                      onClick={() => handleInputChange('selected_package', pkg.id)}
                    >
                      <CardContent className="p-6">
                        <div className="text-center mb-4">
                          <h3 className="text-xl font-bold text-foreground mb-2 font-myriad">
                            {String(pkg.name || 'Package')}
                          </h3>
                          <div className="text-2xl font-bold text-primary mb-1">
                            {String(pkg.currency || 'KES')}{' '}
                            {Number(pkg.price || 0).toLocaleString()}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {String(pkg.duration || 'One-time')}
                          </div>
                        </div>

                        <div className="text-sm text-muted-foreground mb-4">
                          {String(pkg.description || '')}
                        </div>

                        {pkg.features && pkg.features.length > 0 && (
                          <ul className="space-y-2">
                            {pkg.features.slice(0, 3).map((feature, index) => (
                              <li key={index} className="flex items-center text-sm">
                                <div className="w-2 h-2 bg-primary rounded-full mr-2 flex-shrink-0"></div>
                                {typeof feature === 'string'
                                  ? feature
                                  : feature?.feature || 'Feature'}
                              </li>
                            ))}
                            {pkg.features.length > 3 && (
                              <li className="text-sm text-muted-foreground">
                                +{pkg.features.length - 3} more features
                              </li>
                            )}
                          </ul>
                        )}

                        {formData.selected_package === pkg.id && (
                          <div className="absolute top-3 right-3">
                            <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                              <div className="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {/* Step 2: Review & Payment */}
        {currentStep === 2 && (
          <>
            {/* Personal Information Review */}
            <Card className="form-card main-shadow hover-shadow">
              <CardHeader>
                <CardTitle className="text-xl font-bold font-myriad text-primary">
                  👤 Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 font-myriad">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  <div>
                    <Label className="font-semibold text-primary">Full Name</Label>
                    <p className="text-muted-foreground">{formData.name}</p>
                  </div>
                  <div>
                    <Label className="font-semibold text-primary">Email Address</Label>
                    <p className="text-muted-foreground">{formData.email}</p>
                  </div>
                  <div>
                    <Label className="font-semibold text-primary">Phone Number</Label>
                    <p className="text-muted-foreground">{formData.phone_number}</p>
                  </div>
                  <div>
                    <Label className="font-semibold text-primary">National ID Number</Label>
                    <p className="text-muted-foreground">{formData.id_number}</p>
                  </div>
                  <div>
                    <Label className="font-semibold text-primary">County</Label>
                    <p className="text-muted-foreground">
                      {counties.find((c) => c.id === formData.county)?.name || 'N/A'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Business Information Review (if provided) */}
            {(formData.business_type || formData.registration_purpose) && (
              <Card className="form-card main-shadow hover-shadow">
                <CardHeader>
                  <CardTitle className="text-xl font-bold font-myriad text-primary">
                    🏢 Business Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6 font-myriad">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {formData.business_type && (
                      <div>
                        <Label className="font-semibold text-primary">Business Type</Label>
                        <p className="text-muted-foreground">{formData.business_type}</p>
                      </div>
                    )}
                    {formData.registration_purpose && (
                      <div>
                        <Label className="font-semibold text-primary">Registration Purpose</Label>
                        <p className="text-muted-foreground">{formData.registration_purpose}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Package Selection Review */}
            <Card className="form-card main-shadow hover-shadow">
              <CardHeader>
                <CardTitle className="text-xl font-bold font-myriad text-primary">
                  📦 Selected Service Package
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 font-myriad">
                {(() => {
                  const selectedPackage = servicePackages.find(
                    (p) => p.id === formData.selected_package,
                  )
                  return selectedPackage ? (
                    <div className="bg-primary/5 border border-primary/20 rounded-lg p-8">
                      <div className="flex justify-between items-start mb-6">
                        <div>
                          <h3 className="text-xl font-bold text-primary mb-2">
                            {String(selectedPackage.name)}
                          </h3>
                          <p className="text-muted-foreground">
                            {String(selectedPackage.description)}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-primary">
                            {String(selectedPackage.currency)}{' '}
                            {Number(selectedPackage.price).toLocaleString()}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {String(selectedPackage.duration || 'One-time')}
                          </div>
                        </div>
                      </div>

                      {selectedPackage.features && selectedPackage.features.length > 0 && (
                        <div>
                          <h4 className="font-semibold text-primary mb-3">Package Features:</h4>
                          <ul className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {selectedPackage.features.map((feature, index) => (
                              <li key={index} className="flex items-center text-sm">
                                <div className="w-2 h-2 bg-primary rounded-full mr-3 flex-shrink-0"></div>
                                {typeof feature === 'string'
                                  ? feature
                                  : feature?.feature || 'Feature'}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No package selected</p>
                  )
                })()}
              </CardContent>
            </Card>

            {/* Next Steps */}
            <Card className="form-card main-shadow hover-shadow">
              <CardHeader>
                <CardTitle className="text-xl font-bold font-myriad text-primary">
                  🚀 Next Steps
                </CardTitle>
              </CardHeader>
              <CardContent className="font-myriad">
                <div className="bg-secondary/10 border border-secondary/20 rounded-lg p-8">
                  <h3 className="font-semibold text-primary mb-6">Complete Your Registration:</h3>
                  <ol className="list-decimal list-inside space-y-4 text-muted-foreground">
                    <li className="flex items-start">
                      <span className="mr-2">1.</span>
                      <span>Click "Complete Registration & Pay" below to proceed</span>
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2">2.</span>
                      <span>You will be securely redirected to the payment gateway</span>
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2">3.</span>
                      <span>Complete payment to activate your selected service package</span>
                    </li>
                    <li className="flex items-start">
                      <span className="mr-2">4.</span>
                      <span>
                        Receive confirmation email with your account details and access instructions
                      </span>
                    </li>
                  </ol>

                  <div className="mt-6 p-4 bg-accent/10 border border-accent/20 rounded-lg">
                    <p className="text-sm text-muted-foreground">
                      <strong className="text-accent">Note:</strong> Your account will be created
                      immediately, but service access will be activated only after successful
                      payment completion.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-8">
          <Button
            type="button"
            variant="outline"
            onClick={currentStep === 1 ? () => router.push('/registration') : prevStep}
            className="flex items-center space-x-2 font-myriad border-primary text-primary hover:bg-primary/10"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>{currentStep === 1 ? 'Back to Registration Types' : 'Previous'}</span>
          </Button>

          {currentStep === 1 ? (
            <Button
              type="button"
              onClick={nextStep}
              className="form-button flex items-center space-x-2"
            >
              <span>Continue to Review</span>
              <ChevronRight className="w-4 h-4" />
            </Button>
          ) : (
            <Button
              type="button"
              onClick={handleSubmit}
              disabled={loading}
              className="form-button flex items-center space-x-2"
            >
              <span>{loading ? 'Processing...' : 'Complete Registration & Pay'}</span>
              <ChevronRight className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
