'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Search, X } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

interface FilterSectionProps {
  searchTerm: string
  onSearchChange: (value: string) => void
  selectedCounty: string
  onCountyChange: (value: string) => void
  selectedThematicArea: string
  onThematicAreaChange: (value: string) => void
  counties: string[]
  thematicAreas: string[]
  onClearFilters: () => void
  activeFiltersCount: number
}

export default function FilterSection({
  searchTerm,
  onSearchChange,
  selectedCounty,
  onCountyChange,
  selectedThematicArea,
  onThematicAreaChange,
  counties,
  thematicAreas,
  onClearFilters,
  activeFiltersCount,
}: FilterSectionProps) {
  return (
    <div className="mb-8 space-y-6">
      {/* Horizontal Filter Row */}
      <div className="bg-gray-50 rounded-none p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
          {/* Search Input */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Search
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="Search by name or description..."
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border-gray-300 focus:border-[#7E2518] focus:ring-[#7E2518] rounded-none"
              />
            </div>
          </div>

          {/* County Filter */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Filter by County
            </label>
            <Select value={selectedCounty} onValueChange={onCountyChange}>
              <SelectTrigger className="w-full rounded-none">
                <SelectValue placeholder="All Counties" />
              </SelectTrigger>
              <SelectContent className="rounded-none">
                <SelectItem value="all">All Counties</SelectItem>
                {counties.map((county) => (
                  <SelectItem key={county} value={county}>
                    {county}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Thematic Area Filter */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Filter by Thematic Area
            </label>
            <Select value={selectedThematicArea} onValueChange={onThematicAreaChange}>
              <SelectTrigger className="w-full rounded-none">
                <SelectValue placeholder="All Thematic Areas" />
              </SelectTrigger>
              <SelectContent className="rounded-none">
                <SelectItem value="all">All Thematic Areas</SelectItem>
                {thematicAreas.map((area) => (
                  <SelectItem key={area} value={area}>
                    {area}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Clear Filters Button */}
        {activeFiltersCount > 0 && (
          <div className="flex justify-center mt-6">
            <Button
              variant="outline"
              onClick={onClearFilters}
              className="border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white rounded-none"
            >
              <X className="w-4 h-4 mr-2" />
              Clear All Filters ({activeFiltersCount})
            </Button>
          </div>
        )}
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap justify-center gap-2">
          {selectedCounty !== 'all' && (
            <Badge variant="secondary" className="bg-[#159147] text-white">
              County: {selectedCounty}
              <button
                onClick={() => onCountyChange('all')}
                className="ml-2 hover:bg-white/20 rounded-full p-0.5"
              >
                <X className="w-3 h-3" />
              </button>
            </Badge>
          )}
          {selectedThematicArea !== 'all' && (
            <Badge variant="secondary" className="bg-[#81B1DB] text-white">
              Theme: {selectedThematicArea}
              <button
                onClick={() => onThematicAreaChange('all')}
                className="ml-2 hover:bg-white/20 rounded-full p-0.5"
              >
                <X className="w-3 h-3" />
              </button>
            </Badge>
          )}
          {searchTerm && (
            <Badge variant="secondary" className="bg-[#E8B32C] text-[#7E2518]">
              Search: "{searchTerm}"
              <button
                onClick={() => onSearchChange('')}
                className="ml-2 hover:bg-white/20 rounded-full p-0.5"
              >
                <X className="w-3 h-3" />
              </button>
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}
