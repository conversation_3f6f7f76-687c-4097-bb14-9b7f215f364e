'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight, ArrowLeft, ExternalLink } from 'lucide-react'

interface CountyInfo {
  name: string
  code: string
  population?: string
  area?: string
}

interface CountyCarouselProps {
  county: CountyInfo
  onBack: () => void
}

// Simplified data structure
const countyData: { [key: string]: any } = {
  "Murang'a": {
    exhibitors: [
      {
        id: 1,
        name: "Murang'a Coffee Heritage Cooperative",
        category: 'Traditional Agriculture',
        description: 'Traditional coffee processing methods passed down through generations',
        image: '/placeholder.svg?height=200&width=300&text=Coffee+Heritage',
      },
      {
        id: 2,
        name: 'Kikuyu Traditional Pottery',
        category: 'Cultural Heritage',
        description: 'Ancient pottery techniques using local clay and traditional firing methods',
        image: '/placeholder.svg?height=200&width=300&text=Traditional+Pottery',
      },
      {
        id: 3,
        name: "Murang'a Herbal Medicine Collective",
        category: 'Traditional Medicine',
        description: 'Indigenous medicinal plants and traditional healing practices',
        image: '/placeholder.svg?height=200&width=300&text=Herbal+Medicine',
      },
    ],
  },
  // Default data for other counties
  default: {
    exhibitors: [
      {
        id: 1,
        name: 'Traditional Crafts Collective',
        category: 'Cultural Heritage',
        description: 'Traditional crafts and artisan skills from the local community',
        image: '/placeholder.svg?height=200&width=300&text=Traditional+Crafts',
      },
      {
        id: 2,
        name: 'Agricultural Innovation Hub',
        category: 'Traditional Agriculture',
        description: 'Indigenous farming techniques and crop varieties',
        image: '/placeholder.svg?height=200&width=300&text=Agricultural+Innovation',
      },
    ],
  },
}

export default function CountyCarousel({ county, onBack }: CountyCarouselProps) {
  const [currentSlide, setCurrentSlide] = useState(0)

  // Reset carousel to first slide when county changes
  useEffect(() => {
    setCurrentSlide(0)
  }, [county.name])

  const data = countyData[county.name] || {
    ...countyData.default,
    exhibitors: countyData.default.exhibitors.map((exhibitor) => ({
      ...exhibitor,
      name: exhibitor.name.replace(
        'Traditional Crafts Collective',
        county.name + ' Traditional Crafts',
      ),
    })),
  }

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % data.exhibitors.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + data.exhibitors.length) % data.exhibitors.length)
  }

  const goToSlide = (index: number) => {
    setCurrentSlide(index)
  }

  const currentExhibitor = data.exhibitors[currentSlide]

  return (
    <div className="space-y-6">
      {/* Simple Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={onBack} className="text-[#7E2518] hover:bg-[#7E2518]/10">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Map
        </Button>
        <div className="text-right">
          <h2 className="text-xl font-bold text-[#7E2518]">{county.name} County</h2>
          <p className="text-sm text-gray-600">
            #{county.code} • {data.exhibitors.length} Exhibitors
          </p>
        </div>
      </div>

      {/* Minimalistic Carousel */}
      <div className="bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden">
        {/* Simple Header with Navigation */}
        <div className="bg-[#7E2518] p-4 text-white">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Exhibitor Showcase</h3>
            <div className="flex items-center space-x-4">
              <span className="text-sm">
                {currentSlide + 1} / {data.exhibitors.length}
              </span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={prevSlide}
                  className="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors"
                >
                  <ChevronLeft className="w-4 h-4 text-white" />
                </button>
                <button
                  onClick={nextSlide}
                  className="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors"
                >
                  <ChevronRight className="w-4 h-4 text-white" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Clean Content */}
        <div className="p-6">
          <div className="grid md:grid-cols-2 gap-6">
            {/* Image */}
            <div>
              <img
                src={currentExhibitor.image || '/placeholder.svg'}
                alt={currentExhibitor.name}
                className="w-full h-48 object-cover rounded-lg"
              />
            </div>

            {/* Content */}
            <div className="space-y-4">
              <div>
                <span className="inline-block px-3 py-1 bg-[#E8B32C] text-[#7E2518] text-sm font-medium rounded-full mb-3">
                  {currentExhibitor.category}
                </span>
                <h4 className="text-xl font-bold text-[#7E2518] mb-3">{currentExhibitor.name}</h4>
                <p className="text-gray-600 leading-relaxed">{currentExhibitor.description}</p>
              </div>

              <Button className="bg-[#159147] hover:bg-[#159147]/90 text-white">
                <ExternalLink className="w-4 h-4 mr-2" />
                View Details
              </Button>
            </div>
          </div>
        </div>

        {/* Clean Slide Indicators */}
        <div className="flex justify-center space-x-2 pb-4">
          {data.exhibitors.map((_: any, index: number) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`transition-all duration-300 ${
                index === currentSlide
                  ? 'w-6 h-2 bg-[#7E2518] rounded-full'
                  : 'w-2 h-2 bg-gray-300 hover:bg-gray-400 rounded-full'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Simple Stats */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 text-center">
          <div className="text-2xl font-bold text-[#7E2518]">{data.exhibitors.length}</div>
          <div className="text-sm text-gray-600">Exhibitors</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 text-center">
          <div className="text-2xl font-bold text-[#159147]">3</div>
          <div className="text-sm text-gray-600">Categories</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 text-center">
          <div className="text-2xl font-bold text-[#E8B32C]">5</div>
          <div className="text-sm text-gray-600">Opportunities</div>
        </div>
      </div>
    </div>
  )
}
