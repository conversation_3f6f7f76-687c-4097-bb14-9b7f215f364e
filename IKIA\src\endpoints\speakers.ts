import type { PayloadRequest } from 'payload'

interface TransformedMedia {
  id: string
  filename: string
  url: string
  alt?: string
  width?: number
  height?: number
}

interface TransformedSpeaker {
  id: string
  name: string
  title?: string
  company?: string
  bio?: string
  photo?: string // only a URL now
  slug?: string
}

interface SpeakersResponse {
  speakers: TransformedSpeaker[]
  totalSpeakers: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

const extractTextFromLexical = (richTextData: any): string => {
  try {
    if (!richTextData || typeof richTextData === 'string') return richTextData || ''
    const children = richTextData?.root?.children
    if (Array.isArray(children)) return extractTextFromChildren(children)
    return ''
  } catch {
    return ''
  }
}

const extractTextFromChildren = (children: any[]): string =>
  children
    .map((child) => {
      if (child.type === 'text') return child.text || ''
      if (Array.isArray(child.children)) return extractTextFromChildren(child.children)
      return ''
    })
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim()

const transformMedia = (media: any): string | undefined => {
  if (!media || typeof media === 'string') return undefined
  return media.url || `/api/media/file/${media.filename}`
}

const transformSpeaker = (speaker: any): TransformedSpeaker => ({
  id: speaker.id,
  name: speaker.name,
  title: speaker.title,
  company: speaker.company,
  bio: extractTextFromLexical(speaker.bio),
  photo: transformMedia(speaker.photo),
  slug: speaker.slug,
})

// --- Main handler ---
export const speakersHandler = async (req: PayloadRequest, res: any): Promise<any> => {
  try {
    const { payload } = req
    const {
      page = '1',
      limit = '50',
      sort = 'name', // Optional: allow ?sort=name
      search,
    } = req.query as Record<string, string>

    const where: any = {}

    if (search) {
      where.name = {
        contains: search,
      }
    }

    // Safely parse integers with fallbacks
    const parsedPage = parseInt(page) || 1
    const parsedLimit = parseInt(limit) || 50

    const speakersResult = await payload.find({
      collection: 'speakers',
      where,
      page: parsedPage,
      limit: parsedLimit,
      sort: sort as any,
      depth: 1,
    })

    const transformed: TransformedSpeaker[] = speakersResult.docs.map(transformSpeaker)

    const currentPage = parsedPage
    const currentLimit = parsedLimit
    const totalPages = Math.ceil(speakersResult.totalDocs / currentLimit)

    const response: SpeakersResponse = {
      speakers: transformed,
      totalSpeakers: speakersResult.totalDocs,
      page: currentPage,
      limit: currentLimit,
      totalPages,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    }

    res.status(200).json(response)
  } catch (error) {
    console.error('Error in speakers endpoint:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
