# Payment Collections Schema

## Overview
This document defines the complete Payload CMS collection schemas required for the payment integration system.

## 1. Enhanced Users Collection

```typescript
import type { CollectionConfig } from 'payload'
import { authenticated } from '../access/authenticated'

export const Users: CollectionConfig = {
  slug: 'users',
  auth: {
    tokenExpiration: 7200, // 2 hours
    maxLoginAttempts: 5,
    lockTime: 600000, // 10 minutes
    verify: {
      generateEmailHTML: ({ token, user }) => {
        return `<p>Please verify your email by clicking <a href="${process.env.PAYLOAD_PUBLIC_SERVER_URL}/verify?token=${token}">here</a></p>`
      },
      generateEmailSubject: () => 'Verify your email'
    },
    forgotPassword: {
      generateEmailHTML: ({ token, user }) => {
        return `<p>Reset your password by clicking <a href="${process.env.PAYLOAD_PUBLIC_SERVER_URL}/reset-password?token=${token}">here</a></p>`
      },
      generateEmailSubject: () => 'Reset your password'
    }
  },
  access: {
    admin: authenticated,
    create: () => true, // Allow registration
    delete: authenticated,
    read: authenticated,
    update: ({ req }) => {
      // Users can update their own profile
      if (req.user) {
        return {
          id: {
            equals: req.user.id
          }
        }
      }
      return false
    }
  },
  admin: {
    defaultColumns: ['name', 'email', 'phone_number', 'county', 'role'],
    useAsTitle: 'name'
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      validate: (val) => {
        if (!val || val.length < 2) {
          return 'Name must be at least 2 characters long'
        }
        return true
      }
    },
    {
      name: 'role',
      type: 'select',
      options: [
        { label: 'Citizen', value: 'citizen' },
        { label: 'Business', value: 'business' },
        { label: 'Admin', value: 'admin' },
        { label: 'Payment Processor', value: 'payment_processor' }
      ],
      defaultValue: 'citizen',
      required: true
    },
    {
      name: 'county',
      type: 'relationship',
      relationTo: 'counties',
      label: 'County',
      admin: {
        description: 'The county this user is associated with'
      }
    },
    // eCitizen Integration Fields
    {
      name: 'ecitizen_id',
      type: 'text',
      unique: true,
      admin: {
        readOnly: true,
        description: 'eCitizen user ID from SSO'
      }
    },
    {
      name: 'id_number',
      type: 'text',
      unique: true,
      validate: (val) => {
        if (val && !/^\d{7,8}$/.test(val)) {
          return 'ID number must be 7-8 digits'
        }
        return true
      },
      admin: {
        description: 'National ID number'
      }
    },
    {
      name: 'phone_number',
      type: 'text',
      validate: (val) => {
        if (val && !/^254\d{9}$/.test(val)) {
          return 'Phone number must be in format 254XXXXXXXXX'
        }
        return true
      },
      admin: {
        description: 'Phone number in international format (254XXXXXXXXX)'
      }
    },
    {
      name: 'phone_verified',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        readOnly: true,
        description: 'Whether phone number has been verified'
      }
    },
    // Payment Profile
    {
      name: 'payment_profile',
      type: 'group',
      label: 'Payment Profile',
      fields: [
        {
          name: 'preferred_currency',
          type: 'select',
          options: [
            { label: 'Kenyan Shilling (KES)', value: 'KES' },
            { label: 'US Dollar (USD)', value: 'USD' }
          ],
          defaultValue: 'KES'
        },
        {
          name: 'daily_limit',
          type: 'number',
          defaultValue: 50000,
          min: 0,
          max: 1000000,
          admin: {
            description: 'Daily payment limit in KES'
          }
        },
        {
          name: 'monthly_limit',
          type: 'number',
          defaultValue: 500000,
          min: 0,
          max: 10000000,
          admin: {
            description: 'Monthly payment limit in KES'
          }
        },
        {
          name: 'single_transaction_limit',
          type: 'number',
          defaultValue: 100000,
          min: 0,
          max: 1000000,
          admin: {
            description: 'Single transaction limit in KES'
          }
        }
      ]
    },
    // Notification Preferences
    {
      name: 'notification_preferences',
      type: 'group',
      label: 'Notification Preferences',
      fields: [
        {
          name: 'email_notifications',
          type: 'checkbox',
          defaultValue: true,
          label: 'Email Notifications'
        },
        {
          name: 'sms_notifications',
          type: 'checkbox',
          defaultValue: true,
          label: 'SMS Notifications'
        },
        {
          name: 'payment_confirmations',
          type: 'checkbox',
          defaultValue: true,
          label: 'Payment Confirmations'
        },
        {
          name: 'marketing_emails',
          type: 'checkbox',
          defaultValue: false,
          label: 'Marketing Emails'
        }
      ]
    },
    // User Statistics (Read-only)
    {
      name: 'statistics',
      type: 'group',
      label: 'Payment Statistics',
      admin: {
        readOnly: true
      },
      fields: [
        {
          name: 'total_payments',
          type: 'number',
          defaultValue: 0,
          admin: { readOnly: true }
        },
        {
          name: 'total_amount_paid',
          type: 'number',
          defaultValue: 0,
          admin: { readOnly: true }
        },
        {
          name: 'successful_payments',
          type: 'number',
          defaultValue: 0,
          admin: { readOnly: true }
        },
        {
          name: 'failed_payments',
          type: 'number',
          defaultValue: 0,
          admin: { readOnly: true }
        },
        {
          name: 'last_payment_date',
          type: 'date',
          admin: { readOnly: true }
        }
      ]
    }
  ],
  hooks: {
    beforeChange: [
      ({ data, operation }) => {
        // Ensure phone number format
        if (data.phone_number && !data.phone_number.startsWith('254')) {
          if (data.phone_number.startsWith('0')) {
            data.phone_number = '254' + data.phone_number.substring(1)
          } else if (data.phone_number.startsWith('+254')) {
            data.phone_number = data.phone_number.substring(1)
          }
        }
      }
    ],
    afterChange: [
      ({ doc, operation }) => {
        if (operation === 'create') {
          console.log(`New user registered: ${doc.email}`)
          // Send welcome email
        }
      }
    ]
  },
  timestamps: true
}
```

## 3. Payments Collection

```typescript
import type { CollectionConfig } from 'payload'
import { authenticated } from '../access/authenticated'

export const Payments: CollectionConfig = {
  slug: 'payments',
  access: {
    create: () => false, // Only created via webhooks/system
    read: ({ req }) => {
      if (req.user?.role === 'admin' || req.user?.role === 'payment_processor') return true
      return {
        'invoice.user': {
          equals: req.user?.id
        }
      }
    },
    update: ({ req }) => req.user?.role === 'admin' || req.user?.role === 'payment_processor',
    delete: () => false // Never delete payment records
  },
  admin: {
    defaultColumns: ['transaction_id', 'invoice', 'amount_paid', 'status', 'processed_at'],
    useAsTitle: 'transaction_id',
    group: 'Payments'
  },
  fields: [
    {
      name: 'transaction_id',
      type: 'text',
      unique: true,
      required: true,
      admin: {
        description: 'Unique transaction identifier from payment gateway'
      }
    },
    {
      name: 'invoice',
      type: 'relationship',
      relationTo: 'invoices',
      required: true,
      admin: {
        description: 'Associated invoice for this payment'
      }
    },
    {
      name: 'amount_paid',
      type: 'number',
      required: true,
      min: 0,
      admin: {
        description: 'Amount actually paid'
      }
    },
    {
      name: 'currency',
      type: 'select',
      options: [
        { label: 'Kenyan Shilling (KES)', value: 'KES' },
        { label: 'US Dollar (USD)', value: 'USD' }
      ],
      defaultValue: 'KES',
      required: true
    },
    {
      name: 'payment_method',
      type: 'select',
      options: [
        { label: 'M-PESA', value: 'M-PESA' },
        { label: 'Credit Card', value: 'CARD' },
        { label: 'Bank Transfer', value: 'BANK_TRANSFER' },
        { label: 'Airtel Money', value: 'AIRTEL_MONEY' },
        { label: 'T-Kash', value: 'T_KASH' }
      ],
      admin: {
        description: 'Payment method used'
      }
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Pending', value: 'pending' },
        { label: 'Processing', value: 'processing' },
        { label: 'Settled', value: 'settled' },
        { label: 'Failed', value: 'failed' },
        { label: 'Refunded', value: 'refunded' },
        { label: 'Disputed', value: 'disputed' }
      ],
      defaultValue: 'pending',
      required: true
    },
    {
      name: 'customer_info',
      type: 'group',
      label: 'Customer Information',
      fields: [
        {
          name: 'name',
          type: 'text',
          admin: { description: 'Customer name from payment gateway' }
        },
        {
          name: 'phone_number',
          type: 'text',
          admin: { description: 'Customer phone number' }
        },
        {
          name: 'email',
          type: 'email',
          admin: { description: 'Customer email address' }
        }
      ]
    },
    {
      name: 'gateway_data',
      type: 'group',
      label: 'Gateway Data',
      admin: {
        readOnly: true
      },
      fields: [
        {
          name: 'gateway_transaction_id',
          type: 'text',
          admin: { readOnly: true }
        },
        {
          name: 'gateway_reference',
          type: 'text',
          admin: { readOnly: true }
        },
        {
          name: 'gateway_response',
          type: 'json',
          admin: { readOnly: true }
        },
        {
          name: 'webhook_data',
          type: 'json',
          admin: { readOnly: true }
        }
      ]
    },
    {
      name: 'processed_at',
      type: 'date',
      admin: {
        readOnly: true,
        description: 'When payment was processed'
      }
    },
    {
      name: 'reconciled',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Whether payment has been reconciled'
      }
    },
    {
      name: 'reconciled_at',
      type: 'date',
      admin: {
        description: 'When payment was reconciled'
      }
    },
    {
      name: 'notes',
      type: 'textarea',
      admin: {
        description: 'Internal notes about this payment'
      }
    }
  ],
  hooks: {
    afterChange: [
      async ({ doc, operation, req }) => {
        if (operation === 'create' || (operation === 'update' && doc.status === 'settled')) {
          // Update invoice payment summary
          const payload = req.payload
          const invoice = await payload.findByID({
            collection: 'invoices',
            id: doc.invoice
          })

          if (invoice) {
            const payments = await payload.find({
              collection: 'payments',
              where: {
                invoice: {
                  equals: doc.invoice
                },
                status: {
                  equals: 'settled'
                }
              }
            })

            const totalPaid = payments.docs.reduce((sum, payment) => sum + payment.amount_paid, 0)
            const paymentCount = payments.docs.length

            await payload.update({
              collection: 'invoices',
              id: doc.invoice,
              data: {
                payment_summary: {
                  amount_paid: totalPaid,
                  amount_remaining: invoice.amount - totalPaid,
                  payment_count: paymentCount,
                  last_payment_date: new Date()
                },
                status: totalPaid >= invoice.amount ? 'settled' : 'partial'
              }
            })
          }
        }
      }
    ]
  },
  timestamps: true
}
```

## 4. Payment Logs Collection

```typescript
import type { CollectionConfig } from 'payload'

export const PaymentLogs: CollectionConfig = {
  slug: 'payment-logs',
  access: {
    create: () => false, // Only created by system
    read: ({ req }) => req.user?.role === 'admin' || req.user?.role === 'payment_processor',
    update: () => false,
    delete: () => false
  },
  admin: {
    defaultColumns: ['event_type', 'invoice_reference', 'user_id', 'created_at'],
    useAsTitle: 'event_type',
    group: 'Payments'
  },
  fields: [
    {
      name: 'event_type',
      type: 'select',
      options: [
        { label: 'Payment Initiated', value: 'payment_initiated' },
        { label: 'Payment Completed', value: 'payment_completed' },
        { label: 'Payment Failed', value: 'payment_failed' },
        { label: 'Webhook Received', value: 'webhook_received' },
        { label: 'Webhook Processed', value: 'webhook_processed' },
        { label: 'Error Occurred', value: 'error_occurred' },
        { label: 'Refund Processed', value: 'refund_processed' },
        { label: 'Status Updated', value: 'status_updated' }
      ],
      required: true
    },
    {
      name: 'invoice_reference',
      type: 'text',
      admin: {
        description: 'Invoice or payment reference'
      }
    },
    {
      name: 'user_id',
      type: 'text',
      admin: {
        description: 'User ID associated with the event'
      }
    },
    {
      name: 'transaction_id',
      type: 'text',
      admin: {
        description: 'Transaction ID if applicable'
      }
    },
    {
      name: 'data',
      type: 'json',
      admin: {
        description: 'Event data and context'
      }
    },
    {
      name: 'ip_address',
      type: 'text',
      admin: {
        description: 'IP address of the request'
      }
    },
    {
      name: 'user_agent',
      type: 'text',
      admin: {
        description: 'User agent string'
      }
    },
    {
      name: 'severity',
      type: 'select',
      options: [
        { label: 'Info', value: 'info' },
        { label: 'Warning', value: 'warning' },
        { label: 'Error', value: 'error' },
        { label: 'Critical', value: 'critical' }
      ],
      defaultValue: 'info'
    },
    {
      name: 'processed',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Whether this log entry has been processed/reviewed'
      }
    }
  ],
  timestamps: true
}
```
```
## 2. Invoices Collection
```typescript
import type { CollectionConfig } from 'payload'
import { authenticated } from '../access/authenticated'

export const Invoices: CollectionConfig = {
  slug: 'invoices',
  access: {
    create: authenticated,
    read: ({ req }) => {
      if (req.user?.role === 'admin') return true
      return {
        user: {
          equals: req.user?.id
        }
      }
    },
    update: ({ req }) => {
      if (req.user?.role === 'admin') return true
      return {
        user: {
          equals: req.user?.id
        }
      }
    },
    delete: ({ req }) => req.user?.role === 'admin'
  },
  admin: {
    defaultColumns: ['invoice_number', 'user', 'amount', 'status', 'created_at'],
    useAsTitle: 'invoice_number',
    group: 'Payments'
  },
  fields: [
    {
      name: 'invoice_number',
      type: 'text',
      unique: true,
      required: true,
      admin: {
        readOnly: true,
        description: 'Auto-generated invoice number'
      }
    },
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        description: 'User who owns this invoice'
      }
    },
    {
      name: 'amount',
      type: 'number',
      required: true,
      min: 1,
      max: 10000000,
      admin: {
        description: 'Invoice amount in the specified currency'
      }
    },
    {
      name: 'currency',
      type: 'select',
      options: [
        { label: 'Kenyan Shilling (KES)', value: 'KES' },
        { label: 'US Dollar (USD)', value: 'USD' }
      ],
      defaultValue: 'KES',
      required: true
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
      admin: {
        description: 'Description of goods/services'
      }
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Pending Payment', value: 'pending' },
        { label: 'Processing', value: 'processing' },
        { label: 'Paid', value: 'settled' },
        { label: 'Partially Paid', value: 'partial' },
        { label: 'Failed', value: 'failed' },
        { label: 'Expired', value: 'expired' },
        { label: 'Cancelled', value: 'cancelled' }
      ],
      defaultValue: 'draft',
      required: true,
      admin: {
        description: 'Current status of the invoice'
      }
    },
    {
      name: 'payment_reference',
      type: 'text',
      unique: true,
      admin: {
        readOnly: true,
        description: 'Unique payment reference for tracking'
      }
    },
    {
      name: 'due_date',
      type: 'date',
      admin: {
        description: 'When payment is due'
      }
    },
    // Line Items
    {
      name: 'line_items',
      type: 'array',
      label: 'Line Items',
      fields: [
        {
          name: 'description',
          type: 'text',
          required: true
        },
        {
          name: 'quantity',
          type: 'number',
          required: true,
          min: 1,
          defaultValue: 1
        },
        {
          name: 'unit_price',
          type: 'number',
          required: true,
          min: 0
        },
        {
          name: 'total',
          type: 'number',
          required: true,
          min: 0,
          admin: {
            readOnly: true,
            description: 'Calculated as quantity × unit_price'
          }
        }
      ],
      admin: {
        description: 'Detailed breakdown of invoice items'
      }
    },
    // Pesaflow Integration Data
    {
      name: 'pesaflow_data',
      type: 'group',
      label: 'Pesaflow Data',
      admin: {
        readOnly: true
      },
      fields: [
        {
          name: 'bill_ref_number',
          type: 'text',
          admin: { readOnly: true }
        },
        {
          name: 'checkout_url',
          type: 'text',
          admin: { readOnly: true }
        },
        {
          name: 'gateway_response',
          type: 'json',
          admin: { readOnly: true }
        },
        {
          name: 'last_gateway_sync',
          type: 'date',
          admin: { readOnly: true }
        }
      ]
    },
    // Payment Summary
    {
      name: 'payment_summary',
      type: 'group',
      label: 'Payment Summary',
      admin: {
        readOnly: true
      },
      fields: [
        {
          name: 'amount_paid',
          type: 'number',
          defaultValue: 0,
          admin: { readOnly: true }
        },
        {
          name: 'amount_remaining',
          type: 'number',
          admin: { readOnly: true }
        },
        {
          name: 'payment_count',
          type: 'number',
          defaultValue: 0,
          admin: { readOnly: true }
        },
        {
          name: 'last_payment_date',
          type: 'date',
          admin: { readOnly: true }
        }
      ]
    },
    // Metadata
    {
      name: 'metadata',
      type: 'json',
      admin: {
        description: 'Additional data for integration purposes'
      }
    }
  ],
  hooks: {
    beforeChange: [
      ({ data, operation }) => {
        if (operation === 'create') {
          // Generate invoice number
          data.invoice_number = `INV-${Date.now()}`
          // Generate payment reference
          data.payment_reference = `REF-${Date.now()}`
        }
        
        // Calculate line item totals
        if (data.line_items) {
          data.line_items = data.line_items.map(item => ({
            ...item,
            total: item.quantity * item.unit_price
          }))
          
          // Update invoice amount based on line items
          const calculatedAmount = data.line_items.reduce((sum, item) => sum + item.total, 0)
          if (calculatedAmount > 0) {
            data.amount = calculatedAmount
          }
        }
        
        // Calculate remaining amount
        if (data.payment_summary) {
          data.payment_summary.amount_remaining = data.amount - (data.payment_summary.amount_paid || 0)
        }
      }
    ],
    afterChange: [
      ({ doc, operation }) => {
        if (operation === 'create') {
          console.log(`New invoice created: ${doc.invoice_number}`)
        }
        
        // Update user statistics
        if (doc.status === 'settled') {
          // Update user payment statistics
          // This would be implemented as a separate function
        }
      }
    ]
  },
  timestamps: true
}
```


## 📋 Environment Variables Setup

**IMPORTANT:** All URLs and sensitive values are fetched from environment variables on the backend. No hardcoded defaults are provided for security reasons.

Create a `.env` file with the following variables:

```env
# Payload CMS
PAYLOAD_SECRET=your-payload-secret

# Pesaflow Payment Integration
PESAFLOW_API_CLIENT_ID=your_pesaflow_client_id
PESAFLOW_CLIENT_SECRET=your_pesaflow_secret
PESAFLOW_CLIENT_KEY=your_pesaflow_key
PESAFLOW_UAT_SERVER_URL=https://your-pesaflow-server.com

# Pesaflow Checkout Configuration
PESAFLOW_REQUEST_SERVICE_ID=your_service_id
PESAFLOW_BILL_DESC=Default bill description
PESAFLOW_NOTIFICATION_URL=https://yoursite.com/api/payment/ipn
PESAFLOW_CALLBACK_SUCCESS_URL=https://yoursite.com/payment/success
PESAFLOW_PICTURE_URL=https://yoursite.com/logo.png

# Note: Gateway URL is constructed automatically as: PESAFLOW_UAT_SERVER_URL/api/PaymentAPI/checkout
# User data (clientMSISDN, clientName, etc.) is passed from frontend requests

# eCitizen SSO Integration (All URLs required - no defaults)
ECITIZEN_CLIENT_ID=your_ecitizen_client_id
ECITIZEN_CLIENT_SECRET=your_ecitizen_secret
ECITIZEN_AUTHORIZATION_URL=https://accounts.ecitizen.go.ke/oauth/authorize
ECITIZEN_TOKEN_URL=https://accounts.ecitizen.go.ke/oauth/access-token
ECITIZEN_INTROSPECTION_URL=https://accounts.ecitizen.go.ke/api/oauth/token/introspect
ECITIZEN_USERINFO_URL=https://accounts.ecitizen.go.ke/api/userinfo

# eCitizen USSD Integration
ECITIZEN_MERCHANT_KEY=your_merchant_key
ECITIZEN_MERCHANT_SECRET=your_merchant_secret
```

## 🚀 Quick Start

1. **Install dependencies:**
```bash
npm install
```

2. **Set up environment variables:**
```bash
cp .env.example .env
# Edit .env with your credentials
```

3. **Start the server:**
```bash
npm run dev
```

4. **Test an endpoint:**
```bash
curl -X GET http://localhost:3000/api/events
```

## 📊 Response Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 302 | Redirect (OAuth) |
| 400 | Bad Request - Missing/Invalid parameters |
| 401 | Unauthorized - Invalid credentials/hash |
| 404 | Not Found - Resource doesn't exist |
| 500 | Internal Server Error |

## 🔍 Debugging

Enable debug logging by setting:
```env
DEBUG=true
```

Check server logs for detailed request/response information and hash generation details.

## 📞 Support

For issues with:
- **Pesaflow integration**: Contact Pesaflow support
- **eCitizen SSO**: Contact eCitizen support
- **API implementation**: Check server logs and verify environment variables
