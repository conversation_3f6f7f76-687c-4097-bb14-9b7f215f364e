import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

interface Partner {
  id: string
  name: string
  company?: string
  sponsorshipTier?: string
  category?: string[]
  logo?: string
  about?: string
  contact?: {
    location?: {
      country?: string | null
      city?: string | null
      address?: string | null
    }
    emails?: Array<{ email: string }>
    phones?: Array<{ phone: string }>
  }
}

interface PartnersResponse {
  partners: Partner[]
  totalPartners: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface CreatePartnerRequest {
  name: string
  company?: string
  logo?: number
  about?: string
  category?: string[]
  sponsorshipTier?: string
  contact?: any
  impact?: any[]
  recentProjects?: any[]
  awardsRecognitions?: any[]
  notableContributions?: any[]
}

export const partnersApi = createApi({
  reducerPath: 'partnersApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  tagTypes: ['Partner'],
  endpoints: (builder) => ({
    getPartners: builder.query<PartnersResponse, { limit?: number; page?: number; search?: string }>({
      query: ({ limit = 0, page = 1, search = '' } = {}) => {
        const params = new URLSearchParams()
        if (limit) params.append('limit', limit.toString())
        if (page > 1) params.append('page', page.toString())
        if (search) params.append('search', search)
        return `partners?${params.toString()}`
      },
      providesTags: ['Partner'],
    }),
    createPartner: builder.mutation<Partner, CreatePartnerRequest>({
      query: (newPartner) => ({
        url: 'partners',
        method: 'POST',
        body: newPartner,
      }),
      invalidatesTags: ['Partner'],
    }),
    updatePartner: builder.mutation<Partner, { id: string; data: Partial<CreatePartnerRequest> }>({
      query: ({ id, data }) => ({
        url: `partners/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Partner'],
    }),
    deletePartner: builder.mutation<void, string>({
      query: (id) => ({
        url: `partners/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Partner'],
    }),
  }),
})

export const {
  useGetPartnersQuery,
  useCreatePartnerMutation,
  useUpdatePartnerMutation,
  useDeletePartnerMutation
} = partnersApi


