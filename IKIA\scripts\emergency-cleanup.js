#!/usr/bin/env node

/**
 * Emergency Memory Cleanup Script
 * Forces garbage collection and clears caches
 * 
 * Usage: node scripts/emergency-cleanup.js
 */

const BASE_URL = 'http://localhost:3000'

async function forceGarbageCollection() {
  console.log('🧹 Attempting to force garbage collection...')
  
  try {
    // Try to trigger GC via health endpoint
    const response = await fetch(`${BASE_URL}/api/health-check`)
    const data = await response.json()
    
    console.log('📊 Memory before cleanup:')
    console.log(`   Heap Used: ${Math.round(data.memory.heapUsed / 1024 / 1024)} MB`)
    console.log(`   External: ${Math.round(data.memory.external / 1024 / 1024)} MB`)
    console.log(`   Array Buffers: ${Math.round(data.memory.arrayBuffers / 1024 / 1024)} MB`)
    
    // Wait a moment for potential cleanup
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Check memory again
    const response2 = await fetch(`${BASE_URL}/api/health-check`)
    const data2 = await response2.json()
    
    console.log('\n📊 Memory after cleanup:')
    console.log(`   Heap Used: ${Math.round(data2.memory.heapUsed / 1024 / 1024)} MB`)
    console.log(`   External: ${Math.round(data2.memory.external / 1024 / 1024)} MB`)
    console.log(`   Array Buffers: ${Math.round(data2.memory.arrayBuffers / 1024 / 1024)} MB`)
    
    const heapReduction = data.memory.heapUsed - data2.memory.heapUsed
    const externalReduction = data.memory.external - data2.memory.external
    
    if (heapReduction > 0 || externalReduction > 0) {
      console.log('\n✅ Memory cleanup successful!')
      console.log(`   Heap reduced by: ${Math.round(heapReduction / 1024 / 1024)} MB`)
      console.log(`   External reduced by: ${Math.round(externalReduction / 1024 / 1024)} MB`)
    } else {
      console.log('\n⚠️ No significant memory reduction detected')
      console.log('   Consider restarting the application')
    }
    
  } catch (error) {
    console.error('❌ Failed to perform cleanup:', error.message)
    console.log('\n🔄 RECOMMENDATION: Restart the development server')
    console.log('   1. Stop server: Ctrl+C')
    console.log('   2. Restart: npm run dev')
  }
}

async function checkSystemMemory() {
  console.log('\n🖥️ System Memory Check:')
  
  try {
    // Check available system memory (Linux/Mac)
    const { exec } = require('child_process')
    const util = require('util')
    const execPromise = util.promisify(exec)
    
    try {
      const { stdout } = await execPromise('free -m')
      const lines = stdout.split('\n')
      const memLine = lines[1].split(/\s+/)
      const total = parseInt(memLine[1])
      const used = parseInt(memLine[2])
      const available = parseInt(memLine[6] || memLine[3])
      
      console.log(`   Total System RAM: ${total} MB`)
      console.log(`   Used System RAM: ${used} MB (${Math.round(used/total*100)}%)`)
      console.log(`   Available RAM: ${available} MB`)
      
      if (available < 500) {
        console.log('   🚨 WARNING: Low system memory available!')
      }
    } catch (error) {
      console.log('   ℹ️ System memory check not available on this platform')
    }
  } catch (error) {
    console.log('   ℹ️ System memory check failed')
  }
}

async function emergencyCleanup() {
  console.log('🚨 EMERGENCY MEMORY CLEANUP')
  console.log('='.repeat(50))
  console.log(`📅 ${new Date().toLocaleString()}`)
  
  await checkSystemMemory()
  await forceGarbageCollection()
  
  console.log('\n💡 RECOMMENDATIONS:')
  console.log('   • Restart the development server if memory is still high')
  console.log('   • Avoid uploading large images during development')
  console.log('   • Use the memory monitor: node scripts/monitor-memory.js')
  console.log('   • Consider using Vercel Blob storage for production')
  
  console.log('\n' + '='.repeat(50))
}

// Run cleanup
emergencyCleanup().catch(console.error)
