import Image from 'next/image'
import Link from 'next/link'
import { Card } from '@/components/ui/card'
import { Twitter, Facebook, Linkedin, Instagram } from 'lucide-react'

export interface Speaker {
  id: number
  name: string
  title: string
  company: string
  organisation: string
  category: 'keynote' | 'plenary' | 'otherspeakers'
  topic: string
  bio: {
    root: {
      type: string
      format: string
      indent: number
      version: number
      children: BioNode[]
      direction?: string | null
      textFormat?: number
      textStyle?: string
    }
  }
  photo: {
    id: number
    alt: string
    caption: string | null
    updatedAt: string
    createdAt: string
    url: string
    thumbnailURL: string | null
    filename: string
    mimeType: string
    filesize: number
    width: number
    height: number
    focalX: number
    focalY: number
    sizes: {
      thumbnail: PhotoSize
      square: PhotoSize
      small: PhotoSize
      medium: PhotoSize
      large: PhotoSize
      xlarge: PhotoSize
      og: PhotoSize
    }
  }
  socials: Social[]
  featuredEvents: any[]
  slug: string
  slugLock: boolean
  updatedAt: string
  createdAt: string
}

export interface BioNode {
  type: string
  format: string
  indent: number
  version: number
  mode?: string
  text?: string
  style?: string
  detail?: number
  id?: string
  fields?: {
    url: string
    newTab: boolean
    linkType: string
  }
  children?: BioNode[]
  direction?: string | null
  textFormat?: number
}

export interface PhotoSize {
  url: string | null
  width: number | null
  height: number | null
  mimeType: string | null
  filesize: number | null
  filename: string | null
}

export interface Social {
  platform: 'twitter' | 'facebook' | 'linkedin' | 'instagram'
  url: string
}

interface SpeakerCardProps {
  speaker: Speaker
  size: string
}

export default function SpeakerCard({ speaker, size }: SpeakerCardProps) {
  return (
    <div className="flex flex-col items-center p-6 text-center rounded-lg bg-none">
      <div className={`relative mb-6 ${size}`}>
        <Image
          src={speaker.photo?.url || '/placeholder.svg'}
          alt={speaker.name}
          fill
          className="rounded-full object-cover border-2 border-gray-200"
        />
      </div>
      <h3 className="text-lg font-bold text-gray-900 mb-1">{speaker.name}</h3>
      <p className="text-gray-600 text-sm mb-4">{speaker.title}</p>
    </div>
  )
}
