# eCitizen API Endpoints Implementation

This document outlines all the implemented eCitizen API endpoints based on the official eCitizen API documentation.

## 🔐 Environment Variables Required

### Pesaflow Payment Integration
```env
PESAFLOW_API_CLIENT_ID=your_api_client_id
PESAFLOW_CLIENT_SECRET=your_client_secret
PESAFLOW_CLIENT_KEY=your_client_key
PESAFLOW_UAT_SERVER_URL=https://your-pesaflow-server.com
```

### eCitizen SSO Integration
```env
ECITIZEN_CLIENT_ID=your_client_id
ECITIZEN_CLIENT_SECRET=your_client_secret
ECITIZEN_AUTHORIZATION_URL=https://accounts.ecitizen.go.ke/oauth/authorize
ECITIZEN_TOKEN_URL=https://accounts.ecitizen.go.ke/oauth/access-token
ECITIZEN_INTROSPECTION_URL=https://accounts.ecitizen.go.ke/api/oauth/token/introspect
ECITIZEN_USERINFO_URL=https://accounts.ecitizen.go.ke/api/userinfo
```

### eCitizen USSD Integration
```env
ECITIZEN_MERCHANT_KEY=your_merchant_key
ECITIZEN_MERCHANT_SECRET=your_merchant_secret
```

## 💳 Payment Endpoints

### 1. Payment Validation
**Endpoint:** `POST /api/payment/validate`

**Frontend Request:**
```json
{
  "ref_no": "INV123456",
  "amount": "100.00",
  "currency": "KES"
}
```

**Server Response:**
```json
{
  "status": 200,
  "description": "Bill Found",
  "data": {
    "amount": "100.00",
    "name": "John Doe",
    "currency": "KES"
  }
}
```

### 2. Payment Confirmation
**Endpoint:** `POST /api/payment/confirm`

**Frontend Request:**
```json
{
  "ref_no": "INV123456",
  "amount": "100.00",
  "currency": "KES",
  "gateway_transaction_id": "TXN********9",
  "gateway_transaction_date": "2024-01-01 12:30:01",
  "customer_name": "John Doe",
  "customer_account_number": "************"
}
```

**Server Response:**
```json
{
  "status": 200,
  "description": "Success"
}
```

### 3. Payment Status Query
**Endpoint:** `GET /api/invoice/payment/status`

**Query Parameters:**
- `api_client_id` - Handled by server
- `ref_no` - Invoice reference
- `secure_hash` - Generated by server

**Server Response:**
```json
{
  "status": "settled",
  "ref_no": "INV123456",
  "name": "John Doe",
  "currency": "KES",
  "client_invoice_ref": "CLIENT-REF-123",
  "payment_date": "2024-01-01 12:30:01",
  "amount_paid": "100.00",
  "amount_expected": "100.00"
}
```

### 4. Instant Payment Notification (IPN)
**Endpoint:** `POST /api/payment/ipn`

**Webhook Payload from Pesaflow:**
```json
{
  "payment_channel": "M-PESA",
  "client_invoice_ref": "CLIENT-REF-123",
  "currency": "KES",
  "amount_paid": "100.00",
  "invoice_amount": "100.00",
  "status": "settled",
  "invoice_number": "INV123",
  "payment_date": "2024-01-01 12:30:01",
  "last_payment_amount": "100.00",
  "secure_hash": "generated_hash"
}
```

## 🔐 SSO OAuth Endpoints

### 5. OAuth Authorization
**Endpoint:** `GET /api/oauth/authorize`

**Query Parameters:**
```
?client_id=your_client_id
&redirect_uri=http://localhost:3000/callback
&response_type=code
&scope=openid
&state=random_state
&code_challenge=challenge (optional for PKCE)
&code_challenge_method=S256 (optional for PKCE)
```

**Response:** Redirects to eCitizen authorization page

### 6. Access Token
**Endpoint:** `POST /api/oauth/access-token`

**Request:**
```json
{
  "client_id": "your_client_id",
  "client_secret": "your_client_secret",
  "grant_type": "authorization_code",
  "code": "authorization_code",
  "redirect_uri": "http://localhost:3000/callback",
  "code_verifier": "verifier" // optional for PKCE
}
```

### 7. Token Introspection
**Endpoint:** `POST /api/oauth/token/introspect`

**Request:**
```json
{
  "token": "access_token_to_introspect"
}
```

### 8. User Information
**Endpoint:** `GET /api/userinfo`

**Query Parameters:**
- `access_token` - Valid access token

**Response:**
```json
{
  "id": 1,
  "id_number": "********",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "mobile_number": "************",
  "account_type": "citizen"
}
```

## 📱 USSD Integration

### 9. USSD Validation
**Endpoint:** `POST /api/ussd/validate`

**Request from eCitizen:**
```json
{
  "reference": "INV123456",
  "service_code": "SVC001",
  "secure_hash": "generated_hash"
}
```

**Response:**
```json
{
  "display_info": {
    "Invoice": "INV123456",
    "Amount": "KES 1,500.00",
    "Service": "Business License",
    "Status": "Pending Payment"
  },
  "valid": true
}
```

## 🛒 Existing Endpoints

### 10. Checkout
**Endpoint:** `POST /api/checkout`

### 11. Events
**Endpoint:** `GET /api/events`

### 12. Counties
**Endpoint:** `GET /api/counties`

## 🔒 Security Features

1. **Server-side hash generation** - All sensitive credentials stay on server
2. **Environment variable protection** - API keys and secrets in env vars
3. **Hash verification** - All incoming requests verified with secure hashes
4. **Input validation** - Comprehensive field validation on all endpoints
5. **Error handling** - Proper error responses without exposing internals

## 📝 Implementation Notes

- All endpoints use environment variables for sensitive API values
- Hash generation follows eCitizen specifications exactly
- Frontend only sends required business data, server handles authentication
- Proper error responses with user-friendly messages
- Comprehensive logging for debugging and monitoring
- Modular endpoint structure for easy maintenance

## 🧪 Testing

All endpoints have been tested and are working correctly with proper validation and error handling.
