'use client'

import { Card } from '@/components/ui/card'
import Link from 'next/link'
import { useGetProgramsQuery } from '@/lib/api/programsApi'
import type { Program } from '@/lib/api/programsApi'
import { AlertTriangle, Calendar } from 'lucide-react'

interface _Speaker {
  name: string
  title: string
  company: string
  image: string
}

interface AgendaContentProps {
  activeDay: string
  filter: string
  thematic: string
  topic: string
}

// Helper function to extract plain text from rich text
const extractPlainText = (richText: Program['description']): string => {
  try {
    if (richText?.root?.children && Array.isArray(richText.root.children)) {
      return richText.root.children
        .map((child) => {
          if (
            child &&
            typeof child === 'object' &&
            'children' in child &&
            Array.isArray(child.children)
          ) {
            return child.children
              .map((textNode) => {
                if (textNode && typeof textNode === 'object' && 'text' in textNode) {
                  return String(textNode.text || '')
                }
                return ''
              })
              .join('')
          }
          if (child && typeof child === 'object' && 'text' in child) {
            return String(child.text || '')
          }
          return ''
        })
        .join(' ')
        .trim()
    }
    return ''
  } catch {
    return ''
  }
}

// Helper function to format time display
const formatTimeRange = (startTime?: string | null, endTime?: string | null): string => {
  if (!startTime && !endTime) return ''
  if (startTime && endTime) return `${startTime} - ${endTime}`
  if (startTime) return `${startTime}`
  return ''
}

// Helper function to format date for display
const formatDateForDisplay = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    })
  } catch {
    return dateString
  }
}

// Helper function to get date range for a full day
const getDateRangeForDay = (date: string) => {
  const start = `${date}T00:00:00.000Z`
  const nextDay = new Date(new Date(date).getTime() + 86400000).toISOString().split('T')[0]
  const end = `${nextDay}T00:00:00.000Z`
  return { start, end }
}

// Helper function to get conference dates
const getConferenceDates = () => {
  return {
    'Day 1': '2025-11-19',
    'Day 2': '2025-11-20',
    'Day 3': '2025-11-21',
  }
}

// Helper function to get formatted date display
const getFormattedDate = (day: string) => {
  const dates = {
    'Day 1': 'NOV 19, 2025',
    'Day 2': 'NOV 20, 2025',
    'Day 3': 'NOV 21, 2025',
  }
  return dates[day as keyof typeof dates] || 'NOV 19, 2025'
}

// Helper function to get day label
const getDayLabel = (day: string) => {
  const labels = {
    'Day 1': 'Opening Day',
    'Day 2': 'Innovation Summit',
    'Day 3': 'Panel Discussions',
  }
  return labels[day as keyof typeof labels] || 'Conference Day'
}

// Helper function to get program type info
const getProgramTypeInfo = (type: Program['type']): { name: string; color: string } => {
  if (typeof type === 'object' && type !== null) {
    return {
      name: type.name,
      color: type.color || 'bg-[#22b573]',
    }
  }

  // Fallback for legacy string types
  return { name: String(type), color: 'bg-[#22b573]' }
}

// Helper function to get thematic area info
const getThematicAreaInfo = (
  thematicArea: Program['thematicArea'],
): { name: string; color: string } | null => {
  if (typeof thematicArea === 'object' && thematicArea !== null) {
    return {
      name: thematicArea.name,
      color: thematicArea.color || 'bg-blue-50 border-blue-200 text-blue-700',
    }
  }
  return null
}

export default function AgendaContent({ activeDay, filter, thematic, topic }: AgendaContentProps) {
  const conferenceDates = getConferenceDates()
  const selectedDate = conferenceDates[activeDay as keyof typeof conferenceDates]

  // Get date range for proper filtering
  const { start, end } = getDateRangeForDay(selectedDate)

  // Build where conditions based on filters
  const whereConditions: Record<string, any> = {
    date: {
      greater_than_equal: start,
      less_than: end,
    },
  }

  // Add program type filter (using ID)
  if (thematic !== 'all') {
    whereConditions.type = { equals: parseInt(thematic) }
  }

  // Add thematic area filter (using ID)
  if (topic !== 'all') {
    whereConditions.thematicArea = { equals: parseInt(topic) }
  }

  // Add speaker filter (using ID)
  if (filter !== 'all') {
    whereConditions.speakers = { in: [parseInt(filter)] }
  }

  // Get programs from API for the selected date with filters
  const { data, error, isLoading } = useGetProgramsQuery({
    limit: 50,
    sort: 'date,startTime',
    where: selectedDate ? whereConditions : undefined,
  })

  const programs = data?.docs || []
  const dayPrograms = programs

  // Get current day info
  const currentDate = getFormattedDate(activeDay)
  const currentLabel = getDayLabel(activeDay)

  // Loading state
  if (isLoading) {
    return (
      <div className="bg-gray-50 py-16">
        <div className="max-w-5xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2
              className="text-3xl font-bold text-gray-900 mb-2"
              style={{ fontFamily: 'Myriad Pro Bold, Myriad Pro, Arial, sans-serif' }}
            >
              AGENDA
            </h2>
            <div className="bg-white rounded-lg shadow-sm border inline-block px-8 py-4">
              <div className="animate-pulse">
                <div className="h-6 bg-gray-200 rounded w-32 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-24"></div>
              </div>
            </div>
          </div>
          <div className="space-y-6">
            {[...Array(5)].map((_, index) => (
              <Card key={index} className="p-8 bg-white shadow-sm animate-pulse">
                <div className="flex gap-6">
                  <div className="w-24 h-10 bg-gray-200 rounded"></div>
                  <div className="flex-1 space-y-3">
                    <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-full"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="bg-gray-50 py-16">
        <div className="max-w-5xl mx-auto px-4">
          <div className="text-center">
            <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h3 className="font-bold text-xl text-gray-900 mb-2">Unable to Load Programs</h3>
            <p className="text-gray-600 mb-6">
              We&apos;re having trouble loading the conference programs. Please try again later.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-[#7E2518] text-white font-semibold px-6 py-3 hover:bg-[#7E2518]/90 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Use API data if available, otherwise fallback to static data
  const hasApiData = dayPrograms.length > 0

  return (
    <div className="bg-gray-50 py-16">
      <div className="max-w-5xl mx-auto px-4">
        {/* Day Header */}
        <div className="text-center mb-12">
          <h2
            className="text-3xl font-bold text-gray-900 mb-2"
            style={{ fontFamily: 'Myriad Pro Bold, Myriad Pro, Arial, sans-serif' }}
          >
            AGENDA
          </h2>
          <div className="bg-white rounded-lg shadow-sm border inline-block px-8 py-4">
            <div
              className="font-bold text-xl text-gray-900"
              style={{ fontFamily: 'Myriad Pro Bold, Myriad Pro, Arial, sans-serif' }}
            >
              {currentDate}
            </div>
            <div
              className="font-medium"
              style={{
                color: '#7E2518',
                fontFamily: 'Myriad Pro Medium, Myriad Pro, Arial, sans-serif',
              }}
            >
              {currentLabel}
            </div>
          </div>
        </div>

        {/* Day Schedule */}
        <div className="space-y-6">
          {hasApiData ? (
            // Render API data
            dayPrograms.map((program) => {
              const timeRange = formatTimeRange(program.startTime, program.endTime)
              const description = extractPlainText(program.description)
              const typeInfo = getProgramTypeInfo(program.type)
              const thematicAreaInfo = getThematicAreaInfo(program.thematicArea)

              return (
                <Link key={program.id} href={`/programs/${program.id}`}>
                  <Card className="p-4 sm:p-6 md:p-8 bg-white shadow-sm hover:shadow-md transition-shadow">
                    <div className="flex flex-col md:flex-row gap-4 md:gap-6">
                      {/* Time Block */}
                      <div className="flex-shrink-0 mb-2 md:mb-0">
                        <div
                          className={`px-4 py-2 rounded-lg text-sm font-medium text-center ${typeInfo.color}`}
                          style={{
                            fontFamily: 'Myriad Pro Medium, Myriad Pro, Arial, sans-serif',
                          }}
                        >
                          <div>{formatDateForDisplay(program.date)}</div>
                          <div>{timeRange || 'Time TBA'}</div>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="flex-1">
                        {/* Session Title */}
                        <h3
                          className="font-bold text-lg sm:text-xl text-gray-900 mb-2 sm:mb-3"
                          style={{ fontFamily: 'Myriad Pro Bold, Myriad Pro, Arial, sans-serif' }}
                        >
                          {program.title}
                        </h3>

                        {/* Thematic Area Tag */}
                        {thematicAreaInfo && (
                          <div className="mb-3">
                            <span
                              className={`px-2 py-1 rounded text-xs font-medium border ${thematicAreaInfo.color}`}
                              style={{
                                fontFamily: 'Myriad Pro Medium, Myriad Pro, Arial, sans-serif',
                              }}
                            >
                              {thematicAreaInfo.name}
                            </span>
                          </div>
                        )}

                        {/* Description */}
                        {description && (
                          <p
                            className="text-gray-600 mb-3 sm:mb-4 leading-relaxed text-sm sm:text-base"
                            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                          >
                            {description}
                          </p>
                        )}

                        {/* Speakers */}
                        {program.speakers && program.speakers.length > 0 && (
                          <div className="space-y-4">
                            {program.speakers.length <= 2 ? (
                              // Large speaker cards for 1-2 speakers
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                                {program.speakers.map((speaker, speakerIndex) => (
                                  <div
                                    key={speakerIndex}
                                    className="flex items-center gap-3 sm:gap-4 p-3 sm:p-4 bg-gray-50 rounded-lg"
                                  >
                                    <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-gray-200 flex items-center justify-center">
                                      <span className="text-xs font-medium text-gray-600">
                                        {speaker.name.charAt(0)}
                                      </span>
                                    </div>
                                    <div>
                                      <div
                                        className="font-semibold text-gray-900 text-sm sm:text-base"
                                        style={{
                                          fontFamily:
                                            'Myriad Pro Semibold, Myriad Pro, Arial, sans-serif',
                                        }}
                                      >
                                        {speaker.name}
                                      </div>
                                      <div
                                        className="text-xs sm:text-sm text-gray-600"
                                        style={{
                                          fontFamily:
                                            'Myriad Pro Medium, Myriad Pro, Arial, sans-serif',
                                        }}
                                      >
                                        {speaker.title}
                                      </div>
                                      <div
                                        className="text-xs sm:text-sm text-gray-500"
                                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                                      >
                                        {speaker.company}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              // Panel layout for 3+ speakers
                              <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                                <div
                                  className="text-xs sm:text-sm font-medium text-gray-700 mb-2 sm:mb-3"
                                  style={{
                                    fontFamily: 'Myriad Pro Medium, Myriad Pro, Arial, sans-serif',
                                  }}
                                >
                                  Panel Speakers:
                                </div>
                                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-3 sm:gap-4">
                                  {program.speakers.map((speaker, speakerIndex) => (
                                    <div key={speakerIndex} className="text-center">
                                      <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gray-200 mx-auto mb-1 sm:mb-2 flex items-center justify-center">
                                        <span className="text-xs font-medium text-gray-600">
                                          {speaker.name.charAt(0)}
                                        </span>
                                      </div>
                                      <div
                                        className="text-xs font-medium text-gray-900"
                                        style={{
                                          fontFamily:
                                            'Myriad Pro Semibold, Myriad Pro, Arial, sans-serif',
                                        }}
                                      >
                                        {speaker.name}
                                      </div>
                                      <div
                                        className="text-xs text-gray-600"
                                        style={{
                                          fontFamily:
                                            'Myriad Pro Medium, Myriad Pro, Arial, sans-serif',
                                        }}
                                      >
                                        {speaker.title}
                                      </div>
                                      <div
                                        className="text-xs text-gray-500"
                                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                                      >
                                        {speaker.company}
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        )}

                        {/* Additional Program Info */}
                        <div className="mt-4 flex flex-wrap gap-2">
                          {program.isParallel && (
                            <span className="text-xs bg-orange-50 text-orange-600 px-2 py-1 rounded border border-orange-200">
                              Parallel Session
                            </span>
                          )}
                          {program.registrationRequired && (
                            <span className="text-xs bg-blue-50 text-blue-600 px-2 py-1 rounded border border-blue-200">
                              Registration Required
                            </span>
                          )}
                          <span className="text-xs bg-gray-50 text-gray-600 px-2 py-1 rounded border border-gray-200">
                            {program.venue}
                          </span>
                        </div>
                      </div>
                    </div>
                  </Card>
                </Link>
              )
            })
          ) : (
            // No agenda message
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Calendar className="w-8 h-8 text-gray-400" />
                </div>
                <h3
                  className="text-xl font-bold text-gray-900 mb-2"
                  style={{ fontFamily: 'Myriad Pro Bold, Myriad Pro, Arial, sans-serif' }}
                >
                  No Agenda Yet
                </h3>
                <p
                  className="text-gray-600"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  The agenda for {activeDay} is still being finalized. Please check back later for
                  updates.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
