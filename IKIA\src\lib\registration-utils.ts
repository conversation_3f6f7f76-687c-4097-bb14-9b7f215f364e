// Registration utility functions

export interface FormData {
  // Personal Information
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  organization?: string
  position?: string
  country?: string
  
  // Registration Type
  registrationType?: 'delegate' | 'exhibitor' | 'vip' | 'sponsor'
  
  // Package Selection
  selectedPackage?: string
  
  // Additional Information
  dietaryRequirements?: string
  accessibilityNeeds?: string
  emergencyContact?: string
  emergencyPhone?: string
  
  // Investment Information (for investors)
  investmentAreas?: string[]
  investmentTypes?: string[]
  investmentRange?: string
  
  // Sponsorship Information
  sponsorshipTier?: string
  sponsorshipBenefits?: string[]
  
  // Group Registration
  isGroupRegistration?: boolean
  groupSize?: number
  groupMembers?: Array<{
    firstName: string
    lastName: string
    email: string
    position: string
  }>
}

// Storage keys
const STORAGE_KEYS = {
  FORM_DATA: 'ikia_registration_form_data',
  CURRENT_STEP: 'ikia_registration_current_step',
  REGISTRATION_ID: 'ikia_registration_id'
}

// Save form data to localStorage
export const saveFormDataToStorage = (data: Partial<FormData>): void => {
  try {
    const existingData = loadFormDataFromStorage()
    const updatedData = { ...existingData, ...data }
    localStorage.setItem(STORAGE_KEYS.FORM_DATA, JSON.stringify(updatedData))
  } catch (error) {
    console.error('Error saving form data to storage:', error)
  }
}

// Load form data from localStorage
export const loadFormDataFromStorage = (): FormData => {
  try {
    const data = localStorage.getItem(STORAGE_KEYS.FORM_DATA)
    return data ? JSON.parse(data) : {}
  } catch (error) {
    console.error('Error loading form data from storage:', error)
    return {}
  }
}

// Clear form data from localStorage
export const clearFormDataFromStorage = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEYS.FORM_DATA)
    localStorage.removeItem(STORAGE_KEYS.CURRENT_STEP)
    localStorage.removeItem(STORAGE_KEYS.REGISTRATION_ID)
  } catch (error) {
    console.error('Error clearing form data from storage:', error)
  }
}

// Save current step
export const saveCurrentStep = (step: number): void => {
  try {
    localStorage.setItem(STORAGE_KEYS.CURRENT_STEP, step.toString())
  } catch (error) {
    console.error('Error saving current step:', error)
  }
}

// Load current step
export const loadCurrentStep = (): number => {
  try {
    const step = localStorage.getItem(STORAGE_KEYS.CURRENT_STEP)
    return step ? parseInt(step, 10) : 1
  } catch (error) {
    console.error('Error loading current step:', error)
    return 1
  }
}

// Generate registration ID
export const generateRegistrationId = (): string => {
  const timestamp = Date.now().toString(36)
  const randomStr = Math.random().toString(36).substring(2, 8)
  return `IKIA-${timestamp}-${randomStr}`.toUpperCase()
}

// Save registration ID
export const saveRegistrationId = (id: string): void => {
  try {
    localStorage.setItem(STORAGE_KEYS.REGISTRATION_ID, id)
  } catch (error) {
    console.error('Error saving registration ID:', error)
  }
}

// Load registration ID
export const loadRegistrationId = (): string | null => {
  try {
    return localStorage.getItem(STORAGE_KEYS.REGISTRATION_ID)
  } catch (error) {
    console.error('Error loading registration ID:', error)
    return null
  }
}

// Validate email format
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Validate phone number (basic validation)
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))
}

// Format currency
export const formatCurrency = (amount: number, currency: string = 'KES'): string => {
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

// Calculate total cost based on package and group size
export const calculateTotalCost = (packagePrice: number, groupSize: number = 1): number => {
  return packagePrice * groupSize
}

// Validate required fields
export const validateRequiredFields = (data: FormData, requiredFields: string[]): string[] => {
  const missingFields: string[] = []
  
  requiredFields.forEach(field => {
    if (!data[field as keyof FormData] || data[field as keyof FormData] === '') {
      missingFields.push(field)
    }
  })
  
  return missingFields
}

// Format field name for display
export const formatFieldName = (fieldName: string): string => {
  return fieldName
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim()
}

// Generate QR code data
export const generateQRCode = (registrationData: FormData, registrationId: string): string => {
  return JSON.stringify({
    id: registrationId,
    name: `${registrationData.firstName} ${registrationData.lastName}`,
    email: registrationData.email,
    type: registrationData.registrationType,
    package: registrationData.selectedPackage,
    timestamp: Date.now()
  })
}

// Download QR code as image (placeholder function)
export const downloadQRCode = (qrData: string, filename: string = 'conference-badge-qr.png'): void => {
  // This would typically use a library like html2canvas or similar
  // For now, we'll just log the action
  console.log('QR Code download requested:', { qrData, filename })

  // In a real implementation, you would:
  // 1. Generate the QR code as an image
  // 2. Create a download link
  // 3. Trigger the download

  alert('QR Code download functionality would be implemented here')
}
