import type { PayloadRequest } from 'payload'

interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded'
  timestamp: string
  uptime: number
  version: string
  environment: string
  checks: {
    database: {
      status: 'healthy' | 'unhealthy'
      responseTime?: number
      error?: string
    }
    memory: {
      status: 'healthy' | 'warning' | 'critical'
      usage: {
        used: number
        total: number
        percentage: number
      }
    }
    payload: {
      status: 'healthy' | 'unhealthy'
      collections?: number
      error?: string
    }
    dependencies: {
      status: 'healthy' | 'unhealthy'
      services: {
        [key: string]: 'healthy' | 'unhealthy' | 'unknown'
      }
    }
  }
  metadata: {
    nodeVersion: string
    platform: string
    arch: string
    pid: number
    instanceId?: string
  }
}

/**
 * Health Check Endpoint
 * GET /api/health
 * 
 * Provides comprehensive health status of the application including:
 * - Database connectivity
 * - Memory usage
 * - Payload CMS status
 * - External dependencies
 * - System information
 */
export const healthCheckEndpoint = async (req: PayloadRequest): Promise<Response> => {
  const startTime = Date.now()
  const result: HealthCheckResult = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    checks: {
      database: { status: 'healthy' },
      memory: { 
        status: 'healthy',
        usage: { used: 0, total: 0, percentage: 0 }
      },
      payload: { status: 'healthy' },
      dependencies: { 
        status: 'healthy',
        services: {}
      }
    },
    metadata: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      pid: process.pid,
      instanceId: process.env.INSTANCE_ID
    }
  }

  try {
    // Check database connectivity
    await checkDatabase(req, result)
    
    // Check memory usage
    checkMemoryUsage(result)
    
    // Check Payload CMS status
    await checkPayloadStatus(req, result)
    
    // Check external dependencies
    await checkDependencies(result)
    
    // Determine overall status
    determineOverallStatus(result)
    
    const responseTime = Date.now() - startTime
    
    return new Response(
      JSON.stringify({
        ...result,
        responseTime: `${responseTime}ms`
      }, null, 2),
      {
        status: result.status === 'healthy' ? 200 : result.status === 'degraded' ? 200 : 503,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'X-Health-Check': 'true'
        }
      }
    )
  } catch (error) {
    console.error('Health check failed:', error)
    
    return new Response(
      JSON.stringify({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime: `${Date.now() - startTime}ms`
      }, null, 2),
      {
        status: 503,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'X-Health-Check': 'true'
        }
      }
    )
  }
}

/**
 * Check database connectivity
 */
async function checkDatabase(req: PayloadRequest, result: HealthCheckResult): Promise<void> {
  const dbStartTime = Date.now()
  
  try {
    // Try to perform a simple database operation
    await req.payload.find({
      collection: 'users',
      limit: 1,
      select: {
        id: true
      }
    })
    
    result.checks.database = {
      status: 'healthy',
      responseTime: Date.now() - dbStartTime
    }
  } catch (error) {
    console.error('Database health check failed:', error)
    result.checks.database = {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Database connection failed'
    }
  }
}

/**
 * Check memory usage
 */
function checkMemoryUsage(result: HealthCheckResult): void {
  const memUsage = process.memoryUsage()
  const totalMemory = memUsage.heapTotal
  const usedMemory = memUsage.heapUsed
  const percentage = Math.round((usedMemory / totalMemory) * 100)
  
  result.checks.memory = {
    status: percentage > 90 ? 'critical' : percentage > 75 ? 'warning' : 'healthy',
    usage: {
      used: Math.round(usedMemory / 1024 / 1024), // MB
      total: Math.round(totalMemory / 1024 / 1024), // MB
      percentage
    }
  }
}

/**
 * Check Payload CMS status
 */
async function checkPayloadStatus(req: PayloadRequest, result: HealthCheckResult): Promise<void> {
  try {
    // Get collections count
    const collections = req.payload.config.collections?.length || 0
    
    result.checks.payload = {
      status: 'healthy',
      collections
    }
  } catch (error) {
    console.error('Payload health check failed:', error)
    result.checks.payload = {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Payload CMS error'
    }
  }
}

/**
 * Check external dependencies
 */
async function checkDependencies(result: HealthCheckResult): Promise<void> {
  const services: { [key: string]: 'healthy' | 'unhealthy' | 'unknown' } = {}
  
  // Check environment variables for critical services
  const criticalEnvVars = [
    'DATABASE_URL',
    'PAYLOAD_SECRET'
  ]
  
  for (const envVar of criticalEnvVars) {
    services[envVar] = process.env[envVar] ? 'healthy' : 'unhealthy'
  }
  
  // Check optional services
  const optionalServices = [
    'PESAFLOW_MERCHANT_CODE',
    'ECITIZEN_CLIENT_ID',
    'SMTP_HOST'
  ]
  
  for (const service of optionalServices) {
    services[service] = process.env[service] ? 'healthy' : 'unknown'
  }
  
  result.checks.dependencies = {
    status: Object.values(services).some(status => status === 'unhealthy') ? 'unhealthy' : 'healthy',
    services
  }
}

/**
 * Determine overall health status
 */
function determineOverallStatus(result: HealthCheckResult): void {
  const checks = result.checks
  
  // Critical failures
  if (checks.database.status === 'unhealthy' || 
      checks.payload.status === 'unhealthy' ||
      checks.dependencies.status === 'unhealthy') {
    result.status = 'unhealthy'
    return
  }
  
  // Degraded performance
  if (checks.memory.status === 'critical' || 
      checks.memory.status === 'warning') {
    result.status = 'degraded'
    return
  }
  
  result.status = 'healthy'
}

/**
 * Simple health check endpoint for load balancers
 * GET /api/health/simple
 */
export const simpleHealthCheckEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    // Quick database check
    await req.payload.find({
      collection: 'users',
      limit: 1,
      select: { id: true }
    })
    
    return new Response('OK', {
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'no-cache'
      }
    })
  } catch (error) {
    return new Response('UNHEALTHY', {
      status: 503,
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'no-cache'
      }
    })
  }
}
