'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'

import Link from 'next/link'
import { Card } from '@/components/ui/card'
import { useGetNewsMediaQuery } from '@/lib/api/newsMediaApi'
import { NewsItem } from '../types'

export default function HeadlinesSlider() {
  const { data, isLoading, error } = useGetNewsMediaQuery({
    where: {
      category: { equals: 'event-updates' },
      type: { equals: 'highlight' },
    },
  })

  if (isLoading) {
    return (
      <div>
        <h2 className="text-3xl font-bold text-gray-900 mb-6">Upcoming Events</h2>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="p-4 border rounded-lg bg-white animate-pulse flex space-x-4">
              <div className="w-16 h-16 bg-gray-300 rounded"></div>
              <div className="flex-1 space-y-2 py-1">
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
                <div className="h-3 bg-gray-200 rounded w-5/6"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  // 2. Show Error Message
  if (error) {
    return (
      <div className="relative">
        <h2 className="text-3xl font-bold text-gray-900 mb-6">Upcoming Events</h2>

        {/* Text overlay */}
        <div className="absolute inset-0 z-10 flex flex-col items-center justify-center text-center pointer-events-none">
          <h2 className="text-2xl font-semibold text-red-700 mb-2">
            Failed to load upcoming events.
          </h2>
          <p className="text-gray-600">Please try again later.</p>
        </div>

        {/* Blurred Pulse Skeletons with radial blur */}
        <div className="relative z-0 grid gap-4">
          <div
            className="absolute inset-0 bg-white pointer-events-none rounded-xl"
            style={{
              maskImage: 'radial-gradient(circle at center, rgba(0,0,0,0) 20%, rgba(0,0,0,1) 80%)',
              WebkitMaskImage:
                'radial-gradient(circle at center, rgba(0,0,0,0) 20%, rgba(0,0,0,1) 80%)',
              backdropFilter: 'blur(12px)',
              WebkitBackdropFilter: 'blur(12px)',
              zIndex: 1,
            }}
          />

          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="p-4 border rounded-lg bg-white animate-pulse flex space-x-4 relative z-0"
            >
              <div className="w-16 h-16 bg-gray-300 rounded"></div>
              <div className="flex-1 space-y-2 py-1">
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
                <div className="h-3 bg-gray-200 rounded w-5/6"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  // 3. Show "No Data" if Query Succeeds but Docs Are Empty
  if (data?.docs.length === 0) {
    return (
      <div className="relative">
        <h2 className="text-3xl font-bold text-gray-900 mb-6">Upcoming Events</h2>

        {/* Text overlay */}
        <div className="absolute inset-0 z-10 flex flex-col items-center justify-center text-center pointer-events-none">
          <p className="text-xl font-semibold text-gray-800 bg-white/70 px-4 py-2 rounded">
            No upcoming events available at the moment.
          </p>
        </div>

        {/* Blurred Pulse Skeletons with radial blur */}
        <div className="relative z-0 grid gap-4">
          <div
            className="absolute inset-0 bg-white pointer-events-none rounded-xl"
            style={{
              maskImage: 'radial-gradient(circle at center, rgba(0,0,0,0) 20%, rgba(0,0,0,1) 80%)',
              WebkitMaskImage:
                'radial-gradient(circle at center, rgba(0,0,0,0) 20%, rgba(0,0,0,1) 80%)',
              backdropFilter: 'blur(12px)',
              WebkitBackdropFilter: 'blur(12px)',
              zIndex: 1,
            }}
          />

          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="p-4 border rounded-lg bg-white animate-pulse flex space-x-4 relative z-0"
            >
              <div className="w-16 h-16 bg-gray-300 rounded"></div>
              <div className="flex-1 space-y-2 py-1">
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
                <div className="h-3 bg-gray-200 rounded w-5/6"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div>
      <h2 className="text-3xl font-bold text-gray-900 mb-6">Upcoming Events</h2>
      <div className="space-y-4">
        {data.docs.map((item: NewsItem) => (
          <Link key={item.id} href={`/article/${item.slug}`}>
            <Card className="p-4 hover:shadow-lg transition-shadow cursor-pointer">
              <div className="flex space-x-4">
                <div className="w-16 h-16 bg-gray-200 rounded flex-shrink-0">
                  <Image
                    src={item.image?.url || '/placeholder.svg?height=64&width=64'}
                    alt="Topic thumbnail"
                    width={64}
                    height={64}
                    className="rounded object-cover"
                  />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-1">{item.title}</h4>
                  <p className="text-sm text-gray-600 line-clamp-3">{item.summary}</p>
                </div>
              </div>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  )
}
