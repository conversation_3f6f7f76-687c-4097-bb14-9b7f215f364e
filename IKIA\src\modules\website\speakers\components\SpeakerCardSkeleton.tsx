interface SpeakerCardSkeletonProps {
  size: number
}

export default function SpeakerCardSkeleton({ size }: SpeakerCardSkeletonProps) {
  return (
    <div className="flex flex-col items-center p-6 text-center rounded-lg animate-pulse">
      <div className={`relative ${size} mb-6`}>
        <div className="w-full h-full rounded-full bg-gray-200"></div>
      </div>
      <div className="w-3/4 h-4 bg-gray-200 rounded mb-2"></div>
      <div className="w-1/2 h-3 bg-gray-200 rounded"></div>
    </div>
  )
}
