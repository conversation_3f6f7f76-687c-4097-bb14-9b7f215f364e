import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

// Types for Program Types
export interface ProgramType {
  id: number
  name: string
  slug: string
  description?: string
  color?: string
  icon?: string
  isActive: boolean
  updatedAt: string
  createdAt: string
}

// Types for Thematic Areas (for programs)
export interface ThematicAreaRef {
  id: number
  name: string
  slug?: string
  color?: string
}

// Types for Speakers (for programs)
export interface SpeakerRef {
  id: number
  name: string
  title?: string
  company?: string
  photo?: {
    id: number
    url: string
    alt?: string
  }
}

// Types for Programs
export interface Program {
  id: number
  title: string
  description: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  }
  type: ProgramType | number
  thematicArea?: ThematicAreaRef | number | null
  date: string
  startTime?: string | null
  endTime?: string | null
  venue: string
  speakers?: SpeakerRef[] | null
  capacity?: number | null
  isFeatured: boolean
  isParallel: boolean
  parallelGroup?: string | null
  requirements?: string | null
  materials?: Array<{
    id: number
    url: string
    filename: string
  }> | null
  registrationRequired: boolean
  tags?: Array<{
    tag: string
  }> | null
  slug?: string | null
  updatedAt: string
  createdAt: string
}

export interface ProgramsResponse {
  docs: Program[]
  totalDocs: number
  limit: number
  totalPages: number
  page: number
  pagingCounter: number
  hasPrevPage: boolean
  hasNextPage: boolean
  prevPage?: number | null
  nextPage?: number | null
}

export interface ProgramsQueryParams {
  limit?: number
  page?: number
  sort?: string
  where?: {
    isFeatured?: {
      equals?: boolean
    }
    type?: {
      equals?: string | number
    }
    thematicArea?: {
      equals?: string | number
    }
    speakers?: {
      in?: Array<string | number>
    }
    date?: {
      equals?: string
      greater_than_equal?: string
      less_than_equal?: string
      less_than?: string
    }
    isParallel?: {
      equals?: boolean
    }
    parallelGroup?: {
      equals?: string
    }
    venue?: {
      contains?: string
    }
  }
}

export const programsApi = createApi({
  reducerPath: 'programsApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  tagTypes: ['Program'],
  endpoints: (builder) => ({
    // Get all programs with optional filtering
    getPrograms: builder.query<ProgramsResponse, ProgramsQueryParams | void>({
      query: (params) => {
        const searchParams = new URLSearchParams()

        // Handle the case where params might be void
        if (!params) {
          return 'programs'
        }

        // Add basic params
        if (params.limit) searchParams.append('limit', params.limit.toString())
        if (params.page) searchParams.append('page', params.page.toString())
        if (params.sort) searchParams.append('sort', params.sort)

        // Add where conditions directly as URL parameters instead of JSON encoding
        if (params.where) {
          // Handle featured filter
          if (params.where.isFeatured?.equals !== undefined) {
            searchParams.append(
              'where[isFeatured][equals]',
              params.where.isFeatured.equals.toString(),
            )
          }

          // Handle type filter
          if (params.where.type?.equals !== undefined) {
            searchParams.append('where[type][equals]', params.where.type.equals.toString())
          }

          // Handle thematic area filter
          if (params.where.thematicArea?.equals !== undefined) {
            searchParams.append(
              'where[thematicArea][equals]',
              params.where.thematicArea.equals.toString(),
            )
          }

          // Handle speakers filter
          if (params.where.speakers?.in && params.where.speakers.in.length > 0) {
            params.where.speakers.in.forEach((speakerId) => {
              searchParams.append('where[speakers][in]', speakerId.toString())
            })
          }

          // Handle date filters
          if (params.where.date) {
            if (params.where.date.equals) {
              searchParams.append('where[date][equals]', params.where.date.equals)
            }
            if (params.where.date.greater_than_equal) {
              searchParams.append(
                'where[date][greater_than_equal]',
                params.where.date.greater_than_equal,
              )
            }
            if (params.where.date.less_than_equal) {
              searchParams.append('where[date][less_than_equal]', params.where.date.less_than_equal)
            }
            if (params.where.date.less_than) {
              searchParams.append('where[date][less_than]', params.where.date.less_than)
            }
          }

          // Handle parallel filters
          if (params.where.isParallel?.equals !== undefined) {
            searchParams.append(
              'where[isParallel][equals]',
              params.where.isParallel.equals.toString(),
            )
          }

          if (params.where.parallelGroup?.equals) {
            searchParams.append('where[parallelGroup][equals]', params.where.parallelGroup.equals)
          }

          // Handle venue filter
          if (params.where.venue?.contains) {
            searchParams.append('where[venue][contains]', params.where.venue.contains)
          }
        }

        return `programs?${searchParams.toString()}`
      },
      providesTags: ['Program'],
    }),

    // Get featured programs only
    getFeaturedPrograms: builder.query<ProgramsResponse, { limit?: number }>({
      query: ({ limit = 6 } = {}) =>
        `programs?where[isFeatured][equals]=true&limit=${limit}&sort=-createdAt`,
      providesTags: ['Program'],
    }),

    // Get programs by date
    getProgramsByDate: builder.query<ProgramsResponse, { date: string; limit?: number }>({
      query: ({ date, limit = 50 }) =>
        `programs?where[date][equals]=${date}&limit=${limit}&sort=startTime`,
      providesTags: ['Program'],
    }),

    // Get programs by date range
    getProgramsByDateRange: builder.query<
      ProgramsResponse,
      { startDate: string; endDate: string; limit?: number }
    >({
      query: ({ startDate, endDate, limit = 50 }) =>
        `programs?where[date][greater_than_equal]=${startDate}&where[date][less_than_equal]=${endDate}&limit=${limit}&sort=date,startTime`,
      providesTags: ['Program'],
    }),

    // Get programs by type
    getProgramsByType: builder.query<ProgramsResponse, { type: string; limit?: number }>({
      query: ({ type, limit = 50 }) =>
        `programs?where[type][equals]=${type}&limit=${limit}&sort=startTime`,
      providesTags: ['Program'],
    }),

    // Get parallel programs
    getParallelPrograms: builder.query<ProgramsResponse, { group?: string; limit?: number }>({
      query: ({ group, limit = 50 }) => {
        let queryString = `programs?where[isParallel][equals]=true&limit=${limit}&sort=startTime`
        if (group) {
          queryString += `&where[parallelGroup][equals]=${group}`
        }
        return queryString
      },
      providesTags: ['Program'],
    }),

    // Get single program by ID
    getProgram: builder.query<Program, string | number>({
      query: (id) => `programs/${id}`,
      providesTags: ['Program'],
    }),

    // Get single program by slug
    getProgramBySlug: builder.query<Program, string>({
      query: (slug) => `programs?where[slug][equals]=${slug}&limit=1`,
      transformResponse: (response: ProgramsResponse) => response.docs[0],
      providesTags: ['Program'],
    }),

    // Get program types
    getProgramTypes: builder.query<{ docs: ProgramType[] }, void>({
      query: () => 'program-types?where[isActive][equals]=true&limit=0&sort=name',
      providesTags: ['Program'],
    }),
  }),
})

export const {
  useGetProgramsQuery,
  useGetFeaturedProgramsQuery,
  useGetProgramsByDateQuery,
  useGetProgramsByDateRangeQuery,
  useGetProgramsByTypeQuery,
  useGetParallelProgramsQuery,
  useGetProgramQuery,
  useGetProgramBySlugQuery,
  useGetProgramTypesQuery,
} = programsApi
