'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import Link from 'next/link'
import { ArrowRight, Download, Handshake } from 'lucide-react'

export default function HeroSection() {
  return (
    <section className="py-16 lg:py-20 section-bg-secondary">
      <div className="container mx-auto px-4 grid lg:grid-cols-2 gap-12 items-center">
        {/* Left Content */}
        <div className="lg:pr-8">
          {/* Conference Badge */}
          <div className="inline-flex items-center gap-2 bg-[#7E2518]/8 px-4 py-2 border border-[#7E2518]/15 mb-6">
            <div className="w-2 h-2 bg-[#E8B32C] animate-pulse"></div>
            <span className="text-sm font-medium text-[#7E2518] font-['Myriad_Pro',Arial,sans-serif]">
              Partnership Opportunities
            </span>
          </div>

          <h1 className="text-4xl lg:text-6xl font-bold mb-6 leading-tight text-[#7E2518] font-['Myriad_Pro',Arial,sans-serif]">
            Partners &
            <span className="block bg-gradient-to-r from-[#E8B32C] to-[#C86E36] bg-clip-text text-transparent">
              Sponsors
            </span>
          </h1>

          <div className="w-24 h-1 bg-[#E8B32C] mb-6"></div>

          <p className="text-xl mb-8 text-gray-700 max-w-lg leading-relaxed font-['Myriad_Pro',Arial,sans-serif]">
            Empowering collaboration through culture and heritage. Join us in showcasing
            Kenya&apos;s Indigenous Knowledge Intellectual Assets to the world.
          </p>

          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              asChild
              size="lg"
              className="bg-[#159147] hover:bg-[#0f7a3a] text-white border-0 font-bold transition-all duration-300 main-shadow px-8 font-['Myriad_Pro',Arial,sans-serif]"
            >
              <Link href="#sponsor" className="flex items-center gap-2">
                <Handshake className="w-5 h-5" />
                Become a Sponsor
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              size="lg"
              className="border-2 border-[#7E2518] text-[#7E2518] bg-white hover:bg-[#7E2518] hover:text-white font-bold transition-all duration-300 main-shadow px-8 font-['Myriad_Pro',Arial,sans-serif]"
            >
              <Link
                href="/downloads/sponsorship-prospectus.pdf"
                className="flex items-center gap-2"
              >
                <Download className="w-5 h-5" />
                Download Prospectus
              </Link>
            </Button>
          </div>
        </div>

        {/* Right Content */}
        <div className="relative text-center lg:text-left">
          <div className="relative w-80 h-80 mx-auto lg:mx-0 overflow-hidden">
            {/* Main geometric container */}
            <div className="absolute inset-0 bg-white/10 transform rotate-12 main-shadow border border-white/20">
              {/* Inner pattern */}
              <div className="absolute inset-6 border border-[#E8B32C]/30"></div>
            </div>

            {/* Center icon */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-32 h-32 bg-[#E8B32C] flex items-center justify-center main-shadow">
                <Handshake className="w-16 h-16 text-white" />
              </div>
            </div>

            {/* Floating elements */}
            <div className="absolute top-8 right-8 w-16 h-16 bg-[#159147]/20 transform rotate-12 border border-[#159147]/30 main-shadow transition-transform duration-300"></div>
            <div className="absolute bottom-8 left-8 w-12 h-12 bg-[#81B1DB]/20 transform -rotate-12 border border-[#81B1DB]/30 main-shadow transition-transform duration-300"></div>

            {/* Small floating dots */}
            <div className="absolute top-1/4 right-16 w-3 h-3 bg-[#E8B32C]/60 animate-pulse"></div>
            <div className="absolute bottom-1/4 left-16 w-3 h-3 bg-[#81B1DB]/60 animate-pulse"></div>
          </div>
        </div>
      </div>
    </section>
  )
}
