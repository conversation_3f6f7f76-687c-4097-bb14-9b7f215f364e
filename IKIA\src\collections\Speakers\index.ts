import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'

export const Speakers: CollectionConfig = {
  slug: 'speakers',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'title', 'company'],
  },
  hooks: {
    beforeRead: [
      ({ doc, req }) => {
        if (!doc) return doc
        if (doc.photo?.caption?.root?.children?.[0]?.children?.[0]?.text) {
          doc.photo.caption = doc.photo.caption.root.children[0].children[0].text
        }

        return doc
      },
    ],
  },

  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'title',
      type: 'text',
      label: 'Job Title',
    },
    {
      name: 'company',
      type: 'text',
    },
    {
      name: 'organisation',
      type: 'text',
    },
    {
      name: 'category',
      type: 'select',
      options: ['keynote', 'plenary', 'other'],
      defaultValue: 'keynote',
    },
    {
      name: 'order',
      type: 'number',
      label: 'Display Order',
      required: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'topic',
      type: 'text',
    },
    {
      name: 'bio',
      type: 'richText',
    },
    {
      name: 'photo',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'socials',
      type: 'array',
      fields: [
        {
          name: 'platform',
          type: 'text',
        },
        {
          name: 'url',
          type: 'text',
        },
      ],
    },
    {
      name: 'featuredEvents',
      type: 'relationship',
      relationTo: 'events',
      hasMany: true,
    },
    ...slugField(),
  ],
}

export default Speakers
