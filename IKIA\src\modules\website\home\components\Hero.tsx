import { IkiaActionButtons } from '@/components/ikia-navbar'
import { SkewedContainer } from '@/components/ui/SkewedContainer'
import { ArrowDown, Facebook, Instagram, Linkedin, Youtube } from 'lucide-react'
import { BsTwitterX } from 'react-icons/bs'
import { FaTiktok } from 'react-icons/fa'

const heroButtons = [
  {
    text: 'REGISTRATION',
    href: '/registration',
    variant: 'default' as const,
  },
  {
    text: 'LEARN MORE',
    href: '/about',
    variant: 'green' as const,
  },
]

export default function Hero() {
  // Social media links
  const socialLinks = [
    {
      icon: Facebook,
      href: 'https://www.facebook.com/museumsofkenya',
      name: 'Facebook',
    },
    { icon: BsTwitterX, href: 'https://x.com/museumsofkenya', name: 'X' },
    {
      icon: Instagram,
      href: 'https://www.instagram.com/museumsofkenya/',
      name: 'Instagram',
    },
    {
      icon: Youtube,
      href: 'https://www.youtube.com/@NationalMuseumsOfKenya1',
      name: 'YouTube',
    },
    {
      icon: FaTiktok,
      href: 'https://www.tiktok.com/@museumsofkenya',
      name: 'TikTok',
    },
    {
      icon: Linkedin,
      href: 'https://linkedin.com/company/museumsofkenya',
      name: 'LinkedIn',
    },
  ]

  return (
    <section className="relative h-[720px] lg:h-[90vh] overflow-hidden">
      {/* Background Image with Overlays */}
      <div className="absolute inset-0">
        {/* Background Video */}
        <video
          autoPlay
          muted
          loop
          playsInline
          className="absolute inset-0 w-full h-full object-cover"
          poster="/landing-page-poster.jpg"
        >
          <source src="/landing-page-video.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* Black Overlay with responsive opacity */}
        <div className="absolute inset-0 bg-black/50 sm:bg-black/40" />

        {/* Decorative Pattern Overlay */}
        {/* <div className="absolute inset-0 opacity-10">
          <svg className="w-full h-full" viewBox="0 0 100 100" fill="none">
            <defs>
              <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#7E2518" strokeWidth="0.5" />
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
          </svg>
        </div> */}
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 h-full flex items-start pt-16 sm:pt-12">
        {/* Main Hero Container */}
        <div className="flex flex-col items-center justify-center w-full gap-6 sm:gap-6 py-8 sm:py-12">
          {/* Centered CTA Content */}
          <div className="flex flex-col space-y-6 sm:space-y-8 max-w-2xl text-center">
            {/* Enhanced Badge */}
            <div className="flex justify-center px-4 sm:px-2 lg:px-0">
              <SkewedContainer
                variant="filled"
                size="md"
                className="text-xs sm:text-sm md:text-lg max-w-[90%] sm:max-w-full text-center"
              >
                1<sup className="lowercase">st</sup> INTERNATIONAL INVESTMENT CONFERENCE AND TRADE
                FAIR ON INDIGENOUS KNOWLEDGE INTELLECTUAL ASSETS 2025
              </SkewedContainer>
            </div>

            {/* Enhanced Main Title */}
            <div className="flex flex-col space-y-3 sm:space-y-4">
              <h1 className="font-myriad font-bold text-xl sm:text-2xl lg:text-3xl xl:text-4xl text-white leading-tight max-w-4xl px-2 sm:px-0">
                Unlocking Investment Opportunities in Kenya&apos;s Indigenous Wealth: A New Growth
                Area of The Economy
              </h1>
            </div>

            {/* Enhanced Action Buttons */}
            {/* <div className="flex justify-center relative z-30 px-4 sm:px-0">
              <IkiaActionButtons
                className="gap-4 sm:gap-6 w-full sm:w-auto"
                buttons={heroButtons}
                size="large"
                showOnMobile={true}
              />
            </div> */}

            {/* Social Media Links with Hashtags */}
            <div className="relative z-30 mt-610">
              {/* Desktop Layout - Horizontal */}
              <div className="hidden md:flex justify-center items-center gap-8">
                {/* Left Hashtags */}
                <div className="flex flex-col gap-2 text-right">
                  <div className="text-white/90 font-myriad font-semibold text-sm tracking-wider uppercase hover:text-[#E8B32C] transition-colors duration-300 cursor-pointer">
                    #IKIAConference2025
                  </div>
                  <div className="text-white/80 font-myriad font-medium text-xs tracking-wider uppercase hover:text-[#159147] transition-colors duration-300 cursor-pointer">
                    #HeritageBasedWealth
                  </div>
                </div>

                {/* Social Media Icons */}
                <div className="flex gap-4">
                  {socialLinks.map((social, index) => {
                    const Icon = social.icon
                    return (
                      <a
                        key={index}
                        href={social.href}
                        className="w-12 h-12 bg-transparent border-2 border-white rounded-full flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110"
                        title={social.name}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Icon className="w-6 h-6 text-white" />
                      </a>
                    )
                  })}
                </div>

                {/* Right Hashtags */}
                <div className="flex flex-col gap-2 text-left">
                  <div className="text-white/90 font-myriad font-semibold text-sm tracking-wider uppercase hover:text-[#C86E36] transition-colors duration-300 cursor-pointer">
                    #IKIAConference2025
                  </div>
                  <div className="text-white/80 font-myriad font-medium text-xs tracking-wider uppercase hover:text-[#81B1DB] transition-colors duration-300 cursor-pointer">
                    #IndigenousKnowledge
                  </div>
                </div>
              </div>

              {/* Mobile Layout - Single Column */}
              <div className="md:hidden flex flex-col items-center gap-4">
                {/* Top Hashtags */}
                <div className="flex flex-wrap justify-center gap-3">
                  <div className="text-white/90 font-myriad font-semibold text-xs tracking-wider uppercase hover:text-[#E8B32C] transition-colors duration-300 cursor-pointer">
                    #IKIAConference2025
                  </div>
                  <div className="text-white/90 font-myriad font-semibold text-xs tracking-wider uppercase hover:text-[#C86E36] transition-colors duration-300 cursor-pointer">
                    #HeritageBasedWealth
                  </div>
                </div>

                {/* Social Media Icons */}
                <div className="flex gap-4">
                  {socialLinks.map((social, index) => {
                    const Icon = social.icon
                    return (
                      <a
                        key={index}
                        href={social.href}
                        className="w-12 h-12 bg-transparent border-2 border-white rounded-full flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110"
                        title={social.name}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Icon className="w-6 h-6 text-white" />
                      </a>
                    )
                  })}
                </div>

                {/* Bottom Hashtags */}
                <div className="flex flex-wrap justify-center gap-3">
                  <div className="text-white/80 font-myriad font-medium text-[10px] tracking-wider uppercase hover:text-[#159147] transition-colors duration-300 cursor-pointer">
                    #IKIAConference2025
                  </div>
                  <div className="text-white/80 font-myriad font-medium text-[10px] tracking-wider uppercase hover:text-[#81B1DB] transition-colors duration-300 cursor-pointer">
                    #IndigenousKnowledge
                  </div>
                </div>
              </div>
            </div>

            {/* Scroll Indicator */}
            <div className="flex flex-col items-center space-y-0.5 animate-bounce mt-6">
              <span className="text-white text-sm font-myriad font-bold uppercase tracking-wider">
                SCROLL
              </span>
              <ArrowDown className="w-6 h-6 text-white" />
            </div>
          </div>

          {/* Right image section removed */}
        </div>
      </div>

      {/* Old scroll indicator removed - now inline with content */}

      {/* Bottom Decorative Line */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#E8B32C] via-[#C86E36] to-[#159147]"></div>
    </section>
  )
}
