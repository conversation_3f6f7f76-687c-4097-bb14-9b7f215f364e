#!/usr/bin/env node

/**
 * <PERSON>ript to update a user's role to admin
 * Usage: node scripts/update-user-role.js <email> <role>
 * Example: node scripts/update-user-role.js <EMAIL> admin
 */

import { getPayload } from 'payload'
import config from '../dist/payload.config.js'

async function updateUserRole() {
  const email = process.argv[2]
  const newRole = process.argv[3] || 'admin'

  if (!email) {
    console.error('❌ Please provide an email address')
    console.log('Usage: node scripts/update-user-role.js <email> <role>')
    console.log('Example: node scripts/update-user-role.js <EMAIL> admin')
    process.exit(1)
  }

  try {
    console.log('🔄 Initializing Payload...')
    const payload = await getPayload({ config })

    console.log(`🔍 Looking for user with email: ${email}`)

    // Find the user by email
    const users = await payload.find({
      collection: 'users',
      where: {
        email: {
          equals: email,
        },
      },
    })

    if (users.docs.length === 0) {
      console.error(`❌ User with email ${email} not found`)
      process.exit(1)
    }

    const user = users.docs[0]
    console.log(`✅ Found user: ${user.name || 'No name'} (${user.email})`)
    console.log(`📊 Current role: ${user.role}`)

    if (user.role === newRole) {
      console.log(`✅ User already has role: ${newRole}`)
      process.exit(0)
    }

    // Update the user's role
    console.log(`🔄 Updating role to: ${newRole}`)

    const updatedUser = await payload.update({
      collection: 'users',
      id: user.id,
      data: {
        role: newRole,
      },
    })

    console.log(`✅ Successfully updated user role!`)
    console.log(`📊 User details:`)
    console.log(`   - ID: ${updatedUser.id}`)
    console.log(`   - Name: ${updatedUser.name || 'No name'}`)
    console.log(`   - Email: ${updatedUser.email}`)
    console.log(`   - Role: ${updatedUser.role}`)
    console.log(`   - Updated: ${updatedUser.updatedAt}`)

    if (newRole === 'admin') {
      console.log(`\n🎉 User can now access the admin dashboard at /admin`)
    }

    process.exit(0)
  } catch (error) {
    console.error('❌ Error updating user role:', error.message)
    if (error.stack) {
      console.error('Stack trace:', error.stack)
    }
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Script interrupted')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n👋 Script terminated')
  process.exit(0)
})

updateUserRole()
