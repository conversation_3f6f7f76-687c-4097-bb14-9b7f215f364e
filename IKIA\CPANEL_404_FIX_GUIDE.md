# cPanel 404 Error Fix Guide

This guide addresses the common 404 error when hosting Next.js applications on cPanel with PM2.

## Quick Fix Steps

### 1. Run the Automated Fix Script

```bash
# Make the script executable
chmod +x scripts/fix-cpanel-404.sh

# Run the diagnostic and fix script
./scripts/fix-cpanel-404.sh
```

### 2. Manual Fix Steps

If the automated script doesn't resolve the issue, follow these manual steps:

#### Step 1: Stop Current PM2 Process
```bash
pm2 stop ikia-conference
pm2 delete ikia-conference
```

#### Step 2: Rebuild the Application
```bash
cd /home/<USER>/public_html
npm run build:production
```

#### Step 3: Start with Updated Configuration
```bash
pm2 start ecosystem.config.cjs --env production
pm2 save
```

#### Step 4: Verify the Fix
```bash
# Check PM2 status
pm2 status

# Test local connection
curl http://127.0.0.1:3000/

# Check if port is listening
netstat -tlnp | grep :3000
```

## Common Causes and Solutions

### 1. **Port Binding Issue**
**Problem**: Next.js not binding to the correct host/port
**Solution**: Updated `ecosystem.config.cjs` to explicitly bind to `127.0.0.1:3000`

### 2. **Build Issues**
**Problem**: Application not properly built for production
**Solution**: 
- Run `npm run build:production`
- Ensure `.next` directory exists and contains build files

### 3. **Environment Variables**
**Problem**: Missing or incorrect environment variables
**Solution**: 
- Verify `.env` file exists with correct values
- Check `NEXT_PUBLIC_SERVER_URL` points to your domain

### 4. **PM2 Configuration**
**Problem**: PM2 not starting with correct environment
**Solution**: 
- Use `--env production` flag
- Updated ecosystem.config.cjs with better error handling

### 5. **Apache/htaccess Issues**
**Problem**: Apache not properly proxying requests
**Solution**: 
- Verify `.htaccess` exists and is correct
- Check Apache modules (mod_rewrite, mod_proxy) are enabled

## Troubleshooting Commands

### Check PM2 Status
```bash
pm2 status
pm2 describe ikia-conference
pm2 logs ikia-conference --lines 50
```

### Check Network Connectivity
```bash
# Check if port 3000 is listening
netstat -tlnp | grep :3000

# Test local connection
curl -v http://127.0.0.1:3000/

# Test external connection
curl -v https://ikiaconference.or.ke/
```

### Check File Permissions
```bash
# Ensure proper permissions
chmod +x server.js
chmod +x scripts/*.sh
chown -R ikiaconferenceor:ikiaconferenceor /home/<USER>/public_html
```

### Check Build Output
```bash
# Verify build exists
ls -la .next/
ls -la .next/standalone/

# Check build logs
npm run build:production 2>&1 | tee build.log
```

## Updated Configuration Files

### 1. Enhanced ecosystem.config.cjs
- Added explicit HOST binding to `127.0.0.1`
- Improved error handling and logging
- Better memory management

### 2. Improved server.js
- Direct HTTP server implementation
- Better error handling
- Explicit hostname binding
- Security headers

### 3. Updated next.config.js
- Added `output: 'standalone'` for better compatibility
- Improved compression and security settings

## Verification Steps

After applying fixes, verify everything works:

1. **PM2 Status**: `pm2 status` shows "online"
2. **Local Connection**: `curl http://127.0.0.1:3000/` returns HTML
3. **External Access**: `https://ikiaconference.or.ke/` loads correctly
4. **Logs**: `pm2 logs ikia-conference` shows no errors

## Advanced Troubleshooting

### If Issues Persist

1. **Check Apache Error Logs**
   ```bash
   tail -f /home/<USER>/logs/error_log
   ```

2. **Verify Node.js Version**
   ```bash
   node --version  # Should be 18.20.2+ or 20.9.0+
   ```

3. **Check Memory Usage**
   ```bash
   pm2 monit
   ```

4. **Test with Different Port**
   ```bash
   # Temporarily test on different port
   PORT=3001 npm start
   ```

### Contact Support

If the issue persists after following this guide:

1. Run the diagnostic script: `./scripts/fix-cpanel-404.sh check`
2. Collect logs: `pm2 logs ikia-conference --lines 100 > debug.log`
3. Check Apache error logs
4. Provide all collected information to support

## Prevention

To prevent future 404 issues:

1. Always use the deployment scripts
2. Test locally before deploying
3. Monitor PM2 logs regularly
4. Keep backups of working configurations
5. Use the health check endpoint: `/api/health`

## Quick Reference

```bash
# Essential commands for cPanel hosting
pm2 start ecosystem.config.cjs --env production
pm2 restart ikia-conference
pm2 reload ikia-conference
pm2 logs ikia-conference
pm2 monit

# Build and deploy
npm run build:production
./scripts/deploy.sh deploy

# Troubleshooting
./scripts/fix-cpanel-404.sh
curl http://127.0.0.1:3000/
netstat -tlnp | grep :3000
```
