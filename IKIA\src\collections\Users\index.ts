import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'

export const Users: CollectionConfig = {
  slug: 'users',
  access: {
    admin: authenticated, // Allow all authenticated users to access admin panel
    create: () => true, // Allow anyone to register
    delete: authenticated,
    read: authenticated,
    update: ({ req }) => {
      // Admins can update any user
      if (req.user?.role === 'admin') {
        return true
      }

      // Users can update their own profile
      if (req.user) {
        return {
          id: {
            equals: req.user.id,
          },
        }
      }
      return false
    },
  },
  admin: {
    defaultColumns: [
      'name',
      'email',
      'phone_number',
      'userType',
      'county',
      'verified',
      '_verified',
      'role',
    ],
    useAsTitle: 'name',
    group: 'Users',
  },

  auth: {
    // Preserve verification columns and show verification errors to user
    verify: {
      generateEmailHTML: (args: any) => {
        const token = args?.token
        return `<p>Please verify your email by clicking <a href="${process.env.PAYLOAD_PUBLIC_SERVER_URL}/verify?token=${token}">here</a></p>`
      },
      generateEmailSubject: () => 'Verify your email',
    },
    forgotPassword: {
      generateEmailHTML: (args: any) => {
        const token = args?.token
        return `<p>Reset your password by clicking <a href="${process.env.PAYLOAD_PUBLIC_SERVER_URL}/reset-password?token=${token}">here</a></p>`
      },
      generateEmailSubject: () => 'Reset your password',
    },
    maxLoginAttempts: 5,
    lockTime: 600000, // 10 minutes
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'userType',
      type: 'select',
      required: true,
      options: [
        { label: 'Investor', value: 'investor' },
        { label: 'Exhibitor', value: 'exhibitor' },
        { label: 'Delegate', value: 'delegate' },
        { label: 'Admin', value: 'admin' },
      ],
      defaultValue: 'delegate',
      admin: {
        description: 'Type of user in the system',
      },
      // Not required to maintain compatibility with existing users
    },
    {
      name: 'role',
      type: 'select',
      options: [
        { label: 'Citizen', value: 'citizen' },
        { label: 'Business', value: 'business' },
        { label: 'Admin', value: 'admin' },
        { label: 'Payment Processor', value: 'payment_processor' },
      ],
      defaultValue: 'citizen',
      required: true,
    },
    {
      name: 'county',
      type: 'relationship',
      relationTo: 'counties',
      label: 'County',
      admin: {
        description: 'The county this user is associated with',
      },
      validate: async (val: any, { req }: { req: any }) => {
        if (val) {
          // Ensure county is a valid number
          const countyId = typeof val === 'string' ? parseInt(val) : val

          if (isNaN(countyId) || countyId <= 0) {
            return `Invalid county ID format: ${val}. County ID must be a positive number.`
          }

          // Check if county exists in database
          try {
            const county = await req.payload.findByID({
              collection: 'counties',
              id: countyId,
            })

            if (!county) {
              return `County with ID ${countyId} does not exist. Available counties: 13 (Nairobi), 14 (Mombasa), 15 (Kisumu)`
            }
          } catch (error: any) {
            return `Unable to validate county ID ${countyId}. Available counties: 13 (Nairobi), 14 (Mombasa), 15 (Kisumu)`
          }
        }

        return true
      },
    },
    // Contact Information
    {
      name: 'phone_number',
      type: 'text',
      validate: (val: any) => {
        // Only validate if value is provided and not empty
        if (val && typeof val === 'string' && val.trim() && !/^254\d{9}$/.test(val.trim())) {
          return 'Phone number should be in format 254XXXXXXXXX'
        }
        return true
      },
      admin: {
        description: 'Phone number in international format (254XXXXXXXXX) - optional',
      },
    },
    {
      name: 'id_number',
      type: 'text',
      unique: true,
      validate: (val: any) => {
        // More lenient validation - only validate if value is provided
        if (val && typeof val === 'string' && val.trim() && !/^\d{7,8}$/.test(val.trim())) {
          return 'ID number should be 7-8 digits'
        }
        return true
      },
      admin: {
        description: 'National ID number (unique)',
      },
    },
    // Business Information (Optional)
    {
      name: 'business_type',
      type: 'select',
      options: [
        { label: 'Technology Startup', value: 'Technology Startup' },
        { label: 'Manufacturing', value: 'Manufacturing' },
        { label: 'Agriculture', value: 'Agriculture' },
        { label: 'Services', value: 'Services' },
        { label: 'Retail', value: 'Retail' },
        { label: 'Other', value: 'Other' },
      ],
      admin: {
        description: 'Type of business (optional)',
      },
    },
    {
      name: 'registration_purpose',
      type: 'text',
      admin: {
        description:
          'Purpose of registration (e.g., Business Registration, Investment Facilitation)',
      },
    },
    // Package Selection
    {
      name: 'selected_package',
      type: 'relationship',
      relationTo: 'delegatepackages',
      label: 'Selected Package',
      admin: {
        description: 'Delegate package selected by the user',
      },
    },
    {
      name: 'package_status',
      type: 'select',
      options: [
        { label: 'No Package', value: 'none' },
        { label: 'Package Selected', value: 'selected' },
        { label: 'Payment Pending', value: 'payment_pending' },
        { label: 'Active', value: 'active' },
        { label: 'Expired', value: 'expired' },
        { label: 'Suspended', value: 'suspended' },
      ],
      defaultValue: 'none',
      admin: {
        description: 'Current status of user package',
      },
    },
    {
      name: 'package_expiry',
      type: 'date',
      admin: {
        description: 'When the current package expires',
      },
    },
    // Payment Profile
    {
      name: 'payment_profile',
      type: 'group',
      label: 'Payment Profile',
      fields: [
        {
          name: 'preferred_currency',
          type: 'select',
          options: [
            { label: 'Kenyan Shilling (KES)', value: 'KES' },
            { label: 'US Dollar (USD)', value: 'USD' },
          ],
          defaultValue: 'KES',
        },
        {
          name: 'daily_limit',
          type: 'number',
          defaultValue: 50000,
          min: 0,
          admin: {
            description: 'Daily payment limit in KES',
          },
        },
        {
          name: 'monthly_limit',
          type: 'number',
          defaultValue: 500000,
          min: 0,
          admin: {
            description: 'Monthly payment limit in KES',
          },
        },
      ],
    },
    // Registration Context
    {
      name: 'registration_context',
      type: 'group',
      label: 'Registration Context',
      admin: {
        readOnly: true,
      },
      fields: [
        {
          name: 'created_during_package_flow',
          type: 'checkbox',
          defaultValue: false,
          admin: { readOnly: true, description: 'User was created during package selection' },
        },
        {
          name: 'temporary_password_sent',
          type: 'checkbox',
          defaultValue: false,
          admin: { readOnly: true, description: 'Temporary password was sent via email' },
        },
        {
          name: 'initial_package_invoice',
          type: 'relationship',
          relationTo: 'invoices',
          admin: { readOnly: true, description: 'Initial invoice created during registration' },
        },
      ],
    },
    // User Statistics
    {
      name: 'statistics',
      type: 'group',
      label: 'Payment Statistics',
      admin: {
        readOnly: true,
      },
      fields: [
        {
          name: 'total_payments',
          type: 'number',
          defaultValue: 0,
          admin: { readOnly: true },
        },
        {
          name: 'total_amount_paid',
          type: 'number',
          defaultValue: 0,
          admin: { readOnly: true },
        },
        {
          name: 'last_payment_date',
          type: 'date',
          admin: { readOnly: true },
        },
      ],
    },
    {
      name: 'featured',
      type: 'checkbox',
      label: 'Featured Profile',
      defaultValue: false,
      admin: {
        description: 'Show this user in featured profiles section',
      },
    },
    // Profile Information
    {
      name: 'title',
      type: 'text',
      label: 'Professional Title',
      admin: {
        description: 'Job title or professional designation',
      },
    },
    {
      name: 'organization',
      type: 'text',
      label: 'Organization/Company',
      admin: {
        description: 'Organization or company name',
      },
    },
    {
      name: 'location',
      type: 'text',
      label: 'Location',
      admin: {
        description: 'City, Country or specific location',
      },
    },
    {
      name: 'focus',
      type: 'textarea',
      label: 'Focus Area',
      admin: {
        description: 'Area of expertise or focus',
        rows: 3,
      },
    },
    {
      name: 'bio',
      type: 'richText',
      label: 'Biography',
      admin: {
        description: 'Professional biography or description',
      },
    },
    {
      name: 'profileImage',
      type: 'upload',
      relationTo: 'media',
      label: 'Profile Image',
      admin: {
        description: 'Profile photo or avatar',
      },
    },
    {
      name: 'website',
      type: 'text',
      label: 'Website',
      admin: {
        description: 'Personal or company website',
      },
    },
    {
      name: 'socialLinks',
      type: 'array',
      label: 'Social Media Links',
      fields: [
        {
          name: 'platform',
          type: 'select',
          options: [
            { label: 'LinkedIn', value: 'linkedin' },
            { label: 'Twitter', value: 'twitter' },
            { label: 'Facebook', value: 'facebook' },
            { label: 'Instagram', value: 'instagram' },
            { label: 'Other', value: 'other' },
          ],
        },
        {
          name: 'url',
          type: 'text',
          required: true,
        },
      ],
    },
    // Investor-specific fields
    {
      name: 'investmentInfo',
      type: 'group',
      label: 'Investment Information',
      admin: {
        condition: (data) => data.userType === 'investor',
      },
      fields: [
        {
          name: 'totalInvestment',
          type: 'text',
          label: 'Total Investment',
          admin: {
            description: 'e.g., "$2.5M+ invested"',
          },
        },
        {
          name: 'activeProjects',
          type: 'text',
          label: 'Active Projects',
          admin: {
            description: 'e.g., "12 active projects"',
          },
        },
        {
          name: 'investmentAreas',
          type: 'array',
          label: 'Investment Areas',
          fields: [
            {
              name: 'area',
              type: 'text',
              required: true,
            },
          ],
        },
        {
          name: 'investmentRange',
          type: 'select',
          label: 'Investment Range',
          options: [
            { label: 'Under $50K', value: 'under-50k' },
            { label: '$50K - $100K', value: '50k-100k' },
            { label: '$100K - $500K', value: '100k-500k' },
            { label: '$500K - $1M', value: '500k-1m' },
            { label: 'Over $1M', value: 'over-1m' },
          ],
        },
      ],
    },
    // Exhibitor-specific fields
    {
      name: 'exhibitorInfo',
      type: 'group',
      label: 'Exhibitor Information',
      admin: {
        condition: (data) => data.userType === 'exhibitor',
      },
      fields: [
        {
          name: 'achievement',
          type: 'text',
          label: 'Key Achievement',
          admin: {
            description: 'e.g., "30+ years experience"',
          },
        },
        {
          name: 'projectsCompleted',
          type: 'text',
          label: 'Projects/Work Completed',
          admin: {
            description: 'e.g., "500+ patients helped"',
          },
        },
        {
          name: 'specializations',
          type: 'array',
          label: 'Specializations',
          fields: [
            {
              name: 'specialization',
              type: 'text',
              required: true,
            },
          ],
        },
        {
          name: 'certifications',
          type: 'array',
          label: 'Certifications & Awards',
          fields: [
            {
              name: 'certification',
              type: 'text',
              required: true,
            },
          ],
        },
      ],
    },
    // Rating and verification
    {
      name: 'rating',
      type: 'number',
      label: 'Rating',
      admin: {
        description: 'User rating (1-5 scale)',
        step: 0.1,
      },
      validate: (val: number | null | undefined) => {
        if (val !== null && val !== undefined && (val < 1 || val > 5)) {
          return 'Rating must be between 1 and 5'
        }
        return true
      },
    },
    {
      name: 'verified',
      type: 'checkbox',
      label: 'Verified Profile',
      defaultValue: false,
      admin: {
        description:
          'Mark this profile as verified (admin verification for profile completeness and authenticity)',
      },
    },
    {
      name: 'email_verification_status',
      type: 'ui',
      label: 'Email Verification Status',
      admin: {
        components: {
          Field: () => null, // This will be handled by the built-in _verified field
        },
        description:
          'Email verification is handled automatically. Use the _verified column in the list view to see status.',
      },
    },
    {
      name: 'badge',
      type: 'text',
      label: 'Profile Badge',
      admin: {
        description: 'Special badge or designation (e.g., "Top Investor", "Master Healer")',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Ensure phone number format (only if phone number is provided)
        if (data.phone_number && typeof data.phone_number === 'string') {
          if (data.phone_number.startsWith('0')) {
            data.phone_number = '254' + data.phone_number.substring(1)
          } else if (data.phone_number.startsWith('+254')) {
            data.phone_number = data.phone_number.substring(1)
          }
        }
      },
    ],
    afterChange: [
      ({ doc, operation, previousDoc }) => {
        if (operation === 'create') {
          console.log(`New user registered: ${doc.email}`)
        }

        // Log verification status changes
        if (operation === 'update' && previousDoc) {
          if (doc.verified !== previousDoc.verified) {
            console.log(
              `User ${doc.email} profile verification changed: ${previousDoc.verified} -> ${doc.verified}`,
            )
          }
          if (doc._verified !== previousDoc._verified) {
            console.log(
              `User ${doc.email} email verification changed: ${previousDoc._verified} -> ${doc._verified}`,
            )
          }
        }
      },
    ],
  },
  timestamps: true,
}
