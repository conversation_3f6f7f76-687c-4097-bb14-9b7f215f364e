#!/usr/bin/env node

/**
 * Test script to verify ALL 15 Pesaflow parameters are being generated
 */

const BASE_URL = 'http://localhost:3000/api'

async function testCompletePayload() {
  console.log('🧪 COMPLETE PESAFLOW PAYLOAD TEST')
  console.log('==================================')

  try {
    const registrationData = {
      name: 'Test User Complete',
      email: '<EMAIL>',
      phone_number: '0700123456',
      id_number: '87654321',
      county: 'Nairobi',
      password: 'TestPassword123!',
      selected_package: '2'
    }

    console.log('📤 Sending registration request...')
    
    const response = await fetch(`${BASE_URL}/citizens/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(registrationData),
    })

    const result = await response.json()

    if (result.success) {
      console.log('✅ Registration successful!')
      
      // Check if we have all expected parameters in the response
      const expectedParams = [
        'api_client_id',
        'service_id', 
        'bill_ref_number',
        'customer_phone',
        'customer_name'
      ]

      console.log('\n📋 PARAMETER VERIFICATION')
      console.log('=========================')
      
      expectedParams.forEach(param => {
        const value = result.data.payment[param]
        if (value) {
          console.log(`✅ ${param}: ${value}`)
        } else {
          console.log(`❌ ${param}: MISSING`)
        }
      })

      // Check the checkout URL format
      const checkoutUrl = result.data.payment.checkout_url
      console.log('\n🔗 CHECKOUT URL ANALYSIS')
      console.log('========================')
      console.log(`URL: ${checkoutUrl}`)
      
      if (checkoutUrl.includes('test.pesaflow.com')) {
        console.log('✅ Using correct Pesaflow UAT server')
      } else {
        console.log('⚠️  URL may not be pointing to Pesaflow UAT server')
      }

      if (checkoutUrl.includes('/api/PaymentAPI/checkout')) {
        console.log('✅ Using correct API endpoint')
      } else {
        console.log('⚠️  URL may not be using the correct API endpoint')
      }

      // Check if it's a real checkout URL (not just the API endpoint)
      if (checkoutUrl === 'https://test.pesaflow.com/api/PaymentAPI/checkout') {
        console.log('⚠️  This appears to be just the API endpoint, not a real checkout URL')
        console.log('   Expected: A unique checkout URL from Pesaflow response')
      } else {
        console.log('✅ This appears to be a real checkout URL from Pesaflow')
      }

      console.log('\n📊 RESPONSE SUMMARY')
      console.log('==================')
      console.log(`User ID: ${result.data.user.id}`)
      console.log(`Invoice ID: ${result.data.invoice.id}`)
      console.log(`Amount: ${result.data.invoice.currency} ${result.data.invoice.amount}`)
      console.log(`Package: ${result.data.delegate_package.name}`)
      console.log(`Checkout URL: ${checkoutUrl}`)

    } else {
      console.log('❌ Registration failed:', result.error)
      if (result.message) {
        console.log('   Message:', result.message)
      }
      if (result.missing_parameters) {
        console.log('   Missing Parameters:', result.missing_parameters)
      }
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message)
  }
}

// Expected 15 parameters that should be sent to Pesaflow
function listExpectedParameters() {
  console.log('\n📋 EXPECTED PESAFLOW PARAMETERS (15 total)')
  console.log('==========================================')
  
  const expectedParams = [
    { name: 'apiClientID', mandatory: true, description: 'Provided by Pesaflow' },
    { name: 'serviceID', mandatory: true, description: 'Provided by Pesaflow' },
    { name: 'billRefNumber', mandatory: true, description: 'Unique client transaction number' },
    { name: 'billDesc', mandatory: true, description: 'Description of the invoice' },
    { name: 'clientMSISDN', mandatory: true, description: 'Mobile number (254XXXXXXXXX)' },
    { name: 'clientIDNumber', mandatory: true, description: 'Customer ID Number' },
    { name: 'clientName', mandatory: true, description: 'Customer Name' },
    { name: 'clientEmail', mandatory: true, description: 'Customer Email' },
    { name: 'notificationURL', mandatory: true, description: 'IPN endpoint URL' },
    { name: 'pictureURL', mandatory: false, description: 'Customer image URL' },
    { name: 'callBackURLOnSuccess', mandatory: true, description: 'Success redirect URL' },
    { name: 'currency', mandatory: true, description: 'Currency (KES)' },
    { name: 'amountExpected', mandatory: true, description: 'Invoice amount' },
    { name: 'format', mandatory: false, description: 'Response format (html/json)' },
    { name: 'sendSTK', mandatory: false, description: 'Send M-PESA STK (true/false)' },
    { name: 'secureHash', mandatory: true, description: 'Generated hash for validation' },
  ]

  expectedParams.forEach((param, index) => {
    const status = param.mandatory ? '🔴 MANDATORY' : '🟡 OPTIONAL'
    console.log(`${index + 1:2}. ${param.name.padEnd(20)} ${status} - ${param.description}`)
  })

  console.log(`\nTotal: ${expectedParams.length} parameters`)
  console.log(`Mandatory: ${expectedParams.filter(p => p.mandatory).length}`)
  console.log(`Optional: ${expectedParams.filter(p => !p.mandatory).length}`)
}

// Run the test
async function runTest() {
  listExpectedParameters()
  await testCompletePayload()
  
  console.log('\n🎯 WHAT TO LOOK FOR IN SERVER LOGS')
  console.log('==================================')
  console.log('1. "Built COMPLETE Pesaflow payload" with all 15 parameters')
  console.log('2. "parameterCount: 15" or similar')
  console.log('3. "All mandatory parameters present"')
  console.log('4. Actual Pesaflow API response with real checkout URL')
  console.log('5. No "Missing mandatory parameters" errors')
}

runTest().catch(console.error)
