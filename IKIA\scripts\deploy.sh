#!/bin/bash

# IKIA Conference Deployment Script
# This script handles the complete deployment process for the IKIA Conference application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="ikia-conference"
APP_DIR="/home/<USER>/public_html"
LOG_DIR="$APP_DIR/logs"
BACKUP_DIR="/home/<USER>/backups"
NODE_VERSION="18"
DOMAIN="ikiaconference.or.ke"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as correct user
check_user() {
    if [ "$USER" != "ikiaconferenceor" ]; then
        log_warning "Not running as ikiaconferenceor user. Some operations may fail."
    fi
}

# Check Node.js version
check_node_version() {
    log_info "Checking Node.js version..."
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi

    NODE_CURRENT=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_CURRENT" -lt "$NODE_VERSION" ]; then
        log_error "Node.js version $NODE_CURRENT is too old. Required: $NODE_VERSION+"
        exit 1
    fi
    log_success "Node.js version check passed"
}

# Check PM2 installation
check_pm2() {
    log_info "Checking PM2 installation..."
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 is not installed. Installing PM2..."
        npm install -g pm2
        log_success "PM2 installed successfully"
    else
        log_success "PM2 is already installed"
    fi
}

# Create necessary directories
create_directories() {
    log_info "Creating necessary directories..."
    mkdir -p "$LOG_DIR"
    mkdir -p "$BACKUP_DIR"
    log_success "Directories created"
}

# Backup current deployment
backup_deployment() {
    if [ -d "$APP_DIR/.next" ]; then
        log_info "Creating backup of current deployment..."
        BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR/$BACKUP_NAME"
        cp -r "$APP_DIR/.next" "$BACKUP_DIR/$BACKUP_NAME/"
        cp "$APP_DIR/package.json" "$BACKUP_DIR/$BACKUP_NAME/" 2>/dev/null || true
        log_success "Backup created: $BACKUP_DIR/$BACKUP_NAME"
    fi
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    cd "$APP_DIR"

    if [ -f "pnpm-lock.yaml" ]; then
        log_info "Using pnpm..."
        pnpm install --frozen-lockfile
    elif [ -f "yarn.lock" ]; then
        log_info "Using yarn..."
        yarn install --frozen-lockfile
    else
        log_info "Using npm..."
        npm ci
    fi
    log_success "Dependencies installed"
}

# Build application
build_application() {
    log_info "Building application..."
    cd "$APP_DIR"
    npm run build:production
    log_success "Application built successfully"
}

# Start/Restart PM2 application
deploy_with_pm2() {
    log_info "Deploying with PM2..."
    cd "$APP_DIR"

    # Check if app is already running
    if pm2 describe "$APP_NAME" > /dev/null 2>&1; then
        log_info "Application is running. Stopping current instance..."
        pm2 stop "$APP_NAME"
        log_info "Deleting current PM2 process..."
        pm2 delete "$APP_NAME"
        log_info "Starting fresh application instance..."
        pm2 start ecosystem.config.cjs --env production
    else
        log_info "Starting new application..."
        pm2 start ecosystem.config.cjs --env production
    fi

    # Wait for application to stabilize
    log_info "Waiting for application to stabilize..."
    sleep 3

    # Verify the application started successfully
    if pm2 describe "$APP_NAME" | grep -q "online"; then
        log_success "Application started successfully"
    else
        log_error "Application failed to start properly"
        pm2 logs "$APP_NAME" --lines 20
        return 1
    fi

    # Save PM2 configuration
    pm2 save
    log_success "PM2 deployment completed"
}

# Health check
health_check() {
    log_info "Performing health check..."
    sleep 5  # Wait for app to start

    # Check if PM2 process is running
    if pm2 describe "$APP_NAME" | grep -q "online"; then
        log_success "PM2 process is online"
    else
        log_error "PM2 process is not running properly"
        return 1
    fi

    # Check if application responds locally
    if curl -f -s http://localhost:3000/ > /dev/null 2>&1; then
        log_success "Local application health check passed"
    else
        log_warning "Local health check failed - application may not be responding"
    fi

    # Check if domain responds (if in production)
    if [ "${NODE_ENV:-production}" = "production" ]; then
        if curl -f -s https://"$DOMAIN"/ > /dev/null 2>&1; then
            log_success "Production domain health check passed"
        else
            log_warning "Production domain not responding (SSL/DNS may need configuration)"
        fi
    fi
}

# Main deployment function
deploy() {
    log_info "Starting deployment of $APP_NAME..."

    check_user
    check_node_version
    check_pm2
    create_directories
    backup_deployment
    install_dependencies
    build_application
    deploy_with_pm2
    health_check

    log_success "Deployment completed successfully!"
    log_info "You can monitor the application with: pm2 monit"
    log_info "View logs with: pm2 logs $APP_NAME"
    log_info "Access the application at: https://$DOMAIN"
}

# Script execution
case "${1:-deploy}" in
    "deploy")
        deploy
        ;;
    "backup")
        backup_deployment
        ;;
    "health")
        health_check
        ;;
    "logs")
        pm2 logs "$APP_NAME"
        ;;
    "status")
        pm2 status
        ;;
    "restart")
        cd "$APP_DIR"
        pm2 restart "$APP_NAME"
        ;;
    "stop")
        pm2 stop "$APP_NAME"
        ;;
    "start")
        cd "$APP_DIR"
        pm2 start ecosystem.config.cjs --env production
        pm2 save
        ;;
    *)
        echo "Usage: $0 {deploy|backup|health|logs|status|restart|stop|start}"
        echo ""
        echo "Commands:"
        echo "  deploy  - Full deployment (backup, build, restart)"
        echo "  restart - Restart the application"
        echo "  start   - Start the application"
        echo "  stop    - Stop the application"
        echo "  status  - Show PM2 status"
        echo "  logs    - Show application logs"
        echo "  health  - Run health check"
        echo "  backup  - Create backup only"
        exit 1
        ;;
esac