import type { Metadata } from 'next'
import { AccessibilityStatement } from '@/components/accessibility/AccessibilityStatement'

export const metadata: Metadata = {
  title: 'Accessibility Statement | IKIA Investment Conference',
  description: 'Our commitment to digital accessibility and inclusive design for the IKIA Investment Conference website.',
  openGraph: {
    title: 'Accessibility Statement | IKIA Investment Conference',
    description: 'Our commitment to digital accessibility and inclusive design for the IKIA Investment Conference website.',
  },
}

export default function AccessibilityPage() {
  return (
    <div className="min-h-screen bg-[#FFF8E3] py-20">
      <AccessibilityStatement />
    </div>
  )
}
