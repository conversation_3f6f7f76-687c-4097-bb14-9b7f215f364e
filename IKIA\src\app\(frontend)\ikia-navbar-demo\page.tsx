import { IkiaNavbar } from '@/components/ikia-navbar'

export default function IkiaNavbarDemo() {
  return (
    <div className="min-h-screen bg-background">
      {/* Our new IKIA navbar */}
      <IkiaNavbar />

      <main className="container mx-auto px-4 py-16">
        <div className="text-center space-y-8">
          <h1 className="text-4xl font-bold text-primary font-acquire">IKIA Navbar Demo</h1>

          <p className="text-lg text-muted-foreground font-myriad max-w-2xl mx-auto">
            This page demonstrates the new IKIA navbar component that matches the target design. The
            navbar features the IKIA logo, navigation menu items (HOM<PERSON>, ABOUT, <PERSON>EN<PERSON>, SPEAKERS,
            SHOWCASE), and action buttons (REGISTRATION, SPONSORS).
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto mt-12">
            <div className="bg-card p-6 rounded-lg border border-border">
              <h2 className="text-xl font-semibold text-primary mb-4">Features</h2>
              <ul className="text-left space-y-2 text-muted-foreground">
                <li>✓ Responsive design with mobile menu</li>
                <li>✓ IKIA brand colors and styling</li>
                <li>✓ Active page highlighting</li>
                <li>✓ Hover effects and transitions</li>
                <li>✓ Accessible navigation</li>
              </ul>
            </div>

            <div className="bg-card p-6 rounded-lg border border-border">
              <h2 className="text-xl font-semibold text-secondary mb-4">Navigation</h2>
              <ul className="text-left space-y-2 text-muted-foreground">
                <li>• HOME → /</li>
                <li>• ABOUT → /about</li>
                <li>• AGENDA → /agenda</li>
                <li>• SPEAKERS → /speakers</li>
                <li>• SHOWCASE → /exhibitions</li>
              </ul>
            </div>
          </div>

          <div className="mt-12 p-6 bg-muted/50 rounded-lg">
            <h3 className="text-lg font-semibold text-foreground mb-2">Test Instructions</h3>
            <p className="text-muted-foreground">
              Try clicking on the navigation items and action buttons to test functionality. Resize
              the browser window to test responsive behavior and mobile menu.
            </p>
          </div>
        </div>
      </main>
    </div>
  )
}
