'use client'

import { useCounties, useActiveCounties } from './CountiesContext'

/**
 * Simple debug component for counties context
 * Shows basic status without causing performance issues
 */
export function CountiesDebug() {
  const { counties, loading, error, totalCounties } = useCounties()
  const { counties: activeCounties } = useActiveCounties()

  if (loading) {
    return (
      <div className="p-2 bg-blue-50 border border-blue-200 rounded text-sm">
        Loading counties...
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-2 bg-red-50 border border-red-200 rounded text-sm">
        Error: {error}
      </div>
    )
  }

  return (
    <div className="p-2 bg-green-50 border border-green-200 rounded text-sm">
      <strong>Counties:</strong> {counties.length} total, {activeCounties.length} active
    </div>
  )
}

export default CountiesDebug
