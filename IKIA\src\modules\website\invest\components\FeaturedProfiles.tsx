'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { ExternalLink, User, Building, MapPin, Star, Award, AlertTriangle } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { useGetFeaturedUsersQuery } from '@/lib/api/usersApi'
import type { User as UserType } from '@/modules/website/invest/types'

const profileTypes = [
  { id: 'investor', label: 'INVESTORS', icon: Building, color: 'bg-[#7E2518]' },
  { id: 'exhibitor', label: 'EXHIBITORS', icon: User, color: 'bg-[#159147]' },
]

// Helper functions
const getProfileImage = (user: UserType): string => {
  if (typeof user.profileImage === 'object' && user.profileImage !== null) {
    return (
      user.profileImage.sizes?.medium?.url ||
      user.profileImage.url ||
      '/placeholder.svg?height=200&width=200&text=Profile'
    )
  }
  return '/placeholder.svg?height=200&width=200&text=Profile'
}

const getCountyInfo = (county: UserType['county']) => {
  if (typeof county === 'object' && county !== null) {
    return county.name
  }
  return null
}

const getInvestmentDisplay = (user: UserType): string => {
  if (user.userType === 'investor' && user.investmentInfo?.totalInvestment) {
    return user.investmentInfo.totalInvestment
  }
  return 'Investment details available'
}

const getAchievementDisplay = (user: UserType): string => {
  if (user.userType === 'exhibitor' && user.exhibitorInfo?.achievement) {
    return user.exhibitorInfo.achievement
  }
  return 'Professional achievement'
}

const getProjectsDisplay = (user: UserType): string => {
  if (user.userType === 'investor' && user.investmentInfo?.activeProjects) {
    return user.investmentInfo.activeProjects
  }
  if (user.userType === 'exhibitor' && user.exhibitorInfo?.projectsCompleted) {
    return user.exhibitorInfo.projectsCompleted
  }
  return 'Projects available'
}

export default function FeaturedProfiles() {
  const [activeTab, setActiveTab] = useState('investor')

  // Get featured users from API
  const {
    data: featuredData,
    error,
    isLoading,
  } = useGetFeaturedUsersQuery({
    userType: activeTab,
    limit: 2,
  })

  const featuredUsers = featuredData?.docs || []
  const activeType = profileTypes.find((type) => type.id === activeTab)

  // Loading state
  if (isLoading) {
    return (
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2
              className="font-myriad font-bold text-sm uppercase tracking-wider text-muted-foreground mb-4"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              COMMUNITY
            </h2>
            <h3
              className="font-myriad font-bold text-3xl lg:text-4xl text-primary mb-6"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Meet Our Community
            </h3>
          </div>
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-1 bg-gray-200 animate-pulse h-96"></div>
            <div className="lg:col-span-2 grid md:grid-cols-2 gap-6">
              {[...Array(2)].map((_, index) => (
                <div key={index} className="bg-gray-200 animate-pulse h-96"></div>
              ))}
            </div>
          </div>
        </div>
      </section>
    )
  }

  // Error state
  if (error) {
    return (
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center">
            <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h3 className="font-myriad font-bold text-xl text-gray-900 mb-2">
              Unable to Load Featured Profiles
            </h3>
            <p className="text-gray-600 mb-6">
              We&apos;re having trouble loading the featured profiles. Please try again later.
            </p>
            <Button
              onClick={() => window.location.reload()}
              className="bg-[#7E2518] hover:bg-[#6B1F14] text-white font-semibold"
            >
              Try Again
            </Button>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2
            className="font-myriad font-bold text-sm uppercase tracking-wider text-muted-foreground mb-4"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            COMMUNITY
          </h2>
          <h3
            className="font-myriad font-bold text-3xl lg:text-4xl text-primary mb-6"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            Meet Our Community
          </h3>
          <p
            className="font-myriad text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed"
            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
          >
            Connect with verified investors, authentic knowledge holders, and innovative
            entrepreneurs who are shaping the future of Indigenous Knowledge assets.
          </p>
        </div>

        {/* Enhanced Tab Navigation */}
        <div className="flex flex-wrap justify-center gap-4 mb-16">
          {profileTypes.map((type) => (
            <Button
              key={type.id}
              onClick={() => setActiveTab(type.id)}
              className={`px-8 py-4 font-bold text-lg transition-all duration-300 transform hover:scale-105 ${
                activeTab === type.id
                  ? `${type.color} text-white shadow-2xl`
                  : 'bg-white text-[#7E2518] border-2 border-[#7E2518] hover:bg-[#7E2518] hover:text-white shadow-lg'
              }`}
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              <type.icon className="w-5 h-5 mr-3" />
              {type.label}
            </Button>
          ))}
        </div>

        {/* Profile Cards Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Featured Profile Card */}
          <div className="lg:col-span-1">
            <div
              className={`${activeType?.color} text-white shadow-2xl overflow-hidden transform hover:scale-105 transition-all duration-300`}
            >
              <div className="p-8 text-center">
                <div className="w-24 h-24 bg-white/20 mx-auto mb-6 flex items-center justify-center">
                  {activeType && <activeType.icon className="w-12 h-12 text-white" />}
                </div>
                <h3
                  className="text-2xl font-bold mb-4"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  Featured {activeTab === 'investor' ? 'Investor' : 'Exhibitor'} Bio
                </h3>
                <p
                  className="text-white/90 mb-6 leading-relaxed"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  Discover verified profiles of leading {activeTab}s in the IKIA ecosystem. Each
                  profile is carefully vetted for authenticity and impact.
                </p>
                <Link href={`/invest/${activeTab}s`}>
                  <Button
                    variant="outline"
                    className="border-2 border-white text-white hover:bg-white hover:text-[#7E2518] bg-transparent font-bold"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View All Profiles
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Individual Profile Cards */}
          <div className="lg:col-span-2 grid md:grid-cols-2 gap-6">
            {featuredUsers.length > 0 ? (
              featuredUsers.map((user, index) => (
                <div
                  key={index}
                  className="bg-white border border-gray-200 shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden group hover:-translate-y-2"
                >
                  {/* Profile Header */}
                  <div className="relative p-6 pb-4">
                    {/* Badge */}
                    <div className="absolute top-4 right-4">
                      <span
                        className={`${activeType?.color} text-white text-xs font-bold px-3 py-1`}
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        {user.badge || (user.userType === 'investor' ? 'Investor' : 'Exhibitor')}
                      </span>
                    </div>

                    {/* Profile Image & Basic Info */}
                    <div className="flex items-start space-x-4">
                      <div className="relative">
                        <Image
                          src={getProfileImage(user)}
                          alt={user.name}
                          width={64}
                          height={64}
                          className="w-16 h-16 object-cover border-2 border-gray-200"
                        />
                        {user.verified && (
                          <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-[#159147] flex items-center justify-center">
                            <Award className="w-3 h-3 text-white" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <h4
                          className="text-lg font-bold text-[#7E2518] mb-1"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {user.name}
                        </h4>
                        <p
                          className="text-[#159147] font-semibold text-sm mb-1"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {user.title || 'Professional'}
                        </p>
                        <p
                          className="text-gray-600 text-sm"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {user.organization || 'Organization'}
                        </p>
                      </div>
                    </div>

                    {/* Location & Rating */}
                    <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
                      <div
                        className="flex items-center text-gray-500 text-sm"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        <MapPin className="w-4 h-4 mr-1" />
                        {getCountyInfo(user.county) || user.location || 'Location'}
                      </div>
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-[#E8B32C] fill-current mr-1" />
                        <span
                          className="text-sm font-semibold text-gray-700"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {user.rating || '4.5'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Profile Details */}
                  <div className="px-6 pb-6">
                    <div className="space-y-3 text-sm">
                      <div>
                        <span
                          className="font-semibold text-gray-700"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          Focus Area:
                        </span>
                        <p
                          className="text-gray-600 mt-1"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {user.focus || 'Professional focus area'}
                        </p>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <span
                            className="font-semibold text-gray-700"
                            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                          >
                            {activeTab === 'investors' ? 'Investment:' : 'Achievement:'}
                          </span>
                          <p
                            className="text-[#C86E36] font-semibold mt-1"
                            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                          >
                            {activeTab === 'investor'
                              ? getInvestmentDisplay(user)
                              : getAchievementDisplay(user)}
                          </p>
                        </div>
                        <div>
                          <span
                            className="font-semibold text-gray-700"
                            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                          >
                            Projects:
                          </span>
                          <p
                            className="text-[#159147] font-semibold mt-1"
                            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                          >
                            {getProjectsDisplay(user)}
                          </p>
                        </div>
                      </div>
                    </div>

                    <Button
                      size="sm"
                      className={`w-full mt-6 ${activeType?.color} hover:opacity-90 text-white font-semibold`}
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View Full Profile
                    </Button>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-2 text-center py-8">
                <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h4
                  className="text-lg font-semibold text-gray-600 mb-2"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  No Featured {activeTab === 'investor' ? 'Investors' : 'Exhibitors'} Yet
                </h4>
                <p
                  className="text-gray-500"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  Check back soon for featured profiles in this category.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  )
}
