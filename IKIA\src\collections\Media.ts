import type { CollectionConfig } from 'payload'

import {
  FixedToolbarFeature,
  InlineToolbarFeature,
  lexicalEditor,
} from '@payloadcms/richtext-lexical'
import path from 'path'
import { fileURLToPath } from 'url'

import { anyone } from '../access/anyone'
import { authenticated } from '../access/authenticated'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export const Media: CollectionConfig = {
  slug: 'media',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  fields: [
    {
      name: 'alt',
      type: 'text',
      //required: true,
    },
    {
      name: 'caption',
      type: 'richText',
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [...rootFeatures, FixedToolbarFeature(), InlineToolbarFeature()]
        },
      }),
    },
  ],
  upload: {
    // Files will be stored in Vercel Blob storage if configured, otherwise local storage
    // When Vercel Blob is not configured, files are stored locally
    ...(process.env.BLOB_READ_WRITE_TOKEN &&
    process.env.BLOB_READ_WRITE_TOKEN.startsWith('vercel_blob_rw_')
      ? {} // No staticDir needed for cloud storage
      : { staticDir: path.resolve(dirname, '../../public/media') }), // Local storage fallback
    adminThumbnail: 'thumbnail',
    focalPoint: true,
    // Optimized image processing - essential sizes only
    imageSizes: [
      {
        name: 'thumbnail',
        width: 300,
        // Essential for cards, lists, previews
      },
      {
        name: 'medium',
        width: 800,
        // Good balance for most content
      },
      {
        name: 'og',
        width: 1200,
        height: 630,
        crop: 'center',
        // Required for social media sharing
      },
    ],
  },
  hooks: {
    afterChange: [
      ({ doc, operation }) => {
        // Force garbage collection after image processing
        if (operation === 'create' && global.gc) {
          setTimeout(() => {
            if (global.gc) {
              global.gc()
              console.log(`🧹 Garbage collection triggered after processing: ${doc.filename}`)
            }
          }, 1000)
        }
      },
    ],
  },
}
