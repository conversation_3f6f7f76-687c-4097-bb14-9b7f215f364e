// Invest Module Types

// User from API
export interface User {
  id: number
  name: string
  email: string
  userType: 'investor' | 'exhibitor' | 'delegate' | 'admin'
  county?:
    | {
        id: number
        name: string
        code: string
      }
    | number
    | null
  featured: boolean
  title?: string | null
  organization?: string | null
  location?: string | null
  focus?: string | null
  bio?: {
    root: {
      type: string
      children: {
        type: string
        version: number
        [k: string]: unknown
      }[]
      direction: ('ltr' | 'rtl') | null
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
      indent: number
      version: number
    }
    [k: string]: unknown
  } | null
  profileImage?:
    | {
        id: number
        url: string
        alt?: string
        width?: number
        height?: number
        sizes?: {
          thumbnail?: { url: string; width: number; height: number } | null
          square?: { url: string; width: number; height: number } | null
          small?: { url: string; width: number; height: number } | null
          medium?: { url: string; width: number; height: number } | null
          large?: { url: string; width: number; height: number } | null
        }
      }
    | number
    | null
  website?: string | null
  socialLinks?: Array<{
    platform: 'linkedin' | 'twitter' | 'facebook' | 'instagram' | 'other'
    url: string
    id?: string | null
  }> | null
  investmentInfo?: {
    totalInvestment?: string | null
    activeProjects?: string | null
    investmentAreas?: Array<{
      area: string
      id?: string | null
    }> | null
    investmentRange?: 'under-50k' | '50k-100k' | '100k-500k' | '500k-1m' | 'over-1m' | null
  } | null
  exhibitorInfo?: {
    achievement?: string | null
    projectsCompleted?: string | null
    specializations?: Array<{
      specialization: string
      id?: string | null
    }> | null
    certifications?: Array<{
      certification: string
      id?: string | null
    }> | null
  } | null
  rating?: number | null
  verified: boolean
  badge?: string | null
  updatedAt: string
  createdAt: string
}

// Legacy interfaces for backward compatibility
export interface BaseProfile {
  name: string
  title: string
  organization: string
  location: string
  focus: string
  rating: number
  image: string
  badge: string
  verified: boolean
}

export interface InvestorProfile extends BaseProfile {
  investment: string
  projects: string
}

export interface ExhibitorProfile extends BaseProfile {
  achievement: string
  projects: string
}

export interface ProfileData {
  investors: {
    title: string
    profiles: InvestorProfile[]
  }
  exhibitors: {
    title: string
    profiles: ExhibitorProfile[]
  }
}

export interface ProfileModalProps {
  profile: InvestorProfile | ExhibitorProfile
  isOpen: boolean
  onClose: () => void
  type: 'investor' | 'exhibitor'
}

export interface ProfileCardProps {
  profile: InvestorProfile | ExhibitorProfile
  type: 'investor' | 'exhibitor'
  onClick: () => void
}

export interface ProfileGridProps {
  profiles: InvestorProfile[] | ExhibitorProfile[]
  type: 'investor' | 'exhibitor'
  title: string
}

// Type guards
export const isInvestorProfile = (
  profile: InvestorProfile | ExhibitorProfile,
): profile is InvestorProfile => {
  return 'investment' in profile
}

export const isExhibitorProfile = (
  profile: InvestorProfile | ExhibitorProfile,
): profile is ExhibitorProfile => {
  return 'achievement' in profile
}
