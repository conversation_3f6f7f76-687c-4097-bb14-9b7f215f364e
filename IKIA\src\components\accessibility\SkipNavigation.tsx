'use client'

import React from 'react'
import { cn } from '@/utilities/ui'

interface SkipNavigationProps {
  className?: string
}

export const SkipNavigation: React.FC<SkipNavigationProps> = ({ className }) => {
  return (
    <div className={cn('sr-only focus-within:not-sr-only', className)}>
      <a
        href="#main-content"
        className="absolute top-4 left-4 z-50 bg-[#7E2518] text-white px-4 py-2 rounded-md font-myriad font-semibold text-sm focus:outline-none focus:ring-2 focus:ring-[#E8B32C] focus:ring-offset-2 transition-all duration-200"
      >
        Skip to main content
      </a>
      <a
        href="#navigation"
        className="absolute top-4 left-32 z-50 bg-[#7E2518] text-white px-4 py-2 rounded-md font-myriad font-semibold text-sm focus:outline-none focus:ring-2 focus:ring-[#E8B32C] focus:ring-offset-2 transition-all duration-200"
      >
        Skip to navigation
      </a>
    </div>
  )
}
