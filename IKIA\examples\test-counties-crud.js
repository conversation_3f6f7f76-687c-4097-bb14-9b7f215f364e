// Test script for Counties CRUD API endpoints
// Run with: node examples/test-counties-crud.js

const BASE_URL = 'http://localhost:3000/api'

// Test credentials - make sure you have a user with these credentials
const TEST_EMAIL = '<EMAIL>'
const TEST_PASSWORD = 'password123'

let authToken = null
let createdCountyId = null

async function login() {
  console.log('🔐 Logging in to get auth token...')
  
  try {
    const response = await fetch(`${BASE_URL}/users/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: TEST_EMAIL,
        password: TEST_PASSWORD,
      }),
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Login successful!')
      authToken = data.token
      return true
    } else {
      console.log('❌ Login failed:', data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Login error:', error.message)
    return false
  }
}

async function testCreateCounty() {
  console.log('\n📝 Test 1: CREATE County')
  
  if (!authToken) {
    console.log('❌ No auth token available')
    return false
  }

  const countyData = {
    name: 'Test County',
    code: 'TEST-001',
    coordinates: {
      latitude: -1.5000,
      longitude: 36.5000
    },
    description: 'A test county for CRUD operations',
    isActive: true
  }

  try {
    const response = await fetch(`${BASE_URL}/counties`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(countyData),
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ County created successfully!')
      console.log(`County ID: ${data.county.id}`)
      console.log(`Name: ${data.county.name}`)
      console.log(`Code: ${data.county.code}`)
      createdCountyId = data.county.id
      return true
    } else {
      console.log('❌ Create failed:', data.error || data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Create error:', error.message)
    return false
  }
}

async function testReadCounties() {
  console.log('\n📋 Test 2: READ Counties (List)')
  
  try {
    const response = await fetch(`${BASE_URL}/counties?limit=5`)
    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ Counties retrieved successfully!')
      console.log(`Total counties: ${data.totalCounties}`)
      console.log(`Counties on this page: ${data.counties.length}`)
      data.counties.forEach(county => {
        console.log(`- ${county.name} (${county.code}): ${county.coordinates.latitude}, ${county.coordinates.longitude}`)
      })
      return true
    } else {
      console.log('❌ Read failed:', data.error || data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Read error:', error.message)
    return false
  }
}

async function testReadSingleCounty() {
  console.log('\n🏛️  Test 3: READ Single County')
  
  if (!createdCountyId) {
    console.log('❌ No county ID available')
    return false
  }

  try {
    const response = await fetch(`${BASE_URL}/counties/${createdCountyId}`)
    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ County retrieved successfully!')
      console.log(`Name: ${data.county.name}`)
      console.log(`Code: ${data.county.code}`)
      console.log(`Description: ${data.county.description}`)
      console.log(`Active: ${data.county.isActive}`)
      console.log(`Coordinates: ${data.county.coordinates.latitude}, ${data.county.coordinates.longitude}`)
      return true
    } else {
      console.log('❌ Read single failed:', data.error || data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Read single error:', error.message)
    return false
  }
}

async function testUpdateCounty() {
  console.log('\n✏️  Test 4: UPDATE County')
  
  if (!authToken || !createdCountyId) {
    console.log('❌ No auth token or county ID available')
    return false
  }

  const updateData = {
    name: 'Updated Test County',
    description: 'Updated description for the test county',
    coordinates: {
      latitude: -1.6000,
      longitude: 36.6000
    }
  }

  try {
    const response = await fetch(`${BASE_URL}/counties/${createdCountyId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(updateData),
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ County updated successfully!')
      console.log(`Updated name: ${data.county.name}`)
      console.log(`Updated description: ${data.county.description}`)
      console.log(`Updated coordinates: ${data.county.coordinates.latitude}, ${data.county.coordinates.longitude}`)
      return true
    } else {
      console.log('❌ Update failed:', data.error || data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Update error:', error.message)
    return false
  }
}

async function testPartialUpdate() {
  console.log('\n🔄 Test 5: PARTIAL UPDATE County')
  
  if (!authToken || !createdCountyId) {
    console.log('❌ No auth token or county ID available')
    return false
  }

  const partialUpdateData = {
    isActive: false
  }

  try {
    const response = await fetch(`${BASE_URL}/counties/${createdCountyId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(partialUpdateData),
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ County partially updated successfully!')
      console.log(`Active status: ${data.county.isActive}`)
      console.log(`Name unchanged: ${data.county.name}`)
      return true
    } else {
      console.log('❌ Partial update failed:', data.error || data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Partial update error:', error.message)
    return false
  }
}

async function testDeleteCounty() {
  console.log('\n🗑️  Test 6: DELETE County')
  
  if (!authToken || !createdCountyId) {
    console.log('❌ No auth token or county ID available')
    return false
  }

  try {
    const response = await fetch(`${BASE_URL}/counties/${createdCountyId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authToken}`,
      },
    })

    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ County deleted successfully!')
      console.log(`Deleted county ID: ${data.id}`)
      return true
    } else {
      console.log('❌ Delete failed:', data.error || data.message)
      return false
    }
  } catch (error) {
    console.error('❌ Delete error:', error.message)
    return false
  }
}

async function testValidationErrors() {
  console.log('\n⚠️  Test 7: VALIDATION Errors')
  
  if (!authToken) {
    console.log('❌ No auth token available')
    return false
  }

  // Test invalid coordinates
  const invalidData = {
    name: 'Invalid County',
    code: 'INVALID-001',
    coordinates: {
      latitude: 100, // Invalid: > 90
      longitude: 200 // Invalid: > 180
    }
  }

  try {
    const response = await fetch(`${BASE_URL}/counties`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(invalidData),
    })

    const data = await response.json()
    
    if (response.status === 400) {
      console.log('✅ Validation error caught correctly!')
      console.log(`Error: ${data.error}`)
      return true
    } else {
      console.log('❌ Validation should have failed')
      return false
    }
  } catch (error) {
    console.error('❌ Validation test error:', error.message)
    return false
  }
}

async function runCRUDTests() {
  console.log('🧪 Starting Counties CRUD API Tests')
  console.log('=' .repeat(50))
  
  // Login first
  const loginSuccess = await login()
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication')
    return
  }

  // Run CRUD tests in sequence
  await testCreateCounty()
  await testReadCounties()
  await testReadSingleCounty()
  await testUpdateCounty()
  await testPartialUpdate()
  await testValidationErrors()
  await testDeleteCounty()
  
  console.log('\n' + '=' .repeat(50))
  console.log('🏁 Counties CRUD tests completed!')
}

// Run the tests
runCRUDTests().catch(console.error)
