# =============================================================================
# CORE APPLICATION CONFIGURATION
# =============================================================================

# Database connection string (primary)
DATABASE_URL=postgresql://username:password@127.0.0.1:5432/database_name
# Backward compatibility (fallback)
DATABASE_URI=postgres://username:password@127.0.0.1:5432/database_name

# Database Optimization Settings
DATABASE_MAX_CONNECTIONS=20
DATABASE_IDLE_TIMEOUT=30000
DATABASE_ACQUIRE_TIMEOUT=60000
DATABASE_CONNECTION_POOL_MIN=5
DATABASE_CONNECTION_POOL_MAX=20

# Used to encrypt JWT tokens (generate a random string)
PAYLOAD_SECRET=your-payload-secret-here

# Used to configure CORS, format links and more. No trailing slash
NEXT_PUBLIC_SERVER_URL=http://localhost:3000

# Secret used to authenticate cron jobs
CRON_SECRET=your-cron-secret-here

# Used to validate preview requests
PREVIEW_SECRET=your-preview-secret-here

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================

SMTP_HOST=smtp.gmail.com
SMTP_PORT=465
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Your App Name

# =============================================================================
# PESAFLOW PAYMENT INTEGRATION
# =============================================================================

# Core Pesaflow Configuration (Required by HashService)
PESAFLOW_API_CLIENT_ID=your_pesaflow_client_id
PESAFLOW_CLIENT_SECRET=your_pesaflow_client_secret
PESAFLOW_CLIENT_KEY=your_pesaflow_client_key
PESAFLOW_UAT_SERVER_URL=https://your-pesaflow-server.com

# Pesaflow Checkout Configuration
PESAFLOW_REQUEST_SERVICE_ID=your_service_id
PESAFLOW_BILL_DESC=Payment for services
PESAFLOW_NOTIFICATION_URL=https://yoursite.com/api/payment/ipn
PESAFLOW_CALLBACK_SUCCESS_URL=https://yoursite.com/payment/success
PESAFLOW_PICTURE_URL=https://yoursite.com/logo.png

# =============================================================================
# ECITIZEN SSO INTEGRATION
# =============================================================================

# eCitizen OAuth Configuration (All URLs required - no defaults)
ECITIZEN_CLIENT_ID=your_ecitizen_client_id
ECITIZEN_CLIENT_SECRET=your_ecitizen_client_secret
ECITIZEN_AUTHORIZATION_URL=https://accounts.ecitizen.go.ke/oauth/authorize
ECITIZEN_TOKEN_URL=https://accounts.ecitizen.go.ke/oauth/access-token
ECITIZEN_INTROSPECTION_URL=https://accounts.ecitizen.go.ke/api/oauth/token/introspect
ECITIZEN_USERINFO_URL=https://accounts.ecitizen.go.ke/api/userinfo

# =============================================================================
# ECITIZEN USSD INTEGRATION
# =============================================================================

# eCitizen USSD Configuration
ECITIZEN_MERCHANT_KEY=your_merchant_key
ECITIZEN_MERCHANT_SECRET=your_merchant_secret

# =============================================================================
# IPN OPTIMIZATION SETTINGS
# =============================================================================

# Enable optimized IPN processor for high-volume scenarios
USE_OPTIMIZED_IPN_PROCESSOR=true

# IPN Cache Settings
IPN_CACHE_DURATION_MINUTES=5
IPN_MAX_CONCURRENT_PROCESSING=50
IPN_ENABLE_PERFORMANCE_LOGGING=false

# =============================================================================
# PERFORMANCE MONITORING SETTINGS
# =============================================================================

# Request Logging and Metrics
ENABLE_REQUEST_LOGGING=true
ENABLE_PERFORMANCE_METRICS=true
LOG_LEVEL=info
REQUEST_TIMEOUT_MS=30000

# API Rate Limiting
ENABLE_RATE_LIMITING=true
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_IPN_MAX_REQUESTS=500

# =============================================================================
# SCALING RECOMMENDATIONS
# =============================================================================

# For high-volume production (1000+ IPNs/minute):
# DATABASE_MAX_CONNECTIONS=50
# IPN_MAX_CONCURRENT_PROCESSING=100
# RATE_LIMIT_IPN_MAX_REQUESTS=1000

# =============================================================================
# VERCEL BLOB STORAGE CONFIGURATION
# =============================================================================

# Vercel Blob Storage Token (get from Vercel dashboard)
BLOB_READ_WRITE_TOKEN=your_vercel_blob_token_here

# Optional: Custom blob store configuration
# VERCEL_BLOB_STORE_ID=your_store_id

# For production deployment:
# NODE_ENV=production
# COOKIE_SECURE=true
# PESAFLOW_UAT_SERVER_URL=https://api.pesaflow.com

# =============================================================================
# DEVELOPMENT NOTES
# =============================================================================

# 1. Copy this file to .env and replace placeholder values
# 2. Generate secure random strings for secrets
# 3. Get actual credentials from Pesaflow and eCitizen
# 4. Update URLs to point to your actual domains
# 5. Run: node scripts/validate-env.js to validate configuration
