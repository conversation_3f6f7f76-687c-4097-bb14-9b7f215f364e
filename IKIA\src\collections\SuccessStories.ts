import type { CollectionConfig } from 'payload'

export const SuccessStories: CollectionConfig = {
  slug: 'success-stories',
  admin: {
    useAsTitle: 'title',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media',
    },
    // Investor Information
    {
      name: 'investor',
      type: 'group',
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
        },
        {
          name: 'position',
          type: 'text',
          required: true,
        },
      ],
    },
    // Knowledge Holder Information
    {
      name: 'knowledgeHolder',
      type: 'group',
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
        },
        {
          name: 'title',
          type: 'text',
          required: true,
        },
      ],
    },
    // Impact Metrics
    {
      name: 'impact',
      type: 'array',
      fields: [
        {
          name: 'metric',
          type: 'text',
          required: true,
        },
        {
          name: 'value',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'message',
      type: 'textarea',
      required: true,
    },
    {
      name: 'messageAuthor',
      type: 'text',
    },
    {
      name: 'investmentAmount',
      type: 'number',
      required: true,
    },
    {
      name: 'currency',
      type: 'select',
      defaultValue: 'KES',
      options: [
        { label: 'KES', value: 'KES' },
        { label: 'USD', value: 'USD' },
        { label: 'EUR', value: 'EUR' },
      ],
    },
    {
      name: 'timeline',
      type: 'text',
      required: true,
    },
    // Location Information
    {
      name: 'location',
      type: 'group',
      fields: [
        {
          name: 'county',
          type: 'text',
          required: true,
        },
        {
          name: 'country',
          type: 'text',
          defaultValue: 'Kenya',
          required: true,
        },
      ],
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
    },
  ],
}

export default SuccessStories