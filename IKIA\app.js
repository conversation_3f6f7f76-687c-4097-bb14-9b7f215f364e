#!/usr/bin/env node

/**
 * Passenger-compatible startup file for Next.js application
 * This file is required by Passenger to start the Node.js application
 */

const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')

// Set environment variables
process.env.NODE_ENV = process.env.NODE_ENV || 'production'
process.env.PORT = process.env.PORT || '3000'

const dev = process.env.NODE_ENV !== 'production'
const hostname = 'localhost'
const port = parseInt(process.env.PORT, 10) || 3000

// Create Next.js app
const app = next({ dev, hostname, port })
const handle = app.getRequestHandler()

console.log(`Starting Next.js app in ${process.env.NODE_ENV} mode...`)

app
  .prepare()
  .then(() => {
    const server = createServer(async (req, res) => {
      try {
        // Parse the URL
        const parsedUrl = parse(req.url, true)

        // Handle the request with Next.js
        await handle(req, res, parsedUrl)
      } catch (err) {
        console.error('Error occurred handling', req.url, err)
        res.statusCode = 500
        res.end('Internal Server Error')
      }
    })

    server.listen(port, (err) => {
      if (err) {
        console.error('Failed to start server:', err)
        process.exit(1)
      }
      console.log(`> Ready on http://${hostname}:${port}`)
    })

    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('Received SIGTERM, shutting down gracefully...')
      server.close(() => {
        console.log('Server closed')
        process.exit(0)
      })
    })

    process.on('SIGINT', () => {
      console.log('Received SIGINT, shutting down gracefully...')
      server.close(() => {
        console.log('Server closed')
        process.exit(0)
      })
    })
  })
  .catch((err) => {
    console.error('Failed to start Next.js app:', err)
    process.exit(1)
  })
