'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { ArrowRight, Download, Handshake, Users, Award, Target } from 'lucide-react'
import Link from 'next/link'

export default function SponsorshipCTA() {
  return (
    <section className="py-16 lg:py-20 section-bg-secondary">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Main CTA Card */}
          <Card className="bg-[#7E2518] border-0 main-shadow overflow-hidden relative">
            <CardContent className="relative p-12 lg:p-16 text-center text-white">
              {/* Icon */}
              <div className="w-20 h-20 bg-white/20 flex items-center justify-center mx-auto mb-8">
                <Handshake className="w-10 h-10 text-white" />
              </div>

              {/* Heading */}
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 font-['Myriad_Pro',Arial,sans-serif]">
                Ready to Make an
                <span className="block bg-gradient-to-r from-[#E8B32C] to-[#C86E36] bg-clip-text text-transparent">
                  Impact?
                </span>
              </h2>

              <div className="w-24 h-1 bg-[#E8B32C] mx-auto mb-6"></div>

              <p className="text-xl text-white/90 leading-relaxed max-w-3xl mx-auto mb-12 font-['Myriad_Pro',Arial,sans-serif]">
                Join leading organizations in supporting Kenya&apos;s indigenous knowledge
                preservation and commercialization. Be part of a historic conference that bridges
                tradition with innovation.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
                <Button
                  asChild
                  size="lg"
                  className="bg-white text-[#7E2518] hover:bg-gray-100 font-bold transition-all duration-300 px-8 py-4 main-shadow font-['Myriad_Pro',Arial,sans-serif]"
                >
                  <Link href="/partners-sponsors#sponsor" className="flex items-center gap-2">
                    <Handshake className="w-5 h-5" />
                    Become a Sponsor
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </Button>

                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-2 border-white text-white hover:bg-white hover:text-[#7E2518] font-bold transition-all duration-300 px-8 py-4 bg-transparent main-shadow font-['Myriad_Pro',Arial,sans-serif]"
                >
                  <Link
                    href="/downloads/sponsorship-prospectus.pdf"
                    className="flex items-center gap-2"
                  >
                    <Download className="w-5 h-5" />
                    Download Prospectus
                  </Link>
                </Button>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="w-12 h-12 bg-white/20 flex items-center justify-center mx-auto mb-4">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-3xl font-bold mb-2 font-['Myriad_Pro',Arial,sans-serif]">
                    500+
                  </div>
                  <p className="text-white/80 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                    Expected Delegates
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-white/20 flex items-center justify-center mx-auto mb-4">
                    <Award className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-3xl font-bold mb-2 font-['Myriad_Pro',Arial,sans-serif]">
                    25+
                  </div>
                  <p className="text-white/80 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                    Partner Organizations
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 bg-white/20 flex items-center justify-center mx-auto mb-4">
                    <Target className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-3xl font-bold mb-2 font-['Myriad_Pro',Arial,sans-serif]">
                    40+
                  </div>
                  <p className="text-white/80 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                    Indigenous Knowledge Assets
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Benefits Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
            {[
              {
                icon: Users,
                title: 'Network Access',
                description: 'Connect with government officials, investors, and industry leaders',
                color: '#159147',
              },
              {
                icon: Award,
                title: 'Brand Visibility',
                description: 'Showcase your brand to a targeted, high-value audience',
                color: '#E8B32C',
              },
              {
                icon: Target,
                title: 'Thought Leadership',
                description: 'Position your organization as a leader in cultural preservation',
                color: '#7E2518',
              },
              {
                icon: Handshake,
                title: 'Partnership Opportunities',
                description: 'Discover new business opportunities and strategic partnerships',
                color: '#81B1DB',
              },
            ].map((benefit, index) => (
              <Card
                key={index}
                className="bg-white border border-gray-200 main-shadow hover:shadow-xl transition-all duration-300 group"
              >
                <CardContent className="p-6 text-center">
                  <div
                    className="w-12 h-12 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300"
                    style={{ backgroundColor: benefit.color }}
                  >
                    <benefit.icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="font-bold text-[#7E2518] mb-2 font-['Myriad_Pro',Arial,sans-serif]">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                    {benefit.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
