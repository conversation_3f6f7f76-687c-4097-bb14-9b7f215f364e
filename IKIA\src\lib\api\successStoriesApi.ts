import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

export interface SuccessStory {
  id: number
  title: string
  description: string
  image?: {
    id: number
    url: string
    alt?: string
    width?: number
    height?: number
  }
  investor: {
    name: string
    position: string
  }
  knowledgeHolder: {
    name: string
    title: string
  }
  impact: Array<{
    metric: string
    value: string
  }>
  message: string
  messageAuthor?: string
  investmentAmount: number
  currency: string
  timeline: string
  location: {
    county: string
    country: string
  }
  featured: boolean
  isActive: boolean
  updatedAt: string
  createdAt: string
}

export interface SuccessStoriesResponse {
  docs: SuccessStory[]
  totalDocs: number
  limit: number
  totalPages: number
  page: number
  pagingCounter: number
  hasPrevPage: boolean
  hasNextPage: boolean
  prevPage?: number | null
  nextPage?: number | null
}

export interface SuccessStoriesQueryParams {
  limit?: number
  page?: number
  sort?: string
  where?: {
    featured?: {
      equals?: boolean
    }
    isActive?: {
      equals?: boolean
    }
    title?: {
      contains?: string
    }
  }
}

export const successStoriesApi = createApi({
  reducerPath: 'successStoriesApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  tagTypes: ['SuccessStory'],
  endpoints: (builder) => ({
    getSuccessStories: builder.query<SuccessStoriesResponse, SuccessStoriesQueryParams | void>({
      query: (params) => {
        const searchParams = new URLSearchParams()

        if (!params) {
          return 'success-stories?limit=0&sort=-createdAt'
        }

        if (params.limit !== undefined) {
          searchParams.append('limit', params.limit.toString())
        }
        if (params.page !== undefined) {
          searchParams.append('page', params.page.toString())
        }
        if (params.sort) {
          searchParams.append('sort', params.sort)
        }

        // Handle where conditions
        if (params.where) {
          if (params.where.featured?.equals !== undefined) {
            searchParams.append('where[featured][equals]', params.where.featured.equals.toString())
          }
          if (params.where.isActive?.equals !== undefined) {
            searchParams.append('where[isActive][equals]', params.where.isActive.equals.toString())
          }
          if (params.where.title?.contains) {
            searchParams.append('where[title][contains]', params.where.title.contains)
          }
        }

        return `success-stories?${searchParams.toString()}`
      },
      providesTags: ['SuccessStory'],
    }),

    getFeaturedSuccessStories: builder.query<SuccessStoriesResponse, void>({
      query: () => 'success-stories?where[featured][equals]=true&where[isActive][equals]=true&sort=-createdAt',
      providesTags: ['SuccessStory'],
    }),

    getSuccessStory: builder.query<SuccessStory, string | number>({
      query: (id) => `success-stories/${id}`,
      providesTags: ['SuccessStory'],
    }),
  }),
})

export const {
  useGetSuccessStoriesQuery,
  useGetFeaturedSuccessStoriesQuery,
  useGetSuccessStoryQuery,
} = successStoriesApi