#!/usr/bin/env node

/**
 * Memory Monitoring Script
 * Monitors Node.js memory usage and provides alerts
 *
 * Usage: node scripts/monitor-memory.js
 */

const BASE_URL = 'http://localhost:3001' // Production mode

function formatBytes(bytes) {
  return Math.round(bytes / 1024 / 1024) + ' MB'
}

function formatPercentage(used, total) {
  return Math.round((used / total) * 100) + '%'
}

async function getMemoryStats() {
  try {
    const response = await fetch(`${BASE_URL}/api/health-check`)
    const data = await response.json()
    return data.memory
  } catch (error) {
    console.error('❌ Failed to fetch memory stats:', error.message)
    return null
  }
}

function analyzeMemory(memory) {
  const analysis = {
    status: 'healthy',
    warnings: [],
    recommendations: [],
  }

  const heapUsagePercent = (memory.heapUsed / memory.heapTotal) * 100
  const externalMemoryMB = memory.external / 1024 / 1024
  const arrayBuffersMB = memory.arrayBuffers / 1024 / 1024

  // Heap usage analysis
  if (heapUsagePercent > 90) {
    analysis.status = 'critical'
    analysis.warnings.push('🚨 Heap usage is critically high (>90%)')
    analysis.recommendations.push('Restart the application immediately')
  } else if (heapUsagePercent > 75) {
    analysis.status = 'warning'
    analysis.warnings.push('⚠️ Heap usage is high (>75%)')
    analysis.recommendations.push('Consider restarting the application soon')
  }

  // External memory analysis
  if (externalMemoryMB > 500) {
    analysis.status = analysis.status === 'critical' ? 'critical' : 'warning'
    analysis.warnings.push(`⚠️ External memory is very high (${formatBytes(memory.external)})`)
    analysis.recommendations.push('Check for large file processing or memory leaks')
  }

  // Array buffers analysis
  if (arrayBuffersMB > 400) {
    analysis.status = analysis.status === 'critical' ? 'critical' : 'warning'
    analysis.warnings.push(`⚠️ Array buffers are very high (${formatBytes(memory.arrayBuffers)})`)
    analysis.recommendations.push('Likely caused by image/media processing - consider optimizing')
  }

  return analysis
}

function displayMemoryReport(memory, analysis) {
  console.log('\n📊 MEMORY USAGE REPORT')
  console.log('='.repeat(50))
  console.log(`📅 Timestamp: ${new Date().toLocaleString()}`)
  console.log(`🎯 Status: ${analysis.status.toUpperCase()}`)

  console.log('\n💾 Memory Breakdown:')
  console.log(`   RSS (Total):     ${formatBytes(memory.rss)}`)
  console.log(`   Heap Total:      ${formatBytes(memory.heapTotal)}`)
  console.log(
    `   Heap Used:       ${formatBytes(memory.heapUsed)} (${formatPercentage(memory.heapUsed, memory.heapTotal)})`,
  )
  console.log(`   External:        ${formatBytes(memory.external)}`)
  console.log(`   Array Buffers:   ${formatBytes(memory.arrayBuffers)}`)

  if (analysis.warnings.length > 0) {
    console.log('\n⚠️ WARNINGS:')
    analysis.warnings.forEach((warning) => console.log(`   ${warning}`))
  }

  if (analysis.recommendations.length > 0) {
    console.log('\n💡 RECOMMENDATIONS:')
    analysis.recommendations.forEach((rec) => console.log(`   • ${rec}`))
  }

  console.log('\n' + '='.repeat(50))
}

async function monitorMemory(interval = 30000) {
  console.log('🔍 Starting memory monitoring...')
  console.log(`📊 Checking every ${interval / 1000} seconds`)
  console.log('Press Ctrl+C to stop\n')

  const monitor = async () => {
    const memory = await getMemoryStats()
    if (memory) {
      const analysis = analyzeMemory(memory)
      displayMemoryReport(memory, analysis)

      // Alert for critical status
      if (analysis.status === 'critical') {
        console.log('\n🚨 CRITICAL ALERT: Memory usage is dangerously high!')
        console.log('   Consider restarting the application immediately.')
      }
    }
  }

  // Initial check
  await monitor()

  // Set up interval monitoring
  setInterval(monitor, interval)
}

// Handle command line arguments
const args = process.argv.slice(2)
const intervalArg = args.find((arg) => arg.startsWith('--interval='))
const interval = intervalArg ? parseInt(intervalArg.split('=')[1]) * 1000 : 30000

// Start monitoring
monitorMemory(interval).catch(console.error)

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Memory monitoring stopped.')
  process.exit(0)
})
