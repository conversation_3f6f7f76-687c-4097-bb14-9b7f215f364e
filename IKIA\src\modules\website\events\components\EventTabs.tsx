'use client'

import { useState } from 'react'
import AboutTab from './tabs/AboutTab'
import SpeakersTab from './tabs/SpeakersTab'
import SponsorsTab from './tabs/SponsorsTab'

const tabs = [
  { id: 'about', label: 'About the event' },
  { id: 'speakers', label: 'Speakers' },
  { id: 'sponsors', label: 'Event Sponsors' },
]

export default function EventTabs() {
  const [activeTab, setActiveTab] = useState('about')

  const renderTabContent = () => {
    switch (activeTab) {
      case 'about':
        return <AboutTab />
      case 'speakers':
        return <SpeakersTab />
      case 'sponsors':
        return <SponsorsTab />
      default:
        return <AboutTab />
    }
  }

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Premium Tab Navigation */}
          <div className="relative mb-12">
            {/* Tab Container */}
            <div className="relative bg-white border border-gray-200 shadow-lg overflow-hidden">
              {/* Active Tab Background Slider */}
              <div
                className="absolute top-0 h-full bg-[#7E2518] transition-all duration-500 ease-out"
                style={{
                  width: `${100 / tabs.length}%`,
                  left: `${(tabs.findIndex((tab) => tab.id === activeTab) * 100) / tabs.length}%`,
                }}
              ></div>

              {/* Tab Buttons */}
              <div className="relative flex">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`relative flex-1 px-8 py-6 font-semibold text-base transition-all duration-300 text-center border-r border-gray-200 last:border-r-0 ${
                      activeTab === tab.id
                        ? 'text-white'
                        : 'text-[#7E2518] hover:text-[#C86E36] hover:bg-gray-50'
                    }`}
                  >
                    {/* Tab Label */}
                    <span className="relative z-10 block">{tab.label}</span>

                    {/* Hover Effect Background */}
                    <div
                      className={`absolute inset-0 bg-gray-50 opacity-0 transition-opacity duration-300 ${
                        activeTab !== tab.id ? 'hover:opacity-100' : ''
                      }`}
                    ></div>

                    {/* Bottom Border Indicator */}
                    <div
                      className={`absolute bottom-0 left-1/2 transform -translate-x-1/2 h-1 bg-[#E8B32C] transition-all duration-300 ${
                        activeTab === tab.id ? 'w-12 opacity-100' : 'w-0 opacity-0'
                      }`}
                    ></div>
                  </button>
                ))}
              </div>

              {/* Decorative Bottom Border */}
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gray-200"></div>
            </div>
          </div>

          {/* Enhanced Tab Content */}
          <div className="relative">
            <div className="bg-white border border-gray-200 shadow-lg overflow-hidden">
              {/* Content Header */}
              <div className="bg-[#7E2518] text-white p-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-2xl font-bold">
                    {tabs.find((tab) => tab.id === activeTab)?.label}
                  </h3>
                  <div className="flex space-x-1">
                    {tabs.map((tab) => (
                      <div
                        key={tab.id}
                        className={`w-2 h-2 transition-all duration-300 ${
                          activeTab === tab.id ? 'bg-[#E8B32C]' : 'bg-white/30'
                        }`}
                        style={{ borderRadius: '50%' }}
                      ></div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Content Body */}
              <div className="p-8 min-h-[500px] animate-fadeIn">{renderTabContent()}</div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out;
        }
      `}</style>
    </section>
  )
}
