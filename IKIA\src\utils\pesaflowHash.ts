import crypto from 'crypto'

// Legacy function for backward compatibility
export function generatePesaflowHash(dataString: string, secret: string): string {
  const hmac = crypto.createHmac('sha256', secret)
  hmac.update(dataString)
  const base16 = hmac.digest('hex').toLowerCase()
  return Buffer.from(base16).toString('base64')
}

/**
 * HashService class for handling all eCitizen API hash generation
 * Automatically loads configuration from environment variables
 */
export class HashService {
  private static instance: HashService
  private apiClientId: string
  private clientSecret: string
  private clientKey: string

  private constructor() {
    const { PESAFLOW_API_CLIENT_ID, PESAFLOW_CLIENT_SECRET, PESAFLOW_CLIENT_KEY } = process.env

    if (!PESAFLOW_API_CLIENT_ID || !PESAFLOW_CLIENT_SECRET || !PESAFLOW_CLIENT_KEY) {
      throw new Error(
        'Missing required Pesaflow configuration: PESAFLOW_API_CLIENT_ID, PESAFLOW_CLIENT_SECRET, PESAFLOW_CLIENT_KEY',
      )
    }

    this.apiClientId = PESAFLOW_API_CLIENT_ID
    this.clientSecret = PESAFLOW_CLIENT_SECRET
    this.clientKey = PESAFLOW_CLIENT_KEY
  }

  public static getInstance(): HashService {
    if (!HashService.instance) {
      HashService.instance = new HashService()
    }
    return HashService.instance
  }

  private generateHash(dataString: string, key: string): string {
    return generatePesaflowHash(dataString, key)
  }

  /**
   * Generate hash for payment validation
   * data_string = api_client_id + ref_no + amount + secret
   */
  public generatePaymentValidationHash(
    ref_no: string,
    amount: string,
  ): {
    hash: string
    api_client_id: string
    payload: object
  } {
    const dataString = `${this.apiClientId}${ref_no}${amount}${this.clientSecret}`
    const hash = this.generateHash(dataString, this.clientKey)

    const payload = {
      api_client_id: this.apiClientId,
      ref_no,
      currency: 'KES', // Default currency
      amount,
      secure_hash: hash,
    }

    console.log('Generated payment validation hash:', {
      dataString: `${this.apiClientId}${ref_no}${amount}[SECRET]`,
      hash,
    })

    return { hash, api_client_id: this.apiClientId, payload }
  }

  /**
   * Generate hash for payment confirmation
   * data_string = api_client_id + ref_no + amount + currency + gateway_transaction_id + gateway_transaction_date + customer_name + customer_account_number + secret
   */
  public generatePaymentConfirmationHash(
    ref_no: string,
    amount: string,
    currency: string,
    gateway_transaction_id: string,
    gateway_transaction_date: string,
    customer_name: string,
    customer_account_number: string,
  ): {
    hash: string
    api_client_id: string
    payload: object
  } {
    const dataString = `${this.apiClientId}${ref_no}${amount}${currency}${gateway_transaction_id}${gateway_transaction_date}${customer_name}${customer_account_number}${this.clientSecret}`
    const hash = this.generateHash(dataString, this.clientKey)

    const payload = {
      api_client_id: this.apiClientId,
      ref_no,
      amount,
      currency,
      gateway_transaction_id,
      gateway_transaction_date,
      customer_name,
      customer_account_number,
      secure_hash: hash,
    }

    console.log('Generated payment confirmation hash:', {
      dataString: `${this.apiClientId}${ref_no}${amount}${currency}${gateway_transaction_id}${gateway_transaction_date}${customer_name}${customer_account_number}[SECRET]`,
      hash,
    })

    return { hash, api_client_id: this.apiClientId, payload }
  }

  /**
   * Generate hash for payment status query
   * data_string = api_client_id + ref_no (using key only, no secret)
   */
  public generatePaymentStatusHash(ref_no: string): {
    hash: string
    api_client_id: string
    queryParams: URLSearchParams
  } {
    const dataString = `${this.apiClientId}${ref_no}`
    const hash = this.generateHash(dataString, this.clientKey)

    const queryParams = new URLSearchParams({
      api_client_id: this.apiClientId,
      ref_no,
      secure_hash: hash,
    })

    console.log('Generated payment status hash:', {
      dataString,
      hash,
    })

    return { hash, api_client_id: this.apiClientId, queryParams }
  }

  /**
   * Verify incoming hash for IPN
   * data_string = client_invoice_ref + invoice_number + amount_paid + payment_date + secret
   */
  public verifyIPNHash(
    client_invoice_ref: string,
    invoice_number: string,
    amount_paid: string,
    payment_date: string,
    received_hash: string,
  ): boolean {
    const expectedDataString = `${client_invoice_ref}${invoice_number}${amount_paid}${payment_date}${this.clientSecret}`
    const expectedHash = this.generateHash(expectedDataString, this.clientKey)

    const isValid = received_hash === expectedHash

    console.log('IPN hash verification:', {
      dataString: `${client_invoice_ref}${invoice_number}${amount_paid}${payment_date}[SECRET]`,
      received: received_hash,
      expected: expectedHash,
      valid: isValid,
    })

    return isValid
  }

  /**
   * Generate hash for checkout with all required Pesaflow parameters
   * data_string = apiClientID + amountExpected + serviceID + clientIDNumber + currency + billRefNumber + billDesc + clientName + secret
   */
  public generateCheckoutHash(
    amountExpected: string,
    serviceID: string,
    clientIDNumber: string,
    currency: string,
    billRefNumber: string,
    billDesc: string,
    clientName: string,
  ): {
    hash: string
    api_client_id: string
    payload: object
  } {
    const dataString = `${this.apiClientId}${amountExpected}${serviceID}${clientIDNumber}${currency}${billRefNumber}${billDesc}${clientName}${this.clientSecret}`
    const hash = this.generateHash(dataString, this.clientKey)

    const payload = {
      apiClientID: this.apiClientId,
      amountExpected,
      serviceID,
      clientIDNumber,
      currency,
      billRefNumber,
      billDesc,
      clientName,
      secureHash: hash,
    }

    console.log('Generated new checkout hash:', {
      dataString: `${this.apiClientId}${amountExpected}${serviceID}${clientIDNumber}${currency}${billRefNumber}${billDesc}${clientName}[SECRET]`,
      hash,
      payload,
    })

    return { hash, api_client_id: this.apiClientId, payload }
  }

  /**
   * Get API client ID
   */
  public getApiClientId(): string {
    return this.apiClientId
  }
}
