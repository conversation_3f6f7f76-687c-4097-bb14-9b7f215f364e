import type { PayloadRequest } from 'payload'
import { HashService } from '../utils/pesaflowHash'

// Types for the eCitizen payment validation endpoint

export const pesaflowValidatePaymentEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('Payment validation endpoint called')
    console.log('Request body:', req.body)
    console.log('Request body type:', typeof req.body)

    // Handle request body parsing
    let parsedBody: any
    if (req.body && typeof req.body === 'object' && !(req.body instanceof ReadableStream)) {
      parsedBody = req.body
    } else if (typeof req.body === 'string') {
      try {
        parsedBody = JSON.parse(req.body)
      } catch (_e) {
        return new Response(
          JSON.stringify({
            status: 400,
            description: 'Invalid JSON in request body',
          }),
          {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }
    } else if (req.body instanceof ReadableStream) {
      try {
        const reader = req.body.getReader()
        const chunks: Uint8Array[] = []
        let done = false

        while (!done) {
          const { value, done: readerDone } = await reader.read()
          done = readerDone
          if (value) {
            chunks.push(value)
          }
        }

        const bodyText = new TextDecoder().decode(
          new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0)).map((_, i) => {
            let offset = 0
            for (const chunk of chunks) {
              if (i < offset + chunk.length) {
                return chunk[i - offset]
              }
              offset += chunk.length
            }
            return 0
          }),
        )

        parsedBody = JSON.parse(bodyText)
      } catch (_e) {
        return new Response(
          JSON.stringify({
            status: 400,
            description: 'Failed to parse request body',
          }),
          {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }
    } else {
      return new Response(
        JSON.stringify({
          status: 400,
          description: 'Invalid request body format',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    console.log('Parsed body:', parsedBody)

    // Frontend only needs to send: ref_no, amount, currency
    // Server will handle: api_client_id, secret, and hash generation
    const { ref_no, amount, currency } = parsedBody

    // Validate required fields from frontend
    const missingFields = [
      !ref_no && 'ref_no',
      !amount && 'amount',
      !currency && 'currency',
    ].filter(Boolean)

    if (missingFields.length > 0) {
      return new Response(
        JSON.stringify({
          status: 400,
          description: 'Missing required fields',
          missingFields,
          message: `Please provide the following required fields: ${missingFields.join(', ')}`,
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Use HashService to generate hash and payload
    let hashService: HashService
    try {
      hashService = HashService.getInstance()
    } catch (error) {
      console.error('HashService initialization failed:', error)
      return new Response(
        JSON.stringify({
          status: 500,
          description: 'Internal Error - Missing configuration',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Generate hash and payload using HashService
    const {
      hash: secureHash,
      api_client_id: apiClientID,
      payload,
    } = hashService.generatePaymentValidationHash(ref_no, amount)

    // Update payload with correct currency
    const payloadToSend = {
      ...payload,
      currency, // Use the currency from request
    }

    // Get external service URL
    const { PESAFLOW_UAT_SERVER_URL: pesaflowServerUrl } = process.env

    // Option 1: Validate against external Pesaflow service (if configured and accessible)
    if (pesaflowServerUrl) {
      try {
        const baseUrl = pesaflowServerUrl.endsWith('/')
          ? pesaflowServerUrl.slice(0, -1)
          : pesaflowServerUrl

        console.log('Making external validation call to:', `${baseUrl}/api/payment/validate`)

        // Add timeout to prevent hanging
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 5000) // 5 second timeout

        const response = await fetch(`${baseUrl}/api/payment/validate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payloadToSend),
          signal: controller.signal,
        })

        clearTimeout(timeoutId)
        const responseData = await response.json()

        // Return the response from external service
        return new Response(JSON.stringify(responseData), {
          status: response.status,
          headers: { 'Content-Type': 'application/json' },
        })
      } catch (error) {
        console.error(
          'External validation service error, falling back to local validation:',
          error instanceof Error ? error.message : 'Unknown error',
        )
        // Fall through to local validation instead of returning error
      }
    }

    // Enhanced database validation with invoice support
    const billExists = await validateBillInDatabase(ref_no, amount, currency, req.payload)

    if (!billExists.found) {
      return new Response(
        JSON.stringify({
          status: 404,
          description: 'Payment not found',
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Bill found - return success response according to eCitizen specification
    return new Response(
      JSON.stringify({
        status: 200,
        description: 'Bill Found',
        data: {
          amount: billExists.amount,
          name: billExists.customerName,
          currency: billExists.currency,
          invoice_number: billExists.invoiceNumber,
          package_name: billExists.packageName,
        },
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  } catch (error) {
    console.error('Payment validation error:', error)
    return new Response(
      JSON.stringify({
        status: 500,
        description: 'Internal Error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}

// Enhanced helper function to validate bill in database with invoice support
async function validateBillInDatabase(
  ref_no: string,
  amount: string,
  currency: string,
  payload: any,
) {
  try {
    // First, try to find invoice by payment reference
    const invoices = await payload.find({
      collection: 'invoices',
      where: {
        payment_reference: {
          equals: ref_no,
        },
      },
      populate: ['user', 'package'],
    })

    if (invoices.docs.length > 0) {
      const invoice = invoices.docs[0]

      // Validate amount and currency match
      if (invoice.amount.toString() === amount && invoice.currency === currency) {
        // Check if invoice is payable
        if (['pending', 'processing', 'partial'].includes(invoice.status)) {
          return {
            found: true,
            amount: invoice.amount.toString(),
            customerName: invoice.customer_info?.name || invoice.user?.name || 'Customer',
            currency: invoice.currency,
            invoiceNumber: invoice.invoice_number,
            packageName: invoice.package?.name || 'Service Package',
          }
        }
      }
    }

    // Fallback: try to find by invoice number
    const invoicesByNumber = await payload.find({
      collection: 'invoices',
      where: {
        invoice_number: {
          equals: ref_no,
        },
      },
      populate: ['user', 'package'],
    })

    if (invoicesByNumber.docs.length > 0) {
      const invoice = invoicesByNumber.docs[0]

      if (invoice.amount.toString() === amount && invoice.currency === currency) {
        if (['pending', 'processing', 'partial'].includes(invoice.status)) {
          return {
            found: true,
            amount: invoice.amount.toString(),
            customerName: invoice.customer_info?.name || invoice.user?.name || 'Customer',
            currency: invoice.currency,
            invoiceNumber: invoice.invoice_number,
            packageName: invoice.package?.name || 'Service Package',
          }
        }
      }
    }

    // Legacy validation for demo purposes (bills starting with "VALID")
    if (ref_no.startsWith('VALID')) {
      return {
        found: true,
        amount: amount,
        customerName: 'Demo Customer',
        currency: currency,
        invoiceNumber: ref_no,
        packageName: 'Demo Package',
      }
    }

    return {
      found: false,
    }
  } catch (error) {
    console.error('Error validating bill in database:', error)
    return {
      found: false,
    }
  }
}
