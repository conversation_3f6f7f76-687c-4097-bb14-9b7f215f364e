'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Separator from '@/components/ui/separator'
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Share2,
  Heart,
  ArrowLeft,
  AlertTriangle,
  FileText,
  Download,
} from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { useGetProgramQuery } from '@/lib/api/programsApi'
import type { Program } from '@/lib/api/programsApi'

interface AgendaDetailsProps {
  programId: string
}

// Helper functions
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

const formatTime = (timeString: string | null | undefined): string => {
  if (!timeString) return 'TBA'
  // Assuming time is in HH:MM format
  const [hours, minutes] = timeString.split(':')
  const hour = parseInt(hours, 10)
  const ampm = hour >= 12 ? 'PM' : 'AM'
  const displayHour = hour % 12 || 12
  return `${displayHour}:${minutes} ${ampm}`
}

const getProgramTypeInfo = (type: Program['type']) => {
  if (typeof type === 'object' && type !== null) {
    return {
      name: type.name,
      color: type.color || '#149047',
    }
  }
  return { name: 'Program', color: '#149047' }
}

const getThematicAreaInfo = (thematicArea: Program['thematicArea']) => {
  if (typeof thematicArea === 'object' && thematicArea !== null) {
    return {
      name: thematicArea.name,
      color: thematicArea.color || '#159147',
    }
  }
  return null
}

const getSpeakerImage = (speaker: any): string => {
  if (speaker.photo?.url) {
    return speaker.photo.url
  }
  return '/placeholder.svg?height=80&width=80&text=Speaker'
}

const renderRichText = (richText: Program['description']): string => {
  // Simple extraction of text from rich text structure
  // In a real implementation, you might want to use a proper rich text renderer
  try {
    const children = richText?.root?.children || []
    return children
      .map((child: any) => {
        if (child.children) {
          return child.children.map((c: any) => c.text || '').join('')
        }
        return child.text || ''
      })
      .join(' ')
  } catch {
    return 'Program description available'
  }
}

export default function AgendaDetails({ programId }: AgendaDetailsProps) {
  // Get program data from API
  const { data: program, error, isLoading } = useGetProgramQuery(programId)

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-green-50">
        <div className="container mx-auto px-4 py-12">
          <div className="animate-pulse">
            <div className="h-64 bg-gray-200 rounded mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2 space-y-8">
                <div className="h-96 bg-gray-200 rounded"></div>
                <div className="h-64 bg-gray-200 rounded"></div>
              </div>
              <div className="space-y-6">
                <div className="h-48 bg-gray-200 rounded"></div>
                <div className="h-32 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error || !program) {
    return (
      <div className="min-h-screen bg-green-50">
        <div className="container mx-auto px-4 py-12">
          <div className="text-center py-16">
            <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Program Not Found</h1>
            <p className="text-gray-600 mb-6">
              The program you&apos;re looking for doesn&apos;t exist or has been removed.
            </p>
            <Link href="/programs">
              <Button className="bg-[#149047] hover:bg-[#117A3A] text-white">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Programs
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  const programTypeInfo = getProgramTypeInfo(program.type)
  const thematicAreaInfo = getThematicAreaInfo(program.thematicArea)
  const description = renderRichText(program.description)

  return (
    <div className="min-h-screen bg-green-50">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-[#149047] text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-4 py-16 md:py-24">
          <div className="max-w-4xl">
            <div className="flex items-center gap-4 mb-4">
              <Link href="/programs">
                <Button variant="ghost" size="sm" className="text-white hover:bg-white/10 p-2">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
              <Badge className="bg-white/20 text-white hover:bg-white/30">
                {programTypeInfo.name}
              </Badge>
              {thematicAreaInfo && (
                <Badge className="bg-white/20 text-white hover:bg-white/30">
                  {thematicAreaInfo.name}
                </Badge>
              )}
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">{program.title}</h1>
            <p className="text-xl md:text-2xl mb-8 text-white max-w-3xl">{description}</p>

            {/* Key Event Info */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="flex items-center gap-3">
                <Calendar className="h-6 w-6 text-green-200" />
                <div>
                  <p className="font-semibold">{formatDate(program.date)}</p>
                  <p className="text-green-200">Program</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Clock className="h-6 w-6 text-green-200" />
                <div>
                  <p className="font-semibold">
                    {formatTime(program.startTime)} - {formatTime(program.endTime)}
                  </p>
                  <p className="text-green-200">EAT</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <MapPin className="h-6 w-6 text-green-200" />
                <div>
                  <p className="font-semibold">{program.venue}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* About Section */}
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">About This Program</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="prose max-w-none">
                  <p className="text-gray-600 leading-relaxed">{description}</p>
                </div>

                {/* Program Details */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#149047]">
                      {program.capacity || 'Open'}
                    </div>
                    <div className="text-sm text-gray-500">Capacity</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#149047]">
                      {program.speakers?.length || 0}
                    </div>
                    <div className="text-sm text-gray-500">Speakers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#149047]">
                      {program.isParallel ? 'Yes' : 'No'}
                    </div>
                    <div className="text-sm text-gray-500">Parallel Session</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#149047]">
                      {program.registrationRequired ? 'Required' : 'Open'}
                    </div>
                    <div className="text-sm text-gray-500">Registration</div>
                  </div>
                </div>

                {/* Requirements */}
                {program.requirements && (
                  <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <h4 className="font-semibold text-yellow-800 mb-2">Requirements</h4>
                    <p className="text-yellow-700 text-sm">{program.requirements}</p>
                  </div>
                )}

                {/* Tags */}
                {program.tags && program.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 pt-4">
                    {program.tags.map((tagItem, index) => (
                      <Badge key={index} variant="outline">
                        {tagItem.tag}
                      </Badge>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Speakers Section */}
            {program.speakers && program.speakers.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl flex items-center gap-2">
                    <Users className="h-6 w-6" />
                    Program Speakers
                  </CardTitle>
                  <CardDescription>Meet the experts leading this program</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {program.speakers.map((speaker, index) => (
                      <div key={index} className="flex gap-4">
                        <Image
                          src={getSpeakerImage(speaker)}
                          alt={speaker.name || 'Speaker'}
                          width={80}
                          height={80}
                          className="rounded-full object-cover"
                        />
                        <div>
                          <h3 className="font-semibold text-lg">{speaker.name}</h3>
                          {speaker.title && (
                            <p className="text-[#149047] font-medium">{speaker.title}</p>
                          )}
                          {speaker.company && (
                            <p className="text-sm text-gray-600 mt-1">{speaker.company}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Supporting Materials */}
            {program.materials && program.materials.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl flex items-center gap-2">
                    <FileText className="h-6 w-6" />
                    Supporting Materials
                  </CardTitle>
                  <CardDescription>Download resources for this program</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {program.materials.map((material, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          <FileText className="h-5 w-5 text-gray-400" />
                          <span className="font-medium">{material.filename}</span>
                        </div>
                        <Button variant="outline" size="sm" asChild>
                          <a href={material.url} target="_blank" rel="noopener noreferrer">
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </a>
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Info */}
            <Card>
              <CardHeader>
                <CardTitle>Program Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start gap-3">
                  <Calendar className="h-5 w-5 text-gray-400 mt-0.5" />
                  <div>
                    <p className="font-medium">Date & Time</p>
                    <p className="text-sm text-gray-600">{formatDate(program.date)}</p>
                    <p className="text-sm text-gray-600">
                      {formatTime(program.startTime)} - {formatTime(program.endTime)} EAT
                    </p>
                  </div>
                </div>

                <Separator />

                <div className="flex items-start gap-3">
                  <MapPin className="h-5 w-5 text-gray-400 mt-0.5" />
                  <div>
                    <p className="font-medium">Venue</p>
                    <p className="text-sm text-gray-600">{program.venue}</p>
                  </div>
                </div>

                <Separator />

                <div className="flex items-start gap-3">
                  <Users className="h-5 w-5 text-gray-400 mt-0.5" />
                  <div>
                    <p className="font-medium">Program Type</p>
                    <p className="text-sm text-gray-600">{programTypeInfo.name}</p>
                    {thematicAreaInfo && (
                      <p className="text-sm text-gray-600">{thematicAreaInfo.name}</p>
                    )}
                  </div>
                </div>

                {program.capacity && (
                  <>
                    <Separator />
                    <div className="flex items-start gap-3">
                      <Users className="h-5 w-5 text-gray-400 mt-0.5" />
                      <div>
                        <p className="font-medium">Capacity</p>
                        <p className="text-sm text-gray-600">{program.capacity} attendees</p>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Program Actions */}
            <Card className="bg-green-50 border-green-200">
              <CardContent className="p-6 text-center">
                <h3 className="font-semibold text-lg mb-2">Interested in this program?</h3>
                <p className="text-sm text-gray-600 mb-4">
                  {program.registrationRequired
                    ? 'This program requires separate registration.'
                    : 'This program is included with your conference registration.'}
                </p>
                <div className="space-y-2">
                  <Button className="w-full" asChild>
                    <Link href="/programs">View All Programs</Link>
                  </Button>
                  {program.registrationRequired && (
                    <Button variant="outline" className="w-full">
                      Register for Program
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
