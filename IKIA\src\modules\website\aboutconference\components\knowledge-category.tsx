'use client'

import Image from 'next/image'

interface KnowledgeCategoryProps {
  title: string
  description: string
  bulletPoints: string[]
  imagePosition: 'left' | 'right'
}

export default function KnowledgeCategory({
  title,
  description,
  bulletPoints,
  imagePosition,
}: KnowledgeCategoryProps) {
  const content = (
    <div className={`space-y-6 ${imagePosition === 'right' ? 'lg:order-1' : 'lg:order-2'}`}>
      <h2 className="text-2xl md:text-3xl font-semibold text-gray-900">{title}</h2>
      <p className="text-gray-600 text-lg leading-relaxed">{description}</p>
      <ul className="space-y-3">
        {bulletPoints.map((point, index) => (
          <li key={index} className="text-gray-500 flex items-start">
            <span className="mr-3 mt-2 w-1.5 h-1.5 bg-gray-400 rounded-full flex-shrink-0"></span>
            <span className="leading-relaxed">{point}</span>
          </li>
        ))}
      </ul>
    </div>
  )

  const image = (
    <div
      className={`flex justify-center ${imagePosition === 'right' ? 'lg:order-2' : 'lg:order-1'}`}
    >
      <div className="relative aspect-square w-full max-w-[350px] border border-gray-200 rounded-lg overflow-hidden">
        <Image
          src="/placeholder.svg?height=350&width=350"
          alt={title}
          fill
          className="object-cover"
        />
      </div>
    </div>
  )

  return (
    <div className="max-w-5xl mx-auto">
      <div className="grid lg:grid-cols-2 gap-12 items-center">
        {/* On small screens, always show in the same order as large screens */}
        {imagePosition === 'left' ? (
          <>
            {/* Image first on small screens, left on large screens */}
            <div className="flex justify-center lg:order-1">
              <div className="relative aspect-square w-full max-w-[350px] border border-gray-200 rounded-lg overflow-hidden">
                <Image
                  src="/placeholder.svg?height=350&width=350"
                  alt={title}
                  fill
                  className="object-cover"
                />
              </div>
            </div>
            {/* Text second on small screens, right on large screens */}
            <div className="space-y-6 lg:order-2">
              <h2 className="text-2xl md:text-3xl font-semibold text-gray-900">{title}</h2>
              <p className="text-gray-600 text-lg leading-relaxed">{description}</p>
              <ul className="space-y-3">
                {bulletPoints.map((point, index) => (
                  <li key={index} className="text-gray-500 flex items-start">
                    <span className="mr-3 mt-2 w-1.5 h-1.5 bg-gray-400 rounded-full flex-shrink-0"></span>
                    <span className="leading-relaxed">{point}</span>
                  </li>
                ))}
              </ul>
            </div>
          </>
        ) : (
          <>
            {/* Text first on small screens, left on large screens */}
            <div className="space-y-6 lg:order-1">
              <h2 className="text-2xl md:text-3xl font-semibold text-gray-900">{title}</h2>
              <p className="text-gray-600 text-lg leading-relaxed">{description}</p>
              <ul className="space-y-3">
                {bulletPoints.map((point, index) => (
                  <li key={index} className="text-gray-500 flex items-start">
                    <span className="mr-3 mt-2 w-1.5 h-1.5 bg-gray-400 rounded-full flex-shrink-0"></span>
                    <span className="leading-relaxed">{point}</span>
                  </li>
                ))}
              </ul>
            </div>
            {/* Image second on small screens, right on large screens */}
            <div className="flex justify-center lg:order-2">
              <div className="relative aspect-square w-full max-w-[350px] border border-gray-200 rounded-lg overflow-hidden">
                <Image
                  src="/placeholder.svg?height=350&width=350"
                  alt={title}
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
