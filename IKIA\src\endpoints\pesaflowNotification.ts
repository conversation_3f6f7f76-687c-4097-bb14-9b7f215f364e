import type { PayloadRequest } from 'payload'
import crypto from 'crypto'

interface PesaflowPaymentReference {
  payment_reference: string
  payment_date: string
  inserted_at: string
  currency: string
  amount: string
}

interface PesaflowNotificationPayload {
  payment_channel: string
  client_invoice_ref: string
  payment_reference: PesaflowPaymentReference[]
  currency: string
  amount_paid: string
  invoice_amount: string
  status: string
  invoice_number: string
  payment_date: string
  last_payment_amount: string
  secure_hash: string
}

/**
 * Dedicated Pesaflow payment notification endpoint
 * Handles successful payment callbacks from Pesaflow's payment gateway
 * URL: POST /api/payment/callback/pesaflow/notification
 */
export const pesaflowNotificationEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('Pesaflow notification received:', {
      headers: Object.fromEntries(req.headers.entries()),
      body: req.body,
    })

    // Get the payload from request body
    let payload: PesaflowNotificationPayload
    try {
      if (req.json) {
        payload = (await req.json()) as PesaflowNotificationPayload
      } else {
        throw new Error('No JSON parser available')
      }
    } catch (error) {
      console.error('Failed to parse JSON payload:', error)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid JSON payload',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Debug logging
    console.log('Payload validation check:', {
      client_invoice_ref: payload.client_invoice_ref,
      client_invoice_ref_type: typeof payload.client_invoice_ref,
      payment_reference: payload.payment_reference,
      payment_reference_type: typeof payload.payment_reference,
      payment_reference_length: Array.isArray(payload.payment_reference)
        ? payload.payment_reference.length
        : 'not array',
      secure_hash: payload.secure_hash,
      secure_hash_type: typeof payload.secure_hash,
    })

    // Validate required fields
    if (!payload.client_invoice_ref || !payload.payment_reference || !payload.secure_hash) {
      console.error('Validation failed:', {
        client_invoice_ref_check: !!payload.client_invoice_ref,
        payment_reference_check: !!payload.payment_reference,
        secure_hash_check: !!payload.secure_hash,
      })
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing required fields: client_invoice_ref, payment_reference, or secure_hash',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Verify secure hash
    const isValidHash = await verifyPesaflowSecureHash(payload)
    if (!isValidHash) {
      console.error('Invalid Pesaflow secure hash:', payload.secure_hash)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid secure hash - notification rejected',
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Extract payment reference (use first payment reference)
    const primaryPaymentRef = payload.payment_reference[0]
    if (!primaryPaymentRef) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'No payment reference found in notification',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    console.log('Processing Pesaflow notification:', {
      payment_reference: primaryPaymentRef.payment_reference,
      status: payload.status,
      amount: payload.amount_paid,
      client_invoice_ref: payload.client_invoice_ref,
    })

    // Check for duplicate notifications
    const existingNotification = await checkDuplicateNotification(
      req.payload,
      primaryPaymentRef.payment_reference,
    )
    if (existingNotification) {
      console.log('Duplicate notification ignored:', primaryPaymentRef.payment_reference)
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Duplicate notification ignored',
          notification_id: existingNotification.id,
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Find related invoice
    const invoice = await findInvoiceByPesaflowRef(
      req.payload,
      payload.client_invoice_ref,
      payload.invoice_number,
    )

    // Create notification record
    const notificationData = {
      payment_channel: payload.payment_channel,
      client_invoice_ref: payload.client_invoice_ref,
      payment_reference: primaryPaymentRef.payment_reference,
      payment_date: primaryPaymentRef.payment_date,
      inserted_at: primaryPaymentRef.inserted_at,
      currency: payload.currency,
      amount_paid: parseFloat(payload.amount_paid),
      invoice_amount: parseFloat(payload.invoice_amount),
      last_payment_amount: parseFloat(payload.last_payment_amount),
      status: payload.status as 'settled' | 'pending' | 'failed' | 'cancelled',
      invoice_number: payload.invoice_number,
      secure_hash: payload.secure_hash,
      invoice: invoice?.id,
      user: invoice?.user,
      service_package: invoice?.service_package,
      processing_status: 'pending' as 'pending' | 'processed' | 'failed' | 'duplicate' | 'invalid',
      hash_verified: isValidHash,
      ip_address: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown',
      user_agent: req.headers.get('user-agent') || 'Pesaflow-Notification',
      raw_payload: payload as any,
    }

    const notificationRecord = await req.payload.create({
      collection: 'pesaflow-notifications',
      data: notificationData,
    })

    // Process payment if status is settled
    const result = await processPaymentNotification(
      req.payload,
      payload,
      invoice,
      String(notificationRecord.id),
    )

    console.log('Pesaflow notification processing result:', {
      success: result.success,
      message: result.message,
      notification_id: notificationRecord.id,
    })

    // Return success response (Pesaflow expects 200 OK for successful processing)
    return new Response(
      JSON.stringify({
        success: result.success,
        message: result.message,
        notification_id: notificationRecord.id,
        invoice_updated: result.invoice_updated,
        user_updated: result.user_updated,
        timestamp: new Date().toISOString(),
      }),
      {
        status: result.success ? 200 : 400,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  } catch (error: any) {
    console.error('Pesaflow notification endpoint error:', error)

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Internal server error processing Pesaflow notification',
        message: error.message,
        timestamp: new Date().toISOString(),
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}

/**
 * Verify Pesaflow secure hash
 * Implements Pesaflow's signature verification process
 */
async function verifyPesaflowSecureHash(payload: PesaflowNotificationPayload): Promise<boolean> {
  try {
    // Get Pesaflow secret key from environment variables
    const secretKey = process.env.PESAFLOW_SECRET_KEY
    if (!secretKey) {
      console.warn('PESAFLOW_SECRET_KEY not configured - skipping hash verification')
      return true // Allow in development, but log warning
    }

    // Create the string to hash according to Pesaflow documentation
    // Typically: client_invoice_ref + invoice_number + amount_paid + currency + status + secret_key
    const stringToHash = [
      payload.client_invoice_ref,
      payload.invoice_number,
      payload.amount_paid,
      payload.currency,
      payload.status,
      secretKey,
    ].join('')

    // Generate SHA256 hash
    const calculatedHash = crypto.createHash('sha256').update(stringToHash).digest('hex')

    // Convert to base64 (as Pesaflow sends base64 encoded hash)
    const calculatedHashBase64 = Buffer.from(calculatedHash, 'hex').toString('base64')

    console.log('Hash verification:', {
      stringToHash: stringToHash.replace(secretKey, '[REDACTED]'),
      calculatedHash: calculatedHash.substring(0, 10) + '...',
      calculatedHashBase64: calculatedHashBase64.substring(0, 10) + '...',
      receivedHash: payload.secure_hash.substring(0, 10) + '...',
      matches: calculatedHashBase64 === payload.secure_hash,
    })

    return calculatedHashBase64 === payload.secure_hash
  } catch (error) {
    console.error('Error verifying Pesaflow secure hash:', error)
    return false
  }
}

/**
 * Check for duplicate notifications
 */
async function checkDuplicateNotification(payload: any, paymentReference: string) {
  const existing = await payload.find({
    collection: 'pesaflow-notifications',
    where: {
      payment_reference: {
        equals: paymentReference,
      },
    },
    limit: 1,
  })

  return existing.docs.length > 0 ? existing.docs[0] : null
}

/**
 * Process payment notification and update invoice/user
 */
async function processPaymentNotification(
  payload: any,
  pesaflowPayload: PesaflowNotificationPayload,
  invoice: any,
  notificationId: string,
): Promise<{
  success: boolean
  message: string
  invoice_updated?: boolean
  user_updated?: boolean
}> {
  try {
    let invoiceUpdated = false
    let userUpdated = false

    // Update notification status to processing
    await payload.update({
      collection: 'pesaflow-notifications',
      id: notificationId,
      data: {
        processing_status: 'pending',
        processed_at: new Date().toISOString(),
      },
    })

    if (pesaflowPayload.status === 'settled') {
      if (!invoice) {
        await payload.update({
          collection: 'pesaflow-notifications',
          id: notificationId,
          data: {
            processing_status: 'failed',
            processing_notes: 'Related invoice not found',
          },
        })
        return {
          success: false,
          message: 'Related invoice not found',
        }
      }

      // Update invoice to paid
      await payload.update({
        collection: 'invoices',
        id: invoice.id,
        data: {
          status: 'settled',
          paid_at: pesaflowPayload.payment_date,
          payment_reference: pesaflowPayload.payment_reference[0].payment_reference,
          payment_method: 'pesaflow',
          notes: `Payment settled via ${pesaflowPayload.payment_channel}. Amount: ${pesaflowPayload.amount_paid} ${pesaflowPayload.currency}`,
        },
      })
      invoiceUpdated = true

      // Update user package status if service package exists
      if (invoice.service_package || invoice.package) {
        let packageId = invoice.service_package || invoice.package

        // Handle case where packageId might be an object with id property
        if (typeof packageId === 'object' && packageId !== null) {
          packageId = packageId.id || packageId
        }

        console.log('Finding service package:', {
          packageId,
          packageIdType: typeof packageId,
          invoice_service_package: invoice.service_package,
          invoice_package: invoice.package,
        })

        const servicePackage = await payload.findByID({
          collection: 'delegatepackages',
          id: packageId,
        })

        // Calculate expiry date based on package duration
        const expiryDate = new Date()
        if (servicePackage.duration?.value && servicePackage.duration?.unit) {
          const durationValue = servicePackage.duration.value
          const durationUnit = servicePackage.duration.unit

          switch (durationUnit) {
            case 'days':
              expiryDate.setDate(expiryDate.getDate() + durationValue)
              break
            case 'months':
              expiryDate.setMonth(expiryDate.getMonth() + durationValue)
              break
            case 'years':
              expiryDate.setFullYear(expiryDate.getFullYear() + durationValue)
              break
            default:
              expiryDate.setDate(expiryDate.getDate() + 365) // Default to 1 year
          }
        } else {
          expiryDate.setDate(expiryDate.getDate() + 365) // Default to 1 year
        }

        // Update user package status and statistics
        const userUpdateData: any = {
          selected_package: packageId,
          package_status: 'active',
          package_expiry: expiryDate.toISOString(),
          'statistics.total_payments': 1, // Increment payment count
          'statistics.total_amount_paid': parseFloat(pesaflowPayload.amount_paid),
          'statistics.last_payment_date': pesaflowPayload.payment_date,
        }

        // If this is a registration payment, mark user as having completed package flow
        if (invoice.registration_context?.is_registration_payment) {
          userUpdateData['registration_context.package_flow_completed'] = true
          userUpdateData['registration_context.package_activated_at'] = new Date().toISOString()
        }

        await payload.update({
          collection: 'users',
          id: invoice.user,
          data: userUpdateData,
        })
        userUpdated = true

        console.log('User package activated:', {
          user_id: invoice.user,
          package_id: packageId,
          package_name: servicePackage.name,
          expiry_date: expiryDate.toISOString(),
          is_registration_payment: invoice.registration_context?.is_registration_payment || false,
        })
      }

      // Mark notification as processed
      await payload.update({
        collection: 'pesaflow-notifications',
        id: notificationId,
        data: {
          processing_status: 'processed',
          processing_notes: 'Payment processed successfully',
        },
      })

      return {
        success: true,
        message: 'Pesaflow payment settled successfully',
        invoice_updated: invoiceUpdated,
        user_updated: userUpdated,
      }
    } else {
      // Handle non-settled statuses
      if (invoice) {
        let invoiceStatus = 'pending'
        if (pesaflowPayload.status === 'failed') invoiceStatus = 'failed'
        if (pesaflowPayload.status === 'cancelled') invoiceStatus = 'cancelled'

        await payload.update({
          collection: 'invoices',
          id: invoice.id,
          data: {
            status: invoiceStatus,
            notes: `Payment ${pesaflowPayload.status}: ${pesaflowPayload.payment_reference[0].payment_reference}`,
          },
        })
        invoiceUpdated = true
      }

      await payload.update({
        collection: 'pesaflow-notifications',
        id: notificationId,
        data: {
          processing_status: 'processed',
          processing_notes: `Payment status: ${pesaflowPayload.status}`,
        },
      })

      return {
        success: true,
        message: `Payment ${pesaflowPayload.status} processed`,
        invoice_updated: invoiceUpdated,
      }
    }
  } catch (error: any) {
    // Mark notification as failed
    await payload.update({
      collection: 'pesaflow-notifications',
      id: notificationId,
      data: {
        processing_status: 'failed',
        processing_notes: `Processing error: ${error.message}`,
      },
    })

    return {
      success: false,
      message: `Payment processing error: ${error.message}`,
    }
  }
}

/**
 * Find invoice by client_invoice_ref or invoice_number
 * This helper function can be used by the PaymentCallbackService
 */
export async function findInvoiceByPesaflowRef(
  payload: any,
  clientInvoiceRef: string,
  invoiceNumber?: string,
): Promise<any> {
  // Try to find by invoice number first (our internal invoice number)
  if (invoiceNumber) {
    const invoicesByNumber = await payload.find({
      collection: 'invoices',
      where: {
        invoice_number: {
          equals: invoiceNumber,
        },
      },
      limit: 1,
    })

    if (invoicesByNumber.docs.length > 0) {
      return invoicesByNumber.docs[0]
    }
  }

  // Try to find by client_invoice_ref (could be stored in payment_reference field)
  const invoicesByRef = await payload.find({
    collection: 'invoices',
    where: {
      payment_reference: {
        equals: clientInvoiceRef,
      },
    },
    limit: 1,
  })

  if (invoicesByRef.docs.length > 0) {
    return invoicesByRef.docs[0]
  }

  // Try to find by ID if client_invoice_ref looks like an ID
  if (clientInvoiceRef.match(/^[0-9a-fA-F]{24}$/) || !isNaN(Number(clientInvoiceRef))) {
    try {
      const invoiceById = await payload.findByID({
        collection: 'invoices',
        id: clientInvoiceRef,
      })
      return invoiceById
    } catch {
      // ID not found, continue
    }
  }

  return null
}
