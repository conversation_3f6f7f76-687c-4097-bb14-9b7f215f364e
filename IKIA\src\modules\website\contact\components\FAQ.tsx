'use client'

import { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'

const faqSections = [
  {
    section: 'Registration',
    items: [
      {
        question: 'How can I register?',
        answer: 'Register online via the official website using the “Register Now” button.',
      },
      {
        question: 'What are the fees and payment options?',
        answer:
          'Fees vary by participant type. Payments accepted via MPesa, bank transfer, and credit/debit cards.',
      },
      {
        question: 'Can I register at the venue?',
        answer:
          'Yes, on-site registration will be available, but advance registration is encouraged.',
      },
      {
        question: 'Can I change or cancel my registration?',
        answer: 'Yes. Contact the registration team via email before the cancellation deadline.',
      },
    ],
  },
  {
    section: 'Attendance',
    items: [
      {
        question: 'Who is eligible to attend?',
        answer:
          'Open to stakeholders, researchers, students, exhibitors, government, and the public.',
      },
      {
        question: 'Is attendance free or ticketed?',
        answer:
          'Some events are free, others require tickets or fees—check the schedule for details.',
      },
      {
        question: 'What documents do I need to bring?',
        answer: 'Your registration confirmation, ID/passport, and any payment receipts.',
      },
      {
        question: 'Are children or family members allowed?',
        answer: 'Yes, but children must be supervised. Some sessions are for adults only.',
      },
    ],
  },
  {
    section: 'Participation',
    items: [
      {
        question: 'How do I become a speaker or panellist?',
        answer: 'Submit a proposal via the “Call for Speakers” form on the website.',
      },
      {
        question: 'How do I apply to exhibit?',
        answer: 'Complete the “Exhibitor Registration” form online.',
      },
      {
        question: 'Are booths or exhibition spaces charged?',
        answer:
          'Yes. Fees depend on size and location. Discounts may apply for youth/community organizations.',
      },
    ],
  },
  {
    section: 'On-Site Experience',
    items: [
      {
        question: 'Will food and drinks be provided?',
        answer: 'Yes. Meals and refreshments will be sold at affordable prices by vetted vendors.',
      },
      {
        question: 'What is the dress code?',
        answer:
          'Smart casual or culturally appropriate. Traditional wear is encouraged for performers.',
      },
      {
        question: 'Is the event accessible for persons with disabilities?',
        answer: 'Yes, including ramps, accessible toilets, and assistance upon request.',
      },
      {
        question: 'Are translation services available?',
        answer: 'Available for select sessions. Request via registration form.',
      },
    ],
  },
  {
    section: 'Safety & Policies',
    items: [
      {
        question: 'COVID-19 safety protocols',
        answer: 'Sanitizers throughout. Mask and temperature checks may apply.',
      },
      {
        question: 'Lost and found policy',
        answer: 'Report or retrieve items at the main information desk. Items held for 7 days.',
      },
      {
        question: 'Code of conduct',
        answer: 'Respectful behavior is expected. Harassment or disruption will not be tolerated.',
      },
      {
        question: 'Security and emergency contact',
        answer: 'On-site security present. Emergency numbers will be posted.',
      },
    ],
  },
]

export default function FAQ() {
  const [openIndex, setOpenIndex] = useState<{ section: number; item: number } | null>(null)

  const toggleItem = (sectionIndex: number, itemIndex: number) => {
    setOpenIndex(
      openIndex?.section === sectionIndex && openIndex?.item === itemIndex
        ? null
        : { section: sectionIndex, item: itemIndex },
    )
  }

  return (
    <section className="max-w-4xl mx-auto px-4">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-[#7E2518] mb-4">Frequently Asked Questions</h2>
        <p className="text-gray-600">Find answers to common questions about IKIA events</p>
      </div>

      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden divide-y divide-gray-200">
        {faqSections.map((section, sectionIndex) => (
          <div key={sectionIndex} className="p-6">
            <h3 className="text-xl font-semibold text-[#7E2518] mb-6">{section.section}</h3>

            {section.items.map((faq, itemIndex) => {
              const isOpen = openIndex?.section === sectionIndex && openIndex?.item === itemIndex
              return (
                <div key={itemIndex} className="py-6">
                  <button
                    onClick={() => toggleItem(sectionIndex, itemIndex)}
                    className="w-full flex items-center justify-between text-left focus:outline-none group"
                  >
                    <h4 className="text-base font-semibold text-black group-hover:text-[#C86E36] transition-colors">
                      {faq.question}
                    </h4>
                    {isOpen ? (
                      <ChevronUp className="w-5 h-5 text-[#7E2518]" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-[#7E2518]" />
                    )}
                  </button>
                  {isOpen && (
                    <div className="mt-2 text-gray-600 leading-relaxed text-sm">{faq.answer}</div>
                  )}
                  {/* Full-width divider after each question except last */}
                  {itemIndex < section.items.length - 1 && (
                    <div className="mt-6 border-t border-gray-200"></div>
                  )}
                </div>
              )
            })}
          </div>
        ))}
      </div>
    </section>
  )
}
