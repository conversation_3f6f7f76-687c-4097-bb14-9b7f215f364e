'use client'

const partners = [
  {
    name: 'Republic of Kenya',
    logo: '/Court of arms.png',
    description: 'Government of Kenya',
  },
  {
    name: 'National Museum of Kenya',
    logo: '/National Museums of Kenya.png',
    description: 'Cultural Heritage Institution',
  },
  {
    name: 'National Policy Institute',
    logo: '/NPI.png',
    description: 'Policy Research & Development',
  },
  {
    name: 'Kenya Vision 2030',
    logo: '/Vision 2030.png',
    description: 'National Development Blueprint',
  },
  {
    name: 'Council of Governors',
    logo: '/Council of Governors.png',
    description: 'County Government Leadership',
  },
]

export default function PartnersSection() {
  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6">
        {/* Premium Partners Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8 items-center justify-items-center">
          {partners.map((partner, index) => (
            <div key={index} className="group relative">
              {/* Main Container with minimal padding */}
              <div className="relative bg-white shadow-lg transition-all duration-300 border border-gray-200 overflow-hidden w-[200px] h-[200px]">
                {/* Logo Display with minimal padding */}
                <div className="absolute inset-0 flex items-center justify-center p-2">
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={partner.logo || '/placeholder.svg'}
                    alt={partner.name}
                    className="max-w-full max-h-full object-contain"
                    style={{
                      maxWidth: '95%',
                      maxHeight: '95%',
                      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))',
                    }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      target.src =
                        '/placeholder.svg?height=100&width=100&text=' +
                        encodeURIComponent(partner.name)
                    }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
