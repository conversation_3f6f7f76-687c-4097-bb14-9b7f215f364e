# Payment Integration API Specification

## Overview
This document provides detailed API specifications for the enhanced payment integration system, building upon the existing eCitizen and Pesaflow integrations.

## Base URL
```
Production: https://your-domain.com/api
Development: http://localhost:3000/api
```

## Authentication
All payment-related endpoints require authentication via Payload CMS JWT tokens.

### Headers
```
Authorization: Bearer <payload_jwt_token>
Content-Type: application/json
```

### Authentication Flow
Users authenticate through Payload CMS built-in authentication:
1. Login via `POST /api/users/login` with email/password
2. Receive JWT token in response
3. Include token in Authorization header for all API calls
4. eCitizen integration happens in backend during payment processing

## 1. Enhanced Payment Endpoints

### 1.1 Initiate Payment
**Endpoint:** `POST /api/payments/initiate`

**Description:** Creates an invoice and initiates payment process with enhanced user validation.

**Request Body:**
```json
{
  "amount": 1500.00,
  "currency": "KES",
  "description": "Service payment for business license",
  "service_type": "business_license",
  "metadata": {
    "customer_reference": "CUST-123",
    "department": "business_services"
  },
  "callback_url": "https://your-app.com/payment/callback",
  "webhook_url": "https://your-app.com/api/webhooks/payment"
}
```

**Response (Success):**
```json
{
  "success": true,
  "data": {
    "invoice_id": "inv_**********",
    "payment_reference": "REF-**********",
    "checkout_url": "https://pesaflow.com/checkout/...",
    "expires_at": "2024-01-01T15:30:00Z",
    "status": "pending",
    "qr_code": "data:image/png;base64,..."
  }
}
```

**Response (Error):**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_AMOUNT",
    "message": "Amount must be between 1 and 1000000",
    "details": {
      "field": "amount",
      "provided": -100,
      "constraints": {
        "min": 1,
        "max": 1000000
      }
    }
  }
}
```

### 1.2 Payment Status Query
**Endpoint:** `GET /api/payments/status/{payment_reference}`

**Description:** Retrieves current payment status with detailed information.

**Response:**
```json
{
  "success": true,
  "data": {
    "payment_reference": "REF-**********",
    "invoice_id": "inv_**********",
    "status": "settled",
    "amount_expected": 1500.00,
    "amount_paid": 1500.00,
    "currency": "KES",
    "payment_method": "M-PESA",
    "transaction_id": "TXN123456789",
    "created_at": "2024-01-01T14:00:00Z",
    "completed_at": "2024-01-01T14:30:00Z",
    "customer_info": {
      "name": "John Doe",
      "phone": "254700000000"
    },
    "timeline": [
      {
        "status": "pending",
        "timestamp": "2024-01-01T14:00:00Z",
        "description": "Payment initiated"
      },
      {
        "status": "processing",
        "timestamp": "2024-01-01T14:25:00Z",
        "description": "Payment being processed"
      },
      {
        "status": "settled",
        "timestamp": "2024-01-01T14:30:00Z",
        "description": "Payment completed successfully"
      }
    ]
  }
}
```

### 1.3 Payment History
**Endpoint:** `GET /api/payments/history`

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `status`: Filter by status (pending, settled, failed, etc.)
- `from_date`: Start date (ISO 8601)
- `to_date`: End date (ISO 8601)
- `amount_min`: Minimum amount filter
- `amount_max`: Maximum amount filter

**Response:**
```json
{
  "success": true,
  "data": {
    "payments": [
      {
        "payment_reference": "REF-**********",
        "amount": 1500.00,
        "currency": "KES",
        "status": "settled",
        "description": "Business license payment",
        "created_at": "2024-01-01T14:00:00Z",
        "completed_at": "2024-01-01T14:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_items": 95,
      "items_per_page": 20,
      "has_next": true,
      "has_previous": false
    },
    "summary": {
      "total_amount": 142500.00,
      "successful_payments": 92,
      "failed_payments": 3,
      "success_rate": 96.8
    }
  }
}
```

### 1.4 Cancel Payment
**Endpoint:** `POST /api/payments/{payment_reference}/cancel`

**Description:** Cancels a pending payment if still possible.

**Response:**
```json
{
  "success": true,
  "data": {
    "payment_reference": "REF-**********",
    "status": "cancelled",
    "cancelled_at": "2024-01-01T14:15:00Z",
    "refund_info": {
      "eligible": false,
      "reason": "Payment not yet processed"
    }
  }
}
```

## 2. Invoice Management Endpoints

### 2.1 Create Invoice
**Endpoint:** `POST /api/invoices`

**Request Body:**
```json
{
  "amount": 2500.00,
  "currency": "KES",
  "description": "Professional services",
  "line_items": [
    {
      "description": "Consultation fee",
      "quantity": 2,
      "unit_price": 1000.00,
      "total": 2000.00
    },
    {
      "description": "Processing fee",
      "quantity": 1,
      "unit_price": 500.00,
      "total": 500.00
    }
  ],
  "due_date": "2024-01-15T23:59:59Z",
  "metadata": {
    "project_id": "PROJ-123",
    "client_reference": "CLIENT-456"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "invoice_id": "inv_**********",
    "invoice_number": "INV-2024-001",
    "status": "draft",
    "amount": 2500.00,
    "currency": "KES",
    "created_at": "2024-01-01T10:00:00Z",
    "due_date": "2024-01-15T23:59:59Z",
    "payment_url": "https://your-app.com/pay/inv_**********"
  }
}
```

### 2.2 Get Invoice Details
**Endpoint:** `GET /api/invoices/{invoice_id}`

**Response:**
```json
{
  "success": true,
  "data": {
    "invoice_id": "inv_**********",
    "invoice_number": "INV-2024-001",
    "status": "pending",
    "amount": 2500.00,
    "amount_paid": 0.00,
    "currency": "KES",
    "description": "Professional services",
    "line_items": [...],
    "customer": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "254700000000"
    },
    "created_at": "2024-01-01T10:00:00Z",
    "due_date": "2024-01-15T23:59:59Z",
    "payment_attempts": [
      {
        "payment_reference": "REF-**********",
        "amount": 2500.00,
        "status": "pending",
        "created_at": "2024-01-01T14:00:00Z"
      }
    ]
  }
}
```

## 3. User Management Endpoints

### 3.1 Get User Payment Profile
**Endpoint:** `GET /api/users/me/payment-profile`

**Response:**
```json
{
  "success": true,
  "data": {
    "user_id": "user_123",
    "payment_methods": [
      {
        "type": "mobile_money",
        "provider": "M-PESA",
        "phone_number": "254700000000",
        "is_verified": true,
        "is_default": true
      }
    ],
    "preferences": {
      "currency": "KES",
      "notification_email": true,
      "notification_sms": true
    },
    "limits": {
      "daily_limit": 50000.00,
      "monthly_limit": 500000.00,
      "single_transaction_limit": 100000.00
    },
    "statistics": {
      "total_payments": 45,
      "total_amount": 125000.00,
      "average_amount": 2777.78,
      "success_rate": 97.8
    }
  }
}
```

### 3.2 Update Payment Preferences
**Endpoint:** `PUT /api/users/me/payment-preferences`

**Request Body:**
```json
{
  "preferred_currency": "KES",
  "notification_email": true,
  "notification_sms": false,
  "auto_retry_failed_payments": true,
  "payment_confirmation_required": false
}
```

## 4. Webhook Endpoints

### 4.1 Payment Status Webhook
**Endpoint:** `POST /api/webhooks/payment-status`

**Description:** Receives payment status updates from Pesaflow.

**Request Body (from Pesaflow):**
```json
{
  "event_type": "payment.completed",
  "payment_reference": "REF-**********",
  "transaction_id": "TXN123456789",
  "status": "settled",
  "amount": 1500.00,
  "currency": "KES",
  "payment_method": "M-PESA",
  "customer_phone": "254700000000",
  "timestamp": "2024-01-01T14:30:00Z",
  "secure_hash": "generated_by_pesaflow"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Webhook processed successfully",
  "processed_at": "2024-01-01T14:30:05Z"
}
```

## 5. Error Codes and Handling

### 5.1 Standard Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      "field": "field_name",
      "constraint": "validation_rule"
    },
    "timestamp": "2024-01-01T14:30:00Z",
    "request_id": "req_**********"
  }
}
```

### 5.2 Common Error Codes
- `AUTHENTICATION_REQUIRED`: User not authenticated
- `INSUFFICIENT_PERMISSIONS`: User lacks required permissions
- `INVALID_AMOUNT`: Amount validation failed
- `PAYMENT_EXPIRED`: Payment session expired
- `DUPLICATE_PAYMENT`: Payment already exists
- `GATEWAY_ERROR`: External payment gateway error
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `VALIDATION_ERROR`: Request validation failed
- `INTERNAL_ERROR`: Server error

## 6. Rate Limiting

### 6.1 Rate Limits
- **Payment Initiation**: 10 requests per minute per user
- **Status Queries**: 60 requests per minute per user
- **Webhook Processing**: 1000 requests per minute (global)
- **General API**: 100 requests per minute per user

### 6.2 Rate Limit Headers
```
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 7
X-RateLimit-Reset: 1640995200
```

## 7. Testing Endpoints

### 7.1 Test Payment Creation
**Endpoint:** `POST /api/test/payments/create`

**Description:** Creates test payments for development (only available in development mode).

**Request Body:**
```json
{
  "amount": 100.00,
  "currency": "KES",
  "auto_complete": true,
  "delay_seconds": 30
}
```

### 7.2 Simulate Webhook
**Endpoint:** `POST /api/test/webhooks/simulate`

**Description:** Simulates webhook delivery for testing.

**Request Body:**
```json
{
  "payment_reference": "REF-**********",
  "status": "settled",
  "delay_seconds": 5
}
```
