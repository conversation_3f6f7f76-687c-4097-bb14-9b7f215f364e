<VirtualHost *:80>
    ServerName ikiaconference.or.ke
    ServerAlias www.ikiaconference.or.ke
    DocumentRoot /home/<USER>/public_html

    # Enable required modules
    LoadModule proxy_module modules/mod_proxy.so
    LoadModule proxy_http_module modules/mod_proxy_http.so
    LoadModule headers_module modules/mod_headers.so
    LoadModule expires_module modules/mod_expires.so
    LoadModule deflate_module modules/mod_deflate.so
    LoadModule rewrite_module modules/mod_rewrite.so

    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"

    # Remove server signature
    ServerTokens Prod
    Header unset Server

    # Enable compression
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png|ico|woff|woff2|ttf|eot)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>

    # Serve static files directly from Apache for better performance
    ProxyPass /_next/static/ !
    ProxyPass /static/ !
    ProxyPass /favicon.ico !
    ProxyPass /robots.txt !
    ProxyPass /sitemap.xml !

    # Block PHP files (this is a Next.js app, not PHP)
    <Files "*.php">
        Require all denied
    </Files>

    # Redirect common PHP requests to Next.js
    RewriteEngine On
    RewriteRule ^index\.php$ / [R=301,L]
    RewriteRule ^admin\.php$ /admin [R=301,L]

    Alias /_next/static/ /home/<USER>/public_html/.next/static/
    Alias /static/ /home/<USER>/public_html/public/
    Alias /favicon.ico /home/<USER>/public_html/public/favicon.ico
    Alias /robots.txt /home/<USER>/public_html/public/robots.txt
    Alias /sitemap.xml /home/<USER>/public_html/public/sitemap.xml

    # Cache static files
    <Directory "/home/<USER>/public_html/.next/static/">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
        Header set Cache-Control "public, immutable"
    </Directory>

    <Directory "/home/<USER>/public_html/public/">
        ExpiresActive On
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
        ExpiresByType image/gif "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/svg+xml "access plus 1 month"
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/pdf "access plus 1 month"
        ExpiresByType text/javascript "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType application/x-javascript "access plus 1 month"
        ExpiresByType application/x-shockwave-flash "access plus 1 month"
        ExpiresByType image/x-icon "access plus 1 year"
        ExpiresByType font/woff "access plus 1 year"
        ExpiresByType font/woff2 "access plus 1 year"
        ExpiresByType application/font-woff "access plus 1 year"
        ExpiresByType application/font-woff2 "access plus 1 year"
    </Directory>

    # Proxy configuration for Node.js app
    ProxyPreserveHost On
    ProxyPass / http://localhost:3000/
    ProxyPassReverse / http://localhost:3000/

    # Health check endpoint (bypass proxy for direct Apache response)
    ProxyPass /apache-health !
    <Location "/apache-health">
        SetHandler server-status
        Require local
    </Location>

    # Logging with more detailed format
    LogFormat "%h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\" %D" combined_with_time
    ErrorLog /home/<USER>/logs/apache_error.log
    CustomLog /home/<USER>/logs/apache_access.log combined_with_time

    # Redirect HTTP to HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

    # Error pages
    ErrorDocument 502 /502.html
    ErrorDocument 503 /503.html
    ErrorDocument 504 /504.html
</VirtualHost>

# HTTPS version (SSL enabled)
<VirtualHost *:443>
    ServerName ikiaconference.or.ke
    ServerAlias www.ikiaconference.or.ke
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/ikiaconference.or.ke.crt
    SSLCertificateKeyFile /etc/ssl/private/ikiaconference.or.ke.key
    SSLCertificateChainFile /etc/ssl/certs/ikiaconference.or.ke-chain.crt

    # SSL Security Settings
    SSLProtocol all -SSLv2 -SSLv3 -TLSv1 -TLSv1.1
    SSLCipherSuite ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
    SSLHonorCipherOrder off
    SSLSessionTickets off
    
    DocumentRoot /home/<USER>/public_html

    # Security headers (enhanced for HTTPS)
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';"

    # Remove server signature
    ServerTokens Prod
    Header unset Server

    # Enable compression
    <Location />
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png|ico|woff|woff2|ttf|eot)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </Location>

    # Serve static files directly from Apache for better performance
    ProxyPass /_next/static/ !
    ProxyPass /static/ !
    ProxyPass /favicon.ico !
    ProxyPass /robots.txt !
    ProxyPass /sitemap.xml !

    Alias /_next/static/ /home/<USER>/public_html/.next/static/
    Alias /static/ /home/<USER>/public_html/public/
    Alias /favicon.ico /home/<USER>/public_html/public/favicon.ico
    Alias /robots.txt /home/<USER>/public_html/public/robots.txt
    Alias /sitemap.xml /home/<USER>/public_html/public/sitemap.xml

    # Cache static files
    <Directory "/home/<USER>/public_html/.next/static/">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
        Header set Cache-Control "public, immutable"
    </Directory>

    <Directory "/home/<USER>/public_html/public/">
        ExpiresActive On
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
        ExpiresByType image/gif "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/svg+xml "access plus 1 month"
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/pdf "access plus 1 month"
        ExpiresByType text/javascript "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType application/x-javascript "access plus 1 month"
        ExpiresByType application/x-shockwave-flash "access plus 1 month"
        ExpiresByType image/x-icon "access plus 1 year"
        ExpiresByType font/woff "access plus 1 year"
        ExpiresByType font/woff2 "access plus 1 year"
        ExpiresByType application/font-woff "access plus 1 year"
        ExpiresByType application/font-woff2 "access plus 1 year"
    </Directory>

    # Proxy configuration for Node.js app
    ProxyPreserveHost On
    ProxyPass / http://localhost:3000/
    ProxyPassReverse / http://localhost:3000/

    # WebSocket support (if needed)
    ProxyPass /ws/ ws://localhost:3000/ws/
    ProxyPassReverse /ws/ ws://localhost:3000/ws/

    # Health check endpoint (bypass proxy for direct Apache response)
    ProxyPass /apache-health !
    <Location "/apache-health">
        SetHandler server-status
        Require local
    </Location>

    # Logging with more detailed format
    LogFormat "%h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\" %D" combined_with_time
    ErrorLog /home/<USER>/logs/apache_ssl_error.log
    CustomLog /home/<USER>/logs/apache_ssl_access.log combined_with_time

    # Error pages
    ErrorDocument 502 /502.html
    ErrorDocument 503 /503.html
    ErrorDocument 504 /504.html
</VirtualHost>
