import type { CollectionConfig } from 'payload'

export const Exhibitors: CollectionConfig = {
  slug: 'exhibitors',
  labels: {
    singular: 'Exhibitor',
    plural: 'Exhibitors',
  },
  admin: {
    useAsTitle: 'companyName',
    defaultColumns: ['companyName', 'email', 'selectedPackage', 'registrationStatus', 'createdAt'],
    group: 'Registration',
  },

  access: {
    read: ({ req: { user } }) => {
      if (user?.role === 'admin') return true
      if (user?.role === 'exhibitor') return { email: { equals: user.email } }
      return false
    },
    create: () => true, // Allow public registration
    update: ({ req: { user } }) => {
      if (user?.role === 'admin') return true
      if (user?.role === 'exhibitor') return { email: { equals: user.email } }
      return false
    },
    delete: ({ req: { user } }) => user?.role === 'admin',
  },
  fields: [
    // Personal Information
    {
      name: 'firstName',
      type: 'text',
      required: true,
      admin: {
        description: 'Primary contact first name',
      },
    },
    {
      name: 'lastName',
      type: 'text',
      required: true,
      admin: {
        description: 'Primary contact last name',
      },
    },
    {
      name: 'email',
      type: 'email',
      required: true,
      unique: true,
      admin: {
        description: 'Primary contact email address',
      },
    },
    {
      name: 'phone',
      type: 'text',
      required: true,
      admin: {
        description: 'Primary contact phone number',
      },
    },
    {
      name: 'position',
      type: 'text',
      admin: {
        description: 'Position/title in the company',
      },
    },
    {
      name: 'country',
      type: 'text',
      required: true,
      admin: {
        description: 'Country of origin',
      },
    },
    {
      name: 'city',
      type: 'text',
      admin: {
        description: 'City of origin',
      },
    },

    // Company Information
    {
      name: 'hasCompany',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Whether the exhibitor represents a company',
      },
    },
    {
      name: 'companyName',
      type: 'text',
      admin: {
        description: 'Company/organization name',
        condition: (data) => data.hasCompany,
      },
    },
    {
      name: 'website',
      type: 'text',
      admin: {
        description: 'Company website URL',
        condition: (data) => data.hasCompany,
      },
    },
    {
      name: 'address',
      type: 'textarea',
      admin: {
        description: 'Company address',
        condition: (data) => data.hasCompany,
      },
    },
    {
      name: 'businessType',
      type: 'select',
      options: [
        { label: 'Traditional Foods & Nutrition', value: 'Traditional Foods & Nutrition' },
        {
          label: 'Local Remedies & Traditional Medicine',
          value: 'Local Remedies & Traditional Medicine',
        },
        { label: 'Musicology & Cultural Arts', value: 'Musicology & Cultural Arts' },
        { label: 'Cultural Tourism & Heritage', value: 'Cultural Tourism & Heritage' },
        {
          label: 'Indigenous Technologies & Innovations',
          value: 'Indigenous Technologies & Innovations',
        },
        {
          label: 'Sui Generis Intellectual Property Systems',
          value: 'Sui Generis Intellectual Property Systems',
        },
      ],
      admin: {
        description: 'Type of business/industry',
        condition: (data) => data.hasCompany,
      },
    },
    {
      name: 'businessDescription',
      type: 'textarea',
      admin: {
        description: 'Brief description of the business',
        condition: (data) => data.hasCompany,
      },
    },
    {
      name: 'productsServices',
      type: 'textarea',
      admin: {
        description: 'Products or services offered',
        condition: (data) => data.hasCompany,
      },
    },
    {
      name: 'targetMarket',
      type: 'text',
      admin: {
        description: 'Target market or audience',
        condition: (data) => data.hasCompany,
      },
    },
    {
      name: 'yearsInBusiness',
      type: 'text',
      admin: {
        description: 'Number of years in business',
        condition: (data) => data.hasCompany,
      },
    },

    // Exhibition Details
    {
      name: 'selectedPackage',
      type: 'relationship',
      relationTo: 'delegatepackages',
      required: true,
      admin: {
        description: 'Selected exhibition package',
      },
    },
    {
      name: 'boothRequirement',
      type: 'select',
      options: [
        { label: 'Standard Booth (3x3m)', value: 'standard_3x3' },
        { label: 'Premium Booth (3x6m)', value: 'premium_3x6' },
        { label: 'Corner Booth (3x3m)', value: 'corner_3x3' },
        { label: 'Island Booth (6x6m)', value: 'island_6x6' },
        { label: 'Custom Size', value: 'custom' },
      ],
      admin: {
        description: 'Preferred booth size and type',
      },
    },
    {
      name: 'additionalServices',
      type: 'array',
      fields: [
        {
          name: 'service',
          type: 'select',
          options: [
            { label: 'Electricity Connection', value: 'electricity' },
            { label: 'Internet/WiFi', value: 'internet' },
            { label: 'Audio/Visual Equipment', value: 'av_equipment' },
            { label: 'Furniture Package', value: 'furniture' },
            { label: 'Storage Space', value: 'storage' },
            { label: 'Catering Services', value: 'catering' },
            { label: 'Marketing Materials', value: 'marketing' },
            { label: 'Translation Services', value: 'translation' },
          ],
        },
      ],
      admin: {
        description: 'Additional services required',
      },
    },
    {
      name: 'specialRequirements',
      type: 'textarea',
      admin: {
        description: 'Any special requirements or requests',
      },
    },

    // Additional Representatives
    {
      name: 'hasAdditionalReps',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Whether there are additional company representatives',
      },
    },
    {
      name: 'additionalRepresentatives',
      type: 'array',
      maxRows: 3,
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
        },
        {
          name: 'email',
          type: 'email',
          required: true,
        },
        {
          name: 'phone',
          type: 'text',
          required: true,
        },
        {
          name: 'position',
          type: 'text',
        },
      ],
      admin: {
        description: 'Additional company representatives (max 3)',
        condition: (data) => data.hasAdditionalReps,
      },
    },

    // Registration Status and Metadata
    {
      name: 'registrationStatus',
      type: 'select',
      options: [
        { label: 'Pending Payment', value: 'pending_payment' },
        { label: 'Payment Confirmed', value: 'payment_confirmed' },
        { label: 'Registration Complete', value: 'registration_complete' },
        { label: 'Cancelled', value: 'cancelled' },
      ],
      defaultValue: 'pending_payment',
      admin: {
        description: 'Current registration status',
      },
    },
    {
      name: 'paymentReference',
      type: 'text',
      admin: {
        description: 'Payment reference number',
      },
    },
    {
      name: 'registrationDate',
      type: 'date',
      defaultValue: () => new Date(),
      admin: {
        description: 'Date of registration',
      },
    },
    {
      name: 'notes',
      type: 'textarea',
      admin: {
        description: 'Internal notes about this exhibitor',
      },
    },

    // File Uploads
    {
      name: 'companyLogo',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Company logo for exhibition materials',
      },
    },
    {
      name: 'marketingMaterials',
      type: 'array',
      fields: [
        {
          name: 'file',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'description',
          type: 'text',
        },
      ],
      admin: {
        description: 'Marketing materials and brochures',
      },
    },

    // Agreements and Consents
    {
      name: 'termsAccepted',
      type: 'checkbox',
      required: true,
      admin: {
        description: 'Terms and conditions accepted',
      },
    },
    {
      name: 'exhibitorGuidelines',
      type: 'checkbox',
      required: true,
      admin: {
        description: 'Exhibitor guidelines accepted',
      },
    },
    {
      name: 'mediaConsent',
      type: 'checkbox',
      admin: {
        description: 'Consent for media coverage and photography',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Ensure phone number format
        if (data.phone && typeof data.phone === 'string') {
          if (data.phone.startsWith('0')) {
            data.phone = '254' + data.phone.slice(1)
          }
        }
        return data
      },
    ],
    afterChange: [
      ({ doc, operation }) => {
        if (operation === 'create') {
          console.log(`New exhibitor registered: ${doc.email}`)
        }
      },
    ],
  },
}
