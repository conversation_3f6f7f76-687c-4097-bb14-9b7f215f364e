'use client'

import { Building } from 'lucide-react'
import { useGetPartnersQuery } from '@/lib/api/partnersApi'
import Image from 'next/image'

export default function PartnersSection() {
  const {
    data,
    isLoading,
    error,
    refetch
  } = useGetPartnersQuery({ limit: 0 }, {
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
  })

  const partners = data?.partners || []

  const partnersPerRow = Math.ceil(partners.length / 3)
  const row1 = partners.slice(0, partnersPerRow)
  const row2 = partners.slice(partnersPerRow, partnersPerRow * 2)
  const row3 = partners.slice(partnersPerRow * 2)

  const renderPartnerRow = (rowPartners: typeof partners, rowIndex: number) => (
    <div key={rowIndex} className="flex flex-wrap justify-center items-center gap-8 mb-8">
      {rowPartners.map((partner, index) => (
        <div
          key={`${partner.id}-${rowIndex}-${index}`}
          className="flex-shrink-0 group cursor-pointer"
          title={`${partner.name}${partner.company ? ` - ${partner.company}` : ''}`}
        >
          <div className="w-32 h-20 bg-gray-50 rounded-lg flex items-center justify-center p-4 group-hover:bg-gray-100 transition-colors duration-300 group-hover:shadow-lg border border-gray-100">
            {partner.logo ? (
              <Image
                src={partner.logo}
                alt={partner.name}
                width={96}
                height={64}
                className="object-contain max-w-full max-h-full filter grayscale group-hover:grayscale-0 transition-all duration-300"
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.style.display = 'none'
                  const fallback = target.nextElementSibling as HTMLElement
                  if (fallback) fallback.classList.remove('hidden')
                }}
              />
            ) : null}
            <div className={`${partner.logo ? 'hidden' : ''} w-16 h-12 bg-[#7E2518] rounded flex items-center justify-center`}>
              <Building className="w-8 h-8 text-white" />
            </div>
          </div>
          <p className="text-xs text-center mt-2 text-gray-600 font-medium">
            {partner.name}
          </p>
        </div>
      ))}
    </div>
  )

  if (isLoading) {
    return (
      <section className="py-16 lg:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="flex justify-center">
              <span className="text-[#7E2518] font-bold px-3 py-1 text-xs border border-[#7E2518]/20 flex items-center space-x-1 transform -skew-x-12 mb-4">
                <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
                <span className="ml-2 text-sm font-medium text-[#7E2518] font-['Myriad_Pro',Arial,sans-serif]">
                  Our Partners
                </span>
              </span>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#7E2518] mb-6 font-['Myriad_Pro',Arial,sans-serif]">
              Loading
              <span className="block text-[#159147]">Partners...</span>
            </h2>
          </div>
          <div className="max-w-6xl mx-auto">
            {[1, 2, 3].map((row) => (
              <div key={row} className="flex flex-wrap justify-center items-center gap-8 mb-8">
                {[1, 2, 3, 4, 5].map((item) => (
                  <div key={item} className="w-32 h-20 bg-gray-200 rounded-lg animate-pulse"></div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </section>
    )
  }

  if (error) {
    return (
      <section className="py-16 lg:py-20 bg-white">
        <div className="container mx-auto px-4 text-center">
          <p className="text-red-600 mb-4">Failed to load partners. Please try again later.</p>
          <button
            onClick={() => refetch()}
            className="px-4 py-2 bg-[#7E2518] text-white rounded hover:bg-[#6a1f14] transition-colors"
          >
            Retry
          </button>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 lg:py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="flex justify-center">
            <span className="text-[#7E2518] font-bold px-3 py-1 text-xs border border-[#7E2518]/20 flex items-center space-x-1 transform -skew-x-12 mb-4">
              <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
              <span className="ml-2 text-sm font-medium text-[#7E2518] font-['Myriad_Pro',Arial,sans-serif]">
                Our Partners
              </span>
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#7E2518] mb-6 font-['Myriad_Pro',Arial,sans-serif]">
            Strategic
            <span className="block text-[#159147]">Partnerships</span>
          </h2>
          <div className="w-24 h-1 bg-[#E8B32C] rounded-full mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto font-['Myriad_Pro',Arial,sans-serif]">
            We collaborate with leading organizations, institutions, and communities to advance
            indigenous knowledge preservation and commercialization across Kenya and beyond.
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          {partners.length > 0 ? (
            <>
              {renderPartnerRow(row1, 1)}
              {renderPartnerRow(row2, 2)}
              {renderPartnerRow(row3, 3)}
            </>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500 font-['Myriad_Pro',Arial,sans-serif]">
                No partners available at the moment.
              </p>
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
