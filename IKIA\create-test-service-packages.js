#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create test service packages for exhibitor registration
 */

const BASE_URL = 'http://localhost:3000'

async function createServicePackage(packageData, token) {
  try {
    const response = await fetch(`${BASE_URL}/api/service-packages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `JWT ${token}`,
      },
      body: JSON.stringify(packageData),
    })

    const result = await response.json()
    
    if (response.ok) {
      console.log(`✅ Created package: ${result.doc?.name || result.name}`)
      return result
    } else {
      console.log(`❌ Failed to create package: ${result.error || result.message}`)
      return null
    }
  } catch (error) {
    console.error(`❌ Error creating package:`, error.message)
    return null
  }
}

async function getAuthToken() {
  try {
    const response = await fetch(`${BASE_URL}/api/users/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '01000010',
      }),
    })

    const result = await response.json()
    
    if (response.ok && result.token) {
      console.log('✅ Authentication successful')
      return result.token
    } else {
      console.log('❌ Authentication failed:', result.error || result.message)
      return null
    }
  } catch (error) {
    console.error('❌ Authentication error:', error.message)
    return null
  }
}

async function main() {
  console.log('🏢 Creating Test Service Packages for Exhibitor Registration')
  console.log('=' .repeat(60))

  // Get authentication token
  const token = await getAuthToken()
  if (!token) {
    console.log('❌ Cannot proceed without authentication')
    return
  }

  // Define test service packages
  const packages = [
    {
      name: 'Standard Exhibition Package',
      description: 'Perfect for small to medium exhibitors showcasing traditional products',
      price: 25000,
      currency: 'KES',
      duration: '3 days',
      packageType: 'exhibition',
      isActive: true,
      features: [
        { feature: '3x3m booth space' },
        { feature: 'Basic furniture package (table, chairs)' },
        { feature: 'Electricity connection' },
        { feature: 'Company listing in exhibition catalog' },
        { feature: 'Basic signage' }
      ]
    },
    {
      name: 'Premium Exhibition Package',
      description: 'Enhanced package for established businesses with premium positioning',
      price: 45000,
      currency: 'KES',
      duration: '3 days',
      packageType: 'exhibition',
      isActive: true,
      features: [
        { feature: '3x6m premium booth space' },
        { feature: 'Premium furniture package' },
        { feature: 'Electricity and internet connection' },
        { feature: 'Featured listing in exhibition catalog' },
        { feature: 'Custom signage and branding' },
        { feature: 'Audio/visual equipment' },
        { feature: 'Storage space' }
      ]
    },
    {
      name: 'Corner Booth Package',
      description: 'Strategic corner positioning for maximum visibility',
      price: 35000,
      currency: 'KES',
      duration: '3 days',
      packageType: 'exhibition',
      isActive: true,
      features: [
        { feature: '3x3m corner booth space' },
        { feature: 'Enhanced visibility from two sides' },
        { feature: 'Standard furniture package' },
        { feature: 'Electricity connection' },
        { feature: 'Priority listing in catalog' },
        { feature: 'Enhanced signage options' }
      ]
    },
    {
      name: 'Island Booth Package',
      description: 'Premium island booth for maximum exposure and impact',
      price: 75000,
      currency: 'KES',
      duration: '3 days',
      packageType: 'exhibition',
      isActive: true,
      features: [
        { feature: '6x6m island booth space' },
        { feature: '360-degree visibility' },
        { feature: 'Premium furniture and fixtures' },
        { feature: 'Full electricity and internet' },
        { feature: 'Featured catalog placement' },
        { feature: 'Custom booth design support' },
        { feature: 'Audio/visual equipment included' },
        { feature: 'Dedicated storage area' },
        { feature: 'Priority networking opportunities' }
      ]
    }
  ]

  console.log(`\n📦 Creating ${packages.length} service packages...`)

  let successCount = 0
  for (const packageData of packages) {
    const result = await createServicePackage(packageData, token)
    if (result) {
      successCount++
    }
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  console.log(`\n✅ Successfully created ${successCount}/${packages.length} service packages`)

  // Verify packages were created
  console.log('\n🔍 Verifying created packages...')
  try {
    const response = await fetch(`${BASE_URL}/api/service-packages`)
    const result = await response.json()
    
    if (result.docs && result.docs.length > 0) {
      console.log(`✅ Found ${result.docs.length} service packages in database`)
      result.docs.forEach(pkg => {
        console.log(`   • ${pkg.name} - ${pkg.currency} ${pkg.price} (${pkg.isActive ? 'Active' : 'Inactive'})`)
      })
    } else {
      console.log('❌ No service packages found in database')
    }
  } catch (error) {
    console.error('❌ Error verifying packages:', error.message)
  }

  console.log('\n🎉 Service package creation complete!')
  console.log('You can now test the exhibitor registration workflow.')
}

main().catch(console.error)
