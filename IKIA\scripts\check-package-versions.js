#!/usr/bin/env node

/**
 * Package Version Checker
 *
 * This script checks that all Lexical and Payload packages have consistent versions
 * to prevent the dependency mismatch errors.
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

function checkPackageVersions() {
  console.log('🔍 Checking package versions for consistency...\n')

  try {
    // Read package.json
    const packageJsonPath = path.join(process.cwd(), 'package.json')
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))

    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }

    // Check Lexical packages
    console.log('📦 LEXICAL PACKAGES:')
    console.log('='.repeat(50))

    const lexicalPackages = Object.entries(dependencies)
      .filter(([name]) => name.startsWith('@lexical/'))
      .sort()

    const lexicalVersions = new Set()
    lexicalPackages.forEach(([name, version]) => {
      console.log(`   ${name}: ${version}`)
      lexicalVersions.add(version)
    })

    if (lexicalVersions.size === 1) {
      console.log(`   ✅ All Lexical packages use version: ${Array.from(lexicalVersions)[0]}`)
    } else {
      console.log(
        `   ❌ Version mismatch! Found versions: ${Array.from(lexicalVersions).join(', ')}`,
      )
    }

    // Check Payload packages
    console.log('\n📦 PAYLOAD PACKAGES:')
    console.log('='.repeat(50))

    const payloadPackages = Object.entries(dependencies)
      .filter(([name]) => name.startsWith('@payloadcms/') || name === 'payload')
      .sort()

    const payloadVersions = new Set()
    payloadPackages.forEach(([name, version]) => {
      console.log(`   ${name}: ${version}`)
      payloadVersions.add(version.replace(/[\^~]/, '')) // Remove version prefixes
    })

    if (payloadVersions.size === 1) {
      console.log(`   ✅ All Payload packages use version: ${Array.from(payloadVersions)[0]}`)
    } else {
      console.log(
        `   ❌ Version mismatch! Found versions: ${Array.from(payloadVersions).join(', ')}`,
      )
    }

    // Overall status
    console.log('\n📊 OVERALL STATUS:')
    console.log('='.repeat(50))

    const allGood = lexicalVersions.size === 1 && payloadVersions.size === 1

    if (allGood) {
      console.log('✅ All package versions are consistent!')
      console.log('🚀 Ready to start the development server.')
    } else {
      console.log('❌ Package version mismatches detected!')
      console.log('💡 Run: rm -rf node_modules package-lock.json && npm install')
    }

    // Check for version prefixes that might cause issues
    console.log('\n🔍 CHECKING FOR VERSION PREFIXES:')
    console.log('='.repeat(50))

    const hasVersionPrefixes = Object.entries(dependencies)
      .filter(([name]) => name.startsWith('@lexical/') || name.startsWith('@payloadcms/'))
      .filter(([, version]) => version.startsWith('^') || version.startsWith('~'))

    if (hasVersionPrefixes.length > 0) {
      console.log('⚠️  Found version prefixes that might cause issues:')
      hasVersionPrefixes.forEach(([name, version]) => {
        console.log(`   ${name}: ${version}`)
      })
      console.log('💡 Consider removing ^ and ~ prefixes for exact version matching.')
    } else {
      console.log('✅ No problematic version prefixes found.')
    }

    return allGood
  } catch (error) {
    console.error('❌ Error reading package.json:', error.message)
    return false
  }
}

// Run the check
if (require.main === module) {
  const success = checkPackageVersions()
  process.exit(success ? 0 : 1)
}

module.exports = { checkPackageVersions }
