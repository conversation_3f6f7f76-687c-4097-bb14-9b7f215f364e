import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getTimeAgo(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()

  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const month = 30 * day
  const year = 12 * month

  if (diffMs < hour) {
    const minutes = Math.floor(diffMs / minute)
    return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`
  } else if (diffMs < day) {
    const hours = Math.floor(diffMs / hour)
    return `${hours} hour${hours !== 1 ? 's' : ''} ago`
  } else if (diffMs < month) {
    const days = Math.floor(diffMs / day)
    return `${days} day${days !== 1 ? 's' : ''} ago`
  } else if (diffMs < year) {
    const months = Math.floor(diffMs / month)
    return `${months} month${months !== 1 ? 's' : ''} ago`
  } else {
    const years = Math.floor(diffMs / year)
    return `${years} year${years !== 1 ? 's' : ''} ago`
  }
}
