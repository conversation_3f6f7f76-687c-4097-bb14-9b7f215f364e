'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ExternalLink, Building, Search, Users, Globe, Tv } from 'lucide-react'
import { partners } from '../../partners-sponsors/data/partners'
import type { PartnerFilter } from '../../partners-sponsors/types'
import Image from 'next/image'

const filterOptions = [
  { value: 'all' as PartnerFilter, label: 'All Partners', icon: Globe },
  { value: 'strategic' as PartnerFilter, label: 'Strategic', icon: Building },
  { value: 'implementation' as PartnerFilter, label: 'Implementation', icon: Users },
  { value: 'media' as PartnerFilter, label: 'Media', icon: Tv },
  { value: 'community' as PartnerFilter, label: 'Community', icon: Users },
]

const partnerTypeColors = {
  strategic: 'bg-[#7E2518]',
  implementation: 'bg-[#159147]',
  media: 'bg-[#E8B32C]',
  community: 'bg-[#81B1DB]',
}

export default function PartnersGridWithFilters() {
  const [activeFilter, setActiveFilter] = useState<PartnerFilter>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [hoveredCard, setHoveredCard] = useState<string | null>(null)

  const filteredPartners = partners.filter((partner) => {
    const matchesFilter = activeFilter === 'all' || partner.type === activeFilter
    const matchesSearch =
      partner.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      partner.description.toLowerCase().includes(searchQuery.toLowerCase())

    return matchesFilter && matchesSearch
  })

  return (
    <section className="py-16 lg:py-20 section-bg-primary">
      <div className="container mx-auto px-4">
        {/* Filters Section */}
        <div className="mb-12">
          {/* Search Bar */}
          <div className="max-w-md mx-auto mb-8">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="Search partners..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-3 w-full border-gray-200 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent font-['Myriad_Pro',Arial,sans-serif]"
              />
            </div>
          </div>

          {/* Filter Buttons */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {filterOptions.map((option) => {
              const Icon = option.icon
              return (
                <Button
                  key={option.value}
                  variant={activeFilter === option.value ? 'default' : 'outline'}
                  onClick={() => setActiveFilter(option.value)}
                  className={`px-6 py-3 font-bold transition-all duration-300 main-shadow font-['Myriad_Pro',Arial,sans-serif] ${
                    activeFilter === option.value
                      ? 'bg-[#7E2518] text-white hover:bg-[#159147] transform -translate-y-1'
                      : 'border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white'
                  }`}
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {option.label}
                </Button>
              )
            })}
          </div>

          {/* Results Count */}
          <div className="text-center">
            <p className="text-gray-600 font-['Myriad_Pro',Arial,sans-serif]">
              Showing {filteredPartners.length} of {partners.length} partners
            </p>
          </div>
        </div>

        {/* Partners Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredPartners.map((partner) => (
            <Card
              key={partner.id}
              className="group bg-white border border-gray-200 main-shadow hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden"
              onMouseEnter={() => setHoveredCard(partner.id)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              {/* Background Overlay */}
              <div
                className={`absolute inset-0 ${partnerTypeColors[partner.type]} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
              ></div>

              {/* Animated Border Glow */}
              {hoveredCard === partner.id && (
                <div className="absolute -inset-1 bg-[#7E2518]/20 rounded-lg blur-sm animate-pulse"></div>
              )}

              <CardContent className="relative p-8">
                {/* Header with Logo and Badge */}
                <div className="flex items-start justify-between mb-6">
                  <div className="relative w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Image
                      src={partner.logo}
                      alt={partner.name}
                      width={48}
                      height={48}
                      className="object-contain"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement
                        target.style.display = 'none'
                        target.nextElementSibling?.classList.remove('hidden')
                      }}
                    />
                    <div className="hidden w-12 h-12 bg-[#7E2518] rounded-lg flex items-center justify-center">
                      <Building className="w-6 h-6 text-white" />
                    </div>
                  </div>

                  <Badge
                    className={`${partnerTypeColors[partner.type]} text-white border-0 text-xs`}
                  >
                    {partner.type}
                  </Badge>
                </div>

                {/* Partner Name */}
                <h3 className="text-xl font-bold text-[#7E2518] mb-3 group-hover:text-[#159147] transition-colors font-['Myriad_Pro',Arial,sans-serif]">
                  {partner.name}
                </h3>

                {/* Description */}
                <p className="text-gray-600 text-sm mb-6 line-clamp-3 font-['Myriad_Pro',Arial,sans-serif]">
                  {partner.description}
                </p>

                {/* Footer */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {partner.featured && (
                      <Badge variant="outline" className="text-xs border-[#E8B32C] text-[#E8B32C]">
                        Featured
                      </Badge>
                    )}
                  </div>

                  {partner.website && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-[#7E2518] hover:text-[#159147] hover:bg-[#7E2518]/5 p-2"
                      asChild
                    >
                      <a href={partner.website} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredPartners.length === 0 && (
          <div className="text-center py-16">
            <Building className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-gray-500 mb-2 font-['Myriad_Pro',Arial,sans-serif]">
              No partners found
            </h3>
            <p className="text-gray-400 font-['Myriad_Pro',Arial,sans-serif]">
              Try adjusting your search or filter criteria.
            </p>
          </div>
        )}
      </div>
    </section>
  )
}
