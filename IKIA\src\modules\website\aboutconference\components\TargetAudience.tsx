import {
  Users,
  Cpu,
  BookOpen,
  GraduationCap,
  Earth,
  Heart,
  Camera,
  Lightbulb,
  Globe,
  TrendingUp,
  Award,
  Building,
  HelpCircle,
} from 'lucide-react'

import { Card, CardContent } from '@/components/ui/card'

import { useGetTargetAudiencesQuery } from '@/lib/api/targetAudienceApi'

import { TargetAudienceItem } from '../types'

export default function TargetAudience() {
  const { data, error, isLoading } = useGetTargetAudiencesQuery(undefined, {
    refetchOnFocus: false,
    refetchOnReconnect: false,
    refetchOnMountOrArgChange: false,
  })

  const iconMap: Record<string, { icon: any; bgColor: string }> = {
    'Investors & Entrepreneurs': { icon: Building, bgColor: 'bg-[#159147]' },
    'Indigenous Communities': { icon: Users, bgColor: 'bg-[#7E2518]' },
    Policymakers: { icon: Award, bgColor: 'bg-[#E8B32C]' },
    'Researchers & Academia': { icon: Lightbulb, bgColor: 'bg-[#C86E36]' },
    'Development Partners': { icon: Globe, bgColor: 'bg-[#81B1DB]' },
    'Financial Institutions': { icon: TrendingUp, bgColor: 'bg-[#8B4513]' },
  }

  if (isLoading) return <p>Loading...</p>
  if (error) return <p>Error loading data.</p>

  return (
    <>
      {/* Target Audience */}
      <section className="py-16 lg:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-6">
              <Users className="w-8 h-8 text-[#7E2518]" />
              <h2
                className="text-3xl md:text-4xl font-bold text-[#7E2518]"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                Target Audience & Expected Outcomes
              </h2>
            </div>
            <p
              className="text-gray-600 max-w-2xl mx-auto"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Here are the Targeted Audience and Expected Outcomes from the 1<sup>st</sup> IKIA
              Investment Conference and Trade Fair 2025
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {data.docs.map((audience: TargetAudienceItem, index: number) => {
              const { icon: Icon, bgColor } = iconMap[audience.title] || {
                icon: HelpCircle,
                bgColor: 'bg-gray-400',
              }

              const number = String(index + 1).padStart(2, '0')

              return (
                <Card
                  key={audience.id}
                  className="bg-white border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden"
                >
                  <div className={`${bgColor} text-white p-6`}>
                    <div className="flex items-center gap-4 mb-3">
                      <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                        <span
                          className="text-sm font-bold"
                          style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}
                        >
                          {number}
                        </span>
                      </div>
                      <Icon className="w-6 h-6" />
                    </div>
                    <h3
                      className="font-bold text-lg mb-2"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      {audience.title}
                    </h3>
                  </div>
                  <CardContent className="p-6">
                    <p
                      className="text-gray-600"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      {audience.description}
                    </p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>
    </>
  )
}
