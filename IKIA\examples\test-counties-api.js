// Test script for Counties API endpoints
// Run with: node examples/test-counties-api.js

const BASE_URL = 'http://localhost:3000/api'

async function testCountiesAPI() {
  console.log('🗺️  Testing Counties API Endpoints')
  console.log('=' .repeat(50))

  try {
    // Test 1: Get all counties
    console.log('\n📋 Test 1: Get all counties')
    const allCountiesResponse = await fetch(`${BASE_URL}/counties`)
    const allCountiesData = await allCountiesResponse.json()
    
    if (allCountiesResponse.ok) {
      console.log('✅ Success!')
      console.log(`Found ${allCountiesData.totalCounties} counties`)
      console.log(`Page ${allCountiesData.page} of ${allCountiesData.totalPages}`)
      if (allCountiesData.counties.length > 0) {
        console.log(`First county: ${allCountiesData.counties[0].name}`)
      }
    } else {
      console.log('❌ Failed:', allCountiesData)
    }

    // Test 2: Search counties by name
    console.log('\n🔍 Test 2: Search counties by name (Nairobi)')
    const searchResponse = await fetch(`${BASE_URL}/counties?name=Nairobi`)
    const searchData = await searchResponse.json()
    
    if (searchResponse.ok) {
      console.log('✅ Success!')
      console.log(`Found ${searchData.counties.length} counties matching "Nairobi"`)
      searchData.counties.forEach(county => {
        console.log(`- ${county.name} (${county.code})`)
      })
    } else {
      console.log('❌ Failed:', searchData)
    }

    // Test 3: Filter by active status
    console.log('\n✅ Test 3: Filter by active status')
    const activeResponse = await fetch(`${BASE_URL}/counties?active=true&limit=5`)
    const activeData = await activeResponse.json()

    if (activeResponse.ok) {
      console.log('✅ Success!')
      console.log(`Found ${activeData.totalCounties} active counties (showing first 5)`)
      activeData.counties.forEach(county => {
        console.log(`- ${county.name}: ${county.coordinates.latitude}, ${county.coordinates.longitude}`)
      })
    } else {
      console.log('❌ Failed:', activeData)
    }

    // Test 4: Proximity search (near Nairobi)
    console.log('\n📍 Test 4: Proximity search (near Nairobi coordinates)')
    const proximityResponse = await fetch(`${BASE_URL}/counties?near_lat=-1.2921&near_lng=36.8219&radius=100`)
    const proximityData = await proximityResponse.json()
    
    if (proximityResponse.ok) {
      console.log('✅ Success!')
      console.log(`Found ${proximityData.counties.length} counties within 100km of Nairobi`)
      proximityData.counties.forEach(county => {
        const distance = county.distance ? ` (${county.distance.toFixed(1)}km away)` : ''
        console.log(`- ${county.name}${distance}`)
      })
    } else {
      console.log('❌ Failed:', proximityData)
    }

    // Test 5: Filter by county code
    console.log('\n🏷️  Test 5: Filter by county code')
    const codeResponse = await fetch(`${BASE_URL}/counties?code=KE-047`)
    const codeData = await codeResponse.json()

    if (codeResponse.ok) {
      console.log('✅ Success!')
      console.log(`Found ${codeData.counties.length} counties with code KE-047`)
      codeData.counties.forEach(county => {
        console.log(`- ${county.name} (${county.code}): ${county.description}`)
      })
    } else {
      console.log('❌ Failed:', codeData)
    }

    // Test 6: Get single county (if any exist)
    if (allCountiesData.counties && allCountiesData.counties.length > 0) {
      const firstCounty = allCountiesData.counties[0]
      console.log(`\n🏛️  Test 6: Get single county (${firstCounty.name})`)
      
      const singleResponse = await fetch(`${BASE_URL}/counties/${firstCounty.id}`)
      const singleData = await singleResponse.json()
      
      if (singleResponse.ok) {
        console.log('✅ Success!')
        console.log(`County: ${singleData.county.name}`)
        console.log(`Code: ${singleData.county.code}`)
        console.log(`Coordinates: ${singleData.county.coordinates.latitude}, ${singleData.county.coordinates.longitude}`)
        console.log(`Description: ${singleData.county.description}`)
        console.log(`Active: ${singleData.county.isActive}`)
      } else {
        console.log('❌ Failed:', singleData)
      }
    }

    // Test 7: Bounding box search
    console.log('\n🗺️  Test 7: Bounding box search (Kenya approximate bounds)')
    const boundsResponse = await fetch(`${BASE_URL}/counties/bounds?north=5&south=-5&east=42&west=33`)
    const boundsData = await boundsResponse.json()
    
    if (boundsResponse.ok) {
      console.log('✅ Success!')
      console.log(`Found ${boundsData.totalCounties} counties in bounding box`)
      console.log(`Bounds: N:${boundsData.bounds.north}, S:${boundsData.bounds.south}, E:${boundsData.bounds.east}, W:${boundsData.bounds.west}`)
      boundsData.counties.slice(0, 3).forEach(county => {
        console.log(`- ${county.name}: ${county.coordinates.latitude}, ${county.coordinates.longitude}`)
      })
    } else {
      console.log('❌ Failed:', boundsData)
    }

  } catch (error) {
    console.error('❌ Error running tests:', error.message)
  }

  console.log('\n' + '=' .repeat(50))
  console.log('🏁 Counties API tests completed!')
}

// Run the tests
testCountiesAPI().catch(console.error)
