'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Search, Filter, User, Award, Users, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import ProfileCard from '@/modules/website/invest/components/ProfileCard'
import ProfileModal from '@/modules/website/invest/components/ProfileModal'
import {
  useGetUsersByTypeQuery,
  useSearchUsersQuery,
  useGetVerifiedUsersQuery,
  useGetTopRatedUsersQuery,
} from '@/lib/api/usersApi'
import type { User as UserType } from '@/modules/website/invest/types'

export default function AllExhibitorsPage() {
  const [selectedProfile, setSelectedProfile] = useState<UserType | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterBy, setFilterBy] = useState('all')

  const handleProfileClick = (profile: UserType) => {
    setSelectedProfile(profile)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedProfile(null)
  }

  // Get exhibitors based on filter and search
  const shouldSearch = searchTerm.length > 0
  const shouldFilterVerified = filterBy === 'verified'
  const shouldFilterTopRated = filterBy === 'top-rated'

  // Use appropriate query based on current filters
  const {
    data: allExhibitorsData,
    error: allError,
    isLoading: allLoading,
  } = useGetUsersByTypeQuery(
    { userType: 'exhibitor', limit: 50 },
    { skip: shouldSearch || shouldFilterVerified || shouldFilterTopRated },
  )

  const {
    data: searchData,
    error: searchError,
    isLoading: searchLoading,
  } = useSearchUsersQuery({ searchTerm, userType: 'exhibitor', limit: 50 }, { skip: !shouldSearch })

  const {
    data: verifiedData,
    error: verifiedError,
    isLoading: verifiedLoading,
  } = useGetVerifiedUsersQuery(
    { userType: 'exhibitor', limit: 50 },
    { skip: !shouldFilterVerified || shouldSearch },
  )

  const {
    data: topRatedData,
    error: topRatedError,
    isLoading: topRatedLoading,
  } = useGetTopRatedUsersQuery(
    { userType: 'exhibitor', minRating: 4.8, limit: 50 },
    { skip: !shouldFilterTopRated || shouldSearch },
  )

  // Determine which data to use
  const currentData = shouldSearch
    ? searchData
    : shouldFilterVerified
      ? verifiedData
      : shouldFilterTopRated
        ? topRatedData
        : allExhibitorsData

  const exhibitors = currentData?.docs || []
  const isLoading = allLoading || searchLoading || verifiedLoading || topRatedLoading
  const error = allError || searchError || verifiedError || topRatedError

  return (
    <div className="min-h-screen bg-background">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-[#159147] to-[#81B1DB] text-white">
        <div className="max-w-7xl mx-auto px-6 py-16">
          <div className="max-w-4xl mx-auto text-center">
            {/* Back Navigation */}
            <div className="flex justify-start mb-8">
              <Link href="/invest">
                <Button
                  variant="outline"
                  className="border-white text-[#159147] hover:bg-white hover:text-[#159147]"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Invest
                </Button>
              </Link>
            </div>

            <h1
              className="text-4xl md:text-5xl font-bold mb-6"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              All <span className="text-[#E8B32C]">Exhibitors</span>
            </h1>
            <div className="w-24 h-1 bg-white mx-auto mb-6"></div>
            <p
              className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Discover verified Indigenous Knowledge holders and cultural practitioners who are
              preserving and sharing traditional wisdom.
            </p>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
              <div className="bg-white/10 backdrop-blur-md p-6">
                <div className="flex items-center justify-center mb-2">
                  <User className="w-8 h-8 text-[#E8B32C]" />
                </div>
                <div
                  className="text-2xl font-bold"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  {isLoading ? '...' : `${currentData?.totalDocs || 0}+`}
                </div>
                <div
                  className="text-white/80"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  Knowledge Holders
                </div>
              </div>
              <div className="bg-white/10 backdrop-blur-md p-6">
                <div className="flex items-center justify-center mb-2">
                  <Award className="w-8 h-8 text-[#E8B32C]" />
                </div>
                <div
                  className="text-2xl font-bold"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  500+
                </div>
                <div
                  className="text-white/80"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  Years of Experience
                </div>
              </div>
              <div className="bg-white/10 backdrop-blur-md p-6">
                <div className="flex items-center justify-center mb-2">
                  <Users className="w-8 h-8 text-[#E8B32C]" />
                </div>
                <div
                  className="text-2xl font-bold"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  50+
                </div>
                <div
                  className="text-white/80"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  Cultural Practices
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white shadow-md p-6 mb-8">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search exhibitors by name, organization, or focus area..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 focus:ring-2 focus:ring-[#159147] focus:border-transparent"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                />
              </div>

              {/* Filter */}
              <div className="flex items-center gap-2">
                <Filter className="w-5 h-5 text-gray-400" />
                <select
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value)}
                  className="px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-[#159147] focus:border-transparent"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  <option value="all">All Exhibitors</option>
                  <option value="top-rated">Top Rated (4.8+)</option>
                  <option value="verified">Verified Only</option>
                </select>
              </div>
            </div>

            {/* Results Count */}
            <div
              className="mt-4 text-gray-600"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              {isLoading
                ? 'Loading...'
                : `Showing ${exhibitors.length} of ${currentData?.totalDocs || 0} exhibitors`}
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="bg-gray-200 animate-pulse h-96 rounded"></div>
              ))}
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="text-center py-16">
              <User className="w-16 h-16 text-red-500 mx-auto mb-4" />
              <h3
                className="text-xl font-semibold text-gray-600 mb-2"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                Unable to Load Exhibitors
              </h3>
              <p className="text-gray-500" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                Please try again later or refresh the page.
              </p>
            </div>
          )}

          {/* Exhibitors Grid */}
          {!isLoading && !error && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {exhibitors.map((exhibitor, index) => (
                <div key={index} className="w-full">
                  <ProfileCard
                    profile={exhibitor}
                    type="exhibitor"
                    onClick={() => handleProfileClick(exhibitor)}
                  />
                </div>
              ))}
            </div>
          )}

          {/* No Results */}
          {!isLoading && !error && exhibitors.length === 0 && (
            <div className="text-center py-16">
              <div className="text-gray-400 mb-4">
                <Users className="w-16 h-16 mx-auto" />
              </div>
              <h3
                className="text-xl font-semibold text-gray-600 mb-2"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                No exhibitors found
              </h3>
              <p className="text-gray-500" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                Try adjusting your search or filter criteria
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Profile Modal */}
      {selectedProfile && (
        <ProfileModal
          profile={selectedProfile}
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          type="exhibitor"
        />
      )}
    </div>
  )
}
