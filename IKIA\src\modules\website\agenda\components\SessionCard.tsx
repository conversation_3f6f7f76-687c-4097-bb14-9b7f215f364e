import { Card } from '@/components/ui/card'

interface SessionCardProps {
  title: string
  time: string
  description: string
  isBreak?: boolean
}

export default function SessionCard({
  title,
  time,
  description,
  isBreak = false,
}: SessionCardProps) {
  if (isBreak) {
    return (
      <div className="bg-black text-white py-6 px-8 rounded-lg text-center font-medium text-lg">
        {title}
      </div>
    )
  }

  return (
    <Card className="p-4 sm:p-6 md:p-8 mb-6 hover:shadow-lg transition-shadow border border-gray-200">
      <div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
        <div className="w-full sm:w-24 h-24 bg-gray-100 border-2 border-gray-300 flex-shrink-0 relative mx-auto sm:mx-0">
          {/* Placeholder image with X pattern */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-full h-full relative">
              <div className="absolute inset-0 border border-gray-400"></div>
              <div className="absolute top-0 left-0 w-full h-px bg-gray-400 transform rotate-45 origin-top-left"></div>
              <div className="absolute top-0 right-0 w-full h-px bg-gray-400 transform -rotate-45 origin-top-right"></div>
            </div>
          </div>
        </div>
        <div className="flex-1">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-6 mb-4">
            <h3 className="font-semibold text-gray-900 text-base sm:text-lg">{title}</h3>
            <div className="hidden sm:block w-px h-5 bg-gray-300"></div>
            <span className="text-gray-600 text-sm sm:text-base">{time}</span>
          </div>
          <p className="text-gray-600 leading-relaxed text-sm sm:text-base">{description}</p>
        </div>
      </div>
    </Card>
  )
}
