#!/bin/bash

# Database Setup Script for IKIA Conference
# This script initializes the database and restarts the application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="ikia-conference"
APP_DIR="/home/<USER>/public_html"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Setup database
setup_database() {
    log_info "Setting up database for IKIA Conference..."
    cd "$APP_DIR"

    # Check if DATABASE_URL is set
    if [ -z "$DATABASE_URL" ] && [ -z "$DATABASE_URI" ]; then
        log_error "DATABASE_URL or DATABASE_URI environment variable is not set"
        return 1
    fi

    # Stop PM2 instances temporarily
    log_info "Stopping PM2 instances..."
    pm2 stop "$APP_NAME" || true

    # Wait a moment
    sleep 2
    
    # Try to create database tables using Payload
    log_info "Initializing database schema..."
    
    # Method 1: Try payload migrate (if available)
    if npm run payload migrate 2>/dev/null; then
        log_success "Database migration completed successfully"
    else
        log_warning "Migration command not available, trying alternative method..."
        
        # Method 2: Try to run the application once to auto-create tables
        log_info "Starting application to auto-create database schema..."
        timeout 30s npm run start:prod &
        APP_PID=$!
        
        # Wait for application to start and create tables
        sleep 15
        
        # Kill the temporary process
        kill $APP_PID 2>/dev/null || true
        wait $APP_PID 2>/dev/null || true
        
        log_info "Database initialization attempt completed"
    fi
    
    # Restart PM2 instances
    log_info "Restarting PM2 instances..."
    pm2 start ecosystem.config.cjs --env production
    
    # Wait for stabilization
    sleep 5
    
    # Check if application is running
    if pm2 describe "$APP_NAME" | grep -q "online"; then
        log_success "Application restarted successfully"
    else
        log_error "Application failed to restart"
        return 1
    fi
    
    log_success "Database setup completed!"
}

# Run seed data (optional)
seed_data() {
    log_info "Seeding initial data..."
    cd "$APP_DIR"
    
    if npm run seed 2>/dev/null; then
        log_success "Data seeding completed"
    else
        log_warning "Seeding failed or not available"
    fi
}

# Main execution
case "${1:-setup}" in
    "setup")
        setup_database
        ;;
    "seed")
        seed_data
        ;;
    "full")
        setup_database
        seed_data
        ;;
    *)
        echo "Usage: $0 {setup|seed|full}"
        echo ""
        echo "Commands:"
        echo "  setup - Initialize database schema"
        echo "  seed  - Seed initial data"
        echo "  full  - Setup database and seed data"
        exit 1
        ;;
esac
