#!/usr/bin/env node

/**
 * Simple database connection test for Neon PostgreSQL
 * This script tests the database connection without Payload CMS overhead
 */

import { Client } from 'pg'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...')
  const dbUrl = process.env.DATABASE_URL || process.env.DATABASE_URI
  console.log('Database URL:', dbUrl?.replace(/:[^:@]*@/, ':****@'))

  const client = new Client({
    connectionString: dbUrl,
    // Neon-specific settings
    ssl: dbUrl?.includes('neon.tech') ? { rejectUnauthorized: false } : false,
    // Timeout settings
    connectionTimeoutMillis: 10000, // 10 seconds
    query_timeout: 30000, // 30 seconds
    statement_timeout: 30000, // 30 seconds
  })

  try {
    console.log('⏳ Connecting to database...')
    await client.connect()
    console.log('✅ Database connection successful!')

    // Test basic query
    console.log('⏳ Testing basic query...')
    const result = await client.query('SELECT NOW() as current_time, version() as pg_version')
    console.log('✅ Basic query successful!')
    console.log('Current time:', result.rows[0].current_time)
    console.log('PostgreSQL version:', result.rows[0].pg_version)

    // Test table listing (simpler than schema introspection)
    console.log('⏳ Testing table listing...')
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
      LIMIT 10
    `)
    console.log('✅ Table listing successful!')
    console.log(
      'Found tables:',
      tablesResult.rows.map((row) => row.table_name),
    )

    // Test connection pool info
    console.log('⏳ Testing connection info...')
    const connInfo = await client.query(`
      SELECT 
        current_database() as database_name,
        current_user as user_name,
        inet_server_addr() as server_ip,
        inet_server_port() as server_port
    `)
    console.log('✅ Connection info retrieved!')
    console.log('Database:', connInfo.rows[0].database_name)
    console.log('User:', connInfo.rows[0].user_name)
    console.log('Server:', connInfo.rows[0].server_ip + ':' + connInfo.rows[0].server_port)
  } catch (error) {
    console.error('❌ Database connection failed!')
    console.error('Error details:', error.message)
    console.error('Error code:', error.code)

    if (error.code === 'ETIMEDOUT') {
      console.error('🔧 Timeout error - possible solutions:')
      console.error('  1. Check if Neon database is active (not sleeping)')
      console.error('  2. Verify network connectivity')
      console.error('  3. Check if connection string is correct')
      console.error('  4. Try increasing timeout values')
    }

    if (error.code === 'ENOTFOUND') {
      console.error('🔧 DNS resolution error - possible solutions:')
      console.error('  1. Check internet connection')
      console.error('  2. Verify database hostname in connection string')
    }

    process.exit(1)
  } finally {
    await client.end()
    console.log('🔌 Database connection closed.')
  }
}

// Run the test
testDatabaseConnection().catch(console.error)
