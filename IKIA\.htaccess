# Test if .htaccess is working
RewriteEngine On

# Exclude cPanel system files from proxying
RewriteCond %{REQUEST_URI} !^/cgi-sys/
RewriteCond %{REQUEST_URI} !^/cpanel/
RewriteCond %{REQUEST_URI} !^/.well-known/

# Proxy all requests to Node.js app (including root path)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ http://127.0.0.1:3000/$1 [P,L,QSA]

# Alternative: Direct proxy (uncomment if above doesn't work)
# ProxyPreserveHost On
# ProxyPass /cgi-sys !
# ProxyPass / http://127.0.0.1:3000/
# ProxyPassReverse / http://127.0.0.1:3000/

# Static file handling
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 year"
    Header append Cache-Control "public, immutable"
</FilesMatch>

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>
