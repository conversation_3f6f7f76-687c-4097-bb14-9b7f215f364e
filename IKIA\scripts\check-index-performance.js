#!/usr/bin/env node

/**
 * Database Index Performance Checker
 *
 * This script checks the performance and usage of database indexes
 * and provides recommendations for optimization
 *
 * Usage: node scripts/check-index-performance.js
 */

import { Pool } from 'pg'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URI,
})

async function checkIndexPerformance() {
  console.log('📊 Checking database index performance...\n')

  const client = await pool.connect()

  try {
    // Check index usage statistics
    console.log('📈 INDEX USAGE STATISTICS:')
    console.log('='.repeat(80))

    const indexUsage = await client.query(`
      SELECT
        schemaname,
        relname as tablename,
        indexrelname as indexname,
        idx_tup_read,
        idx_tup_fetch,
        idx_scan,
        CASE
          WHEN idx_scan = 0 THEN 'UNUSED'
          WHEN idx_scan < 10 THEN 'LOW USAGE'
          WHEN idx_scan < 100 THEN 'MODERATE USAGE'
          ELSE 'HIGH USAGE'
        END as usage_level
      FROM pg_stat_user_indexes
      WHERE schemaname = 'public'
      ORDER BY idx_scan DESC, relname, indexrelname
    `)

    indexUsage.rows.forEach((row) => {
      console.log(`${row.tablename}.${row.indexname}:`)
      console.log(
        `   Scans: ${row.idx_scan} | Tuples Read: ${row.idx_tup_read} | Usage: ${row.usage_level}`,
      )
    })

    // Check table statistics
    console.log('\n📋 TABLE STATISTICS:')
    console.log('='.repeat(80))

    const tableStats = await client.query(`
      SELECT 
        schemaname,
        relname as tablename,
        n_tup_ins as inserts,
        n_tup_upd as updates,
        n_tup_del as deletes,
        n_live_tup as live_tuples,
        n_dead_tup as dead_tuples,
        last_vacuum,
        last_autovacuum,
        last_analyze,
        last_autoanalyze
      FROM pg_stat_user_tables 
      WHERE schemaname = 'public'
      ORDER BY n_live_tup DESC
    `)

    tableStats.rows.forEach((row) => {
      console.log(`${row.tablename}:`)
      console.log(`   Live Tuples: ${row.live_tuples} | Dead Tuples: ${row.dead_tuples}`)
      console.log(
        `   Operations: ${row.inserts} inserts, ${row.updates} updates, ${row.deletes} deletes`,
      )
      console.log(`   Last Analyze: ${row.last_analyze || 'Never'}`)
    })

    // Check slow queries (if pg_stat_statements is enabled)
    console.log('\n🐌 CHECKING FOR SLOW QUERIES:')
    console.log('='.repeat(80))

    try {
      const slowQueries = await client.query(`
        SELECT 
          query,
          calls,
          total_time,
          mean_time,
          rows
        FROM pg_stat_statements 
        WHERE query LIKE '%invoices%' OR query LIKE '%pesaflow%' OR query LIKE '%users%'
        ORDER BY mean_time DESC 
        LIMIT 10
      `)

      if (slowQueries.rows.length > 0) {
        slowQueries.rows.forEach((row, index) => {
          console.log(`${index + 1}. Query: ${row.query.substring(0, 100)}...`)
          console.log(
            `   Calls: ${row.calls} | Avg Time: ${Math.round(row.mean_time)}ms | Total Time: ${Math.round(row.total_time)}ms`,
          )
        })
      } else {
        console.log('   No slow query data available (pg_stat_statements not enabled)')
      }
    } catch (error) {
      console.log('   pg_stat_statements extension not available')
    }

    // Check index sizes
    console.log('\n💾 INDEX SIZES:')
    console.log('='.repeat(80))

    const indexSizes = await client.query(`
      SELECT
        schemaname,
        relname as tablename,
        indexrelname as indexname,
        pg_size_pretty(pg_relation_size(indexrelid)) as size
      FROM pg_stat_user_indexes
      WHERE schemaname = 'public'
      ORDER BY pg_relation_size(indexrelid) DESC
    `)

    indexSizes.rows.forEach((row) => {
      console.log(`${row.tablename}.${row.indexname}: ${row.size}`)
    })

    // Performance recommendations
    console.log('\n🚀 PERFORMANCE RECOMMENDATIONS:')
    console.log('='.repeat(80))

    const recommendations = []

    // Check for unused indexes
    const unusedIndexes = indexUsage.rows.filter((row) => row.idx_scan === 0)
    if (unusedIndexes.length > 0) {
      recommendations.push(
        `❌ Found ${unusedIndexes.length} unused indexes - consider dropping them`,
      )
      unusedIndexes.forEach((idx) => {
        console.log(`   - ${idx.tablename}.${idx.indexname}`)
      })
    }

    // Check for tables that need VACUUM
    const needVacuum = tableStats.rows.filter((row) => row.dead_tuples > row.live_tuples * 0.1)
    if (needVacuum.length > 0) {
      recommendations.push(`🧹 ${needVacuum.length} tables need VACUUM`)
      needVacuum.forEach((table) => {
        console.log(`   - ${table.tablename} (${table.dead_tuples} dead tuples)`)
      })
    }

    // Check for tables that need ANALYZE
    const needAnalyze = tableStats.rows.filter(
      (row) =>
        !row.last_analyze ||
        new Date(row.last_analyze) < new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    )
    if (needAnalyze.length > 0) {
      recommendations.push(`📊 ${needAnalyze.length} tables need ANALYZE`)
      needAnalyze.forEach((table) => {
        console.log(`   - ${table.tablename}`)
      })
    }

    // Check for missing indexes on foreign keys
    const missingFKIndexes = await client.query(`
      SELECT 
        conrelid::regclass AS table_name,
        conname AS constraint_name,
        pg_get_constraintdef(oid) AS constraint_def
      FROM pg_constraint 
      WHERE contype = 'f' 
        AND conrelid::regclass::text IN ('users', 'invoices', 'pesaflow_notifications', 'exhibitors')
    `)

    if (missingFKIndexes.rows.length > 0) {
      console.log('\n🔗 FOREIGN KEY CONSTRAINTS:')
      missingFKIndexes.rows.forEach((row) => {
        console.log(`   ${row.table_name}: ${row.constraint_name}`)
      })
    }

    if (recommendations.length === 0) {
      console.log('✅ Database indexes are performing well!')
    }

    // IPN-specific performance check
    console.log('\n⚡ IPN PERFORMANCE CHECK:')
    console.log('='.repeat(80))

    try {
      // Test invoice lookup performance
      const invoiceLookupStart = Date.now()
      await client.query("EXPLAIN ANALYZE SELECT * FROM invoices WHERE invoice_number = 'INV-test'")
      const invoiceLookupTime = Date.now() - invoiceLookupStart

      console.log(`Invoice lookup performance: ${invoiceLookupTime}ms`)

      if (invoiceLookupTime > 10) {
        console.log('⚠️  Invoice lookup is slow - check invoice_number index')
      } else {
        console.log('✅ Invoice lookup performance is good')
      }
    } catch (error) {
      console.log('Could not test IPN performance:', error.message)
    }

    console.log('\n📚 MAINTENANCE COMMANDS:')
    console.log('='.repeat(80))
    console.log('Run these commands for maintenance:')
    console.log('1. VACUUM ANALYZE; -- Full database maintenance')
    console.log('2. REINDEX DATABASE your_database_name; -- Rebuild all indexes')
    console.log('3. SELECT pg_stat_reset(); -- Reset statistics')
  } catch (error) {
    console.error('❌ Error checking index performance:', error)
  } finally {
    client.release()
    await pool.end()
  }
}

// Main execution
checkIndexPerformance()
  .then(() => {
    console.log('\n🎉 Index performance check completed!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Fatal error:', error)
    process.exit(1)
  })

export { checkIndexPerformance }
