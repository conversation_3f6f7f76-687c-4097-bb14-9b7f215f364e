'use client'
import { Card, CardContent } from '@/components/ui/card'
import { Clock, MapPin, Users } from 'lucide-react'
import { IkiaActionButtons } from '@/components/ikia-navbar'
import { useGetFeaturedProgramsQuery } from '@/lib/api/programsApi'
import type { Program } from '@/lib/api/programsApi'
import { agendaButtons } from '../data/buttonConfigs'
import Link from 'next/link'

// Helper function to extract plain text from rich text
const extractPlainText = (richText: Program['description']): string => {
  try {
    if (richText?.root?.children && Array.isArray(richText.root.children)) {
      return richText.root.children
        .map((child) => {
          if (
            child &&
            typeof child === 'object' &&
            'children' in child &&
            Array.isArray(child.children)
          ) {
            return child.children
              .map((textNode) => {
                if (textNode && typeof textNode === 'object' && 'text' in textNode) {
                  return String(textNode.text || '')
                }
                return ''
              })
              .join('')
          }
          if (child && typeof child === 'object' && 'text' in child) {
            return String(child.text || '')
          }
          return ''
        })
        .join(' ')
        .trim()
    }
    return ''
  } catch {
    return ''
  }
}

// Helper function to format time display
const formatTimeRange = (startTime?: string | null, endTime?: string | null): string => {
  if (!startTime && !endTime) return ''
  if (startTime && endTime) return `${startTime} - ${endTime}`
  if (startTime) return `${startTime}`
  return ''
}

// Helper function to format date display
const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    })
  } catch {
    return dateString
  }
}

// Helper function to get program type info
const getProgramTypeInfo = (type: Program['type']): { name: string; color: string } => {
  if (typeof type === 'object' && type !== null) {
    return {
      name: type.name,
      color: type.color || 'bg-gray-100 border-gray-300 text-gray-600',
    }
  }

  // Fallback for legacy string types
  const typeString = String(type)
  switch (typeString) {
    case 'keynote':
      return { name: 'Keynote', color: 'bg-red-50 border-red-200 text-red-700' }
    case 'panel':
      return { name: 'Panel', color: 'bg-blue-50 border-blue-200 text-blue-700' }
    case 'workshop':
      return { name: 'Workshop', color: 'bg-green-50 border-green-200 text-green-700' }
    case 'networking':
      return { name: 'Networking', color: 'bg-orange-50 border-orange-200 text-orange-700' }
    case 'exhibition':
      return { name: 'Exhibition', color: 'bg-yellow-50 border-yellow-200 text-yellow-700' }
    case 'breakout':
      return { name: 'Breakout', color: 'bg-purple-50 border-purple-200 text-purple-700' }
    default:
      return { name: typeString, color: 'bg-gray-100 border-gray-300 text-gray-600' }
  }
}

// Helper function to get thematic area info
const getThematicAreaInfo = (
  thematicArea: Program['thematicArea'],
): { name: string; color: string } | null => {
  if (typeof thematicArea === 'object' && thematicArea !== null) {
    return {
      name: thematicArea.name,
      color: thematicArea.color || 'bg-blue-50 border-blue-200 text-blue-700',
    }
  }
  return null
}

export default function AgendaSection() {
  const { data, error, isLoading } = useGetFeaturedProgramsQuery(
    { limit: 6 },
    {
      refetchOnFocus: false,
      refetchOnReconnect: false,
      refetchOnMountOrArgChange: false,
    },
  )

  // Use API data or show loading/error states
  const programs = data?.docs || []

  // Loading state
  if (isLoading) {
    return (
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="font-myriad font-bold text-sm uppercase tracking-wider text-[#7E2518] mb-4">
              Featured Programs
            </h2>
            <h3 className="font-myriad font-bold text-3xl lg:text-4xl text-black mb-6">
              Conference Programs & Agenda
            </h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <Card key={index} className="border-2 border-gray-200 animate-pulse">
                <CardContent className="p-6 space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-full"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    )
  }

  // Error state
  if (error) {
    return (
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center">
            <h2 className="font-myriad font-bold text-sm uppercase tracking-wider text-[#7E2518] mb-4">
              Featured Programs
            </h2>
            <h3 className="font-myriad font-bold text-3xl lg:text-4xl text-black mb-6">
              Conference Programs & Agenda
            </h3>
            <p className="text-gray-600 mb-8">
              Unable to load programs at the moment. Please try again later.
            </p>
            <Link href="/programs">
              <button className="bg-[#7E2518] text-white font-myriad font-semibold px-8 py-4 hover:bg-[#7E2518]/90 transition-colors">
                View All Programs
              </button>
            </Link>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="font-myriad font-bold text-sm uppercase tracking-wider text-[#7E2518] mb-4">
            Featured Programs
          </h2>
          <h3 className="font-myriad font-bold text-3xl lg:text-4xl text-black mb-6">
            Programs & Agenda
          </h3>
          <p className="font-myriad text-lg text-gray-700 max-w-3xl mx-auto">
            Join us for a full day of insights, networking, and discovery. Explore our featured
            programs including keynotes, panels, workshops, and exhibitions.
          </p>
        </div>

        {/* Programs Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {programs.map((program) => {
            const timeRange = formatTimeRange(program.startTime, program.endTime)
            const description = extractPlainText(program.description)
            const programDate = formatDate(program.date)
            const typeInfo = getProgramTypeInfo(program.type)
            const thematicAreaInfo = getThematicAreaInfo(program.thematicArea)

            return (
              <Card
                key={program.id}
                className="border-2 border-[#A0503A]/30 hover:border-[#7E2518]/40 transition-all duration-300 hover:shadow-lg group bg-gray-50"
              >
                <CardContent className="p-6 space-y-4">
                  {/* Date, Time and Type Badge */}
                  <div className="flex items-center justify-between">
                    <div className="flex flex-col space-y-1">
                      <div className="flex items-center space-x-2 text-[#7E2518]">
                        <Clock className="w-4 h-4" />
                        <span className="font-myriad font-bold text-sm">{programDate}</span>
                      </div>
                      {timeRange && (
                        <span className="font-myriad text-xs text-gray-600 ml-6">{timeRange}</span>
                      )}
                    </div>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-myriad font-semibold border ${typeInfo.color}`}
                    >
                      {typeInfo.name}
                    </span>
                  </div>

                  {/* Title */}
                  <h4 className="font-myriad font-bold text-lg text-black leading-tight">
                    {program.title}
                  </h4>

                  {/* Thematic Area Tag */}
                  {thematicAreaInfo && (
                    <div className="flex items-center">
                      <span
                        className={`px-2 py-1 rounded text-xs font-myriad font-medium border ${thematicAreaInfo.color}`}
                      >
                        {thematicAreaInfo.name}
                      </span>
                    </div>
                  )}

                  {/* Description */}
                  <p className="font-myriad text-sm text-gray-600 leading-relaxed">
                    {description || 'Program details coming soon...'}
                  </p>

                  {/* Venue */}
                  <div className="flex items-center space-x-2 text-[#E8B32C]">
                    <MapPin className="w-4 h-4" />
                    <span className="font-myriad text-sm font-medium">{program.venue}</span>
                  </div>

                  {/* Speakers */}
                  {program.speakers && program.speakers.length > 0 && (
                    <div className="flex items-start space-x-2 text-gray-600">
                      <Users className="w-4 h-4 mt-0.5" />
                      <div className="flex flex-wrap gap-1">
                        {program.speakers.map((speaker, index) => (
                          <span
                            key={index}
                            className="font-myriad text-xs bg-white px-2 py-1 rounded border border-gray-200"
                          >
                            {speaker.name}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Parallel Session Indicator */}
                  {program.isParallel && (
                    <div className="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded border border-orange-200">
                      Parallel Session
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* View Full Programs Button */}
        <div className="text-center">
          <IkiaActionButtons
            buttons={agendaButtons}
            size="large"
            showOnMobile={true}
            layout="horizontal"
            className="justify-center"
          />
        </div>
      </div>
    </section>
  )
}
