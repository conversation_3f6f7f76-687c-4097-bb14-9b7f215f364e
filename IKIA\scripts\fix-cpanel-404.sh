#!/bin/bash

# =============================================================================
# cPanel 404 Fix Script for Next.js with PM2
# =============================================================================

set -e

# Configuration
APP_NAME="ikia-conference"
APP_DIR="/home/<USER>/public_html"
LOG_FILE="$APP_DIR/logs/fix-404.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Create logs directory
mkdir -p "$APP_DIR/logs"

log_info "Starting cPanel 404 troubleshooting for $APP_NAME..."

# 1. Check if PM2 is running
check_pm2_status() {
    log_info "Checking PM2 status..."
    
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 is not installed or not in PATH"
        return 1
    fi
    
    pm2 status | tee -a "$LOG_FILE"
    
    if pm2 describe "$APP_NAME" > /dev/null 2>&1; then
        log_success "PM2 process '$APP_NAME' found"
        pm2 describe "$APP_NAME" | tee -a "$LOG_FILE"
    else
        log_warning "PM2 process '$APP_NAME' not found"
        return 1
    fi
}

# 2. Check if port 3000 is listening
check_port_listening() {
    log_info "Checking if port 3000 is listening..."
    
    if netstat -tlnp 2>/dev/null | grep ":3000 " > /dev/null; then
        log_success "Port 3000 is listening"
        netstat -tlnp | grep ":3000 " | tee -a "$LOG_FILE"
    else
        log_error "Port 3000 is not listening"
        return 1
    fi
}

# 3. Test local connection
test_local_connection() {
    log_info "Testing local connection to http://127.0.0.1:3000..."
    
    if curl -f -s -m 10 http://127.0.0.1:3000/ > /dev/null 2>&1; then
        log_success "Local connection successful"
    else
        log_error "Local connection failed"
        log_info "Attempting to get response details..."
        curl -v http://127.0.0.1:3000/ 2>&1 | head -20 | tee -a "$LOG_FILE"
        return 1
    fi
}

# 4. Check .htaccess configuration
check_htaccess() {
    log_info "Checking .htaccess configuration..."
    
    if [ -f "$APP_DIR/.htaccess" ]; then
        log_success ".htaccess file exists"
        log_info "Current .htaccess content:"
        cat "$APP_DIR/.htaccess" | tee -a "$LOG_FILE"
    else
        log_error ".htaccess file not found"
        return 1
    fi
}

# 5. Check Next.js build
check_nextjs_build() {
    log_info "Checking Next.js build..."
    
    if [ -d "$APP_DIR/.next" ]; then
        log_success ".next directory exists"
        log_info "Build info:"
        ls -la "$APP_DIR/.next/" | tee -a "$LOG_FILE"
    else
        log_error ".next directory not found - application not built"
        return 1
    fi
}

# 6. Check environment variables
check_environment() {
    log_info "Checking environment variables..."
    
    if [ -f "$APP_DIR/.env" ]; then
        log_success ".env file exists"
        log_info "Environment variables (sensitive values hidden):"
        grep -E "^[A-Z_]+" "$APP_DIR/.env" | sed 's/=.*/=***/' | tee -a "$LOG_FILE"
    else
        log_warning ".env file not found"
    fi
}

# 7. Fix common issues
fix_issues() {
    log_info "Attempting to fix common issues..."
    
    cd "$APP_DIR"
    
    # Stop current PM2 process
    if pm2 describe "$APP_NAME" > /dev/null 2>&1; then
        log_info "Stopping current PM2 process..."
        pm2 stop "$APP_NAME"
        pm2 delete "$APP_NAME"
    fi
    
    # Rebuild application
    log_info "Rebuilding application..."
    npm run build:production 2>&1 | tee -a "$LOG_FILE"
    
    # Start with explicit configuration
    log_info "Starting PM2 with production environment..."
    pm2 start ecosystem.config.cjs --env production 2>&1 | tee -a "$LOG_FILE"
    
    # Wait for startup
    sleep 5
    
    # Save PM2 configuration
    pm2 save
    
    log_success "Fix attempt completed"
}

# 8. Final verification
verify_fix() {
    log_info "Verifying fix..."
    
    # Check PM2 status
    if pm2 describe "$APP_NAME" | grep -q "online"; then
        log_success "PM2 process is online"
    else
        log_error "PM2 process is not online"
        pm2 logs "$APP_NAME" --lines 20 | tee -a "$LOG_FILE"
        return 1
    fi
    
    # Test local connection
    sleep 3
    if curl -f -s -m 10 http://127.0.0.1:3000/ > /dev/null 2>&1; then
        log_success "Local connection working"
    else
        log_error "Local connection still failing"
        return 1
    fi
    
    # Test external connection (if possible)
    if curl -f -s -m 10 https://ikiaconference.or.ke/ > /dev/null 2>&1; then
        log_success "External connection working"
    else
        log_warning "External connection not working - may need DNS/SSL configuration"
    fi
}

# Main execution
main() {
    log_info "=== cPanel 404 Fix Script Started ==="
    
    # Run diagnostics
    check_pm2_status || log_warning "PM2 status check failed"
    check_port_listening || log_warning "Port listening check failed"
    test_local_connection || log_warning "Local connection test failed"
    check_htaccess || log_warning "htaccess check failed"
    check_nextjs_build || log_warning "Next.js build check failed"
    check_environment || log_warning "Environment check failed"
    
    # Ask user if they want to attempt fixes
    echo ""
    read -p "Do you want to attempt automatic fixes? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        fix_issues
        verify_fix
    fi
    
    log_info "=== Script completed. Check logs at: $LOG_FILE ==="
}

# Script execution
case "${1:-main}" in
    "main"|"")
        main
        ;;
    "check")
        check_pm2_status
        check_port_listening
        test_local_connection
        check_htaccess
        ;;
    "fix")
        fix_issues
        verify_fix
        ;;
    "logs")
        pm2 logs "$APP_NAME"
        ;;
    *)
        echo "Usage: $0 [main|check|fix|logs]"
        echo "  main  - Run full diagnostic and optional fix (default)"
        echo "  check - Run diagnostics only"
        echo "  fix   - Run fixes only"
        echo "  logs  - Show PM2 logs"
        ;;
esac
