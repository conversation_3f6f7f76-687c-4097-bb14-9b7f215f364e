"use client"

import type React from "react"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Building2, ChevronRight, ChevronLeft } from "lucide-react"
import { SponsorshipTierGrid } from "@/modules/website/registration/components/PackageGrid"
import { StepIndicator, sponsorSteps } from "@/modules/website/registration/components/StepIndicator"
import { OTPVerification } from "@/modules/website/registration/components/OTPVerification"
import { ImageUpload } from "@/modules/website/registration/components/ImageUpload"
import { PaymentMethod } from "@/modules/website/registration/components/PaymentMethod"
import { TicketAllocation, TicketAllocation as TicketAllocationType } from "@/modules/website/registration/components/TicketAllocation"
import { sponsorshipTiers as allSponsorshipTiers, countries as countriesList } from "@/modules/website/registration/lib/registration-data"
import {
  saveFormDataToStorage,
  GroupRegistrationState,
  initializeGroupRegistration,
  addGroupMember,
  updateGroupMember,
  removeGroupMember,
  navigateToMember
} from "@/modules/website/registration/lib/registration-utils"
import { GroupRegistration } from "@/modules/website/registration/components/GroupRegistration"

const sponsorshipAreas = [
  "Conference Sessions",
  "Networking Events",
  "Welcome Reception",
  "Gala Dinner",
  "Coffee Breaks",
  "Conference Materials",
  "Technology & Innovation Showcase",
  "Cultural Performances",
  "Site Visits",
  "Awards Ceremony",
]

export default function SponsorRegistrationForm() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [groupState, setGroupState] = useState<GroupRegistrationState>(initializeGroupRegistration())
  const [isOtpVerified, setIsOtpVerified] = useState(false)
  const [profileImage, setProfileImage] = useState<File | null>(null)
  const [profileImagePreview, setProfileImagePreview] = useState<string | null>(null)
  const [ticketAllocation, setTicketAllocation] = useState<TicketAllocationType | null>(null)

  const [formData, setFormData] = useState({
    // Personal Information (for group registration compatibility)
    firstName: "",
    lastName: "",

    // Company Information
    companyName: "",
    contactPerson: "",
    email: "",
    phone: "",
    website: "",
    country: "",
    city: "",
    address: "",
    
    // Company Details
    companyDescription: "",
    industryType: "",
    companySize: "",
    annualRevenue: "",
    
    // Sponsorship Information
    selectedTier: "",
    sponsorshipAreas: [] as string[],
    customRequirements: "",
    marketingObjectives: "",

    // Ticket Allocation
    allocatedTickets: {
      vip: 0,
      delegate: 0,
      total: 0
    },
    ticketDistribution: [] as Array<{
      name: string,
      email: string,
      type: 'VIP' | 'Delegate'
    }>,
    
    // Brand Information
    brandGuidelines: "",
    logoFiles: null as File | null,
    marketingMaterials: "",
    
    // Representatives
    representative1Name: "",
    representative1Email: "",
    representative1Position: "",
    representative2Name: "",
    representative2Email: "",
    representative2Position: "",
    
    // Special Package Details
    mediaPartnerDetails: "",
    receptionRequirements: "",
    merchandiseSpecs: "",
    technologySpecs: "",

    // Agreements
    termsAccepted: false,
    sponsorshipAgreement: false,
    marketingConsent: false,
  })

  // Calculate ticket allocation based on sponsorship tier
  const calculateTicketAllocation = (tierName: string) => {
    // Find the tier in the data to get exact ticket allocation
    const tier = allSponsorshipTiers.find(t => t.name === tierName)
    if (tier) {
      return {
        vip: tier.vipTickets || 0,
        delegate: tier.delegateTickets || 0,
        total: (tier.vipTickets || 0) + (tier.delegateTickets || 0)
      }
    }

    // Fallback for manual allocations if tier not found
    const allocations = {
      "Title Sponsor": { vip: 15, delegate: 0, total: 15 },
      "Platinum Sponsor": { vip: 5, delegate: 5, total: 10 },
      "Gold Sponsor": { vip: 2, delegate: 3, total: 5 },
      "Silver Sponsor": { vip: 1, delegate: 2, total: 3 },
      "Bronze Sponsor": { vip: 0, delegate: 2, total: 2 },
    }
    return allocations[tierName as keyof typeof allocations] || { vip: 0, delegate: 0, total: 0 }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value
      }

      // Update ticket allocation when tier changes
      if (field === 'selectedTier' && value) {
        const allocation = calculateTicketAllocation(value)
        newData.allocatedTickets = allocation
        // Reset ticket distribution when tier changes
        newData.ticketDistribution = []
      }

      return newData
    })
  }

  // Multi-step handlers
  const handleImageChange = (file: File | null, preview: string | null) => {
    setProfileImage(file)
    setProfileImagePreview(preview)
  }

  const handleOtpVerification = (verified: boolean) => {
    setIsOtpVerified(verified)
  }

  const handleTicketAllocationChange = (allocation: TicketAllocationType) => {
    setTicketAllocation(allocation)
  }

  const nextStep = () => {
    if (currentStep < sponsorSteps.length) {
      setCompletedSteps(prev => [...prev, currentStep])
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const canProceedToNextStep = () => {
    switch (currentStep) {
      case 1: // Company details step
        return formData.companyName &&
               formData.contactPerson &&
               formData.email &&
               formData.phone &&
               formData.country &&
               formData.selectedTier &&
               isOtpVerified &&
               formData.termsAccepted
      case 2: // Ticket allocation step
        return ticketAllocation?.isComplete || false
      case 3: // Review step
        return true
      case 4: // Payment step
        return true
      default:
        return false
    }
  }

  const getSelectedTierDetails = () => {
    return allSponsorshipTiers.find(tier => tier.id === formData.selectedTier)
  }

  const handlePaymentComplete = (paymentData: any) => {
    // Save registration data
    const registrationData = {
      ...formData,
      profileImage: profileImagePreview,
      ticketAllocation,
      paymentData,
      registrationType: 'sponsor',
      isGroupRegistration: groupState.isGroupMode,
      groupMembers: groupState.isGroupMode ? groupState.members : undefined,
      submissionDate: new Date().toISOString(),
    }

    saveFormDataToStorage("ikia-registration", registrationData)

    // Navigate to success page
    router.push('/registration/success')
  }

  // Ticket distribution management
  const addTicketRecipient = () => {
    if (formData.ticketDistribution.length < formData.allocatedTickets.total) {
      setFormData(prev => ({
        ...prev,
        ticketDistribution: [
          ...prev.ticketDistribution,
          { name: "", email: "", type: "Delegate" as const }
        ]
      }))
    }
  }

  const updateTicketRecipient = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      ticketDistribution: prev.ticketDistribution.map((ticket, i) =>
        i === index ? { ...ticket, [field]: value } : ticket
      )
    }))
  }

  const removeTicketRecipient = (index: number) => {
    setFormData(prev => ({
      ...prev,
      ticketDistribution: prev.ticketDistribution.filter((_, i) => i !== index)
    }))
  }

  const getAvailableTicketTypes = (currentIndex: number) => {
    const usedVipTickets = formData.ticketDistribution.filter((ticket, index) =>
      index !== currentIndex && ticket.type === 'VIP'
    ).length
    const usedDelegateTickets = formData.ticketDistribution.filter((ticket, index) =>
      index !== currentIndex && ticket.type === 'Delegate'
    ).length

    const availableTypes = []
    if (usedVipTickets < formData.allocatedTickets.vip) {
      availableTypes.push('VIP')
    }
    if (usedDelegateTickets < formData.allocatedTickets.delegate) {
      availableTypes.push('Delegate')
    }

    return availableTypes
  }

  const handleSponsorshipAreaChange = (area: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      sponsorshipAreas: checked 
        ? [...prev.sponsorshipAreas, area]
        : prev.sponsorshipAreas.filter(a => a !== area)
    }))
  }

  // Group registration handlers
  const handleToggleGroupMode = (enabled: boolean) => {
    setGroupState(prev => ({ ...prev, isGroupMode: enabled }))
  }

  const handleAddMember = () => {
    setGroupState(prev => addGroupMember(prev, formData))
    // Reset form for next member
    setFormData({
      // Personal Information (for group registration compatibility)
      firstName: "",
      lastName: "",

      // Company Information
      companyName: "",
      contactPerson: "",
      email: "",
      phone: "",
      website: "",
      country: "",
      city: "",
      address: "",

      // Company Details
      companyDescription: "",
      industryType: "",
      companySize: "",
      annualRevenue: "",

      // Sponsorship Information
      selectedTier: "",
      sponsorshipAreas: [] as string[],
      customRequirements: "",
      marketingObjectives: "",

      // Ticket Allocation
      allocatedTickets: {
        vip: 0,
        delegate: 0,
        total: 0
      },
      ticketDistribution: [] as Array<{
        name: string,
        email: string,
        type: 'VIP' | 'Delegate'
      }>,

      // Brand Information
      brandGuidelines: "",
      logoFiles: null as File | null,
      marketingMaterials: "",

      // Representatives
      representative1Name: "",
      representative1Email: "",
      representative1Position: "",
      representative2Name: "",
      representative2Email: "",
      representative2Position: "",

      // Special Package Details
      mediaPartnerDetails: "",
      receptionRequirements: "",
      merchandiseSpecs: "",
      technologySpecs: "",

      // Agreements
      termsAccepted: false,
      sponsorshipAgreement: false,
      marketingConsent: false,
    })
  }

  const handleNavigateToMember = (index: number) => {
    // Save current form data before navigating
    setGroupState(prev => updateGroupMember(prev, prev.currentMemberIndex, formData))

    // Navigate to the selected member
    const newState = navigateToMember(groupState, index)
    setGroupState(newState)

    // Load the member's data into the form
    if (index < newState.members.length) {
      const memberData = newState.members[index] as any
      setFormData(memberData)
    } else {
      // New member - reset form
      setFormData({
        // Personal Information (for group registration compatibility)
        firstName: "",
        lastName: "",

        // Company Information
        companyName: "",
        contactPerson: "",
        email: "",
        phone: "",
        website: "",
        country: "",
        city: "",
        address: "",

        // Company Details
        companyDescription: "",
        industryType: "",
        companySize: "",
        annualRevenue: "",

        // Sponsorship Information
        selectedTier: "",
        sponsorshipAreas: [] as string[],
        customRequirements: "",
        marketingObjectives: "",

        // Ticket Allocation
        allocatedTickets: {
          vip: 0,
          delegate: 0,
          total: 0
        },
        ticketDistribution: [] as Array<{
          name: string,
          email: string,
          type: 'VIP' | 'Delegate'
        }>,

        // Brand Information
        brandGuidelines: "",
        logoFiles: null as File | null,
        marketingMaterials: "",

        // Representatives
        representative1Name: "",
        representative1Email: "",
        representative1Position: "",
        representative2Name: "",
        representative2Email: "",
        representative2Position: "",

        // Special Package Details
        mediaPartnerDetails: "",
        receptionRequirements: "",
        merchandiseSpecs: "",
        technologySpecs: "",

        // Agreements
        termsAccepted: false,
        sponsorshipAgreement: false,
        marketingConsent: false,
      })
    }
  }

  const handleRemoveMember = (index: number) => {
    setGroupState(prev => removeGroupMember(prev, index))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Prepare registration data
    let registrationData

    if (groupState.isGroupMode) {
      // Update current member data and include all group members
      const updatedGroupState = updateGroupMember(groupState, groupState.currentMemberIndex, formData)
      registrationData = {
        registrationType: "Conference Sponsor",
        isGroupRegistration: true,
        groupMembers: [...updatedGroupState.members, formData],
        submissionDate: new Date().toISOString()
      }
    } else {
      // Single registration
      registrationData = {
        ...formData,
        registrationType: "Conference Sponsor",
        isGroupRegistration: false,
        submissionDate: new Date().toISOString()
      }
    }

    saveFormDataToStorage("ikia-registration", registrationData)

    console.log("Sponsor form submitted:", registrationData)
    router.push("/registration/success")
  }



  return (
    <div className="max-w-6xl mx-auto px-4 py-8 font-myriad">
      {/* Enhanced Header with Homepage Design */}
      <div className="mb-12">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-8 font-myriad hover:bg-primary/10 text-primary"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Registration Types
        </Button>

        {/* Hero Section matching homepage style */}
        <div className="text-center mb-8 relative">
          <div className="relative bg-gradient-to-br from-background via-card to-muted/20 p-8 md:p-12 border border-border overflow-hidden main-shadow">
            {/* Heritage Elements */}
            <div className="absolute top-6 left-8 heritage-dot heritage-dot-primary"></div>
            <div
              className="absolute top-12 right-12 heritage-dot heritage-dot-accent"
              style={{ animationDelay: '1s' }}
            ></div>
            <div
              className="absolute bottom-8 left-12 heritage-dot heritage-dot-secondary"
              style={{ animationDelay: '2s' }}
            ></div>

            <div className="relative z-10">
              {/* Icon */}
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-primary/80 mb-6 shadow-lg">
                <Building2 className="w-8 h-8 text-primary-foreground" />
              </div>

              <h1 className="text-3xl md:text-5xl font-bold text-foreground mb-4 font-myriad">
                Sponsor <span className="text-primary">Registration</span>
              </h1>

              <p className="text-base md:text-lg text-muted-foreground mb-4 font-myriad">
                Become a <span className="text-primary font-semibold">Strategic Partner</span> and drive{' '}
                <span className="text-secondary font-semibold">Meaningful Impact</span>
              </p>

              <p className="text-muted-foreground text-sm italic font-myriad">
                &ldquo;Where <span className="text-primary">partnership</span> creates{' '}
                <span className="text-secondary">lasting change</span>&rdquo;
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Step Indicator */}
      <StepIndicator
        steps={sponsorSteps}
        currentStep={currentStep}
        completedSteps={completedSteps}
        className="mb-8"
      />

      {/* Step Content */}
      <div className="space-y-8">

        {/* Step 1: Company Details & Sponsorship Selection */}
        {currentStep === 1 && (
          <>
            {/* Group Registration */}
            <GroupRegistration
              groupState={groupState}
              onToggleGroupMode={handleToggleGroupMode}
              onAddMember={handleAddMember}
              onNavigateToMember={handleNavigateToMember}
              onRemoveMember={handleRemoveMember}
              currentMemberData={formData}
            />

            {/* Company Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-bold font-myriad">
                  Company Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="companyName">Company Name *</Label>
                    <Input
                      id="companyName"
                      value={formData.companyName}
                      onChange={(e) => handleInputChange("companyName", e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="contactPerson">Contact Person *</Label>
                    <Input
                      id="contactPerson"
                      value={formData.contactPerson}
                      onChange={(e) => handleInputChange("contactPerson", e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange("phone", e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="country">Country *</Label>
                    <Select value={formData.country} onValueChange={(value) => handleInputChange("country", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select your country" />
                      </SelectTrigger>
                      <SelectContent>
                        {countriesList.map((country) => (
                          <SelectItem key={country} value={country}>
                            {country}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      value={formData.website}
                      onChange={(e) => handleInputChange("website", e.target.value)}
                      placeholder="https://www.example.com"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="companyDescription">Company Description</Label>
                  <Textarea
                    id="companyDescription"
                    placeholder="Brief description of your company and its mission..."
                    value={formData.companyDescription}
                    onChange={(e) => handleInputChange("companyDescription", e.target.value)}
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Sponsorship Tier Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-bold font-myriad">
                  Select Sponsorship Tier
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SponsorshipTierGrid
                  tiers={allSponsorshipTiers}
                  selectedTier={formData.selectedTier}
                  onTierSelect={(tierId) => handleInputChange("selectedTier", tierId)}
                />
              </CardContent>
            </Card>

            {/* Company Logo Upload */}
            <ImageUpload
              onImageChange={handleImageChange}
              currentImage={profileImagePreview}
              label="Company Logo"
              description="Upload your company logo for conference materials"
            />

            {/* OTP Verification */}
            <OTPVerification
              email={formData.email}
              phone={formData.phone}
              onVerificationComplete={handleOtpVerification}
            />

            {/* Terms and Conditions */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="terms"
                      checked={formData.termsAccepted}
                      onCheckedChange={(checked) => handleInputChange("termsAccepted", checked)}
                    />
                    <Label htmlFor="terms" className="text-sm leading-relaxed">
                      I agree to the <a href="#" className="text-blue-600 hover:underline">Sponsorship Terms and Conditions</a> and
                      <a href="#" className="text-blue-600 hover:underline ml-1">Privacy Policy</a> *
                    </Label>
                  </div>
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="marketing"
                      checked={formData.marketingConsent}
                      onCheckedChange={(checked) => handleInputChange("marketingConsent", checked)}
                    />
                    <Label htmlFor="marketing" className="text-sm leading-relaxed">
                      I consent to receiving marketing communications about future events and partnership opportunities
                    </Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {/* Step 2: Ticket Allocation */}
        {currentStep === 2 && formData.selectedTier && (
          <TicketAllocation
            sponsorshipTier={getSelectedTierDetails()!}
            onAllocationChange={handleTicketAllocationChange}
          />
        )}

        {/* Step 3: Review Details */}
        {currentStep === 3 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
                Review Your Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="font-semibold">Company Name</Label>
                    <p>{formData.companyName}</p>
                  </div>
                  <div>
                    <Label className="font-semibold">Contact Person</Label>
                    <p>{formData.contactPerson}</p>
                  </div>
                  <div>
                    <Label className="font-semibold">Email</Label>
                    <p>{formData.email}</p>
                  </div>
                  <div>
                    <Label className="font-semibold">Phone</Label>
                    <p>{formData.phone}</p>
                  </div>
                  <div>
                    <Label className="font-semibold">Country</Label>
                    <p>{formData.country}</p>
                  </div>
                  <div>
                    <Label className="font-semibold">Sponsorship Tier</Label>
                    <p>{getSelectedTierDetails()?.name || 'N/A'}</p>
                  </div>
                </div>

                {formData.companyDescription && (
                  <div>
                    <Label className="font-semibold">Company Description</Label>
                    <p>{formData.companyDescription}</p>
                  </div>
                )}

                {profileImagePreview && (
                  <div>
                    <Label className="font-semibold">Company Logo</Label>
                    <img src={profileImagePreview} alt="Company Logo" className="w-20 h-20 rounded-lg object-cover" />
                  </div>
                )}

                {ticketAllocation && (
                  <div>
                    <Label className="font-semibold">Ticket Allocation</Label>
                    <p>{ticketAllocation.totalAllocated} tickets allocated ({ticketAllocation.vipTickets.length} VIP, {ticketAllocation.delegateTickets.length} Delegate)</p>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Label className="font-semibold">OTP Verification</Label>
                  <span className={`px-2 py-1 rounded text-sm ${isOtpVerified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {isOtpVerified ? 'Verified' : 'Not Verified'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 4: Payment */}
        {currentStep === 4 && (
          <PaymentMethod
            amount={getSelectedTierDetails()?.price || 0}
            currency="KES"
            onPaymentComplete={handlePaymentComplete}
            registrationType="sponsor"
          />
        )}
        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={currentStep === 1 ? () => router.push('/registration') : prevStep}
            className="flex items-center space-x-2"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>{currentStep === 1 ? 'Back to Registration Types' : 'Previous'}</span>
          </Button>

          <Button
            type="button"
            onClick={nextStep}
            disabled={!canProceedToNextStep()}
            className="flex items-center space-x-2"
          >
            <span>
              {currentStep === sponsorSteps.length ? 'Complete Registration' : 'Next Step'}
            </span>
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
