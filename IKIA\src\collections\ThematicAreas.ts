import type { CollectionConfig } from 'payload'
import { authenticated } from '../access/authenticated'
import { anyone } from '../access/anyone'
import { slugField } from '@/fields/slug'

export const ThematicAreas: CollectionConfig = {
  slug: 'thematic-areas',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'description', 'isActive'],
    group: 'Conference',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Thematic Area Name',
      admin: {
        description: 'Name of the thematic area (e.g., "Digital Innovation", "Sustainability")',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Description',
      admin: {
        description: 'Brief description of this thematic area',
        rows: 3,
      },
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media',
      label: 'Thematic Area Image',
      admin: {
        description: 'Optional image to represent this thematic area',
      },
    },
    {
      name: 'color',
      type: 'text',
      label: 'Theme Color',
      admin: {
        description: 'Hex color code for this theme (e.g., #1E40AF)',
      },
      validate: (val: string | null | undefined) => {
        if (val && !/^#[0-9A-F]{6}$/i.test(val)) {
          return 'Please enter a valid hex color code (e.g., #1E40AF)'
        }
        return true
      },
    },
    {
      name: 'isActive',
      type: 'checkbox',
      label: 'Active',
      defaultValue: true,
      admin: {
        description: 'Whether this thematic area is active and should appear in filters',
      },
    },
    ...slugField(),
  ],
  timestamps: true,
}

export default ThematicAreas
