# Proxy approach - Forward all requests to Node.js app on port 3000
RewriteEngine On

# Enable proxy if available
<IfModule mod_proxy.c>
    <IfModule mod_proxy_http.c>
        ProxyPreserveHost On
        ProxyPass / http://localhost:3000/
        ProxyPassReverse / http://localhost:3000/
    </IfModule>
</IfModule>

# Fallback: Use rewrite rules if proxy modules aren't available
<IfModule !mod_proxy.c>
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ http://localhost:3000/$1 [P,L]
</IfModule>

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
