import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

export const enquiryApi = createApi({
  reducerPath: 'enquiryApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  endpoints: (builder) => ({
    createEnquiry: builder.mutation<any, any>({
      query: (enquiryData) => ({
        url: 'enquiry',
        method: 'POST',
        body: enquiryData,
      }),
    }),
  }),
})

export const { useCreateEnquiryMutation } = enquiryApi