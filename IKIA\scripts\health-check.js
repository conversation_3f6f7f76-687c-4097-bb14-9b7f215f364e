#!/usr/bin/env node

/**
 * Health Check Script for IKIA Conference Application
 * This script performs comprehensive health checks on the application
 */

const http = require('http');
const https = require('https');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  app: {
    name: 'ikia-conference',
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost',
    timeout: 10000, // 10 seconds
  },
  endpoints: [
    '/',
    '/api/health',
    '/admin',
    '/api/globals/site-settings',
  ],
  database: {
    enabled: true,
    timeout: 5000,
  },
  pm2: {
    enabled: true,
  },
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Logging functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  debug: (msg) => console.log(`${colors.cyan}[DEBUG]${colors.reset} ${msg}`),
};

// Health check results
const results = {
  overall: true,
  checks: {},
  timestamp: new Date().toISOString(),
  duration: 0,
};

// HTTP request helper
function makeRequest(url, timeout = CONFIG.app.timeout) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    const startTime = Date.now();
    
    const req = protocol.get(url, (res) => {
      const duration = Date.now() - startTime;
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data,
          duration,
        });
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(timeout, () => {
      req.destroy();
      reject(new Error(`Request timeout after ${timeout}ms`));
    });
  });
}

// Execute shell command
function execCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject({ error, stderr });
      } else {
        resolve(stdout.trim());
      }
    });
  });
}

// Check if application is responding
async function checkApplicationHealth() {
  log.info('Checking application health...');
  
  const baseUrl = `http://${CONFIG.app.host}:${CONFIG.app.port}`;
  const endpointResults = {};
  
  for (const endpoint of CONFIG.endpoints) {
    const url = `${baseUrl}${endpoint}`;
    
    try {
      const response = await makeRequest(url);
      const isHealthy = response.statusCode >= 200 && response.statusCode < 400;
      
      endpointResults[endpoint] = {
        status: isHealthy ? 'healthy' : 'unhealthy',
        statusCode: response.statusCode,
        duration: response.duration,
        url,
      };
      
      if (isHealthy) {
        log.success(`${endpoint} - ${response.statusCode} (${response.duration}ms)`);
      } else {
        log.error(`${endpoint} - ${response.statusCode} (${response.duration}ms)`);
        results.overall = false;
      }
    } catch (error) {
      endpointResults[endpoint] = {
        status: 'error',
        error: error.message,
        url,
      };
      log.error(`${endpoint} - ${error.message}`);
      results.overall = false;
    }
  }
  
  results.checks.endpoints = endpointResults;
}

// Check PM2 process status
async function checkPM2Status() {
  if (!CONFIG.pm2.enabled) {
    log.info('PM2 check disabled');
    return;
  }
  
  log.info('Checking PM2 status...');
  
  try {
    const output = await execCommand(`pm2 describe ${CONFIG.app.name} --silent`);
    const processInfo = JSON.parse(output);
    
    if (processInfo && processInfo.length > 0) {
      const process = processInfo[0];
      const isOnline = process.pm2_env.status === 'online';
      
      results.checks.pm2 = {
        status: isOnline ? 'online' : 'offline',
        pid: process.pid,
        uptime: process.pm2_env.pm_uptime,
        restarts: process.pm2_env.restart_time,
        memory: process.monit.memory,
        cpu: process.monit.cpu,
      };
      
      if (isOnline) {
        log.success(`PM2 process is online (PID: ${process.pid})`);
      } else {
        log.error(`PM2 process is ${process.pm2_env.status}`);
        results.overall = false;
      }
    } else {
      results.checks.pm2 = { status: 'not_found' };
      log.error('PM2 process not found');
      results.overall = false;
    }
  } catch (error) {
    results.checks.pm2 = { status: 'error', error: error.message };
    log.error(`PM2 check failed: ${error.message}`);
    results.overall = false;
  }
}

// Check system resources
async function checkSystemResources() {
  log.info('Checking system resources...');
  
  try {
    // Check memory usage
    const memInfo = await execCommand('free -m');
    const memLines = memInfo.split('\n');
    const memData = memLines[1].split(/\s+/);
    const totalMem = parseInt(memData[1]);
    const usedMem = parseInt(memData[2]);
    const memUsagePercent = Math.round((usedMem / totalMem) * 100);
    
    // Check disk usage
    const diskInfo = await execCommand('df -h /');
    const diskLines = diskInfo.split('\n');
    const diskData = diskLines[1].split(/\s+/);
    const diskUsagePercent = parseInt(diskData[4].replace('%', ''));
    
    results.checks.system = {
      memory: {
        total: totalMem,
        used: usedMem,
        percentage: memUsagePercent,
      },
      disk: {
        usage: diskData[4],
        percentage: diskUsagePercent,
      },
    };
    
    log.success(`Memory usage: ${memUsagePercent}%`);
    log.success(`Disk usage: ${diskUsagePercent}%`);
    
    // Warning thresholds
    if (memUsagePercent > 90) {
      log.warning('High memory usage detected');
    }
    if (diskUsagePercent > 90) {
      log.warning('High disk usage detected');
    }
  } catch (error) {
    results.checks.system = { status: 'error', error: error.message };
    log.error(`System check failed: ${error.message}`);
  }
}

// Main health check function
async function runHealthCheck() {
  const startTime = Date.now();
  
  log.info('Starting comprehensive health check...');
  log.info(`Application: ${CONFIG.app.name}`);
  log.info(`Target: ${CONFIG.app.host}:${CONFIG.app.port}`);
  log.info('─'.repeat(50));
  
  try {
    await Promise.all([
      checkApplicationHealth(),
      checkPM2Status(),
      checkSystemResources(),
    ]);
  } catch (error) {
    log.error(`Health check failed: ${error.message}`);
    results.overall = false;
  }
  
  results.duration = Date.now() - startTime;
  
  // Print summary
  log.info('─'.repeat(50));
  if (results.overall) {
    log.success(`Health check completed successfully in ${results.duration}ms`);
  } else {
    log.error(`Health check failed in ${results.duration}ms`);
  }
  
  // Save results to file
  const resultsPath = path.join(__dirname, '..', 'logs', 'health-check.json');
  try {
    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
    log.info(`Results saved to: ${resultsPath}`);
  } catch (error) {
    log.warning(`Could not save results: ${error.message}`);
  }
  
  // Exit with appropriate code
  process.exit(results.overall ? 0 : 1);
}

// Handle command line arguments
if (require.main === module) {
  runHealthCheck().catch((error) => {
    log.error(`Unexpected error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runHealthCheck, results };
