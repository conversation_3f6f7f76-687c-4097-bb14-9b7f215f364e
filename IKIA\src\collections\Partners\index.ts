
import { CollectionConfig } from 'payload'

const Partners: CollectionConfig = {
  slug: 'partners',
  access: {
    read: () => true,
  },
  admin: {
    useAsTitle: 'name',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      access: {
        read: () => true,
      },
    },
    {
      name: 'company',
      type: 'text',
      access: {
        read: () => true,
      },
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
      access: {
        read: () => true,
      },
    },
    {
      name: 'sponsorshipTier',
      type: 'select',
      options: ['platinum', 'gold', 'silver', 'bronze'],
      access: {
        read: () => true,
      },
    },
    {
      name: 'eventsSponsored',
      type: 'relationship',
      relationTo: 'events',
      hasMany: true,
      access: {
        read: () => true,
      },
    },
    {
      name: 'about',
      type: 'textarea',
      access: {
        read: () => true,
      },
    },
    {
      name: 'notableContributions',
      type: 'array',
      fields: [{ name: 'contribution', type: 'text' }],
      access: {
        read: () => true,
      },
    },
    {
      name: 'awardsRecognitions',
      type: 'array',
      fields: [{ name: 'award', type: 'text' }],
      access: {
        read: () => true,
      },
    },
    {
      name: 'recentProjects',
      type: 'array',
      fields: [{ name: 'project', type: 'text' }],
      access: {
        read: () => true,
      },
    },
    {
      name: 'impact',
      type: 'array',
      fields: [{ name: 'impactItem', type: 'text' }],
      access: {
        read: () => true,
      },
    },
    {
      name: 'contact',
      type: 'group',
      access: {
        read: () => true,
      },
      fields: [
        {
          name: 'emails',
          type: 'array',
          fields: [{ name: 'email', type: 'email' }],
          access: {
            read: () => true,
          },
        },
        {
          name: 'phones',
          type: 'array',
          fields: [{ name: 'phone', type: 'text' }],
          access: {
            read: () => true,
          },
        },
        {
          name: 'location',
          type: 'group',
          access: {
            read: () => true,
          },
          fields: [
            {
              name: 'city',
              type: 'text',
              access: { read: () => true },
            },
            {
              name: 'country',
              type: 'text',
              access: { read: () => true },
            },
            {
              name: 'address',
              type: 'textarea',
              access: { read: () => true },
            },
          ],
        },
      ],
    },
    // Hidden/sensitive fields
    {
      name: 'internalNotes',
      type: 'textarea',
      admin: {
        description: 'Internal notes - not visible to public',
      },
      access: {
        read: ({ req: { user } }) => Boolean(user), // Only authenticated users
      },
    },
    {
      name: 'category',
      type: 'select',
      hasMany: true,
      options: ['strategic', 'community', 'implementation', 'media'],
      access: {
        read: () => true,
      },
    },
  ],
}

export default Partners

