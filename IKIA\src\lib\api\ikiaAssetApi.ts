import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { serializeParams } from '@/lib/serialize'

// Types for IKIA Assets
export interface IKIAAsset {
  id: number
  title: string
  description?: string | null
  categories?: Array<{
    name?: string | null
    id?: string | null
  }> | null
  tags?: Array<{
    name?: string | null
    id?: string | null
  }> | null
  location: string
  county?:
    | {
        id: number
        name: string
        code: string
      }
    | number
    | null
  thematicArea:
    | {
        id: number
        name: string
        description?: string
        color?: string
        slug?: string
      }
    | number
  documentedBy?: string | null
  yearDocumented?: number | null
  investmentPotential?: 'high' | 'medium' | 'low' | null
  featured?: boolean | null
  readyForInvestment?: boolean | null
  investmentNeeded?: number | null
  expectedReturn?: string | null
  timeline?: string | null
  riskLevel?: 'low' | 'medium' | 'high' | null
  impactScore?: number | null
  highlights?: Array<{
    highlight: string
    id?: string | null
  }> | null
  image?:
    | {
        id: number
        url: string
        alt?: string
        width?: number
        height?: number
        sizes?: {
          thumbnail?: { url: string; width: number; height: number } | null
          square?: { url: string; width: number; height: number } | null
          small?: { url: string; width: number; height: number } | null
          medium?: { url: string; width: number; height: number } | null
          large?: { url: string; width: number; height: number } | null
        }
      }
    | number
    | null
  slug?: string | null
  updatedAt: string
  createdAt: string
}

export interface IKIAAssetsResponse {
  docs: IKIAAsset[]
  totalDocs: number
  limit: number
  totalPages: number
  page: number
  pagingCounter: number
  hasPrevPage: boolean
  hasNextPage: boolean
  prevPage?: number | null
  nextPage?: number | null
}

export interface IKIAAssetsQueryParams {
  limit?: number
  page?: number
  sort?: string
  where?: {
    featured?: {
      equals?: boolean
    }
    readyForInvestment?: {
      equals?: boolean
    }
    thematicArea?: {
      equals?: number | string
    }
    county?: {
      equals?: number | string
    }
    investmentPotential?: {
      equals?: string
    }
    riskLevel?: {
      equals?: string
    }
    title?: {
      contains?: string
    }
    location?: {
      contains?: string
    }
  }
}

export const ikiaAssetApi = createApi({
  reducerPath: 'ikiaAssetApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  tagTypes: ['IKIAAsset'],
  endpoints: (builder) => ({
    // Get all IKIA assets with optional filtering
    getIkiaAssets: builder.query<IKIAAssetsResponse, IKIAAssetsQueryParams | void>({
      query: (params) => {
        const queryParams = params || {}
        const queryString = serializeParams(queryParams)
        return `ikia-asset?${queryString.toString()}`
      },
      providesTags: ['IKIAAsset'],
    }),

    // Get featured IKIA assets
    getFeaturedIkiaAssets: builder.query<IKIAAssetsResponse, { limit?: number }>({
      query: ({ limit = 6 } = {}) => {
        const params = {
          where: { featured: { equals: true } },
          limit,
          sort: '-createdAt',
        }
        const queryString = serializeParams(params)
        return `ikia-asset?${queryString.toString()}`
      },
      providesTags: ['IKIAAsset'],
    }),

    // Get investment-ready IKIA assets
    getInvestmentReadyAssets: builder.query<IKIAAssetsResponse, IKIAAssetsQueryParams | void>({
      query: (params) => {
        const queryParams = params || {}
        const mergedParams = {
          ...queryParams,
          where: {
            ...queryParams.where,
            readyForInvestment: { equals: true },
          },
          sort: queryParams.sort || '-createdAt',
        }
        const queryString = serializeParams(mergedParams)
        return `ikia-asset?${queryString.toString()}`
      },
      providesTags: ['IKIAAsset'],
    }),

    // Get IKIA assets by thematic area
    getAssetsByThematicArea: builder.query<
      IKIAAssetsResponse,
      { thematicAreaId: number | string; limit?: number }
    >({
      query: ({ thematicAreaId, limit = 50 }) => {
        const params = {
          where: { thematicArea: { equals: thematicAreaId } },
          limit,
          sort: '-createdAt',
        }
        const queryString = serializeParams(params)
        return `ikia-asset?${queryString.toString()}`
      },
      providesTags: ['IKIAAsset'],
    }),

    // Get IKIA assets by county
    getAssetsByCounty: builder.query<
      IKIAAssetsResponse,
      { countyId: number | string; limit?: number }
    >({
      query: ({ countyId, limit = 50 }) => {
        const params = {
          where: { county: { equals: countyId } },
          limit,
          sort: '-createdAt',
        }
        const queryString = serializeParams(params)
        return `ikia-asset?${queryString.toString()}`
      },
      providesTags: ['IKIAAsset'],
    }),

    // Get single IKIA asset by ID
    getIkiaAsset: builder.query<IKIAAsset, string | number>({
      query: (id) => `ikia-asset/${id}`,
      providesTags: ['IKIAAsset'],
    }),

    // Get single IKIA asset by slug
    getIkiaAssetBySlug: builder.query<IKIAAsset, string>({
      query: (slug) => {
        const params = {
          where: { slug: { equals: slug } },
          limit: 1,
        }
        const queryString = serializeParams(params)
        return `ikia-asset?${queryString.toString()}`
      },
      transformResponse: (response: IKIAAssetsResponse) => response.docs[0],
      providesTags: ['IKIAAsset'],
    }),
  }),
})

export const {
  useGetIkiaAssetsQuery,
  useGetFeaturedIkiaAssetsQuery,
  useGetInvestmentReadyAssetsQuery,
  useGetAssetsByThematicAreaQuery,
  useGetAssetsByCountyQuery,
  useGetIkiaAssetQuery,
  useGetIkiaAssetBySlugQuery,
} = ikiaAssetApi
