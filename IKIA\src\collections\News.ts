// src/collections/NewsItems.ts

import { CollectionConfig } from 'payload'

import { anyone } from '../access/anyone'
import { authenticated } from '../access/authenticated'
import { slugField } from '@/fields/slug'

export const NewsItem: CollectionConfig = {
  slug: 'news-items',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
  },
  labels: {
    singular: 'News Item',
    plural: 'News Items',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'summary',
      type: 'textarea',
    },
    {
      name: 'content',
      type: 'richText',
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'eventDate',
      label: 'Event/Publish Date',
      type: 'date',
      required: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        { label: 'News Feed', value: 'news-feed' },
        { label: 'Press Releases', value: 'press-releases' },
        { label: 'Blogs', value: 'blogs' },
        { label: 'Social Media', value: 'social-media' },
        { label: 'Event Updates', value: 'event-updates' },
      ],
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Featured', value: 'featured' },
        { label: 'Highlight', value: 'highlight' }, // e.g. upcoming
        { label: 'Regular', value: 'regular' },
      ],
      defaultValue: 'regular',
    },
    {
      name: 'author',
      type: 'text',
    },
    ...slugField(),
  ],
}
