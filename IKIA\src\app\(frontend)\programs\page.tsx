'use client'

import { HeroSection, AgendaFilters, AgendaContent } from '@/modules/website/agenda/components'
import { useState } from 'react'

export default function ProgramsPage() {
  const [activeDay, setActiveDay] = useState('Day 1')
  const [filter, setFilter] = useState('all')
  const [thematic, setThematic] = useState('all')
  const [topic, setTopic] = useState('all')

  return (
    <div className="min-h-screen bg-white">
      <HeroSection />
      <AgendaFilters
        activeDay={activeDay}
        setActiveDay={setActiveDay}
        filter={filter}
        setFilter={setFilter}
        thematic={thematic}
        setThematic={setThematic}
        topic={topic}
        setTopic={setTopic}
      />
      <AgendaContent activeDay={activeDay} filter={filter} thematic={thematic} topic={topic} />
    </div>
  )
}
