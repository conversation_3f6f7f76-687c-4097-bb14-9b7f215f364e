# Events API Pagination Usage

## API Endpoint
The events endpoint supports comprehensive pagination with the following query parameters:

### Query Parameters
- `limit` - Number of items per page (default: 50)
- `page` - Page number starting from 1 (default: 1)
- `sort` - Sort field (default: 'date')
- `day` - Filter by event day (1-5)
- `type` - Filter by event type ('keynote', 'panel', 'workshop', 'exhibition', 'breakout')
- `upcoming` - Filter for upcoming events only ('true')

## API Response Format
```json
{
  "events": [...],
  "totalEvents": 150,
  "page": 1,
  "limit": 10,
  "totalPages": 15,
  "hasNextPage": true,
  "hasPrevPage": false
}
```

## Usage Examples

### Basic Pagination
```bash
# Get first page with 10 events
GET /api/events?limit=10&page=1

# Get second page
GET /api/events?limit=10&page=2

# Get third page with 20 events per page
GET /api/events?limit=20&page=3
```

### With Filters
```bash
# Get upcoming keynote events, page 1
GET /api/events?upcoming=true&type=keynote&limit=10&page=1

# Get day 1 events, sorted by title
GET /api/events?day=1&sort=title&limit=15&page=1
```

## Frontend Usage (React/Next.js)

### Basic Hook for Events with Pagination
```typescript
import { useState, useEffect } from 'react'

interface EventsResponse {
  events: Event[]
  totalEvents: number
  page: number
  limit: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

export function useEvents(initialLimit = 10) {
  const [events, setEvents] = useState<EventsResponse | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [limit, setLimit] = useState(initialLimit)
  const [filters, setFilters] = useState<Record<string, string>>({})

  const fetchEvents = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...filters
      })
      
      const response = await fetch(`/api/events?${params}`)
      if (!response.ok) throw new Error('Failed to fetch events')
      
      const data = await response.json()
      setEvents(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchEvents()
  }, [page, limit, filters])

  return {
    events,
    loading,
    error,
    page,
    setPage,
    limit,
    setLimit,
    filters,
    setFilters,
    refetch: fetchEvents
  }
}
```

### Events List Component with Pagination
```typescript
import { useEvents } from './useEvents'

export function EventsList() {
  const { events, loading, error, page, setPage, setFilters } = useEvents(10)

  if (loading) return <div>Loading events...</div>
  if (error) return <div>Error: {error}</div>
  if (!events) return null

  return (
    <div>
      {/* Filters */}
      <div className="filters">
        <select onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}>
          <option value="">All Types</option>
          <option value="keynote">Keynote</option>
          <option value="panel">Panel</option>
          <option value="workshop">Workshop</option>
        </select>
        
        <label>
          <input 
            type="checkbox" 
            onChange={(e) => setFilters(prev => 
              e.target.checked 
                ? { ...prev, upcoming: 'true' }
                : { ...prev, upcoming: undefined }
            )}
          />
          Upcoming only
        </label>
      </div>

      {/* Events List */}
      <div className="events-list">
        {events.events.map(event => (
          <div key={event.id} className="event-card">
            <h3>{event.title}</h3>
            <p>{event.description}</p>
            <p>Date: {event.formattedDate}</p>
            {event.formattedTime && <p>Time: {event.formattedTime}</p>}
          </div>
        ))}
      </div>

      {/* Pagination Controls */}
      <div className="pagination">
        <button 
          disabled={!events.hasPrevPage}
          onClick={() => setPage(page - 1)}
        >
          Previous
        </button>
        
        <span>
          Page {events.page} of {events.totalPages} 
          ({events.totalEvents} total events)
        </span>
        
        <button 
          disabled={!events.hasNextPage}
          onClick={() => setPage(page + 1)}
        >
          Next
        </button>
      </div>
    </div>
  )
}
```

### Advanced Pagination Component
```typescript
interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  hasNextPage: boolean
  hasPrevPage: boolean
}

export function Pagination({ 
  currentPage, 
  totalPages, 
  onPageChange, 
  hasNextPage, 
  hasPrevPage 
}: PaginationProps) {
  const getPageNumbers = () => {
    const pages = []
    const maxVisible = 5
    
    let start = Math.max(1, currentPage - Math.floor(maxVisible / 2))
    let end = Math.min(totalPages, start + maxVisible - 1)
    
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1)
    }
    
    for (let i = start; i <= end; i++) {
      pages.push(i)
    }
    
    return pages
  }

  return (
    <div className="pagination">
      <button 
        disabled={!hasPrevPage}
        onClick={() => onPageChange(currentPage - 1)}
      >
        ← Previous
      </button>
      
      {currentPage > 3 && (
        <>
          <button onClick={() => onPageChange(1)}>1</button>
          {currentPage > 4 && <span>...</span>}
        </>
      )}
      
      {getPageNumbers().map(pageNum => (
        <button
          key={pageNum}
          className={pageNum === currentPage ? 'active' : ''}
          onClick={() => onPageChange(pageNum)}
        >
          {pageNum}
        </button>
      ))}
      
      {currentPage < totalPages - 2 && (
        <>
          {currentPage < totalPages - 3 && <span>...</span>}
          <button onClick={() => onPageChange(totalPages)}>{totalPages}</button>
        </>
      )}
      
      <button 
        disabled={!hasNextPage}
        onClick={() => onPageChange(currentPage + 1)}
      >
        Next →
      </button>
    </div>
  )
}
```

## Key Improvements Made

1. **Fixed upcoming filter**: Now applied at the database level for accurate pagination
2. **Enhanced response**: Added comprehensive pagination metadata
3. **Better performance**: Filters applied before pagination, not after
4. **Complete pagination info**: Includes total pages, next/prev page availability

## Best Practices

1. **Reasonable limits**: Keep page sizes reasonable (10-50 items)
2. **Loading states**: Always show loading indicators
3. **Error handling**: Handle API errors gracefully
4. **URL sync**: Consider syncing pagination state with URL parameters
5. **Accessibility**: Ensure pagination controls are keyboard accessible
