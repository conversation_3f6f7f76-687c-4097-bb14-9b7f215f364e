import { Map<PERSON>in, <PERSON>edin, Twitter } from 'lucide-react'
import Image from 'next/image'

const speakers = [
  {
    name: 'Dr. <PERSON>',
    title: 'Director of Indigenous Knowledge Research',
    organization: 'University of Nairobi',
    image: '/placeholder.svg?height=300&width=300&text=Dr.+<PERSON><PERSON><PERSON>+<PERSON><PERSON>',
    bio: 'Leading researcher in traditional ecological knowledge with over 15 years of experience in community-based conservation.',
    location: 'Nairobi, Kenya',
    social: {
      linkedin: '#',
      twitter: '#',
    },
  },
  {
    name: 'Prof. <PERSON>',
    title: 'Traditional Medicine Specialist',
    organization: 'Kenya Medical Research Institute',
    image: '/placeholder.svg?height=300&width=300&text=Prof<PERSON>+<PERSON>',
    bio: 'Expert in ethnobotany and traditional healing practices, focusing on sustainable harvesting of medicinal plants.',
    location: 'Kisumu, Kenya',
    social: {
      linkedin: '#',
      twitter: '#',
    },
  },
  {
    name: '<PERSON>',
    title: 'Community Elder & Knowledge Keeper',
    organization: 'Kikuyu Cultural Association',
    image: '/placeholder.svg?height=300&width=300&text=<PERSON>+<PERSON>',
    bio: 'Custodian of traditional farming practices and cultural heritage, advocating for Indigenous rights and knowledge preservation.',
    location: "Murang'a, Kenya",
    social: {
      linkedin: '#',
      twitter: '#',
    },
  },
]

export default function SpeakersTab() {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-[#7E2518] mb-4">Featured Speakers</h3>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Learn from leading experts, researchers, and community knowledge keepers who are at the
          forefront of Indigenous Knowledge and Innovation Assets.
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {speakers.map((speaker, index) => (
          <div
            key={index}
            className="bg-white border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow"
          >
            <Image
              src={speaker.image || '/placeholder.svg'}
              alt={speaker.name}
              className="w-full h-64 object-cover"
            />
            <div className="p-6">
              <h4 className="text-xl font-bold text-[#7E2518] mb-2">{speaker.name}</h4>
              <p className="text-[#159147] font-medium mb-1">{speaker.title}</p>
              <p className="text-gray-600 text-sm mb-3">{speaker.organization}</p>

              <div className="flex items-center text-gray-500 text-sm mb-4">
                <MapPin className="w-4 h-4 mr-1" />
                {speaker.location}
              </div>

              <p className="text-gray-700 text-sm leading-relaxed mb-4">{speaker.bio}</p>

              <div className="flex space-x-3">
                <a
                  href={speaker.social.linkedin}
                  className="text-[#81B1DB] hover:text-[#7E2518] transition-colors"
                >
                  <Linkedin className="w-5 h-5" />
                </a>
                <a
                  href={speaker.social.twitter}
                  className="text-[#81B1DB] hover:text-[#7E2518] transition-colors"
                >
                  <Twitter className="w-5 h-5" />
                </a>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
