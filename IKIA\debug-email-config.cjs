// Debug script to check email configuration
// Run with: node debug-email-config.js

// Load environment variables from .env file
require('dotenv').config()

console.log('🔍 Email Configuration Debug')
console.log('=' .repeat(40))

console.log('Environment Variables:')
console.log('SMTP_HOST:', process.env.SMTP_HOST || '❌ Not set')
console.log('SMTP_PORT:', process.env.SMTP_PORT || '❌ Not set')
console.log('SMTP_SECURE:', process.env.SMTP_SECURE || '❌ Not set')
console.log('SMTP_USER:', process.env.SMTP_USER ? '✅ Set' : '❌ Not set')
console.log('SMTP_PASS:', process.env.SMTP_PASS ? '✅ Set (hidden)' : '❌ Not set')
console.log('FROM_EMAIL:', process.env.FROM_EMAIL || '❌ Not set')
console.log('FROM_NAME:', process.env.FROM_NAME || '❌ Not set')

console.log('\n🧪 Testing Nodemailer Transport...')

const nodemailer = require('nodemailer')

if (process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASS) {
  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  })

  console.log('✅ Transport created successfully')
  
  // Test the connection
  transporter.verify((error, success) => {
    if (error) {
      console.log('❌ Transport verification failed:', error.message)
    } else {
      console.log('✅ Transport verification successful!')
    }
    process.exit(0)
  })
} else {
  console.log('❌ Missing required environment variables')
  console.log('Required: SMTP_HOST, SMTP_USER, SMTP_PASS')
  process.exit(1)
}
