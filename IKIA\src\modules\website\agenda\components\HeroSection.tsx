'use client'

import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function HeroSection() {
  return (
    <section className="bg-gray-50 py-20 relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-8 min-h-[60vh]">
          <div className="max-w-2xl flex-1 relative text-center lg:text-left">
            <div className="inline-block border border-gray-300 px-6 py-3 mb-8 text-sm font-medium tracking-wider uppercase">
              WHAT WE WILL DO
            </div>
            <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 mb-10 leading-tight">
              PROGRAM & AGENDA
            </h1>
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center lg:justify-start">
              <Button
                asChild
                className="font-semibold text-xs transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 rounded-none px-8 py-4 bg-primary hover:bg-primary/90 text-primary-foreground"
              >
                <Link href="/registration">REGISTER NOW</Link>
              </Button>
              <Button
                asChild
                className="font-semibold text-xs transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 rounded-none px-8 py-4 bg-secondary hover:bg-secondary/90 text-secondary-foreground border-2 border-secondary"
              >
                <Link href="/agenda/download">VIEW & DOWNLOAD</Link>
              </Button>
            </div>
            {/* Flow connector - visual bridge */}
            <div className="absolute -right-4 lg:-right-8 top-1/2 transform -translate-y-1/2 hidden lg:block">
              <div className="w-12 lg:w-16 h-1 bg-gradient-to-r from-gray-300 via-gray-200 to-transparent opacity-60"></div>
              <div className="w-8 lg:w-12 h-px bg-gradient-to-r from-gray-400 to-transparent mt-2 opacity-40"></div>
              <div className="w-6 lg:w-8 h-px bg-gradient-to-r from-gray-300 to-transparent mt-1 opacity-30"></div>
            </div>
          </div>
          {/* Scrolling Images with seamless flowing effect */}
          <div className="hidden lg:block relative w-96 xl:w-[500px] h-80 xl:h-96 overflow-hidden flex-shrink-0">
            {/* Enhanced flow integration */}
            <div className="absolute -left-16 top-0 bottom-0 w-24 bg-gradient-to-r from-gray-50 via-gray-50/30 to-transparent z-20"></div>
            <div className="absolute inset-0 transform rotate-45 scale-110 origin-left">
              <div className="flex flex-col gap-3 animate-scroll-flow">
                {/* First set of images */}
                <div className="flex gap-3">
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Conference"
                    alt="Conference presentation"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Networking"
                    alt="Networking session"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                </div>
                <div className="flex gap-3">
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Panel"
                    alt="Panel discussion"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Workshop"
                    alt="Innovation workshop"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                </div>
                <div className="flex gap-3">
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Startup"
                    alt="Startup pitch"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Audience"
                    alt="Conference audience"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                </div>
                <div className="flex gap-3">
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Exhibition"
                    alt="Tech exhibition"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Meeting"
                    alt="Business meeting"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                </div>
                {/* Exact duplicate set for seamless loop */}
                <div className="flex gap-3">
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Conference"
                    alt="Conference presentation"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Networking"
                    alt="Networking session"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                </div>
                <div className="flex gap-3">
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Panel"
                    alt="Panel discussion"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Workshop"
                    alt="Innovation workshop"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                </div>
                <div className="flex gap-3">
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Startup"
                    alt="Startup pitch"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Audience"
                    alt="Conference audience"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                </div>
                <div className="flex gap-3">
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Exhibition"
                    alt="Tech exhibition"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                  <img
                    src="/placeholder.svg?height=120&width=180&text=Meeting"
                    alt="Business meeting"
                    className="w-40 h-28 object-cover rounded-lg shadow-lg"
                  />
                </div>
              </div>
            </div>
            {/* Enhanced gradient overlays for flowing effect */}
            <div className="absolute top-0 left-0 right-0 h-24 bg-gradient-to-b from-gray-50 via-gray-50/80 to-transparent z-10"></div>
            <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-gray-50 via-gray-50/80 to-transparent z-10"></div>
            <div className="absolute top-0 bottom-0 right-0 w-20 bg-gradient-to-l from-gray-50 via-gray-50/70 to-transparent z-10"></div>
          </div>
        </div>
      </div>
    </section>
  )
}
