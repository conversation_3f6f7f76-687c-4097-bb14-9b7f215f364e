import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

export const partnershipMatchingApi = createApi({
  reducerPath: 'partnershipMatchingApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  endpoints: (builder) => ({
    createPartnershipMatching: builder.mutation<any, any>({
      query: (partnershipData) => ({
        url: 'partnership-matching',
        method: 'POST',
        body: partnershipData,
      }),
    }),
  }),
})

export const { useCreatePartnershipMatchingMutation } = partnershipMatchingApi

