'use client'

import type React from 'react'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { ArrowLeft, Store, ChevronRight, ChevronLeft } from 'lucide-react'
import {
  StepIndicator,
  exhibitorSteps,
} from '@/modules/website/registration/components/StepIndicator'
import { OTPVerification } from '@/modules/website/registration/components/OTPVerification'
import { ImageUpload } from '@/modules/website/registration/components/ImageUpload'
import { saveFormDataToStorage } from '@/modules/website/registration/lib/registration-utils'
import { GroupRegistration } from '@/modules/website/registration/components/GroupRegistration'
import {
  initializeGroupRegistration,
  addGroupMember,
  updateGroupMember,
  removeGroupMember,
  navigateToMember,
  type GroupRegistrationState,
} from '@/modules/website/registration/lib/registration-utils'
import {
  boothRequirements,
  additionalServices,
} from '@/modules/website/registration/lib/registration-data'
import { useEffect } from 'react'

const businessTypes = [
  'Traditional Foods & Nutrition',
  'Local Remedies & Traditional Medicine',
  'Musicology & Cultural Arts',
  'Cultural Tourism & Heritage',
  'Indigenous Technologies & Innovations',
  'Sui Generis Intellectual Property Systems',
]

const countries = [
  'Kenya',
  'Uganda',
  'Tanzania',
  'Rwanda',
  'Burundi',
  'South Sudan',
  'Ethiopia',
  'Somalia',
  'United States',
  'United Kingdom',
  'Other',
]

export default function ExhibitorRegistrationForm() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [groupState, setGroupState] = useState<GroupRegistrationState>(
    initializeGroupRegistration(),
  )
  const [isOtpVerified, setIsOtpVerified] = useState(false)
  const [profileImage, setProfileImage] = useState<File | null>(null)
  const [profileImagePreview, setProfileImagePreview] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [delegatePackages, setDelegatePackages] = useState<any[]>([])
  const [packagesLoading, setPackagesLoading] = useState(true)

  const [formData, setFormData] = useState({
    // Personal Information (Primary)
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    position: '',
    country: '',
    city: '',

    // Company/Business Information (Optional)
    hasCompany: false,
    companyName: '',
    website: '',
    address: '',
    businessType: '',
    businessDescription: '',
    productsServices: '',
    targetMarket: '',
    yearsInBusiness: '',

    // Exhibition Details
    selectedPackage: '',
    boothRequirement: '',
    additionalServices: [] as string[],
    specialRequirements: '',

    // Additional Representatives (Optional)
    hasAdditionalReps: false,
    representative1Name: '',
    representative1Email: '',
    representative1Phone: '',
    representative2Name: '',
    representative2Email: '',
    representative2Phone: '',

    // Marketing
    companyLogo: null as File | null,
    marketingMaterials: '',
    mediaConsent: false,

    // Agreements
    termsAccepted: false,
    exhibitorGuidelines: false,
  })

  // Load delegate packages on component mount
  useEffect(() => {
    loadDelegatePackages()
  }, [])

  // Multi-step handlers
  const handleImageChange = (file: File | null, preview: string | null) => {
    setProfileImage(file)
    setProfileImagePreview(preview)
  }

  const handleOtpVerification = (verified: boolean) => {
    setIsOtpVerified(verified)
  }

  const nextStep = () => {
    if (currentStep < exhibitorSteps.length) {
      setCompletedSteps((prev) => [...prev, currentStep])
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const canProceedToNextStep = () => {
    switch (currentStep) {
      case 1: // Company details step
        return (
          formData.firstName &&
          formData.lastName &&
          formData.email &&
          formData.phone &&
          formData.country &&
          formData.selectedPackage &&
          isOtpVerified &&
          formData.termsAccepted
        )
      case 2: // Review step
        return true
      case 3: // Payment step
        return true
      default:
        return false
    }
  }

  const loadDelegatePackages = async () => {
    try {
      setPackagesLoading(true)
      const response = await fetch('/api/delegatepackages')
      const result = await response.json()

      // Filter for exhibition packages and active ones
      const packages = (result.docs || result)
        .filter(
          (pkg: any) =>
            pkg.isActive && (pkg.packageType === 'exhibition' || pkg.packageType === 'delegate'),
        )
        .map((pkg: any) => ({
          ...pkg,
          // Ensure price is a number for proper formatting
          price: typeof pkg.price === 'string' ? parseFloat(pkg.price) : pkg.price,
          // Add duration if not present
          duration: pkg.duration || 'One-time',
          // Ensure features is an array
          features: pkg.features || [],
        }))

      setDelegatePackages(packages)
    } catch (error) {
      console.error('Failed to load delegate packages:', error)
      setError('Failed to load delegate packages. Please refresh the page.')
    } finally {
      setPackagesLoading(false)
    }
  }

  const getSelectedPackageDetails = () => {
    return delegatePackages.find((pkg) => pkg.id === formData.selectedPackage)
  }

  const handleRegistrationSubmit = async () => {
    try {
      setLoading(true)
      setError('')

      // Prepare registration data for backend
      const registrationData = {
        // Personal Information
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        position: formData.position,
        country: formData.country,
        city: formData.city,

        // Company Information
        hasCompany: formData.hasCompany,
        companyName: formData.companyName,
        website: formData.website,
        address: formData.address,
        businessType: formData.businessType,
        businessDescription: formData.businessDescription,
        productsServices: formData.productsServices,
        targetMarket: formData.targetMarket,
        yearsInBusiness: formData.yearsInBusiness,

        // Exhibition Details
        selectedPackage: formData.selectedPackage,
        boothRequirement: formData.boothRequirement,
        additionalServices: formData.additionalServices || [],
        specialRequirements: formData.specialRequirements,

        // Additional Representatives
        hasAdditionalReps: groupState.isGroupMode,
        additionalRepresentatives: groupState.isGroupMode
          ? groupState.members.map((member) => ({
              name: `${member.firstName} ${member.lastName}`,
              email: member.email,
              phone: member.phone,
              position: member.position,
            }))
          : [],

        // Agreements
        termsAccepted: formData.termsAccepted,
        exhibitorGuidelines: formData.exhibitorGuidelines,
        mediaConsent: formData.mediaConsent,
      }

      console.log('Submitting exhibitor registration:', registrationData)

      const response = await fetch('/api/exhibitors/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registrationData),
      })

      const result = await response.json()

      if (result.success) {
        console.log('Registration successful:', result)
        setSuccess('Registration successful! Redirecting to payment gateway...')

        // Save registration data locally for reference
        saveFormDataToStorage('ikia-exhibitor-registration', {
          ...registrationData,
          registrationId: result.data.exhibitor.id,
          invoiceId: result.data.invoice.id,
        })

        // Redirect to payment after a short delay
        setTimeout(() => {
          window.location.href = result.checkout_url
        }, 2000)
      } else {
        throw new Error(result.error || 'Registration failed')
      }
    } catch (error: any) {
      console.error('Registration error:', error)
      setError(error.message || 'Registration failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handlePaymentComplete = (paymentData: any) => {
    // This is now handled by the backend registration flow
    // The payment will be processed after registration
    console.log('Payment completed:', paymentData)
    router.push('/registration/success')
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleServiceChange = (service: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      additionalServices: checked
        ? [...prev.additionalServices, service]
        : prev.additionalServices.filter((s) => s !== service),
    }))
  }

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setFormData((prev) => ({
        ...prev,
        companyLogo: file,
      }))
    }
  }

  // Group registration handlers
  const handleToggleGroupMode = () => {
    setGroupState((prev: GroupRegistrationState) => ({
      ...prev,
      isGroupMode: !prev.isGroupMode,
    }))
  }

  const handleAddMember = () => {
    const newState = addGroupMember(groupState, formData)
    setGroupState(newState)
  }

  const handleNavigateToMember = (index: number) => {
    const newState = navigateToMember(groupState, index)
    setGroupState(newState)

    // Load the member's data into the form
    if (index < newState.members.length) {
      const memberData = newState.members[index] as any
      setFormData(memberData)
    } else {
      // New member - reset form
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        position: '',
        country: '',
        city: '',
        hasCompany: false,
        companyName: '',
        website: '',
        address: '',
        businessType: '',
        businessDescription: '',
        productsServices: '',
        targetMarket: '',
        yearsInBusiness: '',
        boothRequirement: '',
        additionalServices: [] as string[],
        specialRequirements: '',
        hasAdditionalReps: false,
        representative1Name: '',
        representative1Email: '',
        representative1Phone: '',
        representative2Name: '',
        representative2Email: '',
        representative2Phone: '',
        companyLogo: null as File | null,
        marketingMaterials: '',
        selectedPackage: '',
        mediaConsent: false,
        termsAccepted: false,
        exhibitorGuidelines: false,
      })
    }
  }

  const handleRemoveMember = (index: number) => {
    setGroupState((prev: GroupRegistrationState) => removeGroupMember(prev, index))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Prepare registration data
    let registrationData

    if (groupState.isGroupMode) {
      // Update current member data and include all group members
      const updatedGroupState = updateGroupMember(
        groupState,
        groupState.currentMemberIndex,
        formData,
      )
      registrationData = {
        registrationType: 'Exhibitor Registration',
        isGroupRegistration: true,
        groupMembers: [...updatedGroupState.members, formData],
        submissionDate: new Date().toISOString(),
      }
    } else {
      // Single registration
      registrationData = {
        ...formData,
        registrationType: 'Exhibitor Registration',
        isGroupRegistration: false,
        submissionDate: new Date().toISOString(),
      }
    }

    saveFormDataToStorage('ikia-registration', registrationData)

    console.log('Exhibitor form submitted:', registrationData)
    router.push('/registration/success')
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8 font-myriad">
      {/* Enhanced Header with Homepage Design */}
      <div className="mb-12">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-8 font-myriad hover:bg-primary/10 text-primary"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Registration Types
        </Button>

        {/* Hero Section matching homepage style */}
        <div className="text-center mb-8 relative">
          <div className="relative bg-gradient-to-br from-background via-card to-muted/20 p-8 md:p-12 border border-border overflow-hidden main-shadow">
            {/* Heritage Elements */}
            <div className="absolute top-6 left-8 heritage-dot heritage-dot-accent"></div>
            <div
              className="absolute top-12 right-12 heritage-dot heritage-dot-secondary"
              style={{ animationDelay: '1s' }}
            ></div>
            <div
              className="absolute bottom-8 left-12 heritage-dot heritage-dot-primary"
              style={{ animationDelay: '2s' }}
            ></div>

            <div className="relative z-10">
              {/* Icon */}
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-accent to-accent/80 mb-6 shadow-lg">
                <Store className="w-8 h-8 text-accent-foreground" />
              </div>

              <h1 className="text-3xl md:text-5xl font-bold text-foreground mb-4 font-myriad">
                Exhibitor <span className="text-accent">Registration</span>
              </h1>

              <p className="text-base md:text-lg text-muted-foreground mb-4 font-myriad">
                <span className="text-accent font-semibold">Showcase Your Innovation</span> and
                connect with <span className="text-secondary font-semibold">Global Audiences</span>
              </p>

              <p className="text-muted-foreground text-sm italic font-myriad">
                &ldquo;Where <span className="text-accent">innovation</span> meets{' '}
                <span className="text-secondary">opportunity</span>&rdquo;
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Step Indicator */}
      <StepIndicator
        steps={exhibitorSteps}
        currentStep={currentStep}
        completedSteps={completedSteps}
        className="mb-8"
      />

      {/* Step Content */}
      <div className="space-y-8">
        {/* Step 1: Company Details & Package Selection */}
        {currentStep === 1 && (
          <>
            {/* Group Registration */}
            <GroupRegistration
              groupState={groupState}
              onToggleGroupMode={handleToggleGroupMode}
              onAddMember={handleAddMember}
              onNavigateToMember={handleNavigateToMember}
              onRemoveMember={handleRemoveMember}
              currentMemberData={formData}
            />

            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-bold font-myriad">
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="position">Position/Title</Label>
                    <Input
                      id="position"
                      value={formData.position}
                      onChange={(e) => handleInputChange('position', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="country">Country *</Label>
                    <Select
                      value={formData.country}
                      onValueChange={(value) => handleInputChange('country', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select your country" />
                      </SelectTrigger>
                      <SelectContent>
                        {countries.map((country) => (
                          <SelectItem key={country} value={country}>
                            {country}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Company Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-bold font-myriad">
                  Company/Business Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hasCompany"
                    checked={formData.hasCompany}
                    onCheckedChange={(checked) => handleInputChange('hasCompany', checked)}
                  />
                  <Label htmlFor="hasCompany">I am representing a company/organization</Label>
                </div>

                {formData.hasCompany && (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="companyName">Company/Organization Name</Label>
                        <Input
                          id="companyName"
                          value={formData.companyName}
                          onChange={(e) => handleInputChange('companyName', e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="website">Website</Label>
                        <Input
                          id="website"
                          value={formData.website}
                          onChange={(e) => handleInputChange('website', e.target.value)}
                          placeholder="https://www.example.com"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="businessDescription">Business Description</Label>
                      <Textarea
                        id="businessDescription"
                        placeholder="Brief description of your business..."
                        value={formData.businessDescription}
                        onChange={(e) => handleInputChange('businessDescription', e.target.value)}
                        rows={3}
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Exhibition Package Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="text-xl font-bold font-myriad">Exhibition Package</CardTitle>
              </CardHeader>
              <CardContent>
                {packagesLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-muted-foreground">Loading delegate packages...</div>
                  </div>
                ) : delegatePackages.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No delegate packages available.</p>
                    <Button onClick={loadDelegatePackages} variant="outline" className="mt-4">
                      Retry Loading
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {delegatePackages.map((pkg) => (
                      <div
                        key={pkg.id}
                        className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                          formData.selectedPackage === pkg.id
                            ? 'border-green-500 bg-green-50'
                            : 'border-gray-200 hover:border-green-300'
                        }`}
                        onClick={() => handleInputChange('selectedPackage', pkg.id)}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-semibold text-lg">{pkg.name}</h3>
                          <span className="text-xl font-bold text-green-600">{pkg.price}</span>
                        </div>
                        <p className="text-gray-600 mb-3">{pkg.description}</p>
                        <ul className="space-y-1">
                          {pkg.features.map((feature, index) => (
                            <li key={index} className="flex items-center space-x-2 text-sm">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Company Logo Upload */}
            <ImageUpload
              onImageChange={handleImageChange}
              currentImage={profileImagePreview}
              label="Company Logo"
              description="Upload your company logo for exhibition materials"
            />

            {/* OTP Verification */}
            <OTPVerification
              email={formData.email}
              phone={formData.phone}
              onVerificationComplete={handleOtpVerification}
            />

            {/* Terms and Conditions */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="terms"
                      checked={formData.termsAccepted}
                      onCheckedChange={(checked) => handleInputChange('termsAccepted', checked)}
                    />
                    <Label htmlFor="terms" className="text-sm leading-relaxed">
                      I agree to the{' '}
                      <a href="#" className="text-blue-600 hover:underline">
                        Exhibition Terms and Conditions
                      </a>{' '}
                      and
                      <a href="#" className="text-blue-600 hover:underline ml-1">
                        Privacy Policy
                      </a>{' '}
                      *
                    </Label>
                  </div>
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="guidelines"
                      checked={formData.exhibitorGuidelines}
                      onCheckedChange={(checked) =>
                        handleInputChange('exhibitorGuidelines', checked)
                      }
                    />
                    <Label htmlFor="guidelines" className="text-sm leading-relaxed">
                      I have read and agree to the{' '}
                      <a href="#" className="text-blue-600 hover:underline">
                        Exhibitor Guidelines
                      </a>
                    </Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
        {/* Step 2: Review Details */}
        {currentStep === 2 && (
          <Card>
            <CardHeader>
              <CardTitle
                className="text-xl font-bold"
                style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
              >
                Review Your Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="font-semibold">Name</Label>
                    <p>
                      {formData.firstName} {formData.lastName}
                    </p>
                  </div>
                  <div>
                    <Label className="font-semibold">Email</Label>
                    <p>{formData.email}</p>
                  </div>
                  <div>
                    <Label className="font-semibold">Phone</Label>
                    <p>{formData.phone}</p>
                  </div>
                  <div>
                    <Label className="font-semibold">Country</Label>
                    <p>{formData.country}</p>
                  </div>
                  {formData.position && (
                    <div>
                      <Label className="font-semibold">Position</Label>
                      <p>{formData.position}</p>
                    </div>
                  )}
                  <div>
                    <Label className="font-semibold">Exhibition Package</Label>
                    <p>{getSelectedPackageDetails()?.name || 'N/A'}</p>
                  </div>
                </div>

                {formData.hasCompany && formData.companyName && (
                  <div>
                    <Label className="font-semibold">Company</Label>
                    <p>{formData.companyName}</p>
                  </div>
                )}

                {formData.businessDescription && (
                  <div>
                    <Label className="font-semibold">Business Description</Label>
                    <p>{formData.businessDescription}</p>
                  </div>
                )}

                {profileImagePreview && (
                  <div>
                    <Label className="font-semibold">Company Logo</Label>
                    <img
                      src={profileImagePreview}
                      alt="Company Logo"
                      className="w-20 h-20 rounded-lg object-cover"
                    />
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Label className="font-semibold">OTP Verification</Label>
                  <span
                    className={`px-2 py-1 rounded text-sm ${isOtpVerified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
                  >
                    {isOtpVerified ? 'Verified' : 'Not Verified'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Complete Registration */}
        {currentStep === 3 && (
          <Card className="form-card main-shadow hover-shadow">
            <CardHeader>
              <CardTitle className="text-xl font-bold font-myriad text-primary">
                🚀 Complete Registration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 font-myriad">
              {/* Error/Success Messages */}
              {error && (
                <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
                  <p className="text-destructive font-medium">{error}</p>
                </div>
              )}

              {success && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800 font-medium">{success}</p>
                </div>
              )}

              <div className="bg-secondary/10 border border-secondary/20 rounded-lg p-6">
                <h3 className="font-semibold text-primary mb-4">
                  Ready to Complete Your Registration
                </h3>
                <p className="text-muted-foreground mb-6">
                  Click the button below to complete your exhibitor registration. You will be
                  redirected to the secure payment gateway to complete your payment.
                </p>

                <div className="bg-accent/10 border border-accent/20 rounded-lg p-4 mb-6">
                  <h4 className="font-semibold text-accent mb-2">What happens next:</h4>
                  <ol className="list-decimal list-inside space-y-2 text-sm text-muted-foreground">
                    <li>Your exhibitor account will be created</li>
                    <li>An invoice will be generated for your selected package</li>
                    <li>You'll be redirected to the secure payment gateway</li>
                    <li>After payment, you'll receive confirmation and access details</li>
                  </ol>
                </div>

                <Button
                  onClick={handleRegistrationSubmit}
                  disabled={loading}
                  className="w-full form-button"
                  size="lg"
                >
                  {loading ? 'Processing Registration...' : 'Complete Registration & Pay'}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={currentStep === 1 ? () => router.push('/registration') : prevStep}
            className="flex items-center space-x-2"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>{currentStep === 1 ? 'Back to Registration Types' : 'Previous'}</span>
          </Button>

          <Button
            type="button"
            onClick={nextStep}
            disabled={!canProceedToNextStep()}
            className="flex items-center space-x-2"
          >
            <span>
              {currentStep === exhibitorSteps.length ? 'Complete Registration' : 'Next Step'}
            </span>
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
