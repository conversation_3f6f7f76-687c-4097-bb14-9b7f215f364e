/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    pages: Page;
    posts: Post;
    media: Media;
    categories: Category;
    users: User;
    events: Event;
    speakers: Speaker;
    counties: County;
    programs: Program;
    'program-types': ProgramType;
    'thematic-areas': ThematicArea;
    'target-audience': TargetAudience;
    partners: Partner;
    sponsors: Sponsor;
    'sponsorship-packages': SponsorshipPackage;
    packages: Package;
    'ikia-asset': IkiaAsset;
    enquiry: Enquiry;
    delegatepackages: Delegatepackage;
    invoices: Invoice;
    'pesaflow-notifications': PesaflowNotification;
    exhibitors: Exhibitor;
    'news-items': NewsItem;
    'partnership-matching': PartnershipMatching;
    'success-stories': SuccessStory;
    redirects: Redirect;
    forms: Form;
    'form-submissions': FormSubmission;
    search: Search;
    'payload-jobs': PayloadJob;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    pages: PagesSelect<false> | PagesSelect<true>;
    posts: PostsSelect<false> | PostsSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    categories: CategoriesSelect<false> | CategoriesSelect<true>;
    users: UsersSelect<false> | UsersSelect<true>;
    events: EventsSelect<false> | EventsSelect<true>;
    speakers: SpeakersSelect<false> | SpeakersSelect<true>;
    counties: CountiesSelect<false> | CountiesSelect<true>;
    programs: ProgramsSelect<false> | ProgramsSelect<true>;
    'program-types': ProgramTypesSelect<false> | ProgramTypesSelect<true>;
    'thematic-areas': ThematicAreasSelect<false> | ThematicAreasSelect<true>;
    'target-audience': TargetAudienceSelect<false> | TargetAudienceSelect<true>;
    partners: PartnersSelect<false> | PartnersSelect<true>;
    sponsors: SponsorsSelect<false> | SponsorsSelect<true>;
    'sponsorship-packages': SponsorshipPackagesSelect<false> | SponsorshipPackagesSelect<true>;
    packages: PackagesSelect<false> | PackagesSelect<true>;
    'ikia-asset': IkiaAssetSelect<false> | IkiaAssetSelect<true>;
    enquiry: EnquirySelect<false> | EnquirySelect<true>;
    delegatepackages: DelegatepackagesSelect<false> | DelegatepackagesSelect<true>;
    invoices: InvoicesSelect<false> | InvoicesSelect<true>;
    'pesaflow-notifications': PesaflowNotificationsSelect<false> | PesaflowNotificationsSelect<true>;
    exhibitors: ExhibitorsSelect<false> | ExhibitorsSelect<true>;
    'news-items': NewsItemsSelect<false> | NewsItemsSelect<true>;
    'partnership-matching': PartnershipMatchingSelect<false> | PartnershipMatchingSelect<true>;
    'success-stories': SuccessStoriesSelect<false> | SuccessStoriesSelect<true>;
    redirects: RedirectsSelect<false> | RedirectsSelect<true>;
    forms: FormsSelect<false> | FormsSelect<true>;
    'form-submissions': FormSubmissionsSelect<false> | FormSubmissionsSelect<true>;
    search: SearchSelect<false> | SearchSelect<true>;
    'payload-jobs': PayloadJobsSelect<false> | PayloadJobsSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {
    header: Header;
    footer: Footer;
  };
  globalsSelect: {
    header: HeaderSelect<false> | HeaderSelect<true>;
    footer: FooterSelect<false> | FooterSelect<true>;
  };
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: {
      schedulePublish: TaskSchedulePublish;
      inline: {
        input: unknown;
        output: unknown;
      };
    };
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages".
 */
export interface Page {
  id: number;
  title: string;
  hero: {
    type: 'none' | 'highImpact' | 'mediumImpact' | 'lowImpact';
    richText?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
    links?:
      | {
          link: {
            type?: ('reference' | 'custom') | null;
            newTab?: boolean | null;
            reference?:
              | ({
                  relationTo: 'pages';
                  value: number | Page;
                } | null)
              | ({
                  relationTo: 'posts';
                  value: number | Post;
                } | null);
            url?: string | null;
            label: string;
            /**
             * Choose how the link should be rendered.
             */
            appearance?: ('default' | 'outline') | null;
          };
          id?: string | null;
        }[]
      | null;
    media?: (number | null) | Media;
  };
  layout: (CallToActionBlock | ContentBlock | MediaBlock | ArchiveBlock | FormBlock)[];
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (number | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts".
 */
export interface Post {
  id: number;
  title: string;
  heroImage?: (number | null) | Media;
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  relatedPosts?: (number | Post)[] | null;
  categories?: (number | Category)[] | null;
  meta?: {
    title?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (number | null) | Media;
    description?: string | null;
  };
  publishedAt?: string | null;
  authors?: (number | User)[] | null;
  populatedAuthors?:
    | {
        id?: string | null;
        name?: string | null;
      }[]
    | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  alt?: string | null;
  caption?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    medium?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    og?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories".
 */
export interface Category {
  id: number;
  title: string;
  slug?: string | null;
  slugLock?: boolean | null;
  parent?: (number | null) | Category;
  breadcrumbs?:
    | {
        doc?: (number | null) | Category;
        url?: string | null;
        label?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  name: string;
  /**
   * Type of user in the system
   */
  userType: 'investor' | 'exhibitor' | 'delegate' | 'admin';
  role: 'citizen' | 'business' | 'admin' | 'payment_processor';
  /**
   * The county this user is associated with
   */
  county?: (number | null) | County;
  /**
   * Phone number in international format (254XXXXXXXXX) - optional
   */
  phone_number?: string | null;
  /**
   * National ID number (unique)
   */
  id_number?: string | null;
  /**
   * Type of business (optional)
   */
  business_type?: ('Technology Startup' | 'Manufacturing' | 'Agriculture' | 'Services' | 'Retail' | 'Other') | null;
  /**
   * Purpose of registration (e.g., Business Registration, Investment Facilitation)
   */
  registration_purpose?: string | null;
  /**
   * Delegate package selected by the user
   */
  selected_package?: (number | null) | Delegatepackage;
  /**
   * Current status of user package
   */
  package_status?: ('none' | 'selected' | 'payment_pending' | 'active' | 'expired' | 'suspended') | null;
  /**
   * When the current package expires
   */
  package_expiry?: string | null;
  payment_profile?: {
    preferred_currency?: ('KES' | 'USD') | null;
    /**
     * Daily payment limit in KES
     */
    daily_limit?: number | null;
    /**
     * Monthly payment limit in KES
     */
    monthly_limit?: number | null;
  };
  registration_context?: {
    /**
     * User was created during package selection
     */
    created_during_package_flow?: boolean | null;
    /**
     * Temporary password was sent via email
     */
    temporary_password_sent?: boolean | null;
    /**
     * Initial invoice created during registration
     */
    initial_package_invoice?: (number | null) | Invoice;
  };
  statistics?: {
    total_payments?: number | null;
    total_amount_paid?: number | null;
    last_payment_date?: string | null;
  };
  /**
   * Show this user in featured profiles section
   */
  featured?: boolean | null;
  /**
   * Job title or professional designation
   */
  title?: string | null;
  /**
   * Organization or company name
   */
  organization?: string | null;
  /**
   * City, Country or specific location
   */
  location?: string | null;
  /**
   * Area of expertise or focus
   */
  focus?: string | null;
  /**
   * Professional biography or description
   */
  bio?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  /**
   * Profile photo or avatar
   */
  profileImage?: (number | null) | Media;
  /**
   * Personal or company website
   */
  website?: string | null;
  socialLinks?:
    | {
        platform?: ('linkedin' | 'twitter' | 'facebook' | 'instagram' | 'other') | null;
        url: string;
        id?: string | null;
      }[]
    | null;
  investmentInfo?: {
    /**
     * e.g., "$2.5M+ invested"
     */
    totalInvestment?: string | null;
    /**
     * e.g., "12 active projects"
     */
    activeProjects?: string | null;
    investmentAreas?:
      | {
          area: string;
          id?: string | null;
        }[]
      | null;
    investmentRange?: ('under-50k' | '50k-100k' | '100k-500k' | '500k-1m' | 'over-1m') | null;
  };
  exhibitorInfo?: {
    /**
     * e.g., "30+ years experience"
     */
    achievement?: string | null;
    /**
     * e.g., "500+ patients helped"
     */
    projectsCompleted?: string | null;
    specializations?:
      | {
          specialization: string;
          id?: string | null;
        }[]
      | null;
    certifications?:
      | {
          certification: string;
          id?: string | null;
        }[]
      | null;
  };
  /**
   * User rating (1-5 scale)
   */
  rating?: number | null;
  /**
   * Mark this profile as verified (admin verification for profile completeness and authenticity)
   */
  verified?: boolean | null;
  /**
   * Special badge or designation (e.g., "Top Investor", "Master Healer")
   */
  badge?: string | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  _verified?: boolean | null;
  _verificationToken?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  sessions?:
    | {
        id: string;
        createdAt?: string | null;
        expiresAt: string;
      }[]
    | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "counties".
 */
export interface County {
  id: number;
  /**
   * Full name of the county
   */
  name: string;
  /**
   * Unique identifier code for the county (e.g., KE-001)
   */
  code: string;
  coordinates: {
    /**
     * Latitude coordinate (decimal degrees)
     */
    latitude: number;
    /**
     * Longitude coordinate (decimal degrees)
     */
    longitude: number;
  };
  /**
   * General description of the county
   */
  description?: string | null;
  /**
   * Whether this county is currently active in the system
   */
  isActive?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "delegatepackages".
 */
export interface Delegatepackage {
  id: number;
  /**
   * Name of the delegate package (e.g., Basic Package, Premium Package)
   */
  name: string;
  /**
   * Detailed description of what this package includes
   */
  description: string;
  /**
   * Package price in the specified currency
   */
  price: number;
  /**
   * Currency for the package price
   */
  currency: 'KES' | 'USD' | 'EUR' | 'GBP';
  /**
   * Package duration (e.g., "3 days", "1 week", "One-time")
   */
  duration?: string | null;
  /**
   * Type of package for categorization
   */
  packageType: 'delegate' | 'exhibition' | 'sponsorship' | 'special';
  /**
   * Whether this package is available for selection
   */
  isActive?: boolean | null;
  /**
   * Whether this package should be highlighted as featured
   */
  isFeatured?: boolean | null;
  /**
   * Order in which packages are displayed (lower numbers first)
   */
  displayOrder?: number | null;
  /**
   * List of features included in this package
   */
  features?:
    | {
        feature: string;
        /**
         * Whether this feature is included or excluded
         */
        included?: boolean | null;
        /**
         * Additional cost for this feature (if not included)
         */
        additionalCost?: number | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Key benefits of this package
   */
  benefits?:
    | {
        benefit: string;
        /**
         * Icon name for display (e.g., "check", "star", "gift")
         */
        icon?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Any limitations or restrictions for this package
   */
  limitations?:
    | {
        limitation: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Who this package is designed for
   */
  targetAudience?:
    | {
        audience?:
          | ('individual' | 'corporate' | 'students' | 'researchers' | 'government' | 'ngo' | 'media' | 'international')
          | null;
        id?: string | null;
      }[]
    | null;
  /**
   * What is included in this package
   */
  inclusions?: {
    conferenceAccess?: boolean | null;
    meals?: ('none' | 'lunch' | 'all' | 'refreshments') | null;
    materials?: boolean | null;
    certificate?: boolean | null;
    networking?: boolean | null;
    accommodation?: boolean | null;
    transport?: boolean | null;
  };
  /**
   * Advanced pricing options
   */
  pricing?: {
    /**
     * Discounted price for early registrations
     */
    earlyBirdPrice?: number | null;
    /**
     * Last date for early bird pricing
     */
    earlyBirdDeadline?: string | null;
    /**
     * Minimum number of people for group discount
     */
    groupDiscountThreshold?: number | null;
    /**
     * Percentage discount for group registrations
     */
    groupDiscountPercentage?: number | null;
  };
  /**
   * Package availability and capacity settings
   */
  availability?: {
    /**
     * Maximum number of people who can register for this package
     */
    maxCapacity?: number | null;
    /**
     * Number of people currently registered (auto-updated)
     */
    currentRegistrations?: number | null;
    /**
     * Last date for registration
     */
    registrationDeadline?: string | null;
  };
  /**
   * Additional metadata for this package
   */
  metadata?: {
    /**
     * Tags for categorization and search
     */
    tags?:
      | {
          tag: string;
          id?: string | null;
        }[]
      | null;
    /**
     * Internal notes for admin use (not visible to users)
     */
    internalNotes?: string | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "invoices".
 */
export interface Invoice {
  id: number;
  /**
   * Auto-generated invoice number
   */
  invoice_number: string;
  /**
   * User who owns this invoice
   */
  user: number | User;
  /**
   * Delegate package being purchased
   */
  package: number | Delegatepackage;
  /**
   * Invoice amount in the specified currency
   */
  amount: number;
  currency: 'KES' | 'USD';
  /**
   * Current status of the invoice
   */
  status: 'draft' | 'pending' | 'processing' | 'settled' | 'partial' | 'failed' | 'expired' | 'cancelled';
  /**
   * Unique payment reference for tracking
   */
  payment_reference?: string | null;
  /**
   * When payment is due
   */
  due_date?: string | null;
  customer_info?: {
    /**
     * Customer name
     */
    name?: string | null;
    /**
     * Customer email address
     */
    email?: string | null;
    /**
     * Customer phone number
     */
    phone?: string | null;
    /**
     * Customer ID number
     */
    id_number?: string | null;
  };
  pesaflow_data?: {
    bill_ref_number?: string | null;
    checkout_url?: string | null;
    gateway_response?:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
    last_gateway_sync?: string | null;
  };
  payment_summary?: {
    amount_paid?: number | null;
    amount_remaining?: number | null;
    payment_count?: number | null;
    last_payment_date?: string | null;
  };
  registration_context?: {
    /**
     * Whether this invoice was created during user registration
     */
    is_registration_payment?: boolean | null;
    /**
     * Whether user was created during this payment flow
     */
    user_created_during_flow?: boolean | null;
    /**
     * Whether temporary password was sent to user
     */
    temporary_password_sent?: boolean | null;
  };
  /**
   * Additional data for integration purposes
   */
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CallToActionBlock".
 */
export interface CallToActionBlock {
  richText?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  links?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: number | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: number | Post;
              } | null);
          url?: string | null;
          label: string;
          /**
           * Choose how the link should be rendered.
           */
          appearance?: ('default' | 'outline') | null;
        };
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'cta';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentBlock".
 */
export interface ContentBlock {
  columns?:
    | {
        size?: ('oneThird' | 'half' | 'twoThirds' | 'full') | null;
        richText?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        enableLink?: boolean | null;
        link?: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: number | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: number | Post;
              } | null);
          url?: string | null;
          label: string;
          /**
           * Choose how the link should be rendered.
           */
          appearance?: ('default' | 'outline') | null;
        };
        id?: string | null;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'content';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MediaBlock".
 */
export interface MediaBlock {
  media: number | Media;
  id?: string | null;
  blockName?: string | null;
  blockType: 'mediaBlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ArchiveBlock".
 */
export interface ArchiveBlock {
  introContent?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  populateBy?: ('collection' | 'selection') | null;
  relationTo?: 'posts' | null;
  categories?: (number | Category)[] | null;
  limit?: number | null;
  selectedDocs?:
    | {
        relationTo: 'posts';
        value: number | Post;
      }[]
    | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'archive';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "FormBlock".
 */
export interface FormBlock {
  form: number | Form;
  enableIntro?: boolean | null;
  introContent?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  id?: string | null;
  blockName?: string | null;
  blockType: 'formBlock';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "forms".
 */
export interface Form {
  id: number;
  title: string;
  fields?:
    | (
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            defaultValue?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'checkbox';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'country';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'email';
          }
        | {
            message?: {
              root: {
                type: string;
                children: {
                  type: string;
                  version: number;
                  [k: string]: unknown;
                }[];
                direction: ('ltr' | 'rtl') | null;
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
              };
              [k: string]: unknown;
            } | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'message';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'number';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            placeholder?: string | null;
            options?:
              | {
                  label: string;
                  value: string;
                  id?: string | null;
                }[]
              | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'select';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'state';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'text';
          }
        | {
            name: string;
            label?: string | null;
            width?: number | null;
            defaultValue?: string | null;
            required?: boolean | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'textarea';
          }
      )[]
    | null;
  submitButtonLabel?: string | null;
  /**
   * Choose whether to display an on-page message or redirect to a different page after they submit the form.
   */
  confirmationType?: ('message' | 'redirect') | null;
  confirmationMessage?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  redirect?: {
    url: string;
  };
  /**
   * Send custom emails when the form submits. Use comma separated lists to send the same email to multiple recipients. To reference a value from this form, wrap that field's name with double curly brackets, i.e. {{firstName}}. You can use a wildcard {{*}} to output all data and {{*:table}} to format it as an HTML table in the email.
   */
  emails?:
    | {
        emailTo?: string | null;
        cc?: string | null;
        bcc?: string | null;
        replyTo?: string | null;
        emailFrom?: string | null;
        subject: string;
        /**
         * Enter the message that should be sent in this email.
         */
        message?: {
          root: {
            type: string;
            children: {
              type: string;
              version: number;
              [k: string]: unknown;
            }[];
            direction: ('ltr' | 'rtl') | null;
            format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
            indent: number;
            version: number;
          };
          [k: string]: unknown;
        } | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "events".
 */
export interface Event {
  id: number;
  title: string;
  description: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  type: 'keynote' | 'panel' | 'workshop' | 'exhibition' | 'breakout';
  date: string;
  startTime?: string | null;
  endTime?: string | null;
  day: number;
  speakers?: (number | Speaker)[] | null;
  location?: string | null;
  downloads?: (number | null) | Media;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "speakers".
 */
export interface Speaker {
  id: number;
  name: string;
  title?: string | null;
  company?: string | null;
  organisation?: string | null;
  category?: ('keynote' | 'plenary' | 'other') | null;
  order?: number | null;
  topic?: string | null;
  bio?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  photo?: (number | null) | Media;
  socials?:
    | {
        platform?: string | null;
        url?: string | null;
        id?: string | null;
      }[]
    | null;
  featuredEvents?: (number | Event)[] | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "programs".
 */
export interface Program {
  id: number;
  title: string;
  description: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Type of program/session
   */
  type: number | ProgramType;
  /**
   * The thematic area this program belongs to (used for filtering)
   */
  thematicArea?: (number | null) | ThematicArea;
  /**
   * Conference dates: November 19-21, 2025
   */
  date: string;
  /**
   * Format: HH:MM (e.g., 09:00)
   */
  startTime?: string | null;
  /**
   * Format: HH:MM (e.g., 10:30)
   */
  endTime?: string | null;
  /**
   * Where the program will take place
   */
  venue: string;
  /**
   * Speakers participating in this program
   */
  speakers?: (number | Speaker)[] | null;
  /**
   * Maximum number of attendees (optional)
   */
  capacity?: number | null;
  /**
   * Show this program in the featured section on homepage
   */
  isFeatured?: boolean | null;
  /**
   * This program runs parallel to other sessions
   */
  isParallel?: boolean | null;
  /**
   * Group identifier for parallel sessions (e.g., "morning-sessions")
   */
  parallelGroup?: string | null;
  /**
   * Any special requirements or prerequisites
   */
  requirements?: string | null;
  /**
   * Documents, presentations, or other materials
   */
  materials?: (number | Media)[] | null;
  /**
   * Whether separate registration is required for this program
   */
  registrationRequired?: boolean | null;
  /**
   * Tags for categorizing and filtering programs
   */
  tags?:
    | {
        tag: string;
        id?: string | null;
      }[]
    | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "program-types".
 */
export interface ProgramType {
  id: number;
  /**
   * Name of the program type (e.g., Keynote, Panel Discussion)
   */
  name: string;
  /**
   * Brief description of this program type
   */
  description?: string | null;
  /**
   * CSS color class or hex code for UI display (e.g., bg-red-50 border-red-200 text-red-700)
   */
  color?: string | null;
  /**
   * Lucide icon name for display (optional)
   */
  icon?: string | null;
  /**
   * Whether this program type is currently active
   */
  isActive?: boolean | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "thematic-areas".
 */
export interface ThematicArea {
  id: number;
  /**
   * Name of the thematic area (e.g., "Digital Innovation", "Sustainability")
   */
  name: string;
  /**
   * Brief description of this thematic area
   */
  description?: string | null;
  /**
   * Optional image to represent this thematic area
   */
  image?: (number | null) | Media;
  /**
   * Hex color code for this theme (e.g., #1E40AF)
   */
  color?: string | null;
  /**
   * Whether this thematic area is active and should appear in filters
   */
  isActive?: boolean | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "target-audience".
 */
export interface TargetAudience {
  id: number;
  title: string;
  description: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "partners".
 */
export interface Partner {
  id: number;
  name: string;
  company?: string | null;
  logo?: (number | null) | Media;
  sponsorshipTier?: ('platinum' | 'gold' | 'silver' | 'bronze') | null;
  eventsSponsored?: (number | Event)[] | null;
  about?: string | null;
  notableContributions?:
    | {
        contribution?: string | null;
        id?: string | null;
      }[]
    | null;
  awardsRecognitions?:
    | {
        award?: string | null;
        id?: string | null;
      }[]
    | null;
  recentProjects?:
    | {
        project?: string | null;
        id?: string | null;
      }[]
    | null;
  impact?:
    | {
        impactItem?: string | null;
        id?: string | null;
      }[]
    | null;
  contact?: {
    emails?:
      | {
          email?: string | null;
          id?: string | null;
        }[]
      | null;
    phones?:
      | {
          phone?: string | null;
          id?: string | null;
        }[]
      | null;
    location?: {
      city?: string | null;
      country?: string | null;
      address?: string | null;
    };
  };
  /**
   * Internal notes - not visible to public
   */
  internalNotes?: string | null;
  category?: ('strategic' | 'community' | 'implementation' | 'media')[] | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "sponsors".
 */
export interface Sponsor {
  id: number;
  name: string;
  logo: number | Media;
  description?: string | null;
  website?: string | null;
  tier?: ('title' | 'platinum' | 'gold' | 'silver' | 'bronze') | null;
  category: 'financial' | 'service' | 'media' | 'special';
  sponsorshipPackage?: (number | null) | SponsorshipPackage;
  /**
   * Enable if this sponsor should have a dedicated details page
   */
  hasDetailsPage?: boolean | null;
  /**
   * Additional logos for sub-brands or services (e.g., M-Pesa for Safaricom)
   */
  additionalLogos?:
    | {
        name: string;
        logo: number | Media;
        id?: string | null;
      }[]
    | null;
  /**
   * Mark as featured to highlight in special sections
   */
  featured?: boolean | null;
  /**
   * Lower numbers appear first. Leave empty for automatic ordering.
   */
  displayOrder?: number | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "sponsorship-packages".
 */
export interface SponsorshipPackage {
  id: number;
  name: string;
  /**
   * e.g., "KES 10,000,000" or "In-Kind Contribution"
   */
  price: string;
  /**
   * Numeric value for sorting and calculations. Leave empty for in-kind packages.
   */
  priceAmount?: number | null;
  tier?: ('title' | 'platinum' | 'gold' | 'silver' | 'bronze' | 'special') | null;
  category: 'financial' | 'service' | 'media' | 'special';
  /**
   * Maximum number of sponsors allowed for this package
   */
  maxPartners: number;
  /**
   * Current number of sponsors in this package (auto-calculated)
   */
  currentPartners?: number | null;
  /**
   * Detailed description of the sponsorship package
   */
  description?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  benefits?:
    | {
        benefit: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Check if this is an in-kind contribution rather than financial
   */
  isInKind?: boolean | null;
  /**
   * Mark as featured to highlight in package listings
   */
  featured?: boolean | null;
  /**
   * Uncheck to hide from public listings (sold out or discontinued)
   */
  available?: boolean | null;
  /**
   * Upload the detailed sponsorship prospectus document (PDF)
   */
  prospectusDocument?: (number | null) | Media;
  /**
   * Lower numbers appear first. Leave empty for automatic ordering by price.
   */
  displayOrder?: number | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "packages".
 */
export interface Package {
  id: number;
  /**
   * Name of the service package (e.g., Basic Package, Premium Package)
   */
  name: string;
  /**
   * Brief description of what this package includes
   */
  description: string;
  /**
   * Price of the package in the specified currency
   */
  price: number;
  currency: 'KES' | 'USD';
  /**
   * List of features included in this package
   */
  features?:
    | {
        feature: string;
        included?: boolean | null;
        id?: string | null;
      }[]
    | null;
  packageType: 'basic' | 'premium' | 'enterprise' | 'custom';
  duration: {
    value: number;
    unit: 'days' | 'months' | 'years';
  };
  /**
   * Whether this package is available for selection
   */
  isActive?: boolean | null;
  /**
   * Highlight this package as recommended or popular
   */
  isFeatured?: boolean | null;
  /**
   * Order in which packages are displayed (lower numbers first)
   */
  displayOrder?: number | null;
  /**
   * Maximum number of users allowed for this package (optional)
   */
  maxUsers?: number | null;
  /**
   * One-time setup fee (if applicable)
   */
  setupFee?: number | null;
  /**
   * Discount percentage for promotional pricing
   */
  discountPercentage?: number | null;
  /**
   * Expiry date for this package (optional)
   */
  validUntil?: string | null;
  /**
   * Additional package configuration data
   */
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ikia-asset".
 */
export interface IkiaAsset {
  id: number;
  title: string;
  description?: string | null;
  categories?:
    | {
        name?: string | null;
        id?: string | null;
      }[]
    | null;
  tags?:
    | {
        name?: string | null;
        id?: string | null;
      }[]
    | null;
  location: string;
  /**
   * County where this IKIA asset is located
   */
  county?: (number | null) | County;
  /**
   * Thematic area this IKIA asset belongs to
   */
  thematicArea: number | ThematicArea;
  documentedBy?: string | null;
  yearDocumented?: number | null;
  investmentPotential?: ('high' | 'medium' | 'low') | null;
  /**
   * Featured IKIA assets appear in special sections
   */
  featured?: boolean | null;
  /**
   * Mark this IKIA asset as ready for investment opportunities
   */
  readyForInvestment?: boolean | null;
  /**
   * Amount of investment needed
   */
  investmentNeeded?: number | null;
  /**
   * Expected return percentage (e.g., "25-35%")
   */
  expectedReturn?: string | null;
  /**
   * Expected timeline for returns (e.g., "18 months")
   */
  timeline?: string | null;
  /**
   * Investment risk level
   */
  riskLevel?: ('low' | 'medium' | 'high') | null;
  /**
   * Social/environmental impact score (1-10)
   */
  impactScore?: number | null;
  /**
   * Key selling points and highlights
   */
  highlights?:
    | {
        highlight: string;
        id?: string | null;
      }[]
    | null;
  image?: (number | null) | Media;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "enquiry".
 */
export interface Enquiry {
  id: number;
  fullname: string;
  email: string;
  subject?: string | null;
  message: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pesaflow-notifications".
 */
export interface PesaflowNotification {
  id: number;
  /**
   * Payment channel used (M-Pesa, Bank Transfer, etc.)
   */
  payment_channel: string;
  /**
   * Client invoice reference from Pesaflow
   */
  client_invoice_ref: string;
  /**
   * Payment reference from Pesaflow (may be generated if missing)
   */
  payment_reference?: string | null;
  /**
   * Date when payment was made
   */
  payment_date: string;
  /**
   * Date when payment was inserted in Pesaflow system
   */
  inserted_at: string;
  /**
   * Payment currency
   */
  currency: string;
  /**
   * Amount paid by customer
   */
  amount_paid: number;
  /**
   * Original invoice amount
   */
  invoice_amount: number;
  /**
   * Last payment amount
   */
  last_payment_amount: number;
  /**
   * Payment status from Pesaflow
   */
  status: 'settled' | 'pending' | 'failed' | 'cancelled';
  /**
   * Invoice number from Pesaflow
   */
  invoice_number?: string | null;
  /**
   * Security hash from Pesaflow for verification
   */
  secure_hash: string;
  /**
   * User who made the payment
   */
  user?: (number | null) | User;
  /**
   * Delegate package being purchased
   */
  service_package?: (number | null) | Delegatepackage;
  /**
   * Processing status of this notification
   */
  processing_status: 'pending' | 'processed' | 'failed' | 'duplicate' | 'invalid';
  /**
   * When this notification was processed
   */
  processed_at?: string | null;
  /**
   * Notes about notification processing (errors, warnings, etc.)
   */
  processing_notes?: string | null;
  /**
   * Whether the secure hash was successfully verified
   */
  hash_verified?: boolean | null;
  /**
   * IP address of notification sender
   */
  ip_address?: string | null;
  /**
   * User agent of notification sender
   */
  user_agent?: string | null;
  /**
   * Complete raw notification payload from Pesaflow
   */
  raw_payload:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "exhibitors".
 */
export interface Exhibitor {
  id: number;
  /**
   * Primary contact first name
   */
  firstName: string;
  /**
   * Primary contact last name
   */
  lastName: string;
  /**
   * Primary contact email address
   */
  email: string;
  /**
   * Primary contact phone number
   */
  phone: string;
  /**
   * Position/title in the company
   */
  position?: string | null;
  /**
   * Country of origin
   */
  country: string;
  /**
   * City of origin
   */
  city?: string | null;
  /**
   * Whether the exhibitor represents a company
   */
  hasCompany?: boolean | null;
  /**
   * Company/organization name
   */
  companyName?: string | null;
  /**
   * Company website URL
   */
  website?: string | null;
  /**
   * Company address
   */
  address?: string | null;
  /**
   * Type of business/industry
   */
  businessType?:
    | (
        | 'Traditional Foods & Nutrition'
        | 'Local Remedies & Traditional Medicine'
        | 'Musicology & Cultural Arts'
        | 'Cultural Tourism & Heritage'
        | 'Indigenous Technologies & Innovations'
        | 'Sui Generis Intellectual Property Systems'
      )
    | null;
  /**
   * Brief description of the business
   */
  businessDescription?: string | null;
  /**
   * Products or services offered
   */
  productsServices?: string | null;
  /**
   * Target market or audience
   */
  targetMarket?: string | null;
  /**
   * Number of years in business
   */
  yearsInBusiness?: string | null;
  /**
   * Selected exhibition package
   */
  selectedPackage: number | Delegatepackage;
  /**
   * Preferred booth size and type
   */
  boothRequirement?: ('standard_3x3' | 'premium_3x6' | 'corner_3x3' | 'island_6x6' | 'custom') | null;
  /**
   * Additional services required
   */
  additionalServices?:
    | {
        service?:
          | (
              | 'electricity'
              | 'internet'
              | 'av_equipment'
              | 'furniture'
              | 'storage'
              | 'catering'
              | 'marketing'
              | 'translation'
            )
          | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Any special requirements or requests
   */
  specialRequirements?: string | null;
  /**
   * Whether there are additional company representatives
   */
  hasAdditionalReps?: boolean | null;
  /**
   * Additional company representatives (max 3)
   */
  additionalRepresentatives?:
    | {
        name: string;
        email: string;
        phone: string;
        position?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Current registration status
   */
  registrationStatus?: ('pending_payment' | 'payment_confirmed' | 'registration_complete' | 'cancelled') | null;
  /**
   * Payment reference number
   */
  paymentReference?: string | null;
  /**
   * Date of registration
   */
  registrationDate?: string | null;
  /**
   * Internal notes about this exhibitor
   */
  notes?: string | null;
  /**
   * Company logo for exhibition materials
   */
  companyLogo?: (number | null) | Media;
  /**
   * Marketing materials and brochures
   */
  marketingMaterials?:
    | {
        file: number | Media;
        description?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Terms and conditions accepted
   */
  termsAccepted: boolean;
  /**
   * Exhibitor guidelines accepted
   */
  exhibitorGuidelines: boolean;
  /**
   * Consent for media coverage and photography
   */
  mediaConsent?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "news-items".
 */
export interface NewsItem {
  id: number;
  title: string;
  summary?: string | null;
  content?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  image?: (number | null) | Media;
  eventDate?: string | null;
  category: 'news-feed' | 'press-releases' | 'blogs' | 'social-media' | 'event-updates';
  type: 'featured' | 'highlight' | 'regular';
  author?: string | null;
  slug?: string | null;
  slugLock?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "partnership-matching".
 */
export interface PartnershipMatching {
  id: number;
  userType: 'investor' | 'entrepreneur' | 'researcher' | 'ik-holder';
  lookingFor: 'investor' | 'knowledge-holder' | 'business-partner' | 'mentor';
  fullName: string;
  email: string;
  phone: string;
  organization: string;
  areaOfInterest: string;
  investmentRange?: ('under-50k' | '50k-100k' | '100k-500k' | '500k-1m' | 'over-1m') | null;
  experience?: string | null;
  projectDescription: string;
  timeline?: ('immediate' | 'short-term' | 'medium-term' | 'long-term') | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "success-stories".
 */
export interface SuccessStory {
  id: number;
  title: string;
  description: string;
  image?: (number | null) | Media;
  investor: {
    name: string;
    position: string;
  };
  knowledgeHolder: {
    name: string;
    title: string;
  };
  impact?:
    | {
        metric: string;
        value: string;
        id?: string | null;
      }[]
    | null;
  message: string;
  messageAuthor?: string | null;
  investmentAmount: number;
  currency?: ('KES' | 'USD' | 'EUR') | null;
  timeline: string;
  location: {
    county: string;
    country: string;
  };
  featured?: boolean | null;
  isActive?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects".
 */
export interface Redirect {
  id: number;
  /**
   * You will need to rebuild the website when changing this field.
   */
  from: string;
  to?: {
    type?: ('reference' | 'custom') | null;
    reference?:
      | ({
          relationTo: 'pages';
          value: number | Page;
        } | null)
      | ({
          relationTo: 'posts';
          value: number | Post;
        } | null);
    url?: string | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "form-submissions".
 */
export interface FormSubmission {
  id: number;
  form: number | Form;
  submissionData?:
    | {
        field: string;
        value: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This is a collection of automatically created search results. These results are used by the global site search and will be updated automatically as documents in the CMS are created or updated.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "search".
 */
export interface Search {
  id: number;
  title?: string | null;
  priority?: number | null;
  doc: {
    relationTo: 'posts';
    value: number | Post;
  };
  slug?: string | null;
  meta?: {
    title?: string | null;
    description?: string | null;
    image?: (number | null) | Media;
  };
  categories?:
    | {
        relationTo?: string | null;
        categoryID?: string | null;
        title?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs".
 */
export interface PayloadJob {
  id: number;
  /**
   * Input data provided to the job
   */
  input?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  taskStatus?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  completedAt?: string | null;
  totalTried?: number | null;
  /**
   * If hasError is true this job will not be retried
   */
  hasError?: boolean | null;
  /**
   * If hasError is true, this is the error that caused it
   */
  error?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Task execution log
   */
  log?:
    | {
        executedAt: string;
        completedAt: string;
        taskSlug: 'inline' | 'schedulePublish';
        taskID: string;
        input?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        output?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        state: 'failed' | 'succeeded';
        error?:
          | {
              [k: string]: unknown;
            }
          | unknown[]
          | string
          | number
          | boolean
          | null;
        id?: string | null;
      }[]
    | null;
  taskSlug?: ('inline' | 'schedulePublish') | null;
  queue?: string | null;
  waitUntil?: string | null;
  processing?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'pages';
        value: number | Page;
      } | null)
    | ({
        relationTo: 'posts';
        value: number | Post;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'categories';
        value: number | Category;
      } | null)
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'events';
        value: number | Event;
      } | null)
    | ({
        relationTo: 'speakers';
        value: number | Speaker;
      } | null)
    | ({
        relationTo: 'counties';
        value: number | County;
      } | null)
    | ({
        relationTo: 'programs';
        value: number | Program;
      } | null)
    | ({
        relationTo: 'program-types';
        value: number | ProgramType;
      } | null)
    | ({
        relationTo: 'thematic-areas';
        value: number | ThematicArea;
      } | null)
    | ({
        relationTo: 'target-audience';
        value: number | TargetAudience;
      } | null)
    | ({
        relationTo: 'partners';
        value: number | Partner;
      } | null)
    | ({
        relationTo: 'sponsors';
        value: number | Sponsor;
      } | null)
    | ({
        relationTo: 'sponsorship-packages';
        value: number | SponsorshipPackage;
      } | null)
    | ({
        relationTo: 'packages';
        value: number | Package;
      } | null)
    | ({
        relationTo: 'ikia-asset';
        value: number | IkiaAsset;
      } | null)
    | ({
        relationTo: 'enquiry';
        value: number | Enquiry;
      } | null)
    | ({
        relationTo: 'delegatepackages';
        value: number | Delegatepackage;
      } | null)
    | ({
        relationTo: 'invoices';
        value: number | Invoice;
      } | null)
    | ({
        relationTo: 'pesaflow-notifications';
        value: number | PesaflowNotification;
      } | null)
    | ({
        relationTo: 'exhibitors';
        value: number | Exhibitor;
      } | null)
    | ({
        relationTo: 'news-items';
        value: number | NewsItem;
      } | null)
    | ({
        relationTo: 'partnership-matching';
        value: number | PartnershipMatching;
      } | null)
    | ({
        relationTo: 'success-stories';
        value: number | SuccessStory;
      } | null)
    | ({
        relationTo: 'redirects';
        value: number | Redirect;
      } | null)
    | ({
        relationTo: 'forms';
        value: number | Form;
      } | null)
    | ({
        relationTo: 'form-submissions';
        value: number | FormSubmission;
      } | null)
    | ({
        relationTo: 'search';
        value: number | Search;
      } | null)
    | ({
        relationTo: 'payload-jobs';
        value: number | PayloadJob;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pages_select".
 */
export interface PagesSelect<T extends boolean = true> {
  title?: T;
  hero?:
    | T
    | {
        type?: T;
        richText?: T;
        links?:
          | T
          | {
              link?:
                | T
                | {
                    type?: T;
                    newTab?: T;
                    reference?: T;
                    url?: T;
                    label?: T;
                    appearance?: T;
                  };
              id?: T;
            };
        media?: T;
      };
  layout?:
    | T
    | {
        cta?: T | CallToActionBlockSelect<T>;
        content?: T | ContentBlockSelect<T>;
        mediaBlock?: T | MediaBlockSelect<T>;
        archive?: T | ArchiveBlockSelect<T>;
        formBlock?: T | FormBlockSelect<T>;
      };
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CallToActionBlock_select".
 */
export interface CallToActionBlockSelect<T extends boolean = true> {
  richText?: T;
  links?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
              appearance?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ContentBlock_select".
 */
export interface ContentBlockSelect<T extends boolean = true> {
  columns?:
    | T
    | {
        size?: T;
        richText?: T;
        enableLink?: T;
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
              appearance?: T;
            };
        id?: T;
      };
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "MediaBlock_select".
 */
export interface MediaBlockSelect<T extends boolean = true> {
  media?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ArchiveBlock_select".
 */
export interface ArchiveBlockSelect<T extends boolean = true> {
  introContent?: T;
  populateBy?: T;
  relationTo?: T;
  categories?: T;
  limit?: T;
  selectedDocs?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "FormBlock_select".
 */
export interface FormBlockSelect<T extends boolean = true> {
  form?: T;
  enableIntro?: T;
  introContent?: T;
  id?: T;
  blockName?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts_select".
 */
export interface PostsSelect<T extends boolean = true> {
  title?: T;
  heroImage?: T;
  content?: T;
  relatedPosts?: T;
  categories?: T;
  meta?:
    | T
    | {
        title?: T;
        image?: T;
        description?: T;
      };
  publishedAt?: T;
  authors?: T;
  populatedAuthors?:
    | T
    | {
        id?: T;
        name?: T;
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  caption?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        medium?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        og?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories_select".
 */
export interface CategoriesSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  slugLock?: T;
  parent?: T;
  breadcrumbs?:
    | T
    | {
        doc?: T;
        url?: T;
        label?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  name?: T;
  userType?: T;
  role?: T;
  county?: T;
  phone_number?: T;
  id_number?: T;
  business_type?: T;
  registration_purpose?: T;
  selected_package?: T;
  package_status?: T;
  package_expiry?: T;
  payment_profile?:
    | T
    | {
        preferred_currency?: T;
        daily_limit?: T;
        monthly_limit?: T;
      };
  registration_context?:
    | T
    | {
        created_during_package_flow?: T;
        temporary_password_sent?: T;
        initial_package_invoice?: T;
      };
  statistics?:
    | T
    | {
        total_payments?: T;
        total_amount_paid?: T;
        last_payment_date?: T;
      };
  featured?: T;
  title?: T;
  organization?: T;
  location?: T;
  focus?: T;
  bio?: T;
  profileImage?: T;
  website?: T;
  socialLinks?:
    | T
    | {
        platform?: T;
        url?: T;
        id?: T;
      };
  investmentInfo?:
    | T
    | {
        totalInvestment?: T;
        activeProjects?: T;
        investmentAreas?:
          | T
          | {
              area?: T;
              id?: T;
            };
        investmentRange?: T;
      };
  exhibitorInfo?:
    | T
    | {
        achievement?: T;
        projectsCompleted?: T;
        specializations?:
          | T
          | {
              specialization?: T;
              id?: T;
            };
        certifications?:
          | T
          | {
              certification?: T;
              id?: T;
            };
      };
  rating?: T;
  verified?: T;
  badge?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  _verified?: T;
  _verificationToken?: T;
  loginAttempts?: T;
  lockUntil?: T;
  sessions?:
    | T
    | {
        id?: T;
        createdAt?: T;
        expiresAt?: T;
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "events_select".
 */
export interface EventsSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  type?: T;
  date?: T;
  startTime?: T;
  endTime?: T;
  day?: T;
  speakers?: T;
  location?: T;
  downloads?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "speakers_select".
 */
export interface SpeakersSelect<T extends boolean = true> {
  name?: T;
  title?: T;
  company?: T;
  organisation?: T;
  category?: T;
  order?: T;
  topic?: T;
  bio?: T;
  photo?: T;
  socials?:
    | T
    | {
        platform?: T;
        url?: T;
        id?: T;
      };
  featuredEvents?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "counties_select".
 */
export interface CountiesSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  coordinates?:
    | T
    | {
        latitude?: T;
        longitude?: T;
      };
  description?: T;
  isActive?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "programs_select".
 */
export interface ProgramsSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  type?: T;
  thematicArea?: T;
  date?: T;
  startTime?: T;
  endTime?: T;
  venue?: T;
  speakers?: T;
  capacity?: T;
  isFeatured?: T;
  isParallel?: T;
  parallelGroup?: T;
  requirements?: T;
  materials?: T;
  registrationRequired?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "program-types_select".
 */
export interface ProgramTypesSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  color?: T;
  icon?: T;
  isActive?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "thematic-areas_select".
 */
export interface ThematicAreasSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  image?: T;
  color?: T;
  isActive?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "target-audience_select".
 */
export interface TargetAudienceSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "partners_select".
 */
export interface PartnersSelect<T extends boolean = true> {
  name?: T;
  company?: T;
  logo?: T;
  sponsorshipTier?: T;
  eventsSponsored?: T;
  about?: T;
  notableContributions?:
    | T
    | {
        contribution?: T;
        id?: T;
      };
  awardsRecognitions?:
    | T
    | {
        award?: T;
        id?: T;
      };
  recentProjects?:
    | T
    | {
        project?: T;
        id?: T;
      };
  impact?:
    | T
    | {
        impactItem?: T;
        id?: T;
      };
  contact?:
    | T
    | {
        emails?:
          | T
          | {
              email?: T;
              id?: T;
            };
        phones?:
          | T
          | {
              phone?: T;
              id?: T;
            };
        location?:
          | T
          | {
              city?: T;
              country?: T;
              address?: T;
            };
      };
  internalNotes?: T;
  category?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "sponsors_select".
 */
export interface SponsorsSelect<T extends boolean = true> {
  name?: T;
  logo?: T;
  description?: T;
  website?: T;
  tier?: T;
  category?: T;
  sponsorshipPackage?: T;
  hasDetailsPage?: T;
  additionalLogos?:
    | T
    | {
        name?: T;
        logo?: T;
        id?: T;
      };
  featured?: T;
  displayOrder?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "sponsorship-packages_select".
 */
export interface SponsorshipPackagesSelect<T extends boolean = true> {
  name?: T;
  price?: T;
  priceAmount?: T;
  tier?: T;
  category?: T;
  maxPartners?: T;
  currentPartners?: T;
  description?: T;
  benefits?:
    | T
    | {
        benefit?: T;
        id?: T;
      };
  isInKind?: T;
  featured?: T;
  available?: T;
  prospectusDocument?: T;
  displayOrder?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "packages_select".
 */
export interface PackagesSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  price?: T;
  currency?: T;
  features?:
    | T
    | {
        feature?: T;
        included?: T;
        id?: T;
      };
  packageType?: T;
  duration?:
    | T
    | {
        value?: T;
        unit?: T;
      };
  isActive?: T;
  isFeatured?: T;
  displayOrder?: T;
  maxUsers?: T;
  setupFee?: T;
  discountPercentage?: T;
  validUntil?: T;
  metadata?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "ikia-asset_select".
 */
export interface IkiaAssetSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  categories?:
    | T
    | {
        name?: T;
        id?: T;
      };
  tags?:
    | T
    | {
        name?: T;
        id?: T;
      };
  location?: T;
  county?: T;
  thematicArea?: T;
  documentedBy?: T;
  yearDocumented?: T;
  investmentPotential?: T;
  featured?: T;
  readyForInvestment?: T;
  investmentNeeded?: T;
  expectedReturn?: T;
  timeline?: T;
  riskLevel?: T;
  impactScore?: T;
  highlights?:
    | T
    | {
        highlight?: T;
        id?: T;
      };
  image?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "enquiry_select".
 */
export interface EnquirySelect<T extends boolean = true> {
  fullname?: T;
  email?: T;
  subject?: T;
  message?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "delegatepackages_select".
 */
export interface DelegatepackagesSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  price?: T;
  currency?: T;
  duration?: T;
  packageType?: T;
  isActive?: T;
  isFeatured?: T;
  displayOrder?: T;
  features?:
    | T
    | {
        feature?: T;
        included?: T;
        additionalCost?: T;
        id?: T;
      };
  benefits?:
    | T
    | {
        benefit?: T;
        icon?: T;
        id?: T;
      };
  limitations?:
    | T
    | {
        limitation?: T;
        id?: T;
      };
  targetAudience?:
    | T
    | {
        audience?: T;
        id?: T;
      };
  inclusions?:
    | T
    | {
        conferenceAccess?: T;
        meals?: T;
        materials?: T;
        certificate?: T;
        networking?: T;
        accommodation?: T;
        transport?: T;
      };
  pricing?:
    | T
    | {
        earlyBirdPrice?: T;
        earlyBirdDeadline?: T;
        groupDiscountThreshold?: T;
        groupDiscountPercentage?: T;
      };
  availability?:
    | T
    | {
        maxCapacity?: T;
        currentRegistrations?: T;
        registrationDeadline?: T;
      };
  metadata?:
    | T
    | {
        tags?:
          | T
          | {
              tag?: T;
              id?: T;
            };
        internalNotes?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "invoices_select".
 */
export interface InvoicesSelect<T extends boolean = true> {
  invoice_number?: T;
  user?: T;
  package?: T;
  amount?: T;
  currency?: T;
  status?: T;
  payment_reference?: T;
  due_date?: T;
  customer_info?:
    | T
    | {
        name?: T;
        email?: T;
        phone?: T;
        id_number?: T;
      };
  pesaflow_data?:
    | T
    | {
        bill_ref_number?: T;
        checkout_url?: T;
        gateway_response?: T;
        last_gateway_sync?: T;
      };
  payment_summary?:
    | T
    | {
        amount_paid?: T;
        amount_remaining?: T;
        payment_count?: T;
        last_payment_date?: T;
      };
  registration_context?:
    | T
    | {
        is_registration_payment?: T;
        user_created_during_flow?: T;
        temporary_password_sent?: T;
      };
  metadata?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "pesaflow-notifications_select".
 */
export interface PesaflowNotificationsSelect<T extends boolean = true> {
  payment_channel?: T;
  client_invoice_ref?: T;
  payment_reference?: T;
  payment_date?: T;
  inserted_at?: T;
  currency?: T;
  amount_paid?: T;
  invoice_amount?: T;
  last_payment_amount?: T;
  status?: T;
  invoice_number?: T;
  secure_hash?: T;
  user?: T;
  service_package?: T;
  processing_status?: T;
  processed_at?: T;
  processing_notes?: T;
  hash_verified?: T;
  ip_address?: T;
  user_agent?: T;
  raw_payload?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "exhibitors_select".
 */
export interface ExhibitorsSelect<T extends boolean = true> {
  firstName?: T;
  lastName?: T;
  email?: T;
  phone?: T;
  position?: T;
  country?: T;
  city?: T;
  hasCompany?: T;
  companyName?: T;
  website?: T;
  address?: T;
  businessType?: T;
  businessDescription?: T;
  productsServices?: T;
  targetMarket?: T;
  yearsInBusiness?: T;
  selectedPackage?: T;
  boothRequirement?: T;
  additionalServices?:
    | T
    | {
        service?: T;
        id?: T;
      };
  specialRequirements?: T;
  hasAdditionalReps?: T;
  additionalRepresentatives?:
    | T
    | {
        name?: T;
        email?: T;
        phone?: T;
        position?: T;
        id?: T;
      };
  registrationStatus?: T;
  paymentReference?: T;
  registrationDate?: T;
  notes?: T;
  companyLogo?: T;
  marketingMaterials?:
    | T
    | {
        file?: T;
        description?: T;
        id?: T;
      };
  termsAccepted?: T;
  exhibitorGuidelines?: T;
  mediaConsent?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "news-items_select".
 */
export interface NewsItemsSelect<T extends boolean = true> {
  title?: T;
  summary?: T;
  content?: T;
  image?: T;
  eventDate?: T;
  category?: T;
  type?: T;
  author?: T;
  slug?: T;
  slugLock?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "partnership-matching_select".
 */
export interface PartnershipMatchingSelect<T extends boolean = true> {
  userType?: T;
  lookingFor?: T;
  fullName?: T;
  email?: T;
  phone?: T;
  organization?: T;
  areaOfInterest?: T;
  investmentRange?: T;
  experience?: T;
  projectDescription?: T;
  timeline?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "success-stories_select".
 */
export interface SuccessStoriesSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  image?: T;
  investor?:
    | T
    | {
        name?: T;
        position?: T;
      };
  knowledgeHolder?:
    | T
    | {
        name?: T;
        title?: T;
      };
  impact?:
    | T
    | {
        metric?: T;
        value?: T;
        id?: T;
      };
  message?: T;
  messageAuthor?: T;
  investmentAmount?: T;
  currency?: T;
  timeline?: T;
  location?:
    | T
    | {
        county?: T;
        country?: T;
      };
  featured?: T;
  isActive?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "redirects_select".
 */
export interface RedirectsSelect<T extends boolean = true> {
  from?: T;
  to?:
    | T
    | {
        type?: T;
        reference?: T;
        url?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "forms_select".
 */
export interface FormsSelect<T extends boolean = true> {
  title?: T;
  fields?:
    | T
    | {
        checkbox?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              defaultValue?: T;
              id?: T;
              blockName?: T;
            };
        country?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        email?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        message?:
          | T
          | {
              message?: T;
              id?: T;
              blockName?: T;
            };
        number?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        select?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              placeholder?: T;
              options?:
                | T
                | {
                    label?: T;
                    value?: T;
                    id?: T;
                  };
              required?: T;
              id?: T;
              blockName?: T;
            };
        state?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        text?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
        textarea?:
          | T
          | {
              name?: T;
              label?: T;
              width?: T;
              defaultValue?: T;
              required?: T;
              id?: T;
              blockName?: T;
            };
      };
  submitButtonLabel?: T;
  confirmationType?: T;
  confirmationMessage?: T;
  redirect?:
    | T
    | {
        url?: T;
      };
  emails?:
    | T
    | {
        emailTo?: T;
        cc?: T;
        bcc?: T;
        replyTo?: T;
        emailFrom?: T;
        subject?: T;
        message?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "form-submissions_select".
 */
export interface FormSubmissionsSelect<T extends boolean = true> {
  form?: T;
  submissionData?:
    | T
    | {
        field?: T;
        value?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "search_select".
 */
export interface SearchSelect<T extends boolean = true> {
  title?: T;
  priority?: T;
  doc?: T;
  slug?: T;
  meta?:
    | T
    | {
        title?: T;
        description?: T;
        image?: T;
      };
  categories?:
    | T
    | {
        relationTo?: T;
        categoryID?: T;
        title?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-jobs_select".
 */
export interface PayloadJobsSelect<T extends boolean = true> {
  input?: T;
  taskStatus?: T;
  completedAt?: T;
  totalTried?: T;
  hasError?: T;
  error?: T;
  log?:
    | T
    | {
        executedAt?: T;
        completedAt?: T;
        taskSlug?: T;
        taskID?: T;
        input?: T;
        output?: T;
        state?: T;
        error?: T;
        id?: T;
      };
  taskSlug?: T;
  queue?: T;
  waitUntil?: T;
  processing?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "header".
 */
export interface Header {
  id: number;
  navItems?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: number | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: number | Post;
              } | null);
          url?: string | null;
          label: string;
        };
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer".
 */
export interface Footer {
  id: number;
  navItems?:
    | {
        link: {
          type?: ('reference' | 'custom') | null;
          newTab?: boolean | null;
          reference?:
            | ({
                relationTo: 'pages';
                value: number | Page;
              } | null)
            | ({
                relationTo: 'posts';
                value: number | Post;
              } | null);
          url?: string | null;
          label: string;
        };
        id?: string | null;
      }[]
    | null;
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "header_select".
 */
export interface HeaderSelect<T extends boolean = true> {
  navItems?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "footer_select".
 */
export interface FooterSelect<T extends boolean = true> {
  navItems?:
    | T
    | {
        link?:
          | T
          | {
              type?: T;
              newTab?: T;
              reference?: T;
              url?: T;
              label?: T;
            };
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "TaskSchedulePublish".
 */
export interface TaskSchedulePublish {
  input: {
    type?: ('publish' | 'unpublish') | null;
    locale?: string | null;
    doc?:
      | ({
          relationTo: 'pages';
          value: number | Page;
        } | null)
      | ({
          relationTo: 'posts';
          value: number | Post;
        } | null);
    global?: string | null;
    user?: (number | null) | User;
  };
  output?: unknown;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "BannerBlock".
 */
export interface BannerBlock {
  style: 'info' | 'warning' | 'error' | 'success';
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  id?: string | null;
  blockName?: string | null;
  blockType: 'banner';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "CodeBlock".
 */
export interface CodeBlock {
  language?: ('typescript' | 'javascript' | 'css') | null;
  code: string;
  id?: string | null;
  blockName?: string | null;
  blockType: 'code';
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}