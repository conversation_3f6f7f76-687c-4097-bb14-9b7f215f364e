"use client"

import React from 'react'
import { Check, Circle } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface Step {
  id: string
  title: string
  description: string
}

interface StepIndicatorProps {
  steps: Step[]
  currentStep: number
  completedSteps: number[]
  className?: string
}

export function StepIndicator({ 
  steps, 
  currentStep, 
  completedSteps, 
  className = "" 
}: StepIndicatorProps) {
  return (
    <div className={cn("w-full py-6", className)}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const stepNumber = index + 1
          const isCompleted = completedSteps.includes(stepNumber)
          const isCurrent = currentStep === stepNumber
          const isUpcoming = stepNumber > currentStep

          return (
            <div key={step.id} className="flex items-center flex-1">
              {/* Step Circle */}
              <div className="flex flex-col items-center">
                <div
                  className={cn(
                    "w-10 h-10 flex items-center justify-center border-2 transition-all duration-300",
                    {
                      "bg-ikia-green border-ikia-green text-white": isCompleted,
                      "bg-ikia-brown border-ikia-brown text-white": isCurrent,
                      "bg-gray-100 border-gray-300 text-gray-500": isUpcoming,
                    }
                  )}
                >
                  {isCompleted ? (
                    <Check className="w-5 h-5" />
                  ) : (
                    <span className="text-sm font-semibold">{stepNumber}</span>
                  )}
                </div>
                
                {/* Step Info */}
                <div className="mt-2 text-center max-w-24">
                  <p
                    className={cn(
                      "text-sm font-medium transition-colors duration-300",
                      {
                        "text-ikia-green": isCompleted,
                        "text-ikia-brown": isCurrent,
                        "text-gray-500": isUpcoming,
                      }
                    )}
                  >
                    {step.title}
                  </p>
                  <p className="text-xs text-gray-400 mt-1 hidden sm:block">
                    {step.description}
                  </p>
                </div>
              </div>

              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div className="flex-1 mx-4">
                  <div
                    className={cn(
                      "h-0.5 transition-colors duration-300",
                      {
                        "bg-ikia-green": isCompleted,
                        "bg-ikia-brown": isCurrent && completedSteps.includes(stepNumber - 1),
                        "bg-gray-300": isUpcoming || (!isCompleted && !isCurrent),
                      }
                    )}
                  />
                </div>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}

// Common step configurations for different registration types
export const delegateSteps: Step[] = [
  {
    id: 'personal',
    title: 'Personal Info',
    description: 'Basic details'
  },
  {
    id: 'package',
    title: 'Package',
    description: 'Select package'
  },
  {
    id: 'verification',
    title: 'Verification',
    description: 'Verify identity'
  },
  {
    id: 'payment',
    title: 'Payment',
    description: 'Complete payment'
  }
]

export const vipSteps: Step[] = [
  {
    id: 'personal',
    title: 'Personal Info',
    description: 'Basic details'
  },
  {
    id: 'verification',
    title: 'Verification',
    description: 'Verify identity'
  },
  {
    id: 'payment',
    title: 'Payment',
    description: 'Complete payment'
  }
]

export const sponsorSteps: Step[] = [
  {
    id: 'company',
    title: 'Company Info',
    description: 'Organization details'
  },
  {
    id: 'sponsorship',
    title: 'Sponsorship',
    description: 'Select tier'
  },
  {
    id: 'tickets',
    title: 'Tickets',
    description: 'Allocate tickets'
  },
  {
    id: 'verification',
    title: 'Verification',
    description: 'Verify details'
  },
  {
    id: 'payment',
    title: 'Payment',
    description: 'Complete payment'
  }
]

export const exhibitorSteps: Step[] = [
  {
    id: 'company',
    title: 'Company Info',
    description: 'Business details'
  },
  {
    id: 'exhibition',
    title: 'Exhibition',
    description: 'Booth & services'
  },
  {
    id: 'verification',
    title: 'Verification',
    description: 'Verify details'
  },
  {
    id: 'payment',
    title: 'Payment',
    description: 'Complete payment'
  }
]

export const investorSteps: Step[] = [
  {
    id: 'personal',
    title: 'Personal Info',
    description: 'Contact details'
  },
  {
    id: 'investment',
    title: 'Investment',
    description: 'Investment interests'
  },
  {
    id: 'verification',
    title: 'Verification',
    description: 'Verify identity'
  }
]
