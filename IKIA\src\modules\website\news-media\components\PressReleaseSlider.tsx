'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'

import Link from 'next/link'
import { Card } from '@/components/ui/card'
import { useGetNewsMediaQuery } from '@/lib/api/newsMediaApi'
import { NewsItem } from '../types'
import { getTimeAgo } from '@/lib/utils'

export default function PressReleaseSlider() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  const { data, isLoading, error } = useGetNewsMediaQuery({
    where: {
      category: { equals: 'press-releases' },
      type: { equals: 'featured' },
    },
  })

  const nextSlide = () => {
    if (!isAnimating && data?.docs.length > 1) {
      setIsAnimating(true)
      setCurrentSlide((prev) => (prev + 1) % data.docs.length)
      setTimeout(() => setIsAnimating(false), 1000)
    }
  }

  const goToSlide = (index: number) => {
    if (!isAnimating && index !== currentSlide) {
      setIsAnimating(true)
      setCurrentSlide(index)
      setTimeout(() => setIsAnimating(false), 1000)
    }
  }

  useEffect(() => {
    if (!data?.docs || data.docs.length <= 1) return
    const timer = setInterval(() => {
      nextSlide()
    }, 6000)
    return () => clearInterval(timer)
  }, [data])

  if (isLoading) {
    return (
      <div className="relative">
        <Card className="overflow-hidden">
          <div className="relative h-[500px] bg-gray-200 animate-pulse">
            {/* Image skeleton */}
            <div className="absolute inset-0 bg-gray-300" />

            {/* Gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />

            {/* Text skeleton */}
            <div className="absolute bottom-8 left-8 right-8 text-white space-y-4">
              <div className="w-3/4 h-6 bg-gray-400 rounded" />
              <div className="w-1/2 h-4 bg-gray-500 rounded" />
              <div className="flex items-center space-x-4 mt-3">
                <div className="w-20 h-4 bg-red-500 rounded-full" />
                <div className="w-4 h-4 bg-white rounded-full" />
                <div className="w-20 h-4 bg-gray-400 rounded-full" />
              </div>
            </div>
          </div>
        </Card>

        {/* Slide indicators pulse */}
        <div className="flex justify-center space-x-3 mt-6">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={`w-3 h-3 rounded-full bg-gray-300 ${
                i === 0 ? 'bg-red-600 animate-ping' : 'hover:bg-gray-400'
              }`}
            ></div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="relative">
        <div className="absolute inset-0 flex flex-col items-center justify-center z-10 text-center px-4">
          <h2 className="text-2xl font-semibold text-red-700 mb-2">Oops! Something went wrong.</h2>
          <p className="text-gray-600">
            We couldn’t load the Featured Press Release. Please try again later.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 opacity-60 blur-sm pointer-events-none">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="h-64 rounded-lg relative overflow-hidden animate-pulse bg-gradient-to-r from-red-200 via-red-100 to-white"
            >
              <div className="absolute inset-0 bg-[radial-gradient(circle,_rgba(255,255,255,0.4)_0%,_rgba(255,255,255,0.1)_60%,_transparent_100%)]" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (data?.docs?.length === 0) {
    return (
      <div className="relative">
        {/* Centered Text */}
        <div className="absolute inset-0 flex flex-col items-center justify-center z-10 text-center px-4">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">
            No Featured Press Release are available at the moment.
          </h2>
          <p className="text-gray-600">Please check back later.</p>
        </div>

        {/* Pulse Boxes */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 opacity-60 blur-sm pointer-events-none">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="h-64 rounded-lg relative overflow-hidden animate-pulse bg-gradient-radial from-gray-300 via-gray-200 to-gray-100"
            >
              {/* Radial Gradient Blur Layer */}
              <div className="absolute inset-0 bg-[radial-gradient(circle,_rgba(255,255,255,0.6)_0%,_rgba(255,255,255,0.2)_60%,_rgba(255,255,255,0)_100%)]" />
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="relative">
      <Card className="overflow-hidden">
        <div className="relative h-[500px]">
          {data.docs.map((headline: NewsItem, index: number) => {
            const isActive = index === currentSlide
            const isPrev = index === (currentSlide - 1 + data.docs.length) % data.docs.length
            const isNext = index === (currentSlide + 1) % data.docs.length

            let slideClass = 'absolute inset-0 transition-all duration-1000 ease-in-out'

            if (isActive) {
              slideClass += ' opacity-100 transform translate-x-0 scale-100'
            } else if (isNext) {
              slideClass += ' opacity-0 transform translate-x-full scale-95'
            } else if (isPrev) {
              slideClass += ' opacity-0 transform -translate-x-full scale-95'
            } else {
              slideClass += ' opacity-0 transform translate-x-full scale-95'
            }

            return (
              <Link key={index} href={`/news-media/${headline.category}/${headline.slug}`}>
                <div className={slideClass}>
                  <div className="relative w-full h-full overflow-hidden cursor-pointer">
                    <Image
                      src={headline.image?.url ?? '/placeholder.svg'}
                      alt={headline.title}
                      fill
                      className="object-contain transition-transform duration-1000 ease-out hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
                    <div className="absolute bottom-8 left-8 right-8 text-white">
                      <div
                        className={`transform transition-all duration-1000 delay-300 ${
                          isActive ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
                        }`}
                      >
                        <h3 className="text-3xl font-bold mb-3 leading-tight">{headline.title}</h3>
                        <p className="text-gray-200 mb-3 text-lg leading-relaxed line-clamp-1">
                          {headline.summary || headline.content}
                        </p>
                        <div className="flex items-center space-x-4 text-sm">
                          <span className="bg-red-600 px-3 py-1 rounded-full">
                            {getTimeAgo(headline.createdAt)}
                          </span>
                          <span>•</span>
                          <span className="font-medium">{headline.author}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            )
          })}
        </div>
      </Card>

      {/* Enhanced slide indicators */}
      <div className="flex justify-center space-x-3 mt-6">
        {data.docs.map((_: NewsItem, index: number) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`relative overflow-hidden transition-all duration-500 ${
              index === currentSlide
                ? 'w-8 h-3 bg-red-600 rounded-full'
                : 'w-3 h-3 bg-gray-300 hover:bg-gray-400 rounded-full'
            }`}
          >
            {index === currentSlide && (
              <div className="absolute inset-0 bg-red-700 rounded-full animate-pulse" />
            )}
          </button>
        ))}
      </div>
    </div>
  )
}
