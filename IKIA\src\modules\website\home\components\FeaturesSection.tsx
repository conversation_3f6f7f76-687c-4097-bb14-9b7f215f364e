import { Card, CardContent } from '@/components/ui/card'
import { SkewedContainer } from '@/components/ui/SkewedContainer'
import { IkiaActionButtons } from '@/components/ikia-navbar'
import Image from 'next/image'
import { CheckCircle } from 'lucide-react'
import { featuresButtons } from '../data/buttonConfigs'

export default function FeaturesSection() {
  const thematicAreas = [
    {
      title: 'Indigenous Technologies for Industrialization',
      description:
        'Traditional innovations and technological solutions developed by local communities',
      image: '/assets/iconography/traditional-technology-revised.png',
    },
    {
      title: 'Heritage Sites and Cultural Tourism',
      description:
        'Sacred sites, cultural landmarks, and tourism opportunities rooted in tradition',
      image: '/assets/iconography/heritage-sites-revised.png',
    },
    {
      title: 'Traditional Foods and Local Cuisines for Enhanced Nutrition',
      description: 'Indigenous food systems, nutritional knowledge, and culinary heritage',
      image: '/assets/iconography/traditional-foods-and-cuisines.png',
    },
    {
      title: 'Traditional Medicine for One-Health',
      description: 'Indigenous healing practices, medicinal plants, and traditional healthcare',
      image: '/assets/iconography/traditional-medicine-revised.png',
    },
    {
      title: 'Performing Arts and Creative Economy',
      description: 'Cultural expressions, traditional performances, and artistic heritage',
      image: '/assets/iconography/musicology-icon-revised.png',
    },
    {
      title: (
        <>
          Kenya's Indeginous Knowledge Intellectual Assets <i>sui generis</i> Protection System
        </>
      ),
      description: 'Indigenous Knowledge Management Systems and intellectual property frameworks',
      image: '/assets/iconography/knowledge-systems-revised.png',
    },
  ]

  return (
    <section className="py-8 sm:py-12 lg:py-16 bg-[#FFF8E3]">
      <div className="max-w-6xl mx-auto px-4 sm:px-6">
        {/* Conference Header Section */}
        <div className="text-center mb-8 sm:mb-10">
          {/* Skewer Accent */}
          <div className="flex justify-center mb-4">
            <SkewedContainer variant="outlined" size="sm">
              THEMATIC AREAS
            </SkewedContainer>
          </div>

          <div className="max-w-4xl mx-auto px-4 sm:px-6">
            <h2 className="font-myriad font-bold text-2xl sm:text-3xl lg:text-4xl xl:text-5xl text-black leading-tight mb-3">
              1<sup>st</sup> International Investment Conference and Trade Fair on Indigenous
              Knowledge Intellectual Assets 2025
            </h2>
            <p className="font-myriad text-base sm:text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto mt-10 mb-2">
              Explore six key thematic areas representing Kenya&apos;s rich indigenous knowledge and
              intellectual assets
            </p>
            <p className="font-myriad text-base sm:text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto">
              for economic transformation and sustainable development
            </p>
          </div>
        </div>

        {/* Thematic Areas Grid Section - Clean Minimal Design */}
        <div className="mb-8 px-4 sm:px-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {thematicAreas.map((area, index) => (
              <Card
                key={index}
                className="bg-white border border-gray-200 hover:border-gray-300 transition-all duration-300 hover:shadow-lg group"
              >
                <CardContent className="p-4 text-center space-y-3">
                  <div className="flex justify-center">
                    <div className="w-12 h-12 bg-gradient-to-br from-[#7E2518]/5 to-[#E8B32C]/5 rounded-xl flex items-center justify-center transition-all duration-300 group-hover:from-[#7E2518]/10 group-hover:to-[#E8B32C]/10 group-hover:scale-105">
                      <Image
                        src={area.image}
                        alt={area.title}
                        width={24}
                        height={24}
                        className="w-6 h-6 object-contain"
                      />
                    </div>
                  </div>
                  <h3 className="font-myriad font-semibold text-xl text-gray-900 leading-tight">
                    {area.title}
                  </h3>
                  <p className="font-myriad text-sm text-gray-600 leading-relaxed">
                    {area.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Conference Info Section - Square Container */}
        <div className="bg-white/70 p-6 border border-[#A0503A]/30">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-center">
            {/* Benefits Column */}
            <div className="space-y-3">
              <h3 className="font-myriad font-bold text-2xl text-[#7E2518] mb-3">
                Conference Benefits
              </h3>
              <div className="space-y-2">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-[#E8B32C] mt-0.5 flex-shrink-0" />
                  <p className="font-myriad text-sm text-gray-700">
                    Employment and wealth creation opportunities
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-[#E8B32C] mt-0.5 flex-shrink-0" />
                  <p className="font-myriad text-sm text-gray-700">
                    Poverty alleviation through indigenous knowledge
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-[#E8B32C] mt-0.5 flex-shrink-0" />
                  <p className="font-myriad text-sm text-gray-700">
                    Improved biodiversity management practices
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-[#E8B32C] mt-0.5 flex-shrink-0" />
                  <p className="font-myriad text-sm text-gray-700">
                    Achievement of double-digit GDP growth
                  </p>
                </div>
              </div>
            </div>

            {/* CTA Column */}
            <div className="text-center lg:text-right">
              <h3 className="font-myriad font-bold text-2xl text-[#7E2518] mb-4">
                Join the Conference
              </h3>
              <IkiaActionButtons
                buttons={featuresButtons}
                size="large"
                showOnMobile={true}
                layout="vertical"
                fullWidth={true}
                className="gap-3"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
