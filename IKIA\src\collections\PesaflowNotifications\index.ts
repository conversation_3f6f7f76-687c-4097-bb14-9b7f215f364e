import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'

export const PesaflowNotifications: CollectionConfig = {
  slug: 'pesaflow-notifications',
  access: {
    admin: authenticated,
    create: () => true, // Allow Pesaflow to create notifications
    delete: authenticated,
    read: authenticated,
    update: authenticated,
  },
  admin: {
    defaultColumns: ['payment_reference', 'status', 'amount_paid', 'invoice_number', 'createdAt'],
    useAsTitle: 'payment_reference',
    group: 'Payment System',
  },

  fields: [
    {
      name: 'payment_channel',
      type: 'text',
      required: true,
      admin: {
        description: 'Payment channel used (M-Pesa, Bank Transfer, etc.)',
      },
    },
    {
      name: 'client_invoice_ref',
      type: 'text',
      required: true,
      admin: {
        description: 'Client invoice reference from Pesaflow',
      },
    },
    {
      name: 'payment_reference',
      type: 'text',
      required: false, // Can be undefined in some IPN calls
      unique: false, // Remove unique constraint to handle missing references
      admin: {
        description: 'Payment reference from Pesaflow (may be generated if missing)',
      },
    },
    {
      name: 'payment_date',
      type: 'date',
      required: true,
      admin: {
        description: 'Date when payment was made',
      },
    },
    {
      name: 'inserted_at',
      type: 'date',
      required: true,
      admin: {
        description: 'Date when payment was inserted in Pesaflow system',
      },
    },
    {
      name: 'currency',
      type: 'text',
      required: true,
      defaultValue: 'KES',
      admin: {
        description: 'Payment currency',
      },
    },
    {
      name: 'amount_paid',
      type: 'number',
      required: true,
      admin: {
        description: 'Amount paid by customer',
      },
    },
    {
      name: 'invoice_amount',
      type: 'number',
      required: true,
      admin: {
        description: 'Original invoice amount',
      },
    },
    {
      name: 'last_payment_amount',
      type: 'number',
      required: true,
      admin: {
        description: 'Last payment amount',
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        { label: 'Settled', value: 'settled' },
        { label: 'Pending', value: 'pending' },
        { label: 'Failed', value: 'failed' },
        { label: 'Cancelled', value: 'cancelled' },
      ],
      admin: {
        description: 'Payment status from Pesaflow',
      },
    },
    {
      name: 'invoice_number',
      type: 'text',
      required: false,
      admin: {
        description: 'Invoice number from Pesaflow',
      },
    },
    {
      name: 'secure_hash',
      type: 'text',
      required: true,
      admin: {
        description: 'Security hash from Pesaflow for verification',
      },
    },
    // Relationships to existing collections
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      required: false,
      admin: {
        description: 'User who made the payment',
      },
    },
    {
      name: 'service_package',
      type: 'relationship',
      relationTo: 'delegatepackages',
      required: false,
      admin: {
        description: 'Delegate package being purchased',
      },
    },
    // Processing information
    {
      name: 'processing_status',
      type: 'select',
      required: true,
      defaultValue: 'pending',
      options: [
        { label: 'Pending Processing', value: 'pending' },
        { label: 'Processed Successfully', value: 'processed' },
        { label: 'Processing Failed', value: 'failed' },
        { label: 'Duplicate/Ignored', value: 'duplicate' },
        { label: 'Invalid Notification', value: 'invalid' },
      ],
      admin: {
        description: 'Processing status of this notification',
      },
    },
    {
      name: 'processed_at',
      type: 'date',
      required: false,
      admin: {
        description: 'When this notification was processed',
      },
    },
    {
      name: 'processing_notes',
      type: 'textarea',
      required: false,
      admin: {
        description: 'Notes about notification processing (errors, warnings, etc.)',
      },
    },
    {
      name: 'hash_verified',
      type: 'checkbox',
      required: false,
      admin: {
        description: 'Whether the secure hash was successfully verified',
      },
    },
    {
      name: 'ip_address',
      type: 'text',
      required: false,
      admin: {
        description: 'IP address of notification sender',
      },
    },
    {
      name: 'user_agent',
      type: 'text',
      required: false,
      admin: {
        description: 'User agent of notification sender',
      },
    },
    // Raw payload for debugging
    {
      name: 'raw_payload',
      type: 'json',
      required: true,
      admin: {
        description: 'Complete raw notification payload from Pesaflow',
      },
    },
  ],
  timestamps: true,
}
