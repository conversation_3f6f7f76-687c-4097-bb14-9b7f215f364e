import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'

export const Programs: CollectionConfig = {
  slug: 'programs',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'date', 'type', 'venue', 'isFeatured'],
    group: 'Conference',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'Program Title',
    },
    {
      name: 'description',
      type: 'richText',
      required: true,
      label: 'Program Description',
    },
    {
      name: 'type',
      type: 'relationship',
      relationTo: 'program-types',
      required: true,
      label: 'Program Type',
      admin: {
        description: 'Type of program/session',
      },
    },
    {
      name: 'thematicArea',
      type: 'relationship',
      relationTo: 'thematic-areas',
      label: 'Thematic Area',
      admin: {
        description: 'The thematic area this program belongs to (used for filtering)',
      },
    },
    {
      name: 'date',
      type: 'date',
      required: true,
      label: 'Program Date',
      admin: {
        description: 'Conference dates: November 19-21, 2025',
        date: {
          pickerAppearance: 'dayOnly',
        },
      },
      validate: (val: Date | null | undefined) => {
        if (!val) return 'Date is required'

        const programDate = new Date(val)
        const startDate = new Date('2025-11-19')
        const endDate = new Date('2025-11-21')

        if (programDate < startDate || programDate > endDate) {
          return 'Program date must be between November 19-21, 2025'
        }

        return true
      },
    },
    {
      name: 'startTime',
      type: 'text',
      label: 'Start Time',
      admin: {
        description: 'Format: HH:MM (e.g., 09:00)',
      },
    },
    {
      name: 'endTime',
      type: 'text',
      label: 'End Time',
      admin: {
        description: 'Format: HH:MM (e.g., 10:30)',
      },
    },

    {
      name: 'venue',
      type: 'text',
      required: true,
      label: 'Venue/Location',
      admin: {
        description: 'Where the program will take place',
      },
    },
    {
      name: 'speakers',
      type: 'relationship',
      relationTo: 'speakers',
      hasMany: true,
      label: 'Speakers',
      admin: {
        description: 'Speakers participating in this program',
      },
    },
    {
      name: 'capacity',
      type: 'number',
      label: 'Maximum Capacity',
      admin: {
        description: 'Maximum number of attendees (optional)',
      },
    },
    {
      name: 'isFeatured',
      type: 'checkbox',
      label: 'Featured Program',
      defaultValue: false,
      admin: {
        description: 'Show this program in the featured section on homepage',
      },
    },
    {
      name: 'isParallel',
      type: 'checkbox',
      label: 'Parallel Session',
      defaultValue: false,
      admin: {
        description: 'This program runs parallel to other sessions',
      },
    },
    {
      name: 'parallelGroup',
      type: 'text',
      label: 'Parallel Group',
      admin: {
        description: 'Group identifier for parallel sessions (e.g., "morning-sessions")',
        condition: (data) => data.isParallel,
      },
    },
    {
      name: 'requirements',
      type: 'textarea',
      label: 'Requirements',
      admin: {
        description: 'Any special requirements or prerequisites',
        rows: 3,
      },
    },
    {
      name: 'materials',
      type: 'upload',
      relationTo: 'media',
      label: 'Supporting Materials',
      hasMany: true,
      admin: {
        description: 'Documents, presentations, or other materials',
      },
    },
    {
      name: 'registrationRequired',
      type: 'checkbox',
      label: 'Registration Required',
      defaultValue: false,
      admin: {
        description: 'Whether separate registration is required for this program',
      },
    },
    {
      name: 'tags',
      type: 'array',
      label: 'Tags',
      fields: [
        {
          name: 'tag',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        description: 'Tags for categorizing and filtering programs',
      },
    },
    ...slugField(),
  ],
  timestamps: true,
}

export default Programs
