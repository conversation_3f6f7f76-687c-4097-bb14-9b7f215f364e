'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, Card<PERSON>itle } from '@/components/ui/card'
import { CheckCircle, Mail, Phone } from 'lucide-react'

export const AccessibilityStatement: React.FC = () => {
  return (
    <div className="max-w-4xl mx-auto px-6 py-12">
      <Card className="border-2 border-[#A0503A]/30">
        <CardHeader className="bg-[#FFF8E3]">
          <CardTitle className="font-myriad font-bold text-2xl text-[#7E2518] flex items-center gap-3">
            <CheckCircle className="w-6 h-6 text-[#159147]" aria-hidden="true" />
            Accessibility Statement
          </CardTitle>
        </CardHeader>
        <CardContent className="p-8 space-y-6">
          <div className="space-y-4">
            <h2 className="font-myriad font-bold text-xl text-[#7E2518]">
              Our Commitment to Accessibility
            </h2>
            <p className="font-myriad text-base text-gray-700 leading-relaxed">
              1<sup className="lowercase">st</sup> International Investment Conference & Trade Fair
              on Indigenous Knowledge Intellectual Assets is committed to ensuring digital
              accessibility for people with disabilities. We are continually improving the user
              experience for everyone and applying the relevant accessibility standards.
            </p>
          </div>

          <div className="space-y-4">
            <h3 className="font-myriad font-bold text-lg text-[#7E2518]">Conformance Status</h3>
            <p className="font-myriad text-base text-gray-700 leading-relaxed">
              This website aims to conform to the Web Content Accessibility Guidelines (WCAG) 2.1
              Level AA. These guidelines explain how to make web content more accessible to people
              with disabilities.
            </p>
          </div>

          <div className="space-y-4">
            <h3 className="font-myriad font-bold text-lg text-[#7E2518]">Accessibility Features</h3>
            <ul className="space-y-2">
              {[
                'Keyboard navigation support',
                'Screen reader compatibility',
                'High contrast color schemes',
                'Descriptive alt text for images',
                'Semantic HTML structure',
                'Focus indicators for interactive elements',
                'Multi-language support via Google Translate',
                'Resizable text up to 200% without loss of functionality',
              ].map((feature, index) => (
                <li key={index} className="flex items-start gap-3">
                  <CheckCircle
                    className="w-4 h-4 text-[#159147] mt-1 flex-shrink-0"
                    aria-hidden="true"
                  />
                  <span className="font-myriad text-sm text-gray-700">{feature}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="font-myriad font-bold text-lg text-[#7E2518]">
              Feedback and Contact Information
            </h3>
            <p className="font-myriad text-base text-gray-700 leading-relaxed">
              We welcome your feedback on the accessibility of the IKIA Investment Conference
              website. Please let us know if you encounter accessibility barriers:
            </p>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3 p-4 bg-[#159147]/5 rounded-lg border border-[#159147]/20">
                <Mail className="w-5 h-5 text-[#159147]" aria-hidden="true" />
                <div>
                  <div className="font-myriad font-semibold text-sm text-[#7E2518]">Email</div>
                  <a
                    href="mailto:<EMAIL>"
                    className="font-myriad text-sm text-[#159147] hover:underline focus:outline-none focus:ring-2 focus:ring-[#E8B32C] rounded"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
              <div className="flex items-center gap-3 p-4 bg-[#E8B32C]/5 rounded-lg border border-[#E8B32C]/20">
                <Phone className="w-5 h-5 text-[#C86E36]" aria-hidden="true" />
                <div>
                  <div className="font-myriad font-semibold text-sm text-[#7E2518]">Phone</div>
                  <a
                    href="tel:+254700000000"
                    className="font-myriad text-sm text-[#C86E36] hover:underline focus:outline-none focus:ring-2 focus:ring-[#E8B32C] rounded"
                  >
                    +254 700 000 000
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="font-myriad font-bold text-lg text-[#7E2518]">
              Technical Specifications
            </h3>
            <p className="font-myriad text-sm text-gray-600 leading-relaxed">
              Accessibility of the IKIA Investment Conference website relies on the following
              technologies:
            </p>
            <ul className="font-myriad text-sm text-gray-600 space-y-1 ml-4">
              <li>• HTML5</li>
              <li>• CSS3</li>
              <li>• JavaScript (ES6+)</li>
              <li>• ARIA (Accessible Rich Internet Applications)</li>
            </ul>
          </div>

          <div className="bg-[#7E2518]/5 p-4 rounded-lg border border-[#7E2518]/20">
            <p className="font-myriad text-xs text-gray-600 leading-relaxed">
              This statement was created on {new Date().toLocaleDateString()} and is reviewed
              regularly to ensure accuracy.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
