# SSL Certificate Setup Guide for ikiaconference.or.ke

This guide provides step-by-step instructions for setting up SSL certificates for the IKIA Conference website at `https://ikiaconference.or.ke/`.

## Table of Contents

1. [SSL Certificate Options](#ssl-certificate-options)
2. [Let's Encrypt (Free SSL)](#lets-encrypt-free-ssl)
3. [Commercial SSL Certificate](#commercial-ssl-certificate)
4. [SSL Configuration Verification](#ssl-configuration-verification)
5. [Troubleshooting](#troubleshooting)

## SSL Certificate Options

### Option 1: Let's Encrypt (Recommended - Free)
- **Cost**: Free
- **Validity**: 90 days (auto-renewable)
- **Trust**: Trusted by all major browsers
- **Best for**: Most websites including production

### Option 2: Commercial SSL Certificate
- **Cost**: Varies ($50-$500+ per year)
- **Validity**: 1-2 years
- **Features**: Extended validation, warranty, support
- **Best for**: High-traffic commercial sites

## Let's Encrypt (Free SSL)

### Prerequisites
- Domain `ikiaconference.or.ke` must point to your server
- Apache web server installed and running
- Port 80 and 443 open in firewall

### Step 1: Install Certbot

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install certbot python3-certbot-apache -y

# CentOS/RHEL 8+
sudo dnf install certbot python3-certbot-apache -y

# CentOS/RHEL 7
sudo yum install certbot python2-certbot-apache -y
```

### Step 2: Obtain SSL Certificate

```bash
# Stop Apache temporarily (if running)
sudo systemctl stop apache2

# Obtain certificate using standalone mode
sudo certbot certonly --standalone -d ikiaconference.or.ke -d www.ikiaconference.or.ke

# Or use webroot mode if Apache is running
sudo certbot certonly --webroot -w /home/<USER>/public_html -d ikiaconference.or.ke -d www.ikiaconference.or.ke
```

### Step 3: Update Apache Configuration

The SSL certificate files will be created at:
- Certificate: `/etc/letsencrypt/live/ikiaconference.or.ke/fullchain.pem`
- Private Key: `/etc/letsencrypt/live/ikiaconference.or.ke/privkey.pem`

Update the Apache configuration file:

```bash
sudo nano /etc/apache2/sites-available/ikia-conference.conf
```

Replace the SSL certificate paths in the HTTPS virtual host:

```apache
# SSL Configuration
SSLEngine on
SSLCertificateFile /etc/letsencrypt/live/ikiaconference.or.ke/fullchain.pem
SSLCertificateKeyFile /etc/letsencrypt/live/ikiaconference.or.ke/privkey.pem
```

### Step 4: Enable SSL Module and Site

```bash
# Enable SSL module
sudo a2enmod ssl

# Enable the site
sudo a2ensite ikia-conference.conf

# Test Apache configuration
sudo apache2ctl configtest

# Restart Apache
sudo systemctl restart apache2
```

### Step 5: Set Up Auto-Renewal

```bash
# Test renewal
sudo certbot renew --dry-run

# Add to crontab for automatic renewal
sudo crontab -e

# Add this line to renew certificates twice daily
0 12 * * * /usr/bin/certbot renew --quiet && /usr/bin/systemctl reload apache2
```

## Commercial SSL Certificate

### Step 1: Generate Certificate Signing Request (CSR)

```bash
# Create private key
sudo openssl genrsa -out /etc/ssl/private/ikiaconference.or.ke.key 2048

# Generate CSR
sudo openssl req -new -key /etc/ssl/private/ikiaconference.or.ke.key -out /etc/ssl/certs/ikiaconference.or.ke.csr

# When prompted, enter:
# Country Name: KE
# State: Your State
# City: Your City
# Organization: Your Organization
# Organizational Unit: IT Department
# Common Name: ikiaconference.or.ke
# Email: <EMAIL>
# Challenge password: (leave blank)
# Optional company name: (leave blank)
```

### Step 2: Submit CSR to Certificate Authority

1. Copy the CSR content:
   ```bash
   cat /etc/ssl/certs/ikiaconference.or.ke.csr
   ```

2. Submit to your chosen CA (DigiCert, GlobalSign, etc.)

3. Complete domain validation process

### Step 3: Install Certificate

Once you receive the certificate files from your CA:

```bash
# Copy certificate files to server
sudo cp your-certificate.crt /etc/ssl/certs/ikiaconference.or.ke.crt
sudo cp intermediate-certificate.crt /etc/ssl/certs/ikiaconference.or.ke-chain.crt

# Set proper permissions
sudo chmod 644 /etc/ssl/certs/ikiaconference.or.ke.crt
sudo chmod 644 /etc/ssl/certs/ikiaconference.or.ke-chain.crt
sudo chmod 600 /etc/ssl/private/ikiaconference.or.ke.key
```

The Apache configuration is already set up for commercial certificates in the provided `apache-vhost.conf` file.

## SSL Configuration Verification

### Step 1: Test SSL Configuration

```bash
# Test Apache configuration
sudo apache2ctl configtest

# Check SSL certificate
sudo openssl x509 -in /etc/letsencrypt/live/ikiaconference.or.ke/fullchain.pem -text -noout

# Test SSL connection
openssl s_client -connect ikiaconference.or.ke:443 -servername ikiaconference.or.ke
```

### Step 2: Online SSL Testing

Visit these online tools to verify your SSL setup:
- [SSL Labs SSL Test](https://www.ssllabs.com/ssltest/)
- [SSL Checker](https://www.sslshopper.com/ssl-checker.html)

### Step 3: Verify HTTPS Redirect

```bash
# Test HTTP to HTTPS redirect
curl -I http://ikiaconference.or.ke

# Should return 301 redirect to https://
```

## DNS Configuration

Ensure your DNS records are properly configured:

```
# A Records
ikiaconference.or.ke.     IN  A     YOUR_SERVER_IP
www.ikiaconference.or.ke. IN  A     YOUR_SERVER_IP

# Optional: AAAA records for IPv6
ikiaconference.or.ke.     IN  AAAA  YOUR_SERVER_IPv6
www.ikiaconference.or.ke. IN  AAAA  YOUR_SERVER_IPv6

# CAA Record (optional but recommended)
ikiaconference.or.ke.     IN  CAA   0 issue "letsencrypt.org"
```

## Security Headers Verification

The Apache configuration includes security headers. Verify they're working:

```bash
# Check security headers
curl -I https://ikiaconference.or.ke

# Should include headers like:
# Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
# X-Content-Type-Options: nosniff
# X-Frame-Options: DENY
```

## Troubleshooting

### Common Issues

1. **Certificate not trusted**
   ```bash
   # Check certificate chain
   openssl verify -CAfile /etc/ssl/certs/ca-certificates.crt /etc/letsencrypt/live/ikiaconference.or.ke/fullchain.pem
   ```

2. **Mixed content warnings**
   - Ensure all resources (images, CSS, JS) use HTTPS URLs
   - Update any hardcoded HTTP links in your application

3. **SSL handshake failures**
   ```bash
   # Check SSL protocols and ciphers
   nmap --script ssl-enum-ciphers -p 443 ikiaconference.or.ke
   ```

4. **Certificate renewal issues**
   ```bash
   # Check certbot logs
   sudo tail -f /var/log/letsencrypt/letsencrypt.log
   
   # Manual renewal
   sudo certbot renew --force-renewal
   ```

### Apache SSL Errors

1. **SSL module not enabled**
   ```bash
   sudo a2enmod ssl
   sudo systemctl restart apache2
   ```

2. **Certificate file permissions**
   ```bash
   sudo chown root:root /etc/ssl/private/ikiaconference.or.ke.key
   sudo chmod 600 /etc/ssl/private/ikiaconference.or.ke.key
   ```

3. **Virtual host conflicts**
   ```bash
   # Check enabled sites
   sudo apache2ctl -S
   
   # Disable conflicting sites
   sudo a2dissite 000-default
   ```

## Maintenance

### Regular Tasks

1. **Monitor certificate expiration**
   ```bash
   # Check expiration date
   openssl x509 -in /etc/letsencrypt/live/ikiaconference.or.ke/fullchain.pem -noout -dates
   ```

2. **Update security headers** as needed based on security best practices

3. **Monitor SSL Labs rating** and address any issues

4. **Keep Apache and OpenSSL updated**
   ```bash
   sudo apt update && sudo apt upgrade apache2 openssl
   ```

## Support

For additional help:
- Let's Encrypt Community: https://community.letsencrypt.org/
- Apache SSL Documentation: https://httpd.apache.org/docs/2.4/ssl/
- Mozilla SSL Configuration Generator: https://ssl-config.mozilla.org/
