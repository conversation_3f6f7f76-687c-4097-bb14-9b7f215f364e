import type { Payload } from 'payload'

interface EmailCredentials {
  email: string
  password: string
  name: string
  packageName: string
  loginUrl: string
}

interface WelcomeEmailData {
  user: {
    name: string
    email: string
  }
  credentials: {
    email: string
    temporaryPassword: string
  }
  package: {
    name: string
    price: number
    currency: string
  }
  loginUrl: string
  supportEmail: string
}

export class EmailService {
  private payload: Payload

  constructor(payload: Payload) {
    this.payload = payload
  }

  /**
   * Generate a temporary password for new users
   */
  generateTemporaryPassword(): string {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789'
    let password = ''
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return password
  }

  /**
   * Generate a 6-character password with uppercase letters and numbers only
   */
  generate6CharPassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let password = ''
    for (let i = 0; i < 6; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return password
  }

  /**
   * Send welcome email with login credentials to new user
   */
  async sendWelcomeEmailWithCredentials(data: WelcomeEmailData): Promise<boolean> {
    try {
      const emailHTML = this.generateWelcomeEmailHTML(data)
      const emailSubject = `Welcome! Your ${data.package.name} Account is Ready`

      await this.payload.sendEmail({
        to: data.user.email,
        subject: emailSubject,
        html: emailHTML,
      })

      console.log(`Welcome email sent to ${data.user.email}`)
      return true
    } catch (error) {
      console.error('Failed to send welcome email:', error)
      return false
    }
  }

  /**
   * Generate HTML content for welcome email
   */
  private generateWelcomeEmailHTML(data: WelcomeEmailData): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Your New Account</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #007bff; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .credentials { background: #fff; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }
        .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to Your New Account!</h1>
        </div>
        
        <div class="content">
            <h2>Hello ${data.user.name},</h2>
            
            <p>Congratulations! Your account has been successfully created and your <strong>${data.package.name}</strong> package is ready for payment.</p>
            
            <div class="credentials">
                <h3>Your Login Credentials:</h3>
                <p><strong>Email:</strong> ${data.credentials.email}</p>
                <p><strong>Temporary Password:</strong> <code>${data.credentials.temporaryPassword}</code></p>
            </div>
            
            <div class="warning">
                <strong>Important:</strong> Please change your password after your first login for security purposes.
            </div>
            
            <h3>Package Details:</h3>
            <ul>
                <li><strong>Package:</strong> ${data.package.name}</li>
                <li><strong>Price:</strong> ${data.package.currency} ${data.package.price.toLocaleString()}</li>
                <li><strong>Status:</strong> Pending Payment</li>
            </ul>
            
            <p>To complete your registration and activate your package, please:</p>
            <ol>
                <li>Complete your payment (you should have been redirected to the payment page)</li>
                <li>Once payment is confirmed, log in to your account using the credentials above</li>
                <li>Change your temporary password</li>
                <li>Start using your new package!</li>
            </ol>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="${data.loginUrl}" class="button">Login to Your Account</a>
            </div>
            
            <h3>Need Help?</h3>
            <p>If you have any questions or need assistance, please contact our support team at <a href="mailto:${data.supportEmail}">${data.supportEmail}</a></p>
            
            <p>Thank you for choosing our services!</p>
        </div>
        
        <div class="footer">
            <p>This email was sent automatically. Please do not reply to this email.</p>
            <p>If you did not request this account, please contact our support team immediately.</p>
        </div>
    </div>
</body>
</html>
    `
  }

  /**
   * Send payment confirmation email
   */
  async sendPaymentConfirmationEmail(
    userEmail: string,
    data: {
      userName: string
      packageName: string
      amount: number
      currency: string
      invoiceNumber: string
      transactionId: string
    },
  ): Promise<boolean> {
    try {
      const emailHTML = this.generatePaymentConfirmationHTML(data)
      const emailSubject = `Payment Confirmed - ${data.packageName}`

      await this.payload.sendEmail({
        to: userEmail,
        subject: emailSubject,
        html: emailHTML,
      })

      console.log(`Payment confirmation email sent to ${userEmail}`)
      return true
    } catch (error) {
      console.error('Failed to send payment confirmation email:', error)
      return false
    }
  }

  /**
   * Generate HTML content for payment confirmation email
   */
  private generatePaymentConfirmationHTML(data: {
    userName: string
    packageName: string
    amount: number
    currency: string
    invoiceNumber: string
    transactionId: string
  }): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Confirmation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #28a745; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 4px; margin: 20px 0; }
        .details { background: #fff; padding: 15px; border: 1px solid #ddd; margin: 20px 0; }
        .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Payment Confirmed!</h1>
        </div>
        
        <div class="content">
            <h2>Hello ${data.userName},</h2>
            
            <div class="success">
                <strong>Great news!</strong> Your payment has been successfully processed and your <strong>${data.packageName}</strong> is now active.
            </div>
            
            <div class="details">
                <h3>Payment Details:</h3>
                <p><strong>Package:</strong> ${data.packageName}</p>
                <p><strong>Amount Paid:</strong> ${data.currency} ${data.amount.toLocaleString()}</p>
                <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
                <p><strong>Transaction ID:</strong> ${data.transactionId}</p>
                <p><strong>Payment Date:</strong> ${new Date().toLocaleDateString()}</p>
            </div>
            
            <p>Your package is now active and you can start using all the included features.</p>
            
            <p>Thank you for your business!</p>
        </div>
        
        <div class="footer">
            <p>This is an automated confirmation email.</p>
            <p>Keep this email for your records.</p>
        </div>
    </div>
</body>
</html>
    `
  }

  /**
   * Send payment failure notification
   */
  async sendPaymentFailureEmail(
    userEmail: string,
    data: {
      userName: string
      packageName: string
      amount: number
      currency: string
      invoiceNumber: string
      reason?: string
    },
  ): Promise<boolean> {
    try {
      const emailHTML = `
        <h2>Payment Failed</h2>
        <p>Hello ${data.userName},</p>
        <p>Unfortunately, your payment for ${data.packageName} (${data.currency} ${data.amount}) could not be processed.</p>
        <p><strong>Invoice:</strong> ${data.invoiceNumber}</p>
        ${data.reason ? `<p><strong>Reason:</strong> ${data.reason}</p>` : ''}
        <p>Please try again or contact support if the issue persists.</p>
      `

      await this.payload.sendEmail({
        to: userEmail,
        subject: `Payment Failed - ${data.packageName}`,
        html: emailHTML,
      })

      console.log(`Payment failure email sent to ${userEmail}`)
      return true
    } catch (error) {
      console.error('Failed to send payment failure email:', error)
      return false
    }
  }
}

export default EmailService
