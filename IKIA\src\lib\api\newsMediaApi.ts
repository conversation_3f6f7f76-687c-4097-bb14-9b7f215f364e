// services/newsMediaApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { serializeParams } from '@/lib/serialize'

export const newsMediaApi = createApi({
  reducerPath: 'newsMediaApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  endpoints: (builder) => ({
    getNewsMedia: builder.query<any, Record<string, any>>({
      query: (params = {}) => {
        const queryString = serializeParams(params)
        return `news-items?${queryString.toString()}`
      },
    }),
  }),
})

export const { useGetNewsMediaQuery } = newsMediaApi
