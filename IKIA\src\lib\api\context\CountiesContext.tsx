'use client'

import React, { createContext, useContext, useReducer, useCallback, useMemo, useEffect } from 'react'
import { countiesApi, type County, type CountiesResponse } from '../counties'

// Types
interface CountiesState {
  counties: County[]
  activeCounties: County[]
  loading: boolean
  error: string | null
  totalCounties: number
  initialized: boolean
}

type CountiesAction =
  | { type: 'FETCH_START' }
  | { type: 'FETCH_SUCCESS'; payload: CountiesResponse }
  | { type: 'FETCH_ERROR'; payload: string }

// Context
const CountiesContext = createContext<CountiesState | null>(null)
const CountiesDispatchContext = createContext<React.Dispatch<CountiesAction> | null>(null)

// Reducer
function countiesReducer(state: CountiesState, action: CountiesAction): CountiesState {
  switch (action.type) {
    case 'FETCH_START':
      return {
        ...state,
        loading: true,
        error: null,
      }
    case 'FETCH_SUCCESS':
      return {
        ...state,
        loading: false,
        error: null,
        counties: action.payload.counties,
        activeCounties: action.payload.counties.filter(county => county.isActive),
        totalCounties: action.payload.totalCounties,
        initialized: true,
      }
    case 'FETCH_ERROR':
      return {
        ...state,
        loading: false,
        error: action.payload,
        initialized: true,
      }
    default:
      return state
  }
}

// Initial state
const initialState: CountiesState = {
  counties: [],
  activeCounties: [],
  loading: false,
  error: null,
  totalCounties: 0,
  initialized: false,
}

// Provider component
export function CountiesProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(countiesReducer, initialState)

  // Memoized fetch function to prevent recreation on every render
  const fetchCounties = useCallback(async () => {
    try {
      dispatch({ type: 'FETCH_START' })
      const response = await countiesApi.getCounties({ limit: 100 }) // Get all counties
      dispatch({ type: 'FETCH_SUCCESS', payload: response })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch counties'
      dispatch({ type: 'FETCH_ERROR', payload: errorMessage })
    }
  }, [])

  // Fetch counties on mount
  useEffect(() => {
    if (!state.initialized) {
      fetchCounties()
    }
  }, [fetchCounties, state.initialized])

  // Memoized context values to prevent unnecessary re-renders
  const contextValue = useMemo(() => state, [state])
  const dispatchValue = useMemo(() => dispatch, [])

  return (
    <CountiesContext.Provider value={contextValue}>
      <CountiesDispatchContext.Provider value={dispatchValue}>
        {children}
      </CountiesDispatchContext.Provider>
    </CountiesContext.Provider>
  )
}

// Custom hooks
export function useCounties() {
  const context = useContext(CountiesContext)
  if (context === null) {
    throw new Error('useCounties must be used within a CountiesProvider')
  }
  return context
}

export function useCountiesDispatch() {
  const context = useContext(CountiesDispatchContext)
  if (context === null) {
    throw new Error('useCountiesDispatch must be used within a CountiesProvider')
  }
  return context
}

// Convenience hooks
export function useActiveCounties() {
  const { activeCounties, loading, error } = useCounties()
  return { counties: activeCounties, loading, error }
}

export function useCountiesActions() {
  const dispatch = useCountiesDispatch()
  
  const refetch = useCallback(async () => {
    try {
      dispatch({ type: 'FETCH_START' })
      const response = await countiesApi.getCounties({ limit: 100 })
      dispatch({ type: 'FETCH_SUCCESS', payload: response })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch counties'
      dispatch({ type: 'FETCH_ERROR', payload: errorMessage })
    }
  }, [dispatch])

  return { refetch }
}
