#!/usr/bin/env node

/**
 * Force Garbage Collection Script
 * Aggressively triggers garbage collection via API endpoint
 */

const BASE_URL = 'http://localhost:3000'

async function forceGC() {
  console.log('🧹 FORCING AGGRESSIVE GARBAGE COLLECTION...')
  
  try {
    // Create an endpoint that forces GC
    const gcEndpoint = `
import type { NextRequest } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    console.log('🧹 Manual garbage collection triggered')
    
    // Force multiple GC cycles
    if (global.gc) {
      for (let i = 0; i < 5; i++) {
        global.gc()
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
    
    const memory = process.memoryUsage()
    
    return Response.json({
      success: true,
      message: 'Garbage collection completed',
      memory: {
        heapUsed: Math.round(memory.heapUsed / 1024 / 1024) + ' MB',
        heapTotal: Math.round(memory.heapTotal / 1024 / 1024) + ' MB',
        external: Math.round(memory.external / 1024 / 1024) + ' MB',
        arrayBuffers: Math.round(memory.arrayBuffers / 1024 / 1024) + ' MB'
      }
    })
  } catch (error) {
    return Response.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
`
    
    // Write the GC endpoint
    const fs = require('fs')
    const path = require('path')
    
    const gcEndpointPath = path.join(process.cwd(), 'src/app/api/force-gc/route.ts')
    const gcDir = path.dirname(gcEndpointPath)
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(gcDir)) {
      fs.mkdirSync(gcDir, { recursive: true })
    }
    
    fs.writeFileSync(gcEndpointPath, gcEndpoint)
    console.log('✅ Created force-gc endpoint')
    
    // Wait for Next.js to compile the new endpoint
    console.log('⏳ Waiting for Next.js to compile...')
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // Call the GC endpoint multiple times
    for (let i = 0; i < 3; i++) {
      console.log(`🔄 GC attempt ${i + 1}/3...`)
      
      try {
        const response = await fetch(`${BASE_URL}/api/force-gc`)
        const result = await response.json()
        
        if (result.success) {
          console.log('✅ GC successful:', result.memory)
        } else {
          console.log('❌ GC failed:', result.error)
        }
      } catch (error) {
        console.log(`❌ GC attempt ${i + 1} failed:`, error.message)
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
    
    // Clean up the endpoint
    fs.unlinkSync(gcEndpointPath)
    fs.rmdirSync(gcDir)
    console.log('🧹 Cleaned up temporary endpoint')
    
  } catch (error) {
    console.error('❌ Force GC failed:', error.message)
  }
}

async function checkMemoryAfterGC() {
  console.log('\n📊 Checking memory after GC...')
  
  try {
    const response = await fetch(`${BASE_URL}/api/health-check`)
    const data = await response.json()
    
    console.log('💾 Current Memory:')
    console.log(`   Heap Used: ${Math.round(data.memory.heapUsed / 1024 / 1024)} MB`)
    console.log(`   Heap Total: ${Math.round(data.memory.heapTotal / 1024 / 1024)} MB`)
    console.log(`   Heap Usage: ${Math.round((data.memory.heapUsed / data.memory.heapTotal) * 100)}%`)
    console.log(`   External: ${Math.round(data.memory.external / 1024 / 1024)} MB`)
    console.log(`   Array Buffers: ${Math.round(data.memory.arrayBuffers / 1024 / 1024)} MB`)
    
    const heapPercent = (data.memory.heapUsed / data.memory.heapTotal) * 100
    const arrayBuffersMB = data.memory.arrayBuffers / 1024 / 1024
    
    if (heapPercent > 90) {
      console.log('\n🚨 STILL CRITICAL: Heap usage > 90%')
      console.log('   RECOMMENDATION: Kill and restart the server process')
    } else if (heapPercent > 75) {
      console.log('\n⚠️ WARNING: Heap usage still high (>75%)')
    } else {
      console.log('\n✅ GOOD: Heap usage is now acceptable')
    }
    
    if (arrayBuffersMB > 400) {
      console.log('🚨 CRITICAL: Array buffers still very high (>400MB)')
      console.log('   This indicates a severe memory leak in image processing')
    }
    
  } catch (error) {
    console.error('❌ Memory check failed:', error.message)
  }
}

async function main() {
  console.log('🚨 EMERGENCY GARBAGE COLLECTION')
  console.log('='.repeat(50))
  
  await forceGC()
  await checkMemoryAfterGC()
  
  console.log('\n' + '='.repeat(50))
  console.log('If memory is still critical, you MUST kill the server process:')
  console.log('1. Find process: ps aux | grep "next dev"')
  console.log('2. Kill process: kill -9 <PID>')
  console.log('3. Restart: npm run dev')
}

main().catch(console.error)
