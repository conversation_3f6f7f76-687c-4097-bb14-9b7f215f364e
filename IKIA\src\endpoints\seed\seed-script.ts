#!/usr/bin/env node

/**
 * Standalone seed script for Payload CMS
 * This can be run independently with: npm run seed
 */

import { getPayload } from 'payload'
import config from '../../payload.config.js'
import { seed } from './index.js'

const runSeed = async (): Promise<void> => {
  try {
    console.log('🌱 Starting database seed...')
    
    // Initialize Payload
    const payload = await getPayload({ config })
    
    console.log('✅ Payload initialized successfully')
    
    // Run the seed function
    await seed({ 
      payload, 
      req: {
        payload,
        user: null,
        t: (key: string) => key,
        locale: 'en',
        fallbackLocale: 'en',
      } as any
    })
    
    console.log('🎉 Database seeded successfully!')
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Error seeding database:', error)
    process.exit(1)
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Seed process interrupted')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n👋 Seed process terminated')
  process.exit(0)
})

// Run the seed
runSeed()
