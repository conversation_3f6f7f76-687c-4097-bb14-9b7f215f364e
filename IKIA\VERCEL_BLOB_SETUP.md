# Vercel Blob Storage Setup Guide

## 🚀 Overview

This guide explains how to configure Payload CMS with Vercel Blob storage for file uploads and media management.

**Note**: Vercel Blob storage is **optional**. If not configured, the system will automatically fall back to local file storage in the `public/media` directory.

## 📋 Prerequisites

1. **Vercel Account**: You need a Vercel account
2. **Vercel Project**: Your project should be deployed or connected to Vercel
3. **Blob Storage Access**: Vercel Blob storage enabled for your project

## 🔧 Configuration Steps

### 1. **Get Vercel Blob Token**

#### Option A: Via Vercel Dashboard
1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Select your project
3. Go to **Settings** → **Environment Variables**
4. Look for or create `BLOB_READ_WRITE_TOKEN`

#### Option B: Via Vercel CLI
```bash
# Install Vercel CLI if not already installed
npm i -g vercel

# Login to Vercel
vercel login

# Link your project
vercel link

# Create blob store and get token
vercel blob create
```

### 2. **Environment Variables Setup**

Add to your `.env` file:
```env
# Vercel Blob Storage Token
BLOB_READ_WRITE_TOKEN=vercel_blob_rw_xxxxxxxxxx

# Optional: Custom blob store configuration
# VERCEL_BLOB_STORE_ID=your_store_id
```

### 3. **Payload Configuration**

The configuration is already set up in `src/payload.config.ts`:

```typescript
import { vercelBlobStorage } from '@payloadcms/storage-vercel-blob'

export default buildConfig({
  // ... other config
  plugins: [
    vercelBlobStorage({
      collections: {
        media: true, // Enable for media collection
      },
      token: process.env.BLOB_READ_WRITE_TOKEN,
    }),
  ],
})
```

### 4. **Media Collection Configuration**

The Media collection (`src/collections/Media.ts`) is configured to work with cloud storage:

```typescript
export const Media: CollectionConfig = {
  slug: 'media',
  upload: {
    // Files stored in Vercel Blob (no staticDir needed)
    adminThumbnail: 'thumbnail',
    focalPoint: true,
    imageSizes: [
      { name: 'thumbnail', width: 300 },
      { name: 'square', width: 500, height: 500 },
      { name: 'small', width: 600 },
      { name: 'medium', width: 900 },
      { name: 'large', width: 1400 },
      { name: 'xlarge', width: 1920 },
      { name: 'og', width: 1200, height: 630, crop: 'center' },
    ],
  },
}
```

## 🎯 Benefits of Vercel Blob Storage

### **Performance**
- ✅ **Global CDN**: Files served from edge locations worldwide
- ✅ **Fast Uploads**: Optimized upload performance
- ✅ **Automatic Compression**: Images automatically optimized

### **Scalability**
- ✅ **Unlimited Storage**: No local disk space limitations
- ✅ **High Availability**: 99.9% uptime guarantee
- ✅ **Auto Scaling**: Handles traffic spikes automatically

### **Developer Experience**
- ✅ **Zero Configuration**: Works out of the box
- ✅ **Automatic Backups**: Built-in redundancy
- ✅ **Version Control**: File versioning support

## 📊 File Management Features

### **Supported File Types**
- **Images**: JPG, PNG, GIF, WebP, SVG
- **Documents**: PDF, DOC, DOCX, TXT
- **Media**: MP4, MP3, WAV
- **Archives**: ZIP, RAR

### **Image Processing**
- **Automatic Thumbnails**: Multiple sizes generated
- **Focal Point**: Smart cropping support
- **Format Optimization**: WebP conversion when supported
- **Responsive Images**: Multiple sizes for different devices

### **Security Features**
- **Access Control**: Payload's built-in permissions
- **Secure URLs**: Signed URLs for private files
- **CORS Support**: Configurable cross-origin access

## 🔍 Usage Examples

### **Upload Files via Admin Panel**
1. Login to Payload admin: `http://localhost:3000/admin`
2. Go to **Collections** → **Media**
3. Click **Create New**
4. Upload your files

### **Access Files in Frontend**
```typescript
// Get media files
const media = await payload.find({
  collection: 'media',
  limit: 10,
})

// Use in React component
{media.docs.map(item => (
  <img 
    key={item.id}
    src={item.url} 
    alt={item.alt}
    width={item.width}
    height={item.height}
  />
))}
```

### **Programmatic Upload**
```typescript
// Upload file programmatically
const result = await payload.create({
  collection: 'media',
  data: {
    alt: 'My uploaded image',
  },
  file: fileBuffer, // or File object
})
```

## 🚨 Troubleshooting

### **Common Issues**

#### **1. Token Not Working**
```bash
# Verify token is set
echo $BLOB_READ_WRITE_TOKEN

# Check Vercel project connection
vercel env ls
```

#### **2. Upload Failures**
- Check token permissions (read/write access)
- Verify file size limits (default: 4.5MB)
- Check network connectivity

#### **3. Images Not Loading**
- Verify CORS settings
- Check file URLs in browser
- Confirm blob storage is active

### **Debug Commands**
```bash
# Check Vercel blob stores
vercel blob ls

# View blob store details
vercel blob inspect <store-id>

# Test upload via CLI
vercel blob put test.jpg
```

## 📈 Monitoring & Analytics

### **Vercel Dashboard**
- **Storage Usage**: Monitor blob storage consumption
- **Bandwidth**: Track file delivery metrics
- **Performance**: View upload/download speeds

### **Payload Analytics**
- **Media Collection**: View uploaded files
- **File Sizes**: Monitor storage usage
- **Upload History**: Track file uploads

## 🔒 Security Best Practices

### **Environment Variables**
- ✅ Never commit tokens to version control
- ✅ Use different tokens for dev/staging/production
- ✅ Rotate tokens regularly

### **Access Control**
- ✅ Configure proper Payload permissions
- ✅ Use signed URLs for private content
- ✅ Implement file type restrictions

### **Content Security**
- ✅ Scan uploaded files for malware
- ✅ Validate file types and sizes
- ✅ Implement rate limiting for uploads

## 🎉 You're All Set!

Your Payload CMS is now configured with Vercel Blob storage:

1. ✅ **Cloud Storage**: Files stored in Vercel Blob
2. ✅ **Global CDN**: Fast file delivery worldwide
3. ✅ **Automatic Scaling**: Handles any traffic volume
4. ✅ **Zero Maintenance**: No server management needed

Start uploading files through the Payload admin panel and enjoy the benefits of cloud storage! 🚀
