import type { PayloadRequest } from 'payload'

/**
 * Debug login endpoint to catch and display verification errors
 * This will help identify why login succeeds but navigation fails
 */
export const debugLoginEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('Debug login endpoint called')
    console.log('Request body:', req.body)

    const { email, password } = req.body

    if (!email || !password) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'MISSING_CREDENTIALS',
            message: 'Email and password are required'
          }
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    try {
      // Attempt login using Payload's built-in login
      const result = await req.payload.login({
        collection: 'users',
        data: {
          email,
          password
        },
        req
      })

      console.log('Login result:', {
        user: {
          id: result.user?.id,
          email: result.user?.email,
          role: result.user?.role,
          _verified: result.user?._verified
        },
        token: result.token ? 'Present' : 'Missing',
        exp: result.exp
      })

      // Check verification status
      if (result.user && result.user._verified === false) {
        return new Response(
          JSON.stringify({
            success: false,
            error: {
              code: 'EMAIL_NOT_VERIFIED',
              message: 'Please verify your email address before accessing the admin panel. Check your email for a verification link.',
              details: {
                email: result.user.email,
                verified: result.user._verified,
                userId: result.user.id
              }
            }
          }),
          {
            status: 403,
            headers: { 'Content-Type': 'application/json' }
          }
        )
      }

      // Check if user has required fields
      const missingFields = []
      if (!result.user?.name) missingFields.push('name')
      if (!result.user?.role) missingFields.push('role')

      if (missingFields.length > 0) {
        return new Response(
          JSON.stringify({
            success: false,
            error: {
              code: 'INCOMPLETE_PROFILE',
              message: `User profile is incomplete. Missing: ${missingFields.join(', ')}`,
              details: {
                missingFields,
                userId: result.user?.id
              }
            }
          }),
          {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          }
        )
      }

      // Login successful - return detailed info
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Login successful',
          data: {
            user: {
              id: result.user.id,
              email: result.user.email,
              name: result.user.name,
              role: result.user.role,
              verified: result.user._verified,
              createdAt: result.user.createdAt,
              updatedAt: result.user.updatedAt
            },
            token: result.token,
            exp: result.exp,
            canAccessAdmin: true,
            redirectUrl: '/admin'
          }
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      )

    } catch (loginError: any) {
      console.error('Login error:', loginError)

      // Parse specific login errors
      let errorCode = 'LOGIN_FAILED'
      let errorMessage = 'Login failed'
      let statusCode = 401

      if (loginError.message) {
        if (loginError.message.includes('verification')) {
          errorCode = 'EMAIL_NOT_VERIFIED'
          errorMessage = 'Email verification required. Please check your email and verify your account.'
          statusCode = 403
        } else if (loginError.message.includes('credentials')) {
          errorCode = 'INVALID_CREDENTIALS'
          errorMessage = 'Invalid email or password'
          statusCode = 401
        } else if (loginError.message.includes('locked')) {
          errorCode = 'ACCOUNT_LOCKED'
          errorMessage = 'Account is temporarily locked due to too many failed login attempts'
          statusCode = 423
        } else if (loginError.message.includes('not found')) {
          errorCode = 'USER_NOT_FOUND'
          errorMessage = 'No account found with this email address'
          statusCode = 404
        }
      }

      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: errorCode,
            message: errorMessage,
            details: {
              originalError: loginError.message,
              stack: loginError.stack
            }
          }
        }),
        {
          status: statusCode,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error: any) {
    console.error('Debug login endpoint error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error during login',
          details: {
            error: error.message,
            stack: error.stack
          }
        }
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}
