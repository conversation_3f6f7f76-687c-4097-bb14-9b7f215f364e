'use client'

import {
  Phone,
  Mail,
  MapPin,
  Calendar,
  Users,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
  Youtube,
  ArrowRight,
  Globe,
  Download,
} from 'lucide-react'
import Image from 'next/image'

export default function Footer() {
  const quickLinks = [
    { name: 'About Conference', href: '/about' },
    { name: 'Resources', href: '/resources' },
    { name: 'Partners', href: '/partners' },
    { name: 'Sponsors', href: '/sponsors' },
    { name: 'Registration', href: '/registration' },
    { name: 'Invest', href: '/invest' },
    { name: 'Contact', href: '/contact' },
  ]

  const supportLinks = [
    { name: 'Contact Us', href: '/contact' },
    { name: 'Terms & Conditions', href: '/documents/terms-conditions.pdf', download: true },
    { name: 'Privacy Policy', href: '/documents/privacy-policy.pdf', download: true },
    { name: 'Accessibility Statement', href: '/accessibility' },
  ]

  const socialLinks = [
    { icon: Facebook, href: '#', name: 'Facebook' },
    { icon: Twitter, href: '#', name: 'Twitter' },
    { icon: Linkedin, href: '#', name: 'LinkedIn' },
    { icon: Instagram, href: '#', name: 'Instagram' },
    { icon: Youtube, href: '#', name: 'YouTube' },
  ]

  const sponsors = [
    { name: 'Court of Arms', logo: '/Court of arms.png' },
    { name: 'National Museums of Kenya', logo: '/National Museums of Kenya.png' },
    { name: 'NPI', logo: '/NPI.png' },
    { name: 'Kenya Vision 2030', logo: '/Vision 2030.png' },
    { name: 'Council of Governors', logo: '/Council of Governors.png' },
  ]

  return (
    <footer className="relative bg-[#7E2518] text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-96 h-96 bg-[#E8B32C] rounded-full blur-3xl transform -translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-[#A0503A] rounded-full blur-3xl transform translate-x-1/2 translate-y-1/2"></div>
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-[#E8B32C] rounded-full blur-3xl transform -translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute top-1/4 right-1/4 w-48 h-48 bg-[#A0503A] rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10">
        {/* Top accent line */}
        <div className="h-1 bg-gradient-to-r from-[#E8B32C] via-[#A0503A] to-[#E8B32C]"></div>

        {/* Main Footer Content */}
        <div className="container mx-auto px-4 py-12 sm:py-16">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-12">
            {/* Conference Info */}
            <div className="sm:col-span-2 lg:col-span-1 space-y-4 sm:space-y-6">
              <div className="space-y-3 sm:space-y-4">
                <h3 className="text-lg sm:text-xl font-myriad font-bold text-[#E8B32C]">
                  1<sup>st</sup> International Investment Conference and Trade Fair on Indigenous
                  Knowledge Intellectual Assets 2025
                </h3>
                <p className="text-white/85 leading-relaxed font-myriad text-sm sm:text-base">
                  Bridging Indigenous Knowledge with Modern Innovation for Sustainable Development
                </p>
              </div>

              {/* Conference Details */}
              <div className="space-y-2 sm:space-y-3">
                <div className="flex items-center gap-2 sm:gap-3 text-white/90">
                  <Calendar className="w-4 h-4 sm:w-5 sm:h-5 text-[#E8B32C] flex-shrink-0" />
                  <span className="text-xs sm:text-sm">November 19-21, 2025</span>
                </div>
                <div className="flex items-center gap-2 sm:gap-3 text-white/90">
                  <MapPin className="w-4 h-4 sm:w-5 sm:h-5 text-[#E8B32C] flex-shrink-0" />
                  <span className="text-xs sm:text-sm">Murang&apos;a County</span>
                </div>
                <div className="flex items-center gap-2 sm:gap-3 text-white/90">
                  <Users className="w-4 h-4 sm:w-5 sm:h-5 text-[#E8B32C] flex-shrink-0" />
                  <span className="text-xs sm:text-sm">1,000+ Expected Attendees</span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div className="space-y-6">
              <h3 className="text-lg font-myriad font-semibold text-[#E8B32C]">Quick Links</h3>
              <ul className="space-y-3">
                {quickLinks.map((link, index) => (
                  <li key={index}>
                    <a
                      href={link.href}
                      className="flex items-center gap-2 text-white/85 hover:text-white hover:translate-x-1 transition-all duration-300 group font-myriad"
                    >
                      <ArrowRight className="w-4 h-4 text-[#E8B32C] group-hover:text-[#C86E36] transition-colors" />
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Support & Legal */}
            <div className="space-y-6">
              <h3 className="text-lg font-myriad font-semibold text-[#E8B32C]">Support & Legal</h3>
              <ul className="space-y-3">
                {supportLinks.map((link, index) => (
                  <li key={index}>
                    <a
                      href={link.href}
                      download={link.download}
                      className="flex items-center gap-2 text-white/85 hover:text-white hover:translate-x-1 transition-all duration-300 group font-myriad"
                    >
                      {link.download ? (
                        <Download className="w-4 h-4 text-[#E8B32C] group-hover:text-[#C86E36] transition-colors" />
                      ) : (
                        <ArrowRight className="w-4 h-4 text-[#E8B32C] group-hover:text-[#C86E36] transition-colors" />
                      )}
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact & Newsletter */}
            <div className="space-y-6">
              <h3 className="text-lg font-myriad font-semibold text-[#E8B32C]">Stay Connected</h3>

              {/* Contact Info */}
              <div className="space-y-4">
                <div className="flex items-center gap-3 text-white/90">
                  <Phone className="w-5 h-5 text-[#E8B32C]" />
                  <span className="text-sm">+254 70 333 555</span>
                </div>
                <div className="flex items-center gap-3 text-white/90">
                  <Mail className="w-5 h-5 text-[#E8B32C]" />
                  <span className="text-sm"><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-white/90">
                  <Globe className="w-5 h-5 text-[#E8B32C]" />
                  <span className="text-sm">www.ikiaconference.or.ke</span>
                </div>
              </div>
            </div>
          </div>

          {/* Social Media & Sponsors Section */}
          <div className="mt-16 pt-8 border-t border-white/20">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              {/* Social Media */}
              <div className="space-y-4">
                <h4 className="font-myriad font-semibold text-[#E8B32C]">Follow Us</h4>
                <div className="flex gap-4">
                  {socialLinks.map((social, index) => {
                    const Icon = social.icon
                    return (
                      <a
                        key={index}
                        href={social.href}
                        className="w-10 h-10 bg-white/15 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/25 transition-all duration-300 hover:scale-110 border border-white/10"
                        title={social.name}
                      >
                        <Icon className="w-5 h-5" />
                      </a>
                    )
                  })}
                </div>
              </div>

              {/* Sponsors */}
              <div className="space-y-4">
                <h4 className="font-myriad font-semibold text-[#E8B32C]">Our Partners</h4>
                <div className="flex flex-wrap gap-4 items-center">
                  {sponsors.map((sponsor, index) => (
                    <div
                      key={index}
                      className="bg-white/15 backdrop-blur-sm rounded-lg p-1 hover:bg-white/25 transition-all duration-300 border border-white/10"
                    >
                      <Image
                        src={sponsor.logo || '/placeholder.svg'}
                        alt={sponsor.name}
                        width={120}
                        height={60}
                        className="h-8 w-auto opacity-85 hover:opacity-100 transition-opacity"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
