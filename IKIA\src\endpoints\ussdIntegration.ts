import type { PayloadRequest } from 'payload'
import { generatePesaflowHash } from '../utils/pesaflowHash'

// Types for the USSD Integration endpoint
export const ussdIntegrationEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('USSD Integration endpoint called')
    console.log('Request body:', req.body)
    console.log('Request body type:', typeof req.body)

    // Parse request body (handle ReadableStream)
    let parsedBody: any
    if (req.body instanceof ReadableStream) {
      const reader = req.body.getReader()
      const chunks: Uint8Array[] = []
      let done = false

      while (!done) {
        const { value, done: streamDone } = await reader.read()
        done = streamDone
        if (value) {
          chunks.push(value)
        }
      }

      const bodyText = new TextDecoder().decode(
        new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0)).map((_, i) => {
          let offset = 0
          for (const chunk of chunks) {
            if (i < offset + chunk.length) {
              return chunk[i - offset]
            }
            offset += chunk.length
          }
          return 0
        }),
      )

      try {
        parsedBody = JSON.parse(bodyText)
      } catch (parseError) {
        console.error('JSON parse error:', parseError)
        return new Response(
          JSON.stringify({
            error: 'Invalid JSON in request body',
            display_info: { Error: 'Invalid request format' },
            valid: false,
          }),
          {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }
    } else if (typeof req.body === 'object' && req.body !== null) {
      parsedBody = req.body
    } else {
      return new Response(
        JSON.stringify({
          error: 'Invalid request body format',
          display_info: { Error: 'Invalid request format' },
          valid: false,
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    console.log('Parsed body:', parsedBody)

    const { reference, service_code } = parsedBody

    // Validate required fields (hash is always generated on backend)
    const missingFields = [!reference && 'reference', !service_code && 'service_code'].filter(
      Boolean,
    )

    if (missingFields.length > 0) {
      return new Response(
        JSON.stringify({
          error: 'Missing required fields',
          missingFields,
          message: `Please provide the following required fields: ${missingFields.join(', ')}`,
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get environment variables for hash verification
    const { ECITIZEN_MERCHANT_KEY: merchantKey, ECITIZEN_MERCHANT_SECRET: merchantSecret } =
      process.env

    const missingConfig = [
      !merchantKey && 'ECITIZEN_MERCHANT_KEY',
      !merchantSecret && 'ECITIZEN_MERCHANT_SECRET',
    ].filter(Boolean)

    if (missingConfig.length > 0) {
      console.error('Missing eCitizen USSD configuration:', missingConfig)
      return new Response(
        JSON.stringify({
          error: 'Internal Error - Missing configuration',
          display_info: { Error: 'Service temporarily unavailable' },
          valid: false,
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Generate secure hash automatically on backend according to eCitizen USSD specification
    // data_string = reference + service_code + merchant_secret
    const dataString = `${reference}${service_code}${merchantSecret}`
    const secureHash = generateUSSDHash(dataString, merchantKey || '')

    console.log('USSD hash generated automatically on backend:', {
      dataString: `${reference}${service_code}[SECRET]`,
      hash: secureHash,
    })

    // Note: In production, eCitizen would call this endpoint with their own hash
    // which should match our generated hash. For now, we always generate it on our side.

    // Validate the reference and service_code
    const validationResult = await validateUSSDReference(reference, service_code)

    if (validationResult.valid) {
      return new Response(
        JSON.stringify({
          display_info: validationResult.display_info,
          valid: true,
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    } else {
      return new Response(
        JSON.stringify({
          display_info: validationResult.display_info || { Error: 'Reference not found' },
          valid: false,
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }
  } catch (error) {
    console.error('USSD Integration endpoint error:', error)
    return new Response(
      JSON.stringify({
        error: 'Internal Error',
        display_info: { Error: 'Service temporarily unavailable' },
        valid: false,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}

// Helper function to generate USSD hash (different from Pesaflow hash)
function generateUSSDHash(dataString: string, merchantKey: string): string {
  try {
    // According to eCitizen USSD documentation:
    // 1. Calculate HMAC-SHA256
    // 2. Convert to hexadecimal
    // 3. Convert to lowercase
    // 4. Base64 encode

    const crypto = require('crypto')
    const hmac = crypto.createHmac('sha256', merchantKey)
    hmac.update(dataString)
    const hash = hmac.digest('hex')
    const lowercaseHash = hash.toLowerCase()
    const base64Encoded = Buffer.from(lowercaseHash).toString('base64')

    return base64Encoded
  } catch (error) {
    console.error('Error generating USSD hash:', error)
    throw error
  }
}

// Helper function to validate USSD reference
// This should be replaced with actual business logic
async function validateUSSDReference(reference: string, serviceCode: string) {
  try {
    console.log('Validating USSD reference:', { reference, serviceCode })

    // In a real implementation, this would:
    // 1. Query database for the reference
    // 2. Check if it belongs to the specified service
    // 3. Return relevant display information
    // 4. Validate payment status, amounts, etc.

    // Example validation logic based on reference patterns
    if (reference.startsWith('INV')) {
      // Invoice reference
      return {
        valid: true,
        display_info: {
          Invoice: reference,
          Amount: 'KES 1,500.00',
          Service: 'Business License',
          Status: 'Pending Payment',
        },
      }
    } else if (reference.startsWith('LIC')) {
      // License reference
      return {
        valid: true,
        display_info: {
          License: reference,
          Type: 'Driving License Renewal',
          Amount: 'KES 3,000.00',
          Expiry: '2025-12-31',
        },
      }
    } else if (reference.startsWith('PERMIT')) {
      // Permit reference
      return {
        valid: true,
        display_info: {
          Permit: reference,
          Type: 'Construction Permit',
          Amount: 'KES 5,000.00',
          Location: 'Nairobi County',
        },
      }
    } else if (reference.length === 8 && /^\d+$/.test(reference)) {
      // ID Number validation
      return {
        valid: true,
        display_info: {
          'ID Number': reference,
          Name: 'John Doe',
          Outstanding: 'KES 2,500.00',
          Services: '3 pending payments',
        },
      }
    } else {
      // Invalid reference
      return {
        valid: false,
        display_info: {
          Error: 'Reference not found',
          Reference: reference,
          Help: 'Check reference number',
        },
      }
    }
  } catch (error) {
    console.error('Error validating USSD reference:', error)
    return {
      valid: false,
      display_info: {
        Error: 'Validation failed',
        Reference: reference,
      },
    }
  }
}
