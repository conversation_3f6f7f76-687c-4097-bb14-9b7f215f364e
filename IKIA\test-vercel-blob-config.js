// Test Vercel Blob Storage Configuration
// Run with: node test-vercel-blob-config.js

console.log('🔍 VERCEL BLOB STORAGE CONFIGURATION TEST')
console.log('=' .repeat(60))

// Check environment variables
const blobToken = process.env.BLOB_READ_WRITE_TOKEN

console.log('📋 ENVIRONMENT VARIABLES:')
console.log(`   BLOB_READ_WRITE_TOKEN: ${blobToken ? (blobToken.startsWith('vercel_blob_rw_') ? '✅ Valid format' : '❌ Invalid format') : '❌ Not set'}`)

if (blobToken) {
  console.log(`   Token preview: ${blobToken.substring(0, 20)}...`)
  
  if (blobToken.startsWith('vercel_blob_rw_')) {
    console.log('   ✅ Token format is correct')
    console.log('   🌐 Vercel Blob storage will be enabled')
  } else {
    console.log('   ❌ Token format is incorrect')
    console.log('   📁 Will fall back to local storage')
  }
} else {
  console.log('   ❌ No token provided')
  console.log('   📁 Will use local storage in public/media/')
}

console.log('\n🔧 STORAGE CONFIGURATION:')
console.log('=' .repeat(60))

const isValidToken = blobToken && blobToken.startsWith('vercel_blob_rw_')

if (isValidToken) {
  console.log('✅ VERCEL BLOB STORAGE ENABLED')
  console.log('   📤 Files uploaded to Vercel Blob')
  console.log('   🌐 Global CDN delivery')
  console.log('   ♾️  Unlimited storage')
  console.log('   🔒 Secure cloud storage')
} else {
  console.log('📁 LOCAL STORAGE ENABLED')
  console.log('   📤 Files uploaded to public/media/')
  console.log('   🏠 Local file system storage')
  console.log('   ⚠️  Limited by server disk space')
  console.log('   💡 Good for development/testing')
}

console.log('\n📊 NEXT STEPS:')
console.log('=' .repeat(60))

if (isValidToken) {
  console.log('🎉 You\'re all set with Vercel Blob storage!')
  console.log('   1. Start your development server: npm run dev')
  console.log('   2. Go to /admin and upload files')
  console.log('   3. Files will be stored in Vercel Blob')
} else {
  console.log('🔧 To enable Vercel Blob storage:')
  console.log('   1. Get a token from Vercel dashboard')
  console.log('   2. Set BLOB_READ_WRITE_TOKEN in .env')
  console.log('   3. Token format: vercel_blob_rw_<store_id>_<random>')
  console.log('   4. Restart your server')
  console.log('')
  console.log('📁 For now, using local storage:')
  console.log('   1. Files will be saved to public/media/')
  console.log('   2. This works fine for development')
  console.log('   3. Consider Vercel Blob for production')
}

console.log('\n🚀 READY TO GO!')

// Test the configuration logic
function testConfigLogic() {
  console.log('\n🧪 TESTING CONFIGURATION LOGIC:')
  console.log('=' .repeat(60))
  
  const testTokens = [
    'vercel_blob_rw_abc123_def456',
    'invalid_token_format',
    '',
    undefined,
    'vercel_blob_rw_store123_random789'
  ]
  
  testTokens.forEach((token, index) => {
    const isValid = token && token.startsWith('vercel_blob_rw_')
    console.log(`   Test ${index + 1}: ${token || 'undefined'} → ${isValid ? '✅ Valid' : '❌ Invalid'}`)
  })
}

testConfigLogic()
