// Direct test of hash generation logic without server
// Run with: node test-hash-generation-direct.js

import crypto from 'crypto'

// Mock environment variables for testing
process.env.PESAFLOW_API_CLIENT_ID = 'test_client_id'
process.env.PESAFLOW_CLIENT_SECRET = 'test_secret'
process.env.PESAFLOW_CLIENT_KEY = 'test_key'

// Import the HashService
import { HashService } from './src/utils/pesaflowHash.ts'

function testHashGenerationDirect() {
  console.log('🔐 DIRECT HASH GENERATION TEST')
  console.log('=' .repeat(60))
  console.log('✨ Testing hash generation logic directly!')
  console.log('')
  
  try {
    const hashService = HashService.getInstance()
    
    // Test data
    const testData = {
      amountExpected: '1500.00',
      serviceID: 'SVC123',
      clientIDNumber: '12345678',
      currency: 'KES',
      billRefNumber: 'BILL-' + Date.now(),
      billDesc: 'Payment for IKIA Conference Registration',
      clientName: '<PERSON>'
    }
    
    console.log('📋 TEST DATA:')
    Object.entries(testData).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`)
    })
    console.log('')
    
    // Generate checkout hash
    console.log('🔗 GENERATING CHECKOUT HASH...')
    const result = hashService.generateCheckoutHash(
      testData.amountExpected,
      testData.serviceID,
      testData.clientIDNumber,
      testData.currency,
      testData.billRefNumber,
      testData.billDesc,
      testData.clientName
    )
    
    console.log('✅ HASH GENERATION SUCCESSFUL!')
    console.log('')
    console.log('📦 GENERATED PAYLOAD:')
    console.log(`   apiClientID: ${result.payload.apiClientID}`)
    console.log(`   amountExpected: ${result.payload.amountExpected}`)
    console.log(`   serviceID: ${result.payload.serviceID}`)
    console.log(`   clientIDNumber: ${result.payload.clientIDNumber}`)
    console.log(`   currency: ${result.payload.currency}`)
    console.log(`   billRefNumber: ${result.payload.billRefNumber}`)
    console.log(`   billDesc: ${result.payload.billDesc}`)
    console.log(`   clientName: ${result.payload.clientName}`)
    console.log(`   secureHash: ${result.hash}`)
    console.log('')
    
    // Show the data string that was hashed
    const dataString = `${result.api_client_id}${testData.amountExpected}${testData.serviceID}${testData.clientIDNumber}${testData.currency}${testData.billRefNumber}${testData.billDesc}${testData.clientName}[SECRET]`
    console.log('🔍 HASH GENERATION DETAILS:')
    console.log(`   Data String: ${dataString}`)
    console.log(`   Hash Algorithm: HMAC-SHA256 -> Base64`)
    console.log(`   Hash Length: ${result.hash.length} characters`)
    console.log('')
    
    // Test other hash methods
    console.log('🧪 TESTING OTHER HASH METHODS:')
    
    // Payment validation hash
    const validationResult = hashService.generatePaymentValidationHash('REF123', '1500.00')
    console.log(`   Payment Validation Hash: ${validationResult.hash.substring(0, 20)}...`)
    
    // Payment status hash
    const statusResult = hashService.generatePaymentStatusHash('REF123')
    console.log(`   Payment Status Hash: ${statusResult.hash.substring(0, 20)}...`)
    
    // Payment confirmation hash
    const confirmationResult = hashService.generatePaymentConfirmationHash(
      'REF123',
      '1500.00',
      'KES',
      'TXN789',
      '2024-01-01 12:30:01',
      'John Doe',
      '254700000000'
    )
    console.log(`   Payment Confirmation Hash: ${confirmationResult.hash.substring(0, 20)}...`)
    
    console.log('')
    console.log('🎯 ALL HASH GENERATION METHODS WORKING!')
    
  } catch (error) {
    console.error('❌ Error:', error.message)
    console.error('Stack:', error.stack)
  }
}

// Manual hash generation for comparison
function manualHashGeneration() {
  console.log('\n🔧 MANUAL HASH GENERATION FOR COMPARISON')
  console.log('=' .repeat(60))
  
  const apiClientId = 'test_client_id'
  const secret = 'test_secret'
  const key = 'test_key'
  
  const testData = {
    amountExpected: '1500.00',
    serviceID: 'SVC123',
    clientIDNumber: '12345678',
    currency: 'KES',
    billRefNumber: 'BILL-123456789',
    billDesc: 'Payment for IKIA Conference Registration',
    clientName: 'John Doe'
  }
  
  // Create data string
  const dataString = `${apiClientId}${testData.amountExpected}${testData.serviceID}${testData.clientIDNumber}${testData.currency}${testData.billRefNumber}${testData.billDesc}${testData.clientName}${secret}`
  
  console.log('📋 MANUAL CALCULATION:')
  console.log(`   Data String: ${dataString.replace(secret, '[SECRET]')}`)
  
  // Generate hash manually
  const hmac = crypto.createHmac('sha256', key)
  hmac.update(dataString)
  const base16 = hmac.digest('hex').toLowerCase()
  const base64Hash = Buffer.from(base16).toString('base64')
  
  console.log(`   HMAC-SHA256 (hex): ${base16.substring(0, 20)}...`)
  console.log(`   Base64 Hash: ${base64Hash}`)
  console.log(`   Hash Length: ${base64Hash.length} characters`)
  console.log('')
  console.log('✅ Manual hash generation complete!')
}

// Run tests
console.log('🚀 STARTING HASH GENERATION TESTS...\n')
testHashGenerationDirect()
manualHashGeneration()
console.log('\n🎉 ALL TESTS COMPLETED!')
