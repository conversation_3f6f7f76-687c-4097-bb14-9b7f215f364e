'use client'

import { useActiveCounties, useCounties } from '../hooks/useCounties'

/**
 * Debug component to test counties API integration
 * Can be temporarily added to any page to verify API is working
 */
export function CountiesDebug() {
  const { counties, loading, error, totalCounties } = useActiveCounties({
    limit: 10,
  })

  // Also test fetching all counties (not just active ones)
  const {
    counties: allCounties,
    loading: allLoading,
    error: allError,
  } = useCounties({
    limit: 10,
  })

  if (loading) {
    return (
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
        <h3 className="font-semibold text-blue-800">Counties API Debug</h3>
        <p className="text-blue-600">Loading counties...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-md">
        <h3 className="font-semibold text-red-800">Counties API Debug - Error</h3>
        <p className="text-red-600">{error}</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Active Counties */}
      <div className="p-4 bg-green-50 border border-green-200 rounded-md">
        <h3 className="font-semibold text-green-800">Active Counties API Debug</h3>
        <p className="text-green-600">
          Loaded {counties.length} of {totalCounties} active counties
        </p>
        {counties.length > 0 ? (
          <div className="mt-2 max-h-32 overflow-y-auto">
            <ul className="text-sm text-green-700">
              {counties.map((county) => (
                <li key={county.id} className="flex justify-between">
                  <span>{county.name}</span>
                  <span className="text-gray-500">({county.code})</span>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <p className="text-orange-600 text-sm mt-2">No active counties found in database</p>
        )}
      </div>

      {/* All Counties */}
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
        <h3 className="font-semibold text-blue-800">All Counties API Debug</h3>
        {allLoading ? (
          <p className="text-blue-600">Loading all counties...</p>
        ) : allError ? (
          <p className="text-red-600">Error: {allError}</p>
        ) : (
          <>
            <p className="text-blue-600">Loaded {allCounties.length} total counties</p>
            {allCounties.length > 0 ? (
              <div className="mt-2 max-h-32 overflow-y-auto">
                <ul className="text-sm text-blue-700">
                  {allCounties.map((county) => (
                    <li key={county.id} className="flex justify-between">
                      <span>{county.name}</span>
                      <span className="text-gray-500">
                        ({county.code}) {county.isActive ? '✓' : '✗'}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            ) : (
              <p className="text-orange-600 text-sm mt-2">No counties found in database at all</p>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default CountiesDebug
