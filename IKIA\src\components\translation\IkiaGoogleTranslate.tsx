'use client'

import React from 'react'
import { useGoogleTranslate } from 'react-custom-google-translate'
import { Globe, ChevronDown, Check } from 'lucide-react'
import { cn } from '@/utilities/ui'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface IkiaGoogleTranslateProps {
  className?: string
  isScrolled?: boolean
}

export const IkiaGoogleTranslate: React.FC<IkiaGoogleTranslateProps> = ({
  className,
  isScrolled = true,
}) => {
  const { currentLanguage, changeLanguage, availableLanguages } = useGoogleTranslate()

  // Get current language label
  const getCurrentLanguageLabel = () => {
    const current = availableLanguages?.find((lang) => lang.value === `en|${currentLanguage}`)
    return current?.label || 'Translate'
  }

  // Handle language change
  const handleLanguageChange = (languageValue: string) => {
    changeLanguage(languageValue)
    console.log('IKIA: Language changed to:', languageValue)
  }

  // Always show the translate button, even if not ready
  // This prevents the "Loading..." state from showing

  return (
    <div className={cn('ikia-google-translate', className)}>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <button className="ikia-translate-button" type="button">
            <Globe className="w-4 h-4" aria-hidden="true" />
            <span>{getCurrentLanguageLabel()}</span>
            <ChevronDown className="w-3 h-3" aria-hidden="true" />
          </button>
        </DropdownMenuTrigger>

        <DropdownMenuContent className="ikia-translate-dropdown" align="start">
          {availableLanguages?.map((language) => (
            <DropdownMenuItem
              key={language.value}
              onClick={() => handleLanguageChange(language.value)}
              className={`ikia-translate-option ${
                language.value === `en|${currentLanguage}` ? 'selected' : ''
              }`}
            >
              <span>{language.label}</span>
              {language.value === `en|${currentLanguage}` && (
                <Check className="w-4 h-4 ml-auto" aria-hidden="true" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Custom IKIA Styling for ShadCN Components */}
      <style jsx>{`
        /* IKIA Translate Button - Navigation Style */
        .ikia-translate-button {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          border-radius: 4px;
          transition: all 0.3s ease;
          cursor: pointer;
          border: 1px solid transparent;
          background: transparent;

          /* Typography - Match IKIA Navigation */
          font-family: inherit;
          font-weight: 600;
          font-size: 12px;

          /* Dynamic colors based on navbar state */
          ${isScrolled
            ? `
              color: #374151;
            `
            : `
              color: white;
            `}
        }

        /* Hover effects - IKIA Navigation Style */
        .ikia-translate-button:hover {
          ${isScrolled
            ? `
              color: #7E2518;
              background-color: rgba(126, 37, 24, 0.05);
              border-color: rgba(126, 37, 24, 0.2);
            `
            : `
              color: #E8B32C;
              background-color: rgba(255, 255, 255, 0.1);
              border-color: rgba(255, 255, 255, 0.2);
            `}
        }

        /* Active state - Yellow outline when dropdown is open */
        .ikia-translate-button[data-state='open'] {
          border-color: #e8b32c;
          box-shadow: 0 0 0 2px rgba(232, 179, 44, 0.2);
          background-color: rgba(232, 179, 44, 0.05);
          border-radius: 4px 4px 0 0; /* Square bottom to connect with dropdown */
        }

        /* Loading state */
        .ikia-translate-button.loading {
          opacity: 0.7;
          cursor: not-allowed;
        }

        /* Square Dropdown - ShadCN DropdownMenuContent Override */
        .ikia-translate-dropdown {
          min-width: 200px !important;
          max-height: 300px !important;
          border: 2px solid #e8b32c !important; /* Yellow outline - the excellent feature! */
          border-radius: 0 !important; /* Square corners - original design */
          box-shadow:
            0 10px 25px rgba(0, 0, 0, 0.15),
            0 0 0 2px rgba(232, 179, 44, 0.3) !important; /* Yellow glow */
          margin-top: 0 !important; /* Connect directly to button */
          border-top: none !important; /* Connect seamlessly with button */
          padding: 0 !important; /* Remove default padding */
        }

        /* Dropdown Options - ShadCN DropdownMenuItem Override */
        .ikia-translate-option {
          display: flex !important;
          align-items: center !important;
          justify-content: space-between !important;
          width: 100% !important;
          padding: 8px 12px !important;
          font-family: inherit !important;
          font-size: 12px !important;
          color: #374151 !important;
          cursor: pointer !important;
          border: none !important;
          background: transparent !important;
          border-bottom: 1px solid #f3f4f6 !important;
          transition: all 0.2s ease !important;
          border-radius: 0 !important; /* Square corners for all options */
          text-align: left !important;
          margin: 0 !important;
        }

        .ikia-translate-option:last-child {
          border-bottom: none !important;
        }

        .ikia-translate-option:hover,
        .ikia-translate-option[data-highlighted] {
          background-color: #7e2518 !important;
          color: white !important;
          border-radius: 0 !important; /* Keep square on hover */
        }

        /* Selected option with yellow accent - excellent design */
        .ikia-translate-option.selected {
          background-color: rgba(232, 179, 44, 0.1) !important; /* Yellow background */
          color: #7e2518 !important;
          font-weight: 600 !important;
          border-left: 3px solid #e8b32c !important; /* Yellow left border accent */
          border-radius: 0 !important; /* Square corners */
        }

        /* Override ShadCN focus styles to maintain square design */
        .ikia-translate-option:focus,
        .ikia-translate-option[data-highlighted] {
          outline: none !important;
          background-color: #7e2518 !important;
          color: white !important;
        }

        /* Use default system scrollbar for better consistency */

        /* Hide Google Translate branding */
        .goog-te-banner-frame,
        .goog-te-gadget-icon,
        .goog-logo-link {
          display: none !important;
        }

        body {
          top: 0 !important;
        }
      `}</style>
    </div>
  )
}
