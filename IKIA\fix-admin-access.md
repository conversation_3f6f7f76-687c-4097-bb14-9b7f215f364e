# Fix Admin Access Issue

## Problem Identified
The user `<EMAIL>` is successfully authenticating but has role `"citizen"` instead of `"admin"`, which prevents access to the admin dashboard.

## Solution Applied

### 1. Updated Access Control
Modified the Users collection access control to properly check for admin role:

```typescript
access: {
  // Admin panel access - only users with admin role
  admin: ({ req }) => {
    return req.user?.role === 'admin'
  },
  // ... other access controls
}
```

### 2. Created User Role Update Tools

#### Option A: API Endpoint
Use the new endpoint to update user role:

```bash
curl -X POST http://localhost:3000/api/update-user-role \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "role": "admin"
  }'
```

#### Option B: Node.js Script
Run the script to update user role:

```bash
# First build the project
npm run build

# Then run the script
node scripts/update-user-role.js <EMAIL> admin
```

## Steps to Fix Admin Access

### Step 1: Update User Role
Choose one of the methods above to promote the user to admin role.

### Step 2: Verify the Update
After updating the role, the API response should show:
```json
{
  "success": true,
  "message": "User role updated successfully",
  "data": {
    "userId": 1,
    "email": "<EMAIL>",
    "name": "kennedy w Kinyua",
    "previousRole": "citizen",
    "newRole": "admin",
    "canAccessAdmin": true
  }
}
```

### Step 3: Test Admin Access
1. **Logout** from the current session
2. **Login again** with the same credentials
3. **Verify** the user object now shows `"role": "admin"`
4. **Access** the admin dashboard at `/admin`

## Expected Result
After the role update, the login response should show:
```json
{
  "user": {
    "id": 1,
    "name": "kennedy w Kinyua",
    "role": "admin",  // ← This should now be "admin"
    "email": "<EMAIL>",
    // ... other fields
  }
}
```

## Security Notes
- The update endpoint allows initial setup without authentication
- In production, only admin users can update roles
- The access control now properly restricts admin panel access to admin role users

## Verification Commands

### Check Current User Role
```bash
curl -X GET http://localhost:3000/api/users/me \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Update Role via API
```bash
curl -X POST http://localhost:3000/api/update-user-role \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "role": "admin"
  }'
```

### Test Admin Login
```bash
curl -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "YOUR_PASSWORD"
  }'
```

The admin dashboard should now be accessible after the role update and re-login.
