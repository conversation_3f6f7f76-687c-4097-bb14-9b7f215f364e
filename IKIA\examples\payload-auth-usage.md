# Payload CMS Built-in Authentication API

Payload CMS automatically provides authentication endpoints when you have a collection with `auth: true`. Since your Users collection has authentication enabled, you can use these built-in endpoints.

## Available Endpoints

All authentication endpoints are available under `/api/users/` (where "users" is your auth collection slug):

### 1. Login
```bash
POST /api/users/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (Success):**
```json
{
  "message": "Auth Passed",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "<PERSON>",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "exp": 1640995200
}
```

### 2. Logout
```bash
POST /api/users/logout
```

**Headers:**
```
Authorization: Bearer <your-jwt-token>
```

**Response:**
```json
{
  "message": "You have been logged out successfully."
}
```

### 3. Get Current User (Me)
```bash
GET /api/users/me
```

**Headers:**
```
Authorization: Bearer <your-jwt-token>
```

**Response:**
```json
{
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "collection": "users",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "exp": 1640995200
}
```

### 4. Refresh Token
```bash
POST /api/users/refresh-token
```

**Headers:**
```
Authorization: Bearer <your-jwt-token>
```

**Response:**
```json
{
  "message": "Token refresh successful",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "exp": 1640995200,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "John Doe"
  }
}
```

### 5. Forgot Password
```bash
POST /api/users/forgot-password
```

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

### 6. Reset Password
```bash
POST /api/users/reset-password
```

**Request Body:**
```json
{
  "token": "reset-token-from-email",
  "password": "newpassword123"
}
```

## Frontend Usage Examples

### React Hook for Authentication
```typescript
import { useState, useEffect, createContext, useContext } from 'react'

interface User {
  id: number
  email: string
  name?: string
}

interface AuthContextType {
  user: User | null
  token: string | null
  login: (email: string, password: string) => Promise<boolean>
  logout: () => Promise<void>
  loading: boolean
}

const AuthContext = createContext<AuthContextType | null>(null)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)

  // Check for existing token on mount
  useEffect(() => {
    const savedToken = localStorage.getItem('auth-token')
    if (savedToken) {
      setToken(savedToken)
      fetchCurrentUser(savedToken)
    } else {
      setLoading(false)
    }
  }, [])

  const fetchCurrentUser = async (authToken: string) => {
    try {
      const response = await fetch('/api/users/me', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
        },
      })

      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
        setToken(authToken)
      } else {
        // Token is invalid
        localStorage.removeItem('auth-token')
        setToken(null)
        setUser(null)
      }
    } catch (error) {
      console.error('Error fetching current user:', error)
      localStorage.removeItem('auth-token')
      setToken(null)
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
        setToken(data.token)
        localStorage.setItem('auth-token', data.token)
        return true
      } else {
        const errorData = await response.json()
        console.error('Login failed:', errorData.message)
        return false
      }
    } catch (error) {
      console.error('Login error:', error)
      return false
    }
  }

  const logout = async () => {
    try {
      if (token) {
        await fetch('/api/users/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setUser(null)
      setToken(null)
      localStorage.removeItem('auth-token')
    }
  }

  return (
    <AuthContext.Provider value={{ user, token, login, logout, loading }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
```

### Login Component
```typescript
import { useState } from 'react'
import { useAuth } from './AuthProvider'

export function LoginForm() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const { login } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    const success = await login(email, password)
    
    if (!success) {
      setError('Invalid email or password')
    }
    
    setLoading(false)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="email">Email:</label>
        <input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="w-full p-2 border rounded"
        />
      </div>
      
      <div>
        <label htmlFor="password">Password:</label>
        <input
          id="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className="w-full p-2 border rounded"
        />
      </div>
      
      {error && (
        <div className="text-red-500">{error}</div>
      )}
      
      <button
        type="submit"
        disabled={loading}
        className="w-full p-2 bg-blue-500 text-white rounded disabled:opacity-50"
      >
        {loading ? 'Logging in...' : 'Login'}
      </button>
    </form>
  )
}
```

### Protected Route Component
```typescript
import { useAuth } from './AuthProvider'

interface ProtectedRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const { user, loading } = useAuth()

  if (loading) {
    return <div>Loading...</div>
  }

  if (!user) {
    return fallback || <div>Please log in to access this page.</div>
  }

  return <>{children}</>
}
```

### API Request Helper with Auth
```typescript
export async function authenticatedFetch(url: string, options: RequestInit = {}) {
  const token = localStorage.getItem('auth-token')
  
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  }

  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }

  const response = await fetch(url, {
    ...options,
    headers,
  })

  // Handle token expiration
  if (response.status === 401) {
    localStorage.removeItem('auth-token')
    window.location.href = '/login'
  }

  return response
}
```

## Key Benefits of Using Built-in Auth

1. **No custom code needed** - Payload handles all the authentication logic
2. **JWT tokens** - Secure, stateless authentication
3. **Password hashing** - Automatic bcrypt hashing
4. **Rate limiting** - Built-in protection against brute force attacks
5. **Account locking** - Automatic account locking after failed attempts
6. **Token refresh** - Built-in token refresh mechanism
7. **Password reset** - Complete forgot/reset password flow

## Security Features

- Passwords are automatically hashed with bcrypt
- JWT tokens with configurable expiration
- Account locking after failed login attempts
- Rate limiting on authentication endpoints
- Secure password reset flow with tokens

## Configuration Options

You can customize authentication behavior in your Users collection:

```typescript
export const Users: CollectionConfig = {
  slug: 'users',
  auth: {
    tokenExpiration: 7200, // 2 hours in seconds
    maxLoginAttempts: 5,
    lockTime: 600000, // 10 minutes in milliseconds
  },
  // ... other config
}
```

This built-in authentication system is production-ready and handles all the security concerns for you!
