'use client'

import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Menu, X } from 'lucide-react'
import { IkiaLogo } from './IkiaLogo'
import { IkiaNavMenu } from './IkiaNavMenu'
import { IkiaActionButtons } from './IkiaActionButtons'
import { IkiaGoogleTranslate } from '@/components/translation/IkiaGoogleTranslate'

interface IkiaNavbarProps {
  className?: string
}

export const IkiaNavbar: React.FC<IkiaNavbarProps> = ({ className = '' }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const pathname = usePathname()

  // Pages that should have transparent navbar behavior
  const transparentPages = ['/', '/invest']
  const shouldBeTransparent = transparentPages.includes(pathname)

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen)
  const closeMenu = () => setIsMenuOpen(false)

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY
      setIsScrolled(scrollTop > 50)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Manage body class for transparent navbar pages
  useEffect(() => {
    if (shouldBeTransparent) {
      document.body.classList.add('transparent-navbar')
    } else {
      document.body.classList.remove('transparent-navbar')
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('transparent-navbar')
    }
  }, [shouldBeTransparent])

  return (
    <header
      id="navigation"
      className={`w-full fixed top-0 z-50 transition-all duration-300 ${
        shouldBeTransparent
          ? isScrolled
            ? 'bg-white shadow-md'
            : 'bg-transparent'
          : 'bg-white shadow-md'
      } ${className}`}
      style={{
        backgroundColor: shouldBeTransparent && !isScrolled ? 'transparent' : undefined,
      }}
    >
      <div className="max-w-screen-xl mx-auto px-8">
        {/* Main Header */}
        <div className="flex items-center justify-between h-20">
          {/* Logo Section */}
          <div className="flex items-center">
            <IkiaLogo />
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center gap-6">
            <IkiaNavMenu isScrolled={shouldBeTransparent ? isScrolled : true} />
            <IkiaGoogleTranslate
              className="flex"
              isScrolled={shouldBeTransparent ? isScrolled : true}
            />
          </div>

          {/* Desktop Action Buttons */}
          <IkiaActionButtons />

          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMenu}
              className={`p-2 transition-colors duration-300 ${
                shouldBeTransparent
                  ? isScrolled
                    ? 'hover:bg-muted text-foreground'
                    : 'hover:bg-white/20 text-white'
                  : 'hover:bg-muted text-foreground'
              }`}
            >
              {isMenuOpen ? (
                <X
                  className={`h-6 w-6 transition-colors duration-300 ${
                    shouldBeTransparent
                      ? isScrolled
                        ? 'text-foreground'
                        : 'text-white'
                      : 'text-foreground'
                  }`}
                />
              ) : (
                <Menu
                  className={`h-6 w-6 transition-colors duration-300 ${
                    shouldBeTransparent
                      ? isScrolled
                        ? 'text-foreground'
                        : 'text-white'
                      : 'text-foreground'
                  }`}
                />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div
          className={`lg:hidden overflow-hidden transition-all duration-300 ease-in-out ${
            isMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'
          }`}
        >
          <div className="py-6 border-t border-white/20 bg-black/95 backdrop-blur-md transition-colors duration-300">
            <div className="container mx-auto px-4 space-y-6">
              {/* Mobile Navigation Menu */}
              <IkiaNavMenu
                isMobile
                isScrolled={shouldBeTransparent ? isScrolled : true}
                onItemClick={closeMenu}
              />

              {/* Mobile Action Buttons */}
              <IkiaActionButtons isMobile />
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
