'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { SkewedContainer } from '@/components/ui/SkewedContainer'
import { IkiaActionButtons } from '@/components/ikia-navbar'
import { Calendar, Clock, Users } from 'lucide-react'
import { countdownButtons } from '../data/buttonConfigs'

interface TimeLeft {
  days: number
  hours: number
  minutes: number
  seconds: number
}

export default function CountdownSection() {
  // Conference date: September 15, 2025
  const conferenceDate = new Date('2025-11-19T09:00:00').getTime()

  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  })

  const calculateTimeLeft = (): TimeLeft => {
    const now = new Date().getTime()
    const difference = conferenceDate - now

    if (difference > 0) {
      return {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
        minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
        seconds: Math.floor((difference % (1000 * 60)) / 1000),
      }
    }

    return { days: 0, hours: 0, minutes: 0, seconds: 0 }
  }

  useEffect(() => {
    // Set initial time
    setTimeLeft(calculateTimeLeft())

    // Update every second
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const timeUnits = [
    { label: 'Days', value: timeLeft.days },
    { label: 'Hours', value: timeLeft.hours },
    { label: 'Minutes', value: timeLeft.minutes },
    { label: 'Seconds', value: timeLeft.seconds },
  ]

  return (
    <section className="py-12 sm:py-16 lg:py-20 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6">
        {/* Section Header */}
        <div className="text-center mb-12 sm:mb-16">
          {/* SkewedContainer Accent */}
          <div className="flex justify-center mb-4 sm:mb-6">
            <SkewedContainer variant="outlined" size="sm">
              COUNTDOWN
            </SkewedContainer>
          </div>

          <div className="max-w-4xl mx-auto">
            <p className="font-myriad text-base sm:text-lg text-gray-700 max-w-2xl mx-auto">
              Join us for this historic event that will shape the future of indigenous knowledge
              commercialization and sustainable investment in Kenya.
            </p>
          </div>
        </div>

        {/* Clean Countdown Display */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto mb-8 sm:mb-12">
          {timeUnits.map((unit) => (
            <Card
              key={unit.label}
              className="border-2 border-[#7E2518]/20 bg-white hover:border-[#7E2518]/40 transition-all duration-300 hover:shadow-lg"
            >
              <CardContent className="p-4 sm:p-6 text-center space-y-2 sm:space-y-3">
                <div className="font-myriad font-bold text-2xl sm:text-3xl lg:text-4xl xl:text-5xl text-[#7E2518]">
                  {unit.value.toString().padStart(2, '0')}
                </div>
                <div className="font-myriad text-xs sm:text-sm font-semibold text-gray-600 uppercase tracking-wider">
                  {unit.label}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Conference Date Display */}
        <div className="text-center space-y-3 sm:space-y-4 mb-8 sm:mb-12">
          <div className="inline-flex items-center space-x-2 sm:space-x-3 bg-gray-50 rounded-lg px-4 sm:px-6 py-3 sm:py-4 border border-gray-200">
            <div className="w-2 h-2 sm:w-3 sm:h-3 bg-[#E8B32C] rounded-full animate-pulse"></div>
            <span className="font-myriad text-base sm:text-lg font-semibold text-black">
              November 19-21, 2025
            </span>
          </div>
          <p className="font-myriad text-sm text-gray-600">
            Mark your calendar and be part of history
          </p>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <IkiaActionButtons
            buttons={countdownButtons}
            size="large"
            showOnMobile={true}
            layout="horizontal"
            className="justify-center"
          />
        </div>
      </div>
    </section>
  )
}
