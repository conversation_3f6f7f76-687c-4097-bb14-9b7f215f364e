import type { PayloadRequest } from 'payload'
import { EmailService } from '../utils/emailService'

interface PackageRegistrationRequest {
  packageId: string
  userData: {
    name: string
    email: string
    phone_number?: string
    id_number?: string
    county?: string
  }
}

interface PackageRegistrationResponse {
  success: boolean
  data?: {
    userId: string
    invoiceId: string
    paymentReference: string
    checkoutUrl: string
    temporaryPassword?: string
  }
  error?: {
    code: string
    message: string
    details?: any
  }
}

export const packageRegistrationEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('Package registration endpoint called')
    console.log('Request body:', req.body)

    const { packageId, userData } = req.body as PackageRegistrationRequest

    // Validate required fields
    const missingFields = [
      !packageId && 'packageId',
      !userData?.name && 'userData.name',
      !userData?.email && 'userData.email',
    ].filter(Boolean)

    if (missingFields.length > 0) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'MISSING_REQUIRED_FIELDS',
            message: `Missing required fields: ${missingFields.join(', ')}`,
            details: { missingFields },
          },
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get the selected package
    const packageDoc = await req.payload.findByID({
      collection: 'packages',
      id: packageId,
    })

    if (!packageDoc) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'PACKAGE_NOT_FOUND',
            message: 'Selected package not found',
          },
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    if (!packageDoc.isActive) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'PACKAGE_NOT_AVAILABLE',
            message: 'Selected package is not available',
          },
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Check if user already exists
    const existingUsers = await req.payload.find({
      collection: 'users',
      where: {
        email: {
          equals: userData.email,
        },
      },
    })

    let user
    let temporaryPassword: string | undefined
    let userCreated = false

    if (existingUsers.docs.length > 0) {
      // User exists, use existing user
      user = existingUsers.docs[0]
      console.log(`Using existing user: ${user.email}`)
    } else {
      // Create new user
      const emailService = new EmailService(req.payload)
      temporaryPassword = emailService.generateTemporaryPassword()

      try {
        user = await req.payload.create({
          collection: 'users',
          data: {
            name: userData.name,
            email: userData.email,
            password: temporaryPassword,
            phone_number: userData.phone_number,
            id_number: userData.id_number,
            county: userData.county,
            role: 'citizen',
            selected_package: packageId,
            package_status: 'selected',
            registration_context: {
              created_during_package_flow: true,
              temporary_password_sent: false, // Will be updated after email is sent
            },
          },
        })
        userCreated = true
        console.log(`New user created: ${user.email}`)
      } catch (error) {
        console.error('Failed to create user:', error)
        return new Response(
          JSON.stringify({
            success: false,
            error: {
              code: 'USER_CREATION_FAILED',
              message: 'Failed to create user account',
              details: error instanceof Error ? error.message : 'Unknown error',
            },
          }),
          {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }
    }

    // Create invoice for the package
    let invoice
    try {
      invoice = await req.payload.create({
        collection: 'invoices',
        data: {
          user: user.id,
          package: packageId,
          amount: packageDoc.price,
          currency: packageDoc.currency,
          status: 'pending',
          customer_info: {
            name: userData.name,
            email: userData.email,
            phone: userData.phone_number,
            id_number: userData.id_number,
          },
          registration_context: {
            is_registration_payment: true,
            user_created_during_flow: userCreated,
            temporary_password_sent: false,
          },
        },
      })
      console.log(`Invoice created: ${invoice.invoice_number}`)
    } catch (error) {
      console.error('Failed to create invoice:', error)
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'INVOICE_CREATION_FAILED',
            message: 'Failed to create invoice',
            details: error instanceof Error ? error.message : 'Unknown error',
          },
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Update user with initial invoice reference
    if (userCreated) {
      await req.payload.update({
        collection: 'users',
        id: user.id,
        data: {
          package_status: 'payment_pending',
          'registration_context.initial_package_invoice': invoice.id,
        },
      })
    }

    // Send welcome email with credentials if user was created
    if (userCreated && temporaryPassword) {
      const emailService = new EmailService(req.payload)
      const loginUrl = process.env.PAYLOAD_PUBLIC_SERVER_URL
        ? `${process.env.PAYLOAD_PUBLIC_SERVER_URL}/admin/login`
        : 'http://localhost:3000/admin/login'

      const emailSent = await emailService.sendWelcomeEmailWithCredentials({
        user: {
          name: userData.name,
          email: userData.email,
        },
        credentials: {
          email: userData.email,
          temporaryPassword,
        },
        package: {
          name: packageDoc.name,
          price: packageDoc.price,
          currency: packageDoc.currency,
        },
        loginUrl,
        supportEmail: process.env.FROM_EMAIL || '<EMAIL>',
      })

      // Update user to mark email as sent
      if (emailSent) {
        await req.payload.update({
          collection: 'users',
          id: user.id,
          data: {
            'registration_context.temporary_password_sent': true,
          },
        })

        await req.payload.update({
          collection: 'invoices',
          id: invoice.id,
          data: {
            'registration_context.temporary_password_sent': true,
          },
        })
      }
    }

    // Generate checkout URL (this would typically call Pesaflow)
    const checkoutUrl = `${process.env.PAYLOAD_PUBLIC_SERVER_URL || 'http://localhost:3000'}/payment/${invoice.payment_reference}`

    return new Response(
      JSON.stringify({
        success: true,
        data: {
          userId: user.id,
          invoiceId: invoice.id,
          paymentReference: invoice.payment_reference,
          checkoutUrl,
          ...(userCreated && temporaryPassword ? { temporaryPassword } : {}),
        },
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  } catch (error) {
    console.error('Package registration endpoint error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}
