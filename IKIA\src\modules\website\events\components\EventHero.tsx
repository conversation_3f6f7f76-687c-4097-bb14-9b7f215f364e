import { Calendar, Clock } from 'lucide-react'

export default function EventHero() {
  return (
    <section className="relative bg-white pt-24 pb-16 lg:pt-28 lg:pb-24 overflow-hidden">
      {/* Full Width Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('/images/hero-background.png')",
        }}
      >
        {/* Dark Overlay for Text Readability */}
        <div className="absolute inset-0 bg-black/40"></div>

        {/* Gradient Overlay for Better Text Contrast */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#7E2518]/80 via-[#7E2518]/60 to-[#7E2518]/40"></div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight drop-shadow-2xl">
            Intelligence worth investing
          </h1>

          <div className="inline-flex items-center bg-[#7E2518]/90 backdrop-blur-sm text-white px-6 py-3 text-lg font-medium shadow-2xl border border-white/20">
            <Calendar className="w-5 h-5 mr-2" />
            <span className="mr-4">Thu 20th Nov 2025</span>
            <Clock className="w-5 h-5 mr-2" />
            <span>(9:00am - 11:30am)</span>
          </div>

          <div className="mt-8 max-w-2xl mx-auto">
            <p className="text-lg text-white leading-relaxed drop-shadow-lg bg-black/20 backdrop-blur-sm p-6 border border-white/20">
              Join us for an exclusive event exploring Indigenous Knowledge and Innovation Assets
              (IKIA) and their potential for sustainable investment and community development.
            </p>
          </div>

          {/* Call to Action Buttons */}
          <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-[#E8B32C] hover:bg-[#C86E36] text-[#7E2518] font-bold px-8 py-4 text-lg transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:-translate-y-1">
              Register Now
            </button>
            <button className="bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white font-bold px-8 py-4 text-lg transition-all duration-300 border-2 border-white/30 hover:border-white/50">
              Learn More
            </button>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#E8B32C] via-[#C86E36] to-[#159147]"></div>
    </section>
  )
}
