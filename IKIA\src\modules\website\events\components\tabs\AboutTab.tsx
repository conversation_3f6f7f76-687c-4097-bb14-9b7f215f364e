export default function AboutTab() {
  return (
    <div className="space-y-8">
      {/* Main Content Grid */}
      <div className="grid lg:grid-cols-2 gap-12 items-start">
        {/* Enhanced Illustration */}
        <div className="flex justify-center lg:justify-start">
          <div className="relative">
            <div className="w-80 h-96 bg-gradient-to-br from-[#81B1DB]/10 to-[#159147]/10 flex items-center justify-center border-2 border-[#81B1DB]/20">
              <svg
                className="w-64 h-80 text-[#81B1DB]"
                viewBox="0 0 200 250"
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg"
              >
                {/* Enhanced Tree-like illustration */}
                <path
                  d="M100 240V180M100 180L80 160M100 180L120 160M80 160L60 140M80 160L100 140M120 160L100 140M120 160L140 140"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="none"
                />
                <circle cx="60" cy="140" r="10" />
                <circle cx="100" cy="140" r="10" />
                <circle cx="140" cy="140" r="10" />
                <path
                  d="M40 120L60 100L80 120M80 120L100 100L120 120M120 120L140 100L160 120"
                  stroke="currentColor"
                  strokeWidth="3"
                  fill="none"
                />
                <rect
                  x="85"
                  y="15"
                  width="30"
                  height="80"
                  rx="15"
                  fill="currentColor"
                  opacity="0.3"
                />
                <text
                  x="100"
                  y="35"
                  textAnchor="middle"
                  className="text-xl font-bold fill-[#7E2518]"
                >
                  IKIA
                </text>
                <text x="100" y="55" textAnchor="middle" className="text-sm fill-[#7E2518]">
                  Knowledge
                </text>
                <text x="100" y="70" textAnchor="middle" className="text-sm fill-[#7E2518]">
                  Innovation
                </text>
                <text x="100" y="85" textAnchor="middle" className="text-sm fill-[#7E2518]">
                  Assets
                </text>
              </svg>
            </div>
            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 w-8 h-8 bg-[#E8B32C] opacity-20"></div>
            <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-[#159147] opacity-20"></div>
          </div>
        </div>

        {/* Enhanced Content */}
        <div className="space-y-6">
          <div className="bg-[#7E2518]/5 p-6 border-l-4 border-[#7E2518]">
            <h3 className="text-2xl font-bold text-[#7E2518] mb-4">
              Indigenous Knowledge Intelligence Assets (IKIA)
            </h3>
            <p className="text-gray-700 leading-relaxed text-lg">
              <strong className="text-[#7E2518]">
                Indigenous Knowledge Intelligence Assets (IKIA)
              </strong>{' '}
              are the time-tested wisdom and practices of Indigenous communities. Passed down
              through generations, they reflect a deep understanding of nature, society, and
              spirituality.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-[#159147]/5 p-4 border border-[#159147]/20">
              <h4 className="font-bold text-[#159147] mb-2">🌱 Practical Solutions</h4>
              <p className="text-gray-700 text-sm leading-relaxed">
                IKIA offers practical solutions in areas like agriculture, medicine, and climate
                adaptation. Traditional farming and land management methods help sustain
                biodiversity.
              </p>
            </div>

            <div className="bg-[#E8B32C]/5 p-4 border border-[#E8B32C]/20">
              <h4 className="font-bold text-[#C86E36] mb-2">🛡️ Protection & Respect</h4>
              <p className="text-gray-700 text-sm leading-relaxed">
                These assets must be protected from exploitation, with Indigenous communities
                leading how their knowledge is shared and used.
              </p>
            </div>
          </div>

          <div className="bg-[#159147]/10 border border-[#159147]/30 p-6">
            <div className="flex items-start space-x-3">
              <div className="text-2xl">💡</div>
              <div>
                <h4 className="font-bold text-[#159147] mb-2">Event Focus</h4>
                <p className="text-[#159147] font-medium">
                  This event will explore how IKIA can drive sustainable development while
                  respecting Indigenous rights and knowledge systems.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Key Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-12">
        <div className="text-center p-4 bg-[#7E2518]/5 border border-[#7E2518]/20">
          <div className="text-2xl font-bold text-[#7E2518]">47</div>
          <div className="text-sm text-gray-600">Counties Represented</div>
        </div>
        <div className="text-center p-4 bg-[#159147]/5 border border-[#159147]/20">
          <div className="text-2xl font-bold text-[#159147]">200+</div>
          <div className="text-sm text-gray-600">Knowledge Assets</div>
        </div>
        <div className="text-center p-4 bg-[#E8B32C]/5 border border-[#E8B32C]/20">
          <div className="text-2xl font-bold text-[#C86E36]">15</div>
          <div className="text-sm text-gray-600">Communities</div>
        </div>
        <div className="text-center p-4 bg-[#81B1DB]/5 border border-[#81B1DB]/20">
          <div className="text-2xl font-bold text-[#81B1DB]">50+</div>
          <div className="text-sm text-gray-600">Researchers</div>
        </div>
      </div>
    </div>
  )
}
