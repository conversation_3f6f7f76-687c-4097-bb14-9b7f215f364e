-- =============================================================================
-- DATABASE INDEXING SCRIPT FOR IKIA PAYMENT SYSTEM
-- =============================================================================
-- This script creates database indexes for optimal performance
-- Run this script after database setup to improve query performance
-- 
-- Usage: psql -d your_database -f scripts/create-database-indexes.sql
-- =============================================================================

-- Users Collection Indexes
-- =============================================================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email);
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_users_id_number ON users(id_number) WHERE id_number IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_phone_number ON users(phone_number) WHERE phone_number IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_county ON users(county);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at ON users(created_at DESC);

-- Invoices Collection Indexes
-- =============================================================================
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_invoice_number ON invoices(invoice_number);
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_payment_reference ON invoices(payment_reference);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_user ON invoices(user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_status ON invoices(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_created_at ON invoices(created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_due_date ON invoices(due_date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_user_status ON invoices(user_id, status);

-- PesaflowNotifications Collection Indexes
-- =============================================================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_payment_reference ON pesaflow_notifications(payment_reference) WHERE payment_reference IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_invoice_number ON pesaflow_notifications(invoice_number);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_client_invoice_ref ON pesaflow_notifications(client_invoice_ref);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_status ON pesaflow_notifications(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_payment_date ON pesaflow_notifications(payment_date DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_created_at ON pesaflow_notifications(created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_secure_hash ON pesaflow_notifications(secure_hash);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_invoice_status ON pesaflow_notifications(invoice_number, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_user_status ON pesaflow_notifications(user_id, status) WHERE user_id IS NOT NULL;

-- Exhibitors Collection Indexes
-- =============================================================================
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_exhibitors_email ON exhibitors(email);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exhibitors_company_name ON exhibitors(company_name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exhibitors_country ON exhibitors(country);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exhibitors_registration_status ON exhibitors("registrationStatus");
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exhibitors_selected_package ON exhibitors("selectedPackage");
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exhibitors_created_at ON exhibitors(created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exhibitors_country_status ON exhibitors(country, "registrationStatus");

-- Events Collection Indexes
-- =============================================================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_title ON events(title);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_date ON events(date);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_type ON events(type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_date_type ON events(date, type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_created_at ON events(created_at DESC);

-- Counties Collection Indexes
-- =============================================================================
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_counties_name ON counties(name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_counties_code ON counties(code) WHERE code IS NOT NULL;

-- DelegatePackages Collection Indexes
-- =============================================================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_delegate_packages_name ON delegatepackages(name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_delegate_packages_price ON delegatepackages(price);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_delegate_packages_currency ON delegatepackages(currency);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_delegate_packages_active ON delegatepackages(active);

-- Speakers Collection Indexes
-- =============================================================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_speakers_name ON speakers(name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_speakers_organization ON speakers(organization) WHERE organization IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_speakers_country ON speakers(country) WHERE country IS NOT NULL;

-- Programs Collection Indexes
-- =============================================================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_programs_title ON programs(title);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_programs_featured ON programs("isFeatured");
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_programs_created_at ON programs(created_at DESC);

-- Posts Collection Indexes
-- =============================================================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_title ON posts(title);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_slug ON posts(slug);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_published_at ON posts(published_at DESC) WHERE published_at IS NOT NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_status ON posts("_status");

-- Media Collection Indexes
-- =============================================================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_media_filename ON media(filename);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_media_mime_type ON media("mimeType");
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_media_created_at ON media(created_at DESC);

-- =============================================================================
-- PERFORMANCE OPTIMIZATION INDEXES
-- =============================================================================

-- Composite indexes for common query patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_user_status_created ON invoices("user", status, created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_invoice_date_status ON pesaflow_notifications(invoice_number, payment_date DESC, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_role_created ON users(role, created_at DESC);

-- =============================================================================
-- FULL-TEXT SEARCH INDEXES (PostgreSQL specific)
-- =============================================================================

-- Full-text search for events
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_fulltext ON events USING gin(to_tsvector('english', title || ' ' || COALESCE(description::text, '')));

-- Full-text search for programs
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_programs_fulltext ON programs USING gin(to_tsvector('english', title || ' ' || COALESCE(description::text, '')));

-- Full-text search for posts
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_fulltext ON posts USING gin(to_tsvector('english', title || ' ' || COALESCE(content::text, '')));

-- =============================================================================
-- PARTIAL INDEXES FOR SPECIFIC CONDITIONS
-- =============================================================================

-- Index only active/published records
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_posts_published ON posts(published_at DESC) WHERE "_status" = 'published';
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_pending ON invoices(created_at DESC) WHERE status IN ('pending', 'processing');
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_recent_settled ON pesaflow_notifications(payment_date DESC) WHERE status = 'settled' AND payment_date > NOW() - INTERVAL '30 days';

-- =============================================================================
-- ANALYZE TABLES FOR QUERY OPTIMIZATION
-- =============================================================================

ANALYZE users;
ANALYZE invoices;
ANALYZE pesaflow_notifications;
ANALYZE exhibitors;
ANALYZE events;
ANALYZE counties;
ANALYZE delegatepackages;
ANALYZE speakers;
ANALYZE programs;
ANALYZE posts;
ANALYZE media;

-- =============================================================================
-- INDEX CREATION SUMMARY
-- =============================================================================

-- Display index creation summary
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
    AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;
