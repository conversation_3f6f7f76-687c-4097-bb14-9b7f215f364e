# Counties API Integration

This directory contains the frontend API client for integrating with the counties backend endpoints.

## Structure

```
src/lib/api/
├── counties.ts           # Main API client
├── hooks/
│   └── useCounties.ts   # React hooks for counties
├── components/
│   └── CountiesDebug.tsx # Debug component
├── index.ts             # Exports
└── README.md           # This file
```

## Usage

### Basic API Client

```typescript
import { countiesApi } from '@/lib/api/counties'

// Get all active counties
const response = await countiesApi.getActiveCounties()

// Search counties by name
const results = await countiesApi.searchCounties('Nairobi')

// Get a specific county
const county = await countiesApi.getCounty('001')
```

### React Hooks

```typescript
import { useActiveCounties, useCounty } from '@/lib/api/hooks/useCounties'

function MyComponent() {
  // Get all active counties
  const { counties, loading, error } = useActiveCounties()

  // Get a specific county
  const { county } = useCounty('001')

  return (
    <div>
      {loading && <p>Loading...</p>}
      {error && <p>Error: {error}</p>}
      {counties.map(county => (
        <div key={county.id}>{county.name}</div>
      ))}
    </div>
  )
}
```

### Map Integration

The `KenyanMap` component now automatically integrates with the counties API:

- **Active counties** from the backend are highlighted on the map
- **Fallback data** is used if the API is unavailable
- **Loading states** and **error handling** are built-in
- **Debug information** is logged to the console

## API Endpoints

The frontend client connects to these backend endpoints:

- `GET /api/counties` - List counties with filtering
- `GET /api/counties/:id` - Get single county
- `GET /api/counties/bounds` - Get counties in bounding box

## Configuration

The API client automatically detects the correct base URL:

- **Browser**: Uses `window.location.origin`
- **Server**: Uses `NEXT_PUBLIC_SITE_URL` or defaults to `localhost:3000`

## Error Handling

All API calls include comprehensive error handling:

- **Network errors** are caught and logged
- **HTTP errors** are converted to meaningful messages
- **Fallback data** is used when appropriate
- **Loading states** prevent UI blocking

## Testing

Use the debug component to verify API integration:

```typescript
import { CountiesDebug } from '@/lib/api/components/CountiesDebug'

// Add to any page temporarily
<CountiesDebug />
```

## Backend Requirements

Ensure the counties collection and endpoints are properly configured:

1. **Counties collection** exists in Payload CMS
2. **Counties endpoints** are registered in the API routes
3. **Active counties** are marked with `isActive: true`
4. **Proper CORS** settings for frontend requests

## Recommendations

### For County Names on Hover

I recommend **using the backend data** for county names because:

1. **Consistency**: All county data comes from one source
2. **Flexibility**: Easy to add/remove counties without code changes
3. **Accuracy**: Backend data is the single source of truth
4. **Maintenance**: No need to keep hardcoded lists in sync

### Fallback Strategy

The current implementation provides the best of both worlds:

- **Primary**: Backend API data (dynamic, accurate)
- **Fallback**: Hardcoded data (reliable, always available)
- **Graceful degradation**: Map works even if API fails

This ensures the map is always functional while providing the most up-to-date data when possible.
