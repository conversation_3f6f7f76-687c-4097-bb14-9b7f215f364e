import { Button } from '@/components/ui/button'
import { ArrowRight, Crown, Award, Trophy, Medal, Building } from 'lucide-react'
import { getSponsors, type SponsorData } from '@/lib/api/sponsors'
import Image from 'next/image'
import Link from 'next/link'

const tierIcons = {
  title: Crown,
  platinum: Award,
  gold: Trophy,
  silver: Medal,
  bronze: Building,
}

const tierColors = {
  title: 'bg-[#7E2518]',
  platinum: 'bg-[#81B1DB]',
  gold: 'bg-[#E8B32C]',
  silver: 'bg-gray-400',
  bronze: 'bg-[#C86E36]',
}

export default async function SponsorsShowcase() {
  // Fetch sponsors from API
  const sponsors = await getSponsors()

  // Group sponsors by tier
  const sponsorsByTier = sponsors.reduce(
    (acc, sponsor) => {
      const tier = sponsor.tier || 'other'
      if (!acc[tier]) acc[tier] = []
      acc[tier].push(sponsor)
      return acc
    },
    {} as Record<string, SponsorData[]>,
  )

  const tierOrder = ['title', 'platinum', 'gold', 'silver', 'bronze', 'other']

  return (
    <section className="py-16 lg:py-20 section-bg-primary">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center">
            <span className="text-[#7E2518] font-bold px-3 py-1 text-xs  border border-[#7E2518]/20 flex items-center space-x-1 transform -skew-x-12 mb-4">
              <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
              <span className="ml-2 text-sm font-medium text-[#7E2518] font-['Myriad_Pro',Arial,sans-serif]">
                Our Sponsors
              </span>
            </span>
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#7E2518] mb-6 font-['Myriad_Pro',Arial,sans-serif]">
            Conference
            <span className="block bg-gradient-to-r from-[#E8B32C] to-[#C86E36] bg-clip-text text-transparent">
              Sponsors
            </span>
          </h2>

          <div className="w-24 h-1 bg-[#E8B32C] mx-auto mb-6"></div>

          <p className="text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto font-['Myriad_Pro',Arial,sans-serif]">
            Meet the organizations supporting Kenya&apos;s first International Investment Conference
            on Indigenous Knowledge Intellectual Assets.
          </p>
        </div>

        {/* Sponsors by Tier */}
        <div className="space-y-12">
          {tierOrder.map((tier) => {
            const tierSponsors = sponsorsByTier[tier]
            if (!tierSponsors || tierSponsors.length === 0) return null

            const TierIcon = tierIcons[tier as keyof typeof tierIcons] || Building
            const tierColor = tierColors[tier as keyof typeof tierColors] || 'bg-gray-400'

            return (
              <div key={tier} className="relative">
                {/* Tier Header */}
                <div className="flex items-center justify-center mb-8">
                  <div className="flex items-center gap-4">
                    <div
                      className={`w-12 h-12 ${tierColor} flex items-center justify-center main-shadow`}
                    >
                      <TierIcon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-[#7E2518] capitalize font-['Myriad_Pro',Arial,sans-serif]">
                      {tier === 'other' ? 'Service & Media Partners' : `${tier} Sponsors`}
                    </h3>
                  </div>
                </div>

                {/* Static Logo Grid */}
                <div className="bg-white main-shadow border border-gray-100 py-8">
                  {/* Special handling for title sponsors with multiple logos */}
                  {tier === 'title' ? (
                    <div className="flex justify-center">
                      {tierSponsors.map((sponsor, index) => {
                        // Check if sponsor has multiple logos (like Safaricom + M-Pesa)
                        const hasMultipleLogos =
                          sponsor.additionalLogos && sponsor.additionalLogos.length > 0

                        return (
                          <div
                            key={`${sponsor.id}-${index}`}
                            className={`group cursor-pointer ${hasMultipleLogos ? 'bg-white rounded-xl shadow-lg border border-gray-100 p-6' : ''}`}
                          >
                            {hasMultipleLogos ? (
                              // Multiple logos in one card with vertical separator
                              <div className="flex items-center gap-6">
                                <div className="flex items-center justify-center">
                                  <Image
                                    src={sponsor.logo.url}
                                    alt={sponsor.logo.alt}
                                    width={120}
                                    height={80}
                                    className="object-contain max-w-full max-h-full"
                                  />
                                </div>

                                {/* Vertical separator */}
                                <div className="w-px h-16 bg-gray-300"></div>

                                {sponsor.additionalLogos?.map((additionalLogo, logoIndex) => (
                                  <div key={logoIndex} className="flex items-center justify-center">
                                    <Image
                                      src={additionalLogo.logo.url}
                                      alt={additionalLogo.logo.alt}
                                      width={120}
                                      height={80}
                                      className="object-contain max-w-full max-h-full"
                                    />
                                  </div>
                                ))}
                              </div>
                            ) : (
                              // Single logo
                              <div className="w-40 h-24 bg-gray-50 rounded-lg flex items-center justify-center p-4 group-hover:bg-gray-100 transition-colors duration-300 group-hover:shadow-lg">
                                <Image
                                  src={sponsor.logo.url}
                                  alt={sponsor.logo.alt}
                                  width={128}
                                  height={80}
                                  className="object-contain max-w-full max-h-full"
                                />
                              </div>
                            )}
                          </div>
                        )
                      })}
                    </div>
                  ) : (
                    // Regular grid for other tiers
                    <div className="flex flex-wrap justify-center items-center gap-8">
                      {tierSponsors.map((sponsor, index) => (
                        <div
                          key={`${sponsor.id}-${index}`}
                          className="flex-shrink-0 group cursor-pointer"
                        >
                          <div className="w-32 h-20 bg-gray-50 rounded-lg flex items-center justify-center p-4 group-hover:bg-gray-100 transition-colors duration-300 group-hover:shadow-lg">
                            <Image
                              src={sponsor.logo.url}
                              alt={sponsor.logo.alt}
                              width={96}
                              height={64}
                              className="object-contain max-w-full max-h-full filter grayscale group-hover:grayscale-0 transition-all duration-300"
                            />
                            <div className="hidden w-16 h-12 bg-[#7E2518] rounded flex items-center justify-center">
                              <Building className="w-8 h-8 text-white" />
                            </div>
                          </div>
                          <p className="text-center text-sm text-gray-600 mt-2 group-hover:text-[#7E2518] transition-colors duration-300 font-['Myriad_Pro',Arial,sans-serif]">
                            {sponsor.name}
                          </p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>

        {/* CTA Section */}
        {/* <div className="text-center mt-16">
          <div className="bg-white main-shadow border border-gray-100 p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-[#7E2518] mb-4 font-['Myriad_Pro',Arial,sans-serif]">
              Want to See All Our Sponsors?
            </h3>
            <p className="text-gray-600 mb-6 font-['Myriad_Pro',Arial,sans-serif]">
              Explore our complete sponsor directory with detailed information about each partner.
            </p>
            <Button
              asChild
              className="bg-[#7E2518] hover:bg-[#159147] text-white border-0 font-bold transition-all duration-500 transform hover:-translate-y-1 px-8 py-3 main-shadow font-['Myriad_Pro',Arial,sans-serif]"
            >
              <Link href="/sponsors" className="flex items-center gap-2">
                View All Sponsors
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
          </div>
        </div> */}
      </div>
    </section>
  )
}
