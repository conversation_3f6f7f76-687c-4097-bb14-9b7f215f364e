import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'

export const ProgramTypes: CollectionConfig = {
  slug: 'program-types',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'color', 'description'],
    group: 'Conference',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Type Name',
      admin: {
        description: 'Name of the program type (e.g., Keynote, Panel Discussion)',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Description',
      admin: {
        description: 'Brief description of this program type',
        rows: 3,
      },
    },
    {
      name: 'color',
      type: 'text',
      label: 'Color Code',
      admin: {
        description:
          'CSS color class or hex code for UI display (e.g., bg-red-50 border-red-200 text-red-700)',
      },
    },
    {
      name: 'icon',
      type: 'text',
      label: 'Icon Name',
      admin: {
        description: 'Lucide icon name for display (optional)',
      },
    },
    {
      name: 'isActive',
      type: 'checkbox',
      label: 'Active',
      defaultValue: true,
      admin: {
        description: 'Whether this program type is currently active',
      },
    },
    ...slugField(),
  ],
  timestamps: true,
}

export default ProgramTypes
