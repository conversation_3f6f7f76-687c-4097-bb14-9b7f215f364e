# User County Update Scripts

## 🎯 **Goal**
Update all existing users to have county ID 2.

## 📋 **Available Methods**

### **Method 1: Node.js Script (Recommended)**
```bash
# Run the Node.js script
node scripts/update-users-county.js
```

**Features:**
- ✅ Detailed progress reporting
- ✅ Error handling and retry logic
- ✅ Verification of target county
- ✅ Shows users before updating
- ✅ Summary report

### **Method 2: Bash <PERSON>t**
```bash
# Run the bash script
./scripts/update-users-county.sh
```

**Features:**
- ✅ Interactive confirmation
- ✅ Real-time progress updates
- ✅ Uses standard Unix tools (curl, jq)
- ✅ Verification after updates

### **Method 3: Manual Curl Commands**
```bash
# Login first
TOKEN=$(curl -s -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}' \
  | jq -r '.token')

# Get all users and update each one
curl -s "http://localhost:3000/api/users?limit=1000" | \
jq -r '.docs[] | select(.county != 2) | .id' | \
while read user_id; do
  echo "Updating user $user_id..."
  curl -X PUT "http://localhost:3000/api/users/$user_id" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{"county": 2}'
done
```

## ⚙️ **Configuration**

### **Update Credentials**
Before running any script, update the admin credentials:

**In Node.js script (`scripts/update-users-county.js`):**
```javascript
const ADMIN_EMAIL = '<EMAIL>'
const ADMIN_PASSWORD = 'your-admin-password'
```

**In Bash script (`scripts/update-users-county.sh`):**
```bash
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your-admin-password"
```

### **Change Target County**
To update users to a different county, change the target county ID:

**Node.js:**
```javascript
const targetCountyId = 3  // Change from 2 to 3
```

**Bash:**
```bash
TARGET_COUNTY_ID=3  # Change from 2 to 3
```

## 🔍 **Pre-Update Verification**

### **Check Current User Distribution:**
```bash
# See how many users are in each county
curl -s "http://localhost:3000/api/users?limit=1000" | \
jq -r '.docs[] | .county // "null"' | sort | uniq -c
```

### **Verify Target County Exists:**
```bash
# Check if county 2 exists
curl "http://localhost:3000/api/counties/2"
```

### **Count Users to Update:**
```bash
# Count users that don't have county 2
curl -s "http://localhost:3000/api/users?limit=1000" | \
jq '[.docs[] | select(.county != 2)] | length'
```

## 🚀 **Step-by-Step Process**

### **1. Preparation**
```bash
# Ensure you have the required tools
which node  # For Node.js script
which curl jq  # For bash script

# Update credentials in the script files
# Test login manually
curl -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"your-password"}'
```

### **2. Run the Update**
```bash
# Choose one method:

# Option A: Node.js (recommended)
node scripts/update-users-county.js

# Option B: Bash script
./scripts/update-users-county.sh

# Option C: Manual curl commands (see above)
```

### **3. Verification**
```bash
# Check users in county 2
curl "http://localhost:3000/api/counties/2/users"

# Count total users in county 2
curl -s "http://localhost:3000/api/counties/2/users" | jq '.totalUsers'

# Verify no users are without a county
curl -s "http://localhost:3000/api/users?limit=1000" | \
jq '[.docs[] | select(.county == null)] | length'
```

## 📊 **Expected Output**

### **Before Update:**
```
Users analysis:
  - Total users: 15
  - Users already in county 2: 3
  - Users to update: 12
```

### **During Update:**
```
[1/12] Updating John Doe...
  ✅ Success
[2/12] Updating Jane Smith...
  ✅ Success
...
```

### **After Update:**
```
Update Summary:
  ✅ Successful updates: 12
  ❌ Failed updates: 0

🎉 All users successfully updated to county 2!
```

## 🛠️ **Troubleshooting**

### **Common Issues:**

**1. Authentication Failed**
```
❌ Login failed: Invalid credentials
```
**Solution:** Update `ADMIN_EMAIL` and `ADMIN_PASSWORD` in the script

**2. County Not Found**
```
❌ County 2 not found!
```
**Solution:** Create county 2 first or change target county ID

**3. Permission Denied**
```
❌ Failed: Unauthorized
```
**Solution:** Ensure the admin user has permission to update users

**4. Network Errors**
```
❌ Error: Connection refused
```
**Solution:** Ensure the server is running on `http://localhost:3000`

### **Manual Verification:**
```bash
# Check server is running
curl -s "http://localhost:3000/api/counties" | jq '.totalCounties'

# Test authentication
TOKEN=$(curl -s -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}' \
  | jq -r '.token')
echo "Token: $TOKEN"

# Test user update manually
curl -X PUT "http://localhost:3000/api/users/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"county": 2}'
```

## 🔄 **Rollback**

If you need to rollback the changes:

```bash
# Update all users back to no county
curl -s "http://localhost:3000/api/users?limit=1000" | \
jq -r '.docs[].id' | \
while read user_id; do
  curl -X PUT "http://localhost:3000/api/users/$user_id" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{"county": null}'
done

# Or update to a different county
# Change '{"county": null}' to '{"county": 1}' for county 1
```

## 📝 **Notes**

- Scripts include safety checks and confirmations
- Updates are done one by one to avoid overwhelming the server
- All scripts verify the target county exists before updating
- Progress is reported in real-time
- Errors are logged and reported in the summary

Choose the method that works best for your environment! 🎉
