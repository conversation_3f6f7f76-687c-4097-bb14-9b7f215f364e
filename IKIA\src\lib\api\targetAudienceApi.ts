import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

export const targetAudienceApi = createApi({
  reducerPath: 'targetAudienceApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }), // Adjust to your Payload base URL
  endpoints: (builder) => ({
    getTargetAudiences: builder.query<any, void>({
      query: () => 'target-audience?limit=0', // Or limit=0 if supported
    }),
  }),
})

export const { useGetTargetAudiencesQuery } = targetAudienceApi
