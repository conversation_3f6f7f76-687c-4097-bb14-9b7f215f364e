import type { PayloadRequest } from 'payload'
import { HashService } from '../utils/pesaflowHash'

type CheckoutRequestBody = {
  clientMSISDN?: string
  clientIDNumber?: string
  clientName?: string
  clientEmail?: string
  currency?: string
  amountExpected?: string
  format?: string
  sendSTK?: string
}

interface PesaflowResponse {
  status: (code: number) => PesaflowResponse
  json: (data: Record<string, unknown>) => void
}

export const pesaflowCheckoutEndpoint = async (
  req: PayloadRequest & { body: CheckoutRequestBody },
  res: PesaflowResponse,
): Promise<void> => {
  // Extract user data from request body (all user-specific data comes from frontend)
  const {
    clientMSISDN,
    clientIDNumber,
    clientName,
    clientEmail,
    currency,
    amountExpected,
    format,
    sendSTK,
  } = req.body

  // Validate required user data
  const missingUserData = [
    !clientMSISDN && 'clientMSISDN',
    !clientIDNumber && 'clientIDNumber',
    !clientName && 'clientName',
    !clientEmail && 'clientEmail',
    !currency && 'currency',
    !amountExpected && 'amountExpected',
  ].filter(Boolean)

  if (missingUserData.length > 0) {
    return res.status(400).json({
      error: 'Missing required user data',
      missingFields: missingUserData,
      message: `Please provide the following required fields: ${missingUserData.join(', ')}`,
    })
  }

  const missingFields = [
    !clientMSISDN && 'Mobile number (MSISDN)',
    !clientIDNumber && 'ID number',
    !clientName && 'Name',
    !clientEmail && 'Email',
    !currency && 'Currency',
    !amountExpected && 'Amount expected',
    !format && 'Format',
    !sendSTK && 'Send STK',
  ].filter(Boolean)

  if (missingFields.length > 0) {
    return res.status(400).json({
      error: 'Missing required client fields',
      missingFields,
      message: `Please provide the following required fields: ${missingFields.join(', ')}`,
    })
  }

  // Get environment variables for URLs and configuration
  const {
    PESAFLOW_REQUEST_SERVICE_ID: serviceID,
    PESAFLOW_BILL_DESC: billDesc,
    PESAFLOW_NOTIFICATION_URL: notificationURL,
    PESAFLOW_PICTURE_URL: pictureURL,
    PESAFLOW_CALLBACK_SUCCESS_URL: callBackURLOnSuccess,
    PESAFLOW_CALLBACK_CANCEL_URL: cancelURL,
    PESAFLOW_UAT_SERVER_URL: PESAFLOW_UAT_SERVER_URL,
  } = process.env

  const billRefNumber = 'billref-' + new Date().getTime() // Generate unique bill reference number

  // Check for required environment variables
  const missingConfig = [
    !serviceID && 'PESAFLOW_REQUEST_SERVICE_ID',
    !billDesc && 'PESAFLOW_BILL_DESC',
    !PESAFLOW_UAT_SERVER_URL && 'PESAFLOW_UAT_SERVER_URL',
  ].filter(Boolean)

  if (missingConfig.length > 0) {
    return res.status(400).json({
      error: 'Missing required Pesaflow configuration',
      missingConfig,
      message: `Missing environment variables: ${missingConfig.join(', ')}`,
    })
  }

  // Use HashService to generate hash and get API client ID
  let hashService: HashService
  try {
    hashService = HashService.getInstance()
  } catch (error) {
    return res.status(500).json({
      error: 'HashService initialization failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    })
  }

  // Generate hash using HashService with all required Pesaflow parameters
  // data_string = apiClientID + amountExpected + serviceID + clientIDNumber + currency + billRefNumber + billDesc + clientName + secret
  const { hash: secureHash, api_client_id: apiClientID } = hashService.generateCheckoutHash(
    amountExpected || '',
    serviceID || '',
    clientIDNumber || '',
    currency || '',
    billRefNumber,
    billDesc || '',
    clientName || '',
  )

  const payloadToSend = {
    apiClientID,
    serviceID,
    billRefNumber,
    billDesc,
    clientMSISDN,
    clientIDNumber,
    clientName,
    clientEmail,
    notificationURL,
    pictureURL,
    callBackURLOnSuccess,
    currency,
    amountExpected,
    format,
    sendSTK,
    secureHash,
  }

  try {
    // Ensure URL is properly formatted and valid
    const baseUrl = PESAFLOW_UAT_SERVER_URL?.endsWith('/')
      ? PESAFLOW_UAT_SERVER_URL.slice(0, -1)
      : PESAFLOW_UAT_SERVER_URL || ''

    // Validate URL
    if (!baseUrl || !baseUrl.startsWith('http')) {
      console.error('Invalid Pesaflow URL:', baseUrl)
      return res.status(500).json({
        error: 'Invalid Pesaflow configuration',
        message: 'PESAFLOW_UAT_SERVER_URL must be a valid URL starting with http:// or https://',
        actualValue: baseUrl || 'undefined',
      })
    }

    // Add timeout for the entire checkout process
    const CHECKOUT_TIMEOUT = 45000 // 45 seconds
    const controller = new AbortController()
    const timeoutId = setTimeout(() => {
      controller.abort()
    }, CHECKOUT_TIMEOUT)

    console.log(`🚀 Initiating checkout request (timeout: ${CHECKOUT_TIMEOUT / 1000}s)...`)
    const startTime = Date.now()

    const response = await fetch(baseUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payloadToSend),
      signal: controller.signal,
    })

    clearTimeout(timeoutId)
    const responseTime = Date.now() - startTime
    console.log(`📡 Checkout response received in ${responseTime}ms`)

    // Handle different response types
    const contentType = response.headers.get('content-type')

    // If response is not JSON, it might be a redirect or HTML page
    if (!contentType?.includes('application/json')) {
      // Check if it's a successful response (2xx status) or redirect (3xx status)
      if (response.ok || (response.status >= 300 && response.status < 400)) {
        // For successful non-JSON responses, check if it's a redirect
        const location = response.headers.get('location')
        if (location) {
          // Direct redirect response
          console.log('Pesaflow redirect response:', { location, status: response.status })
          return res.status(302).json({
            success: true,
            redirect_url: location,
            message: 'Redirecting to payment gateway',
            requestId: billRefNumber,
          })
        }

        // If no location header, get the response text (might be HTML with redirect)
        const text = await response.text()
        console.log('Pesaflow HTML response received:', {
          contentType,
          status: response.status,
          hasContent: text.length > 0,
        })

        // Check if the HTML contains a redirect URL or form
        const redirectMatch = text.match(
          /(?:window\.location|location\.href)\s*=\s*["']([^"']+)["']/i,
        )
        const formActionMatch = text.match(/<form[^>]+action\s*=\s*["']([^"']+)["']/i)

        // Check for location.reload() or other redirect patterns
        const reloadMatch = text.match(/location\.reload\(\)/i)
        const metaRefreshMatch = text.match(
          /<meta[^>]+http-equiv\s*=\s*["']refresh["'][^>]+content\s*=\s*["'][^"']*url=([^"']+)["']/i,
        )

        if (redirectMatch) {
          return res.status(200).json({
            success: true,
            redirect_url: redirectMatch[1],
            message: 'Redirecting to payment gateway',
            requestId: billRefNumber,
          })
        } else if (formActionMatch) {
          return res.status(200).json({
            success: true,
            redirect_url: formActionMatch[1],
            message: 'Redirecting to payment gateway',
            requestId: billRefNumber,
            html_content: text, // Include HTML for form submission
          })
        } else if (metaRefreshMatch) {
          return res.status(200).json({
            success: true,
            redirect_url: metaRefreshMatch[1],
            message: 'Redirecting to payment gateway',
            requestId: billRefNumber,
          })
        } else if (reloadMatch) {
          // For location.reload(), we need to return the current URL or let frontend handle it
          return res.status(200).json({
            success: true,
            reload_required: true,
            message: 'Payment gateway requires page reload',
            requestId: billRefNumber,
            html_content: text,
          })
        } else {
          // Return the HTML content for the frontend to handle
          return res.status(200).json({
            success: true,
            html_content: text,
            message: 'Payment gateway response received',
            requestId: billRefNumber,
          })
        }
      } else {
        // Non-JSON error response
        const text = await response.text()
        console.error('Pesaflow non-JSON error:', {
          status: response.status,
          contentType,
          response: text,
        })

        return res.status(response.status).json({
          error: 'Payment Processing Failed',
          message: 'Payment gateway returned an error. Please try again.',
          requestId: billRefNumber,
        })
      }
    }

    // Handle JSON responses
    const data = await response.json()

    // Check if response status is not successful
    if (!response.ok) {
      // Log technical details for debugging
      console.error('Pesaflow Technical Error:', {
        type: 'API Error Response',
        status: response.status,
        url: response.url,
        requestBody: payloadToSend,
        responseData: data,
      })

      return res.status(500).json({
        error: 'Payment Processing Failed',
        message: 'We could not process your payment at this time. Please try again later.',
        requestId: billRefNumber,
      })
    }

    res.status(response.status).json(data)
  } catch (err) {
    // Handle timeout specifically
    if (err instanceof Error && err.name === 'AbortError') {
      console.error('⏱️ Checkout request timed out after 45 seconds')
      return res.status(408).json({
        error: 'Payment Gateway Timeout',
        message:
          'The payment gateway is taking too long to respond. This may be due to high traffic or server issues. Please try again in a few minutes.',
        code: 'CHECKOUT_TIMEOUT',
        requestId: billRefNumber,
        timeout: '45 seconds',
        suggestions: [
          'Wait a few minutes and try again',
          'Check your internet connection',
          'Contact support if the problem persists',
        ],
      })
    }

    // Handle network connectivity errors
    if (err instanceof TypeError && err.message.includes('fetch')) {
      console.error('🌐 Network connectivity error:', err.message)
      return res.status(503).json({
        error: 'Payment Gateway Unavailable',
        message:
          'Unable to connect to the payment gateway. The service may be temporarily unavailable.',
        code: 'GATEWAY_UNAVAILABLE',
        requestId: billRefNumber,
        suggestions: [
          'Try again in a few minutes',
          'Check if the payment gateway is experiencing issues',
          'Contact support if the problem persists',
        ],
      })
    }

    // Log technical details for debugging
    console.error('🚨 Pesaflow Technical Error:', {
      type: 'Request Failed',
      error:
        err instanceof Error
          ? {
              name: err.name,
              message: err.message,
              stack: err.stack,
            }
          : err,
      requestBody: payloadToSend,
      requestId: billRefNumber,
    })

    // Generic error handling
    return res.status(500).json({
      error: 'Payment Processing Failed',
      message:
        'An unexpected error occurred while processing your payment request. Please try again later.',
      code: 'CHECKOUT_ERROR',
      requestId: billRefNumber,
      errorType: err instanceof Error ? err.constructor.name : 'Unknown',
    })
  }
}
