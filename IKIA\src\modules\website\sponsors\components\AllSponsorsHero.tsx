'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Users, Building, Award, Search, Handshake, ArrowRight, Download } from 'lucide-react'
import Link from 'next/link'

export default function AllSponsorsHero() {
  return (
    <section className="py-16 lg:py-24 section-bg-secondary">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          {/* Conference Badge */}
          <div className="flex justify-center">
            <span className="text-[#7E2518] font-bold px-3 py-1 text-xs  border border-[#7E2518]/20 flex items-center space-x-1 transform -skew-x-12 mb-4">
              <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
              <span className="ml-2 text-sm font-medium text-[#7E2518] font-['Myriad_Pro',Arial,sans-serif]">
                Our Valued Sponsors
              </span>
            </span>
          </div>

          {/* Main Heading */}
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#7E2518] mb-6 leading-tight font-['Myriad_Pro',Arial,sans-serif]">
            Meet Our
            <span className="block bg-gradient-to-r from-[#E8B32C] to-[#C86E36] bg-clip-text text-transparent">
              Sponsors
            </span>
          </h1>

          <div className="w-24 h-1 bg-[#E8B32C] mx-auto mb-6"></div>

          <p className="text-lg md:text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto mb-12 font-['Myriad_Pro',Arial,sans-serif]">
            Discover the organizations and institutions supporting Kenya&apos;s first International
            Investment Conference on Indigenous Knowledge Intellectual Assets. Together, we&apos;re
            preserving heritage and driving innovation.
          </p>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div className="bg-white main-shadow border border-gray-100 p-6 transition-all duration-300">
              <div className="w-12 h-12 bg-[#159147] flex items-center justify-center mx-auto mb-4">
                <Building className="w-6 h-6 text-white" />
              </div>
              <div className="text-3xl font-bold text-[#159147] mb-2 font-['Myriad_Pro',Arial,sans-serif]">
                25+
              </div>
              <p className="text-gray-600 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                Partner Organizations
              </p>
            </div>

            <div className="bg-white main-shadow border border-gray-100 p-6 transition-all duration-300">
              <div className="w-12 h-12 bg-[#E8B32C] flex items-center justify-center mx-auto mb-4">
                <Award className="w-6 h-6 text-white" />
              </div>
              <div className="text-3xl font-bold text-[#E8B32C] mb-2 font-['Myriad_Pro',Arial,sans-serif]">
                5
              </div>
              <p className="text-gray-600 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                Sponsorship Tiers
              </p>
            </div>

            <div className="bg-white main-shadow border border-gray-100 p-6 transition-all duration-300">
              <div className="w-12 h-12 bg-[#7E2518] flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-white" />
              </div>
              <div className="text-3xl font-bold text-[#7E2518] mb-2 font-['Myriad_Pro',Arial,sans-serif]">
                500+
              </div>
              <p className="text-gray-600 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                Conference Delegates
              </p>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              asChild
              size="lg"
              className="bg-[#159147] hover:bg-[#0f7a3a] text-white border-0 font-bold transition-all duration-300 main-shadow px-8 font-['Myriad_Pro',Arial,sans-serif]"
            >
              <Link href="#sponsor" className="flex items-center gap-2">
                <Handshake className="w-5 h-5" />
                Become a Sponsor
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              size="lg"
              className="border-2 border-[#7E2518] text-[#7E2518] bg-white hover:bg-[#7E2518] hover:text-white font-bold transition-all duration-300 main-shadow px-8 font-['Myriad_Pro',Arial,sans-serif]"
            >
              <Link
                href="/downloads/sponsorship-prospectus.pdf"
                className="flex items-center gap-2"
              >
                <Download className="w-5 h-5" />
                Download Prospectus
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
