-- Complete Payment Workflow Database Verification Queries
-- This file contains SQL queries to verify the complete payment workflow
-- and demonstrate how data flows through the system with proper relationships

-- =============================================================================
-- 1. USER STATE VERIFICATION
-- =============================================================================

-- Check user's initial state (before payment)
SELECT 
    id,
    email,
    "firstName",
    "lastName",
    phone,
    selected_package,
    package_status,
    package_expiry,
    "createdAt"
FROM users 
WHERE email = '<EMAIL>';

-- Expected Result (Initial State):
-- selected_package: null
-- package_status: 'inactive' 
-- package_expiry: null

-- =============================================================================
-- 2. SERVICE PACKAGES AVAILABLE
-- =============================================================================

-- Display all available service packages with features
SELECT 
    sp.id,
    sp.name,
    sp.price,
    sp.currency,
    sp.duration_days,
    sp.description,
    COUNT(spf.id) as feature_count
FROM "service-packages" sp
LEFT JOIN (
    SELECT 
        sp_inner.id,
        jsonb_array_elements(sp_inner.features) as feature
    FROM "service-packages" sp_inner
) spf ON sp.id = spf.id
GROUP BY sp.id, sp.name, sp.price, sp.currency, sp.duration_days, sp.description
ORDER BY sp.price;

-- =============================================================================
-- 3. INVOICE CREATION AND RELATIONSHIPS
-- =============================================================================

-- Show invoice with full relationships
SELECT 
    i.id as invoice_id,
    i.invoice_number,
    i.amount,
    i.currency,
    i.status as invoice_status,
    i.payment_reference,
    i.payment_method,
    i.due_date,
    i.paid_at,
    i.notes,
    u.email as user_email,
    u."firstName" || ' ' || u."lastName" as user_name,
    sp.name as package_name,
    sp.price as package_price,
    i."createdAt" as invoice_created
FROM invoices i
JOIN users u ON i.user = u.id
JOIN "service-packages" sp ON i.package = sp.id
WHERE u.email = '<EMAIL>'
ORDER BY i."createdAt" DESC;

-- =============================================================================
-- 4. PESAFLOW NOTIFICATION PROCESSING
-- =============================================================================

-- Show Pesaflow notification with all relationships
SELECT 
    pn.id as notification_id,
    pn.payment_channel,
    pn.client_invoice_ref,
    pn.payment_reference,
    pn.amount_paid,
    pn.currency,
    pn.status as payment_status,
    pn.processing_status,
    pn.processed_at,
    pn.processing_notes,
    pn.hash_verified,
    pn.ip_address,
    pn.user_agent,
    -- Related invoice information
    i.invoice_number,
    i.status as invoice_status,
    -- Related user information  
    u.email as user_email,
    u.package_status as user_package_status,
    -- Related service package information
    sp.name as package_name,
    sp.price as package_price
FROM "pesaflow-notifications" pn
LEFT JOIN invoices i ON pn.invoice = i.id
LEFT JOIN users u ON pn.user = u.id
LEFT JOIN "service-packages" sp ON pn.service_package = sp.id
WHERE u.email = '<EMAIL>'
ORDER BY pn."createdAt" DESC;

-- =============================================================================
-- 5. COMPLETE WORKFLOW AUDIT TRAIL
-- =============================================================================

-- Complete audit trail showing the entire payment workflow
SELECT 
    'User Registration' as workflow_step,
    u."createdAt" as timestamp,
    u.email as reference,
    'User account created' as description,
    1 as step_order
FROM users u
WHERE u.email = '<EMAIL>'

UNION ALL

SELECT 
    'Invoice Creation' as workflow_step,
    i."createdAt" as timestamp,
    i.invoice_number as reference,
    'Invoice generated for ' || sp.name as description,
    2 as step_order
FROM invoices i
JOIN users u ON i.user = u.id
JOIN "service-packages" sp ON i.package = sp.id
WHERE u.email = '<EMAIL>'

UNION ALL

SELECT 
    'Payment Notification' as workflow_step,
    pn."createdAt" as timestamp,
    pn.payment_reference as reference,
    'Pesaflow notification received via ' || pn.payment_channel as description,
    3 as step_order
FROM "pesaflow-notifications" pn
JOIN users u ON pn.user = u.id
WHERE u.email = '<EMAIL>'

UNION ALL

SELECT 
    'Payment Processing' as workflow_step,
    pn.processed_at as timestamp,
    pn.payment_reference as reference,
    'Payment processed: ' || pn.processing_notes as description,
    4 as step_order
FROM "pesaflow-notifications" pn
JOIN users u ON pn.user = u.id
WHERE u.email = '<EMAIL>'
AND pn.processed_at IS NOT NULL

ORDER BY step_order, timestamp;

-- =============================================================================
-- 6. FINAL STATE VERIFICATION
-- =============================================================================

-- Complete final state showing all relationships
SELECT 
    -- User Information
    u.id as user_id,
    u.email,
    u."firstName" || ' ' || u."lastName" as full_name,
    u.phone,
    u.package_status,
    u.package_expiry,
    
    -- Current Package Information
    sp.id as package_id,
    sp.name as package_name,
    sp.price as package_price,
    sp.currency as package_currency,
    sp.duration_days,
    
    -- Invoice Information
    i.id as invoice_id,
    i.invoice_number,
    i.status as invoice_status,
    i.amount as invoice_amount,
    i.payment_reference,
    i.payment_method,
    i.paid_at,
    
    -- Notification Information
    pn.id as notification_id,
    pn.payment_channel,
    pn.amount_paid,
    pn.processing_status,
    pn.hash_verified,
    pn.processed_at,
    
    -- Calculated Fields
    CASE 
        WHEN u.package_expiry > NOW() THEN 'Active'
        WHEN u.package_expiry <= NOW() THEN 'Expired'
        ELSE 'Inactive'
    END as calculated_package_status,
    
    CASE 
        WHEN u.package_expiry IS NOT NULL 
        THEN EXTRACT(days FROM u.package_expiry - NOW())::integer
        ELSE NULL
    END as days_until_expiry

FROM users u
LEFT JOIN "service-packages" sp ON u.selected_package = sp.id
LEFT JOIN invoices i ON i.user = u.id AND i.package = sp.id
LEFT JOIN "pesaflow-notifications" pn ON pn.user = u.id AND pn.invoice = i.id
WHERE u.email = '<EMAIL>'
ORDER BY i."createdAt" DESC, pn."createdAt" DESC
LIMIT 1;

-- =============================================================================
-- 7. DATA INTEGRITY CHECKS
-- =============================================================================

-- Check for orphaned records (data integrity verification)
SELECT 
    'Orphaned Invoices' as check_type,
    COUNT(*) as count,
    'Invoices without valid user references' as description
FROM invoices i
LEFT JOIN users u ON i.user = u.id
WHERE u.id IS NULL

UNION ALL

SELECT 
    'Orphaned Notifications' as check_type,
    COUNT(*) as count,
    'Notifications without valid invoice references' as description
FROM "pesaflow-notifications" pn
LEFT JOIN invoices i ON pn.invoice = i.id
WHERE pn.invoice IS NOT NULL AND i.id IS NULL

UNION ALL

SELECT 
    'Unprocessed Notifications' as check_type,
    COUNT(*) as count,
    'Notifications that failed to process' as description
FROM "pesaflow-notifications" pn
WHERE pn.processing_status = 'failed'

UNION ALL

SELECT 
    'Duplicate Notifications' as check_type,
    COUNT(*) - COUNT(DISTINCT pn.payment_reference) as count,
    'Duplicate payment references (should be 0)' as description
FROM "pesaflow-notifications" pn;

-- =============================================================================
-- 8. PERFORMANCE AND STATISTICS
-- =============================================================================

-- Payment workflow statistics
SELECT 
    COUNT(DISTINCT u.id) as total_users,
    COUNT(DISTINCT i.id) as total_invoices,
    COUNT(DISTINCT pn.id) as total_notifications,
    COUNT(DISTINCT CASE WHEN i.status = 'settled' THEN i.id END) as paid_invoices,
    COUNT(DISTINCT CASE WHEN u.package_status = 'active' THEN u.id END) as active_users,
    ROUND(AVG(i.amount), 2) as average_invoice_amount,
    SUM(CASE WHEN i.status = 'settled' THEN i.amount ELSE 0 END) as total_revenue
FROM users u
LEFT JOIN invoices i ON u.id = i.user
LEFT JOIN "pesaflow-notifications" pn ON i.id = pn.invoice;

-- =============================================================================
-- 9. RECENT ACTIVITY SUMMARY
-- =============================================================================

-- Show recent payment activity (last 7 days)
SELECT 
    DATE(pn."createdAt") as payment_date,
    COUNT(*) as notification_count,
    COUNT(CASE WHEN pn.status = 'settled' THEN 1 END) as successful_payments,
    COUNT(CASE WHEN pn.status = 'failed' THEN 1 END) as failed_payments,
    SUM(pn.amount_paid) as total_amount,
    STRING_AGG(DISTINCT pn.payment_channel, ', ') as payment_channels_used
FROM "pesaflow-notifications" pn
WHERE pn."createdAt" >= NOW() - INTERVAL '7 days'
GROUP BY DATE(pn."createdAt")
ORDER BY payment_date DESC;

-- =============================================================================
-- 10. RELATIONSHIP VALIDATION
-- =============================================================================

-- Validate all foreign key relationships are properly maintained
SELECT 
    'User-Invoice Relationship' as relationship_type,
    COUNT(i.id) as total_records,
    COUNT(u.id) as valid_references,
    COUNT(i.id) - COUNT(u.id) as broken_references
FROM invoices i
LEFT JOIN users u ON i.user = u.id

UNION ALL

SELECT 
    'Invoice-Package Relationship' as relationship_type,
    COUNT(i.id) as total_records,
    COUNT(sp.id) as valid_references,
    COUNT(i.id) - COUNT(sp.id) as broken_references
FROM invoices i
LEFT JOIN "service-packages" sp ON i.package = sp.id

UNION ALL

SELECT 
    'Notification-Invoice Relationship' as relationship_type,
    COUNT(pn.id) as total_records,
    COUNT(i.id) as valid_references,
    COUNT(pn.id) - COUNT(i.id) as broken_references
FROM "pesaflow-notifications" pn
LEFT JOIN invoices i ON pn.invoice = i.id
WHERE pn.invoice IS NOT NULL

UNION ALL

SELECT 
    'Notification-User Relationship' as relationship_type,
    COUNT(pn.id) as total_records,
    COUNT(u.id) as valid_references,
    COUNT(pn.id) - COUNT(u.id) as broken_references
FROM "pesaflow-notifications" pn
LEFT JOIN users u ON pn.user = u.id
WHERE pn.user IS NOT NULL;

-- Expected Result: All broken_references should be 0 for healthy relationships
