# Counties CRUD API Documentation

## 🔧 **Complete CRUD Operations**

I've created full CRUD (Create, Read, Update, Delete) endpoints for the Counties collection with proper authentication and validation.

## 🔗 **API Endpoints**

### 1. **CREATE County**
```bash
POST /api/counties
```

**Authentication:** Required (JWT token)

**Request Body:**
```json
{
  "name": "Nairobi",
  "code": "KE-047",
  "coordinates": {
    "latitude": -1.2921,
    "longitude": 36.8219
  },
  "description": "Kenya's capital and largest city",
  "isActive": true
}
```

**Success Response (201):**
```json
{
  "message": "County created successfully",
  "county": {
    "id": "1",
    "name": "Nairobi",
    "code": "KE-047",
    "coordinates": {
      "latitude": -1.2921,
      "longitude": 36.8219
    },
    "description": "Kenya's capital and largest city",
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**Error Responses:**
- `400` - Missing required fields or invalid coordinates
- `401` - Authentication required
- `409` - County code already exists

### 2. **READ Counties** (List with filters)
```bash
GET /api/counties
```

**Authentication:** Not required (public access)

**Query Parameters:**
- `name` - Search by county name (contains)
- `code` - Filter by exact county code
- `active` - Filter by active status (true/false)
- `near_lat` & `near_lng` - Find counties near coordinates
- `radius` - Radius in km for proximity search (default: 100km)
- `limit` - Items per page (default: 50)
- `page` - Page number (default: 1)
- `sort` - Sort field (default: name)

**Examples:**
```bash
# Get all counties
curl "http://localhost:3000/api/counties"

# Search by name
curl "http://localhost:3000/api/counties?name=Nairobi"

# Get active counties only
curl "http://localhost:3000/api/counties?active=true"

# Find counties near coordinates
curl "http://localhost:3000/api/counties?near_lat=-1.2921&near_lng=36.8219&radius=50"
```

### 3. **READ Single County**
```bash
GET /api/counties/:id
```

**Authentication:** Not required (public access)

**Examples:**
```bash
# Get county by ID
curl "http://localhost:3000/api/counties/1"

# Get county by code
curl "http://localhost:3000/api/counties/KE-047"
```

**Success Response (200):**
```json
{
  "county": {
    "id": "1",
    "name": "Nairobi",
    "code": "KE-047",
    "coordinates": {
      "latitude": -1.2921,
      "longitude": 36.8219
    },
    "description": "Kenya's capital and largest city",
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 4. **UPDATE County**
```bash
PUT /api/counties/:id
```

**Authentication:** Required (JWT token)

**Request Body (partial updates supported):**
```json
{
  "name": "Updated Nairobi",
  "description": "Updated description",
  "coordinates": {
    "latitude": -1.2921,
    "longitude": 36.8219
  },
  "isActive": false
}
```

**Success Response (200):**
```json
{
  "message": "County updated successfully",
  "county": {
    "id": "1",
    "name": "Updated Nairobi",
    "code": "KE-047",
    "coordinates": {
      "latitude": -1.2921,
      "longitude": 36.8219
    },
    "description": "Updated description",
    "isActive": false,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

**Error Responses:**
- `400` - Invalid coordinates or missing ID
- `401` - Authentication required
- `404` - County not found
- `409` - County code already exists (if updating code)

### 5. **DELETE County**
```bash
DELETE /api/counties/:id
```

**Authentication:** Required (JWT token)

**Success Response (200):**
```json
{
  "message": "County deleted successfully",
  "id": 1
}
```

**Error Responses:**
- `400` - Missing county ID
- `401` - Authentication required
- `404` - County not found

### 6. **GET Counties in Bounding Box**
```bash
GET /api/counties/bounds?north=1&south=-5&east=42&west=33
```

**Authentication:** Not required (public access)

## 🧪 **Complete CRUD Examples**

### Create a County:
```bash
curl -X POST http://localhost:3000/api/counties \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Mombasa",
    "code": "KE-001",
    "coordinates": {
      "latitude": -4.0435,
      "longitude": 39.6682
    },
    "description": "Kenya'\''s second-largest city and principal port",
    "isActive": true
  }'
```

### Read All Counties:
```bash
curl "http://localhost:3000/api/counties?limit=10&page=1"
```

### Read Single County:
```bash
curl "http://localhost:3000/api/counties/1"
```

### Update a County:
```bash
curl -X PUT http://localhost:3000/api/counties/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Updated Mombasa",
    "description": "Updated description for Mombasa county"
  }'
```

### Delete a County:
```bash
curl -X DELETE http://localhost:3000/api/counties/1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🔐 **Authentication**

**Required for:**
- ✅ CREATE (POST)
- ✅ UPDATE (PUT) 
- ✅ DELETE

**Not required for:**
- ✅ READ operations (GET)

**How to get JWT token:**
```bash
# Login to get token
curl -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"your-password"}'

# Use token in subsequent requests
curl -X POST http://localhost:3000/api/counties \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test County","code":"TEST-001",...}'
```

## ✅ **Validation Rules**

### Required Fields (CREATE):
- `name` - County name
- `code` - Unique county code
- `coordinates.latitude` - Between -90 and 90
- `coordinates.longitude` - Between -180 and 180

### Optional Fields:
- `description` - Text description
- `isActive` - Boolean (default: true)

### Constraints:
- County codes must be unique
- Coordinates must be valid decimal degrees
- Authentication required for write operations

## 🚨 **Error Handling**

All endpoints return consistent error responses:

```json
{
  "error": "Error type",
  "message": "Detailed error message"
}
```

**Common HTTP Status Codes:**
- `200` - Success
- `201` - Created successfully
- `400` - Bad request (validation errors)
- `401` - Unauthorized (authentication required)
- `404` - Not found
- `409` - Conflict (duplicate county code)
- `500` - Internal server error

## 🔄 **Partial Updates**

The UPDATE endpoint supports partial updates - you only need to include the fields you want to change:

```bash
# Update only the name
curl -X PUT http://localhost:3000/api/counties/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"name": "New County Name"}'

# Update only coordinates
curl -X PUT http://localhost:3000/api/counties/1 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "coordinates": {
      "latitude": -2.0000,
      "longitude": 37.0000
    }
  }'
```

## 📊 **Response Formats**

All successful responses include:
- Consistent JSON structure
- Proper HTTP status codes
- Descriptive success messages
- Complete county data with timestamps

The CRUD API provides complete county management functionality with proper authentication, validation, and error handling! 🎉
