#!/usr/bin/env node

/**
 * Database Indexing Script for IKIA Payment System
 *
 * This script creates database indexes for optimal performance
 * Run this after database setup to improve query performance
 *
 * Usage: node scripts/create-indexes.js
 */

import { Pool } from 'pg'
import fs from 'fs'
import path from 'path'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || process.env.DATABASE_URI,
})

// Index definitions
const indexes = [
  // Users Collection Indexes
  {
    name: 'idx_users_email',
    table: 'users',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email)',
    description: 'Index on user email for fast lookups',
  },
  {
    name: 'idx_users_id_number',
    table: 'users',
    sql: 'CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_users_id_number ON users(id_number) WHERE id_number IS NOT NULL',
    description: 'Unique index on ID number (sparse)',
  },
  {
    name: 'idx_users_phone_number',
    table: 'users',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_phone_number ON users(phone_number) WHERE phone_number IS NOT NULL',
    description: 'Index on phone number (sparse)',
  },
  {
    name: 'idx_users_role',
    table: 'users',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_role ON users(role)',
    description: 'Index on user role for filtering',
  },

  // Invoices Collection Indexes
  {
    name: 'idx_invoices_invoice_number',
    table: 'invoices',
    sql: 'CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_invoice_number ON invoices(invoice_number)',
    description: 'Unique index on invoice number for fast IPN lookups',
  },
  {
    name: 'idx_invoices_payment_reference',
    table: 'invoices',
    sql: 'CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_payment_reference ON invoices(payment_reference)',
    description: 'Unique index on payment reference',
  },
  {
    name: 'idx_invoices_user',
    table: 'invoices',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_user ON invoices(user_id)',
    description: 'Index on user for user invoice queries',
  },
  {
    name: 'idx_invoices_status',
    table: 'invoices',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_status ON invoices(status)',
    description: 'Index on invoice status',
  },
  {
    name: 'idx_invoices_user_status',
    table: 'invoices',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_user_status ON invoices(user_id, status)',
    description: 'Compound index for user invoice status queries',
  },

  // PesaflowNotifications Collection Indexes
  {
    name: 'idx_pesaflow_payment_reference',
    table: 'pesaflow_notifications',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_payment_reference ON pesaflow_notifications(payment_reference) WHERE payment_reference IS NOT NULL',
    description: 'Index on payment reference (sparse)',
  },
  {
    name: 'idx_pesaflow_invoice_number',
    table: 'pesaflow_notifications',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_invoice_number ON pesaflow_notifications(invoice_number)',
    description: 'Index on invoice number for IPN processing',
  },
  {
    name: 'idx_pesaflow_client_invoice_ref',
    table: 'pesaflow_notifications',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_client_invoice_ref ON pesaflow_notifications(client_invoice_ref)',
    description: 'Index on client invoice reference',
  },
  {
    name: 'idx_pesaflow_status',
    table: 'pesaflow_notifications',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_status ON pesaflow_notifications(status)',
    description: 'Index on payment status',
  },
  {
    name: 'idx_pesaflow_payment_date',
    table: 'pesaflow_notifications',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_payment_date ON pesaflow_notifications(payment_date DESC)',
    description: 'Index on payment date (descending)',
  },
  {
    name: 'idx_pesaflow_invoice_status',
    table: 'pesaflow_notifications',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_invoice_status ON pesaflow_notifications(invoice_number, status)',
    description: 'Compound index for invoice status queries',
  },

  // Exhibitors Collection Indexes
  {
    name: 'idx_exhibitors_email',
    table: 'exhibitors',
    sql: 'CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_exhibitors_email ON exhibitors(email)',
    description: 'Unique index on exhibitor email',
  },
  {
    name: 'idx_exhibitors_company_name',
    table: 'exhibitors',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_exhibitors_company_name ON exhibitors(company_name)',
    description: 'Index on company name',
  },

  // Events Collection Indexes
  {
    name: 'idx_events_date',
    table: 'events',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_date ON events(date)',
    description: 'Index on event date',
  },
  {
    name: 'idx_events_type',
    table: 'events',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_events_type ON events(type)',
    description: 'Index on event type',
  },

  // Additional Performance Indexes
  {
    name: 'idx_users_id_number',
    table: 'users',
    sql: 'CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_users_id_number ON users(id_number) WHERE id_number IS NOT NULL',
    description: 'Unique index on ID number (sparse)',
  },
  {
    name: 'idx_users_county',
    table: 'users',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_county ON users(county_id)',
    description: 'Index on user county for filtering',
  },
  {
    name: 'idx_invoices_created_at',
    table: 'invoices',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_created_at ON invoices(created_at DESC)',
    description: 'Index on invoice creation date (descending)',
  },
  {
    name: 'idx_pesaflow_user_status',
    table: 'pesaflow_notifications',
    sql: 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pesaflow_user_status ON pesaflow_notifications(user_id, status) WHERE user_id IS NOT NULL',
    description: 'Compound index for user payment status queries',
  },
]

async function createIndexes() {
  console.log('🔍 Starting database indexing process...\n')

  const client = await pool.connect()

  try {
    let successCount = 0
    let skipCount = 0
    let errorCount = 0

    for (const index of indexes) {
      try {
        console.log(`📊 Creating index: ${index.name}`)
        console.log(`   Table: ${index.table}`)
        console.log(`   Description: ${index.description}`)

        const startTime = Date.now()
        await client.query(index.sql)
        const duration = Date.now() - startTime

        console.log(`   ✅ Created successfully (${duration}ms)\n`)
        successCount++
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log(`   ⚠️  Index already exists, skipping\n`)
          skipCount++
        } else {
          console.log(`   ❌ Error: ${error.message}\n`)
          errorCount++
        }
      }
    }

    // Analyze tables for query optimization
    console.log('📈 Analyzing tables for query optimization...')
    const tables = ['users', 'invoices', 'pesaflow_notifications', 'exhibitors', 'events']

    for (const table of tables) {
      try {
        await client.query(`ANALYZE ${table}`)
        console.log(`   ✅ Analyzed ${table}`)
      } catch (error) {
        console.log(`   ⚠️  Could not analyze ${table}: ${error.message}`)
      }
    }

    console.log('\n' + '='.repeat(60))
    console.log('📊 INDEXING SUMMARY:')
    console.log(`✅ Successfully created: ${successCount} indexes`)
    console.log(`⚠️  Already existed: ${skipCount} indexes`)
    console.log(`❌ Failed: ${errorCount} indexes`)
    console.log(`📈 Total indexes processed: ${indexes.length}`)

    // Show existing indexes
    console.log('\n📋 Current database indexes:')
    const result = await client.query(`
      SELECT 
        schemaname,
        tablename,
        indexname,
        indexdef
      FROM pg_indexes 
      WHERE schemaname = 'public' 
        AND indexname LIKE 'idx_%'
      ORDER BY tablename, indexname
    `)

    result.rows.forEach((row) => {
      console.log(`   ${row.tablename}.${row.indexname}`)
    })

    console.log('\n🎉 Database indexing completed successfully!')
  } catch (error) {
    console.error('❌ Error during indexing process:', error)
    process.exit(1)
  } finally {
    client.release()
    await pool.end()
  }
}

// Performance recommendations
function showPerformanceRecommendations() {
  console.log('\n🚀 PERFORMANCE RECOMMENDATIONS:')
  console.log('1. Monitor query performance with EXPLAIN ANALYZE')
  console.log('2. Run VACUUM ANALYZE regularly for optimal performance')
  console.log('3. Consider connection pooling for high-traffic scenarios')
  console.log('4. Monitor index usage with pg_stat_user_indexes')
  console.log('5. Set up monitoring for slow queries')
  console.log('\n📚 Useful commands:')
  console.log('   - Check index usage: SELECT * FROM pg_stat_user_indexes;')
  console.log('   - Check table stats: SELECT * FROM pg_stat_user_tables;')
  console.log('   - Analyze query: EXPLAIN ANALYZE SELECT ...')
}

// Main execution
createIndexes()
  .then(() => {
    showPerformanceRecommendations()
    process.exit(0)
  })
  .catch((error) => {
    console.error('❌ Fatal error:', error)
    process.exit(1)
  })

export { createIndexes }
