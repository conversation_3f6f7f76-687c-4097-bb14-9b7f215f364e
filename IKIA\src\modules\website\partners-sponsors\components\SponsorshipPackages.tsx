'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Check, Star, Users, Camera, Gift, Crown, Award, Medal, Trophy, Zap } from 'lucide-react'
import { sponsorshipPackages } from '../data/sponsorship-packages'
import type { FilterType } from '../types'

const categoryIcons = {
  financial: Star,
  service: Gift,
  media: Camera,
  special: Zap,
}

const tierIcons = {
  title: Crown,
  platinum: Award,
  gold: Trophy,
  silver: Medal,
  bronze: Users,
}

const tierColors = {
  title: '#7E2518',
  platinum: '#81B1DB',
  gold: '#E8B32C',
  silver: '#9CA3AF',
  bronze: '#C86E36',
}

export default function SponsorshipPackages() {
  const [activeFilter, setActiveFilter] = useState<FilterType>('financial')

  const filteredPackages = sponsorshipPackages.filter((pkg) => pkg.category === activeFilter)
  const financialPackages = sponsorshipPackages.filter((pkg) => pkg.category === 'financial')

  return (
    <section id="sponsor" className="py-16 lg:py-20 section-bg-secondary">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center">
            <span className="text-[#7E2518] font-bold px-3 py-1 text-xs  border border-[#7E2518]/20 flex items-center space-x-1 transform -skew-x-12 mb-4">
              <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
              <span className="ml-2 text-sm font-medium text-[#7E2518] font-['Myriad_Pro',Arial,sans-serif]">
                Sponsorship Opportunities
              </span>
            </span>
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#7E2518] mb-6 font-['Myriad_Pro',Arial,sans-serif]">
            Sponsorship Tiers
            <span className="block bg-gradient-to-r from-[#E8B32C] to-[#C86E36] bg-clip-text text-transparent">
              & Benefits
            </span>
          </h2>

          <div className="w-24 h-1 bg-[#E8B32C] mx-auto mb-6"></div>

          <p className="text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto font-['Myriad_Pro',Arial,sans-serif]">
            Join us as a partner in Kenya&apos;s first International Investment Conference on
            Indigenous Knowledge Intellectual Assets. Choose from financial sponsorships, service
            partnerships, or media collaborations.
          </p>
        </div>

        {/* Filter Tabs */}
        <div className="flex flex-wrap justify-center gap-3 mb-12">
          {Object.entries(categoryIcons).map(([category, IconComponent]) => {
            const isActive = activeFilter === category
            return (
              <Button
                key={category}
                variant={isActive ? 'default' : 'outline'}
                onClick={() => setActiveFilter(category as FilterType)}
                className={`flex items-center gap-2 px-6 py-3 font-medium transition-all duration-300 main-shadow font-['Myriad_Pro',Arial,sans-serif] ${
                  isActive
                    ? 'bg-[#7E2518] text-white hover:bg-[#159147]'
                    : 'border-[#7E2518]/30 text-[#7E2518] hover:bg-[#7E2518]/5'
                }`}
              >
                <IconComponent className="w-4 h-4" />
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </Button>
            )
          })}
        </div>

        {/* Financial Packages - Featured Grid */}
        {activeFilter === 'financial' && (
          <div className="grid lg:grid-cols-3 gap-8 mb-12">
            {financialPackages.slice(0, 3).map((pkg) => {
              const TierIcon = pkg.tier ? tierIcons[pkg.tier] : Star
              return (
                <Card
                  key={pkg.id}
                  className="group relative bg-white border-2 border-gray-200 main-shadow hover:shadow-2xl transition-all duration-300 overflow-hidden"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  {/* Background Gradient */}
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${pkg.tier ? tierColors[pkg.tier] : 'from-gray-100 to-gray-200'} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
                  ></div>

                  <CardHeader className="relative text-center pb-4">
                    <div
                      className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-br ${pkg.tier ? tierColors[pkg.tier] : 'from-gray-400 to-gray-500'} rounded-full flex items-center justify-center shadow-lg`}
                    >
                      <TierIcon className="w-8 h-8 text-white" />
                    </div>

                    <CardTitle
                      className="text-2xl font-bold text-[#7E2518] mb-2"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      {pkg.name}
                    </CardTitle>

                    <div
                      className="text-3xl font-bold text-[#159147] mb-2"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      {pkg.price}
                    </div>

                    <p
                      className="text-gray-600 text-sm"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      {pkg.description}
                    </p>
                  </CardHeader>

                  <CardContent className="relative">
                    <div className="space-y-3 mb-8">
                      {pkg.benefits.slice(0, 6).map((benefit, idx) => (
                        <div key={idx} className="flex items-start gap-3">
                          <div
                            className={`w-5 h-5 rounded-full bg-gradient-to-r ${pkg.tier ? tierColors[pkg.tier] : 'from-gray-400 to-gray-500'} flex items-center justify-center flex-shrink-0 mt-0.5`}
                          >
                            <Check className="w-3 h-3 text-white" />
                          </div>
                          <span
                            className="text-gray-700 text-sm leading-relaxed"
                            style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                          >
                            {benefit}
                          </span>
                        </div>
                      ))}
                      {pkg.benefits.length > 6 && (
                        <div className="text-center">
                          <span className="text-[#7E2518] font-medium text-sm">
                            +{pkg.benefits.length - 6} more benefits
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="text-center mb-6">
                      <div className="text-sm text-gray-600 mb-2">
                        {pkg.currentPartners} of {pkg.maxPartners} spots taken
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full bg-gradient-to-r ${pkg.tier ? tierColors[pkg.tier] : 'from-gray-400 to-gray-500'}`}
                          style={{ width: `${(pkg.currentPartners / pkg.maxPartners) * 100}%` }}
                        ></div>
                      </div>
                    </div>

                    <Button
                      className={`w-full bg-gradient-to-r ${pkg.tier ? tierColors[pkg.tier] : 'from-gray-400 to-gray-500'} hover:shadow-lg text-white border-0 rounded-lg font-bold transition-all duration-500`}
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      Choose {pkg.name}
                    </Button>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}

        {/* Other Categories - List View */}
        {activeFilter !== 'financial' && (
          <div className="grid md:grid-cols-2 gap-8">
            {filteredPackages.map((pkg) => (
              <Card
                key={pkg.id}
                className="group bg-white border border-gray-200 main-shadow hover:shadow-xl transition-all duration-300 overflow-hidden"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                <CardHeader>
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-[#7E2518] rounded-lg flex items-center justify-center">
                      {React.createElement(categoryIcons[pkg.category], {
                        className: 'w-6 h-6 text-white',
                      })}
                    </div>
                    <div>
                      <CardTitle
                        className="text-xl font-bold text-[#7E2518]"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        {pkg.name}
                      </CardTitle>
                      <div
                        className="text-lg font-semibold text-[#159147]"
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        {pkg.price}
                      </div>
                    </div>
                  </div>
                  <p
                    className="text-gray-600 mt-2"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    {pkg.description}
                  </p>
                </CardHeader>

                <CardContent>
                  <div className="space-y-2 mb-6">
                    {pkg.benefits.map((benefit, idx) => (
                      <div key={idx} className="flex items-start gap-3">
                        <Check className="w-4 h-4 text-[#159147] flex-shrink-0 mt-0.5" />
                        <span
                          className="text-gray-700 text-sm"
                          style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                        >
                          {benefit}
                        </span>
                      </div>
                    ))}
                  </div>

                  <Button
                    className="w-full bg-[#7E2518] hover:shadow-lg text-white border-0 rounded-lg font-bold transition-all duration-500"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    Apply for {pkg.name}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </section>
  )
}
