import type { PayloadRequest } from 'payload'
import { HashService } from '../utils/pesaflowHash'

// Types for the eCitizen payment confirmation endpoint
export const pesaflowConfirmPaymentEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('Payment confirmation endpoint called')
    console.log('Request body:', req.body)
    console.log('Request body type:', typeof req.body)

    // Parse request body (handle ReadableStream)
    let parsedBody: any
    if (req.body instanceof ReadableStream) {
      const reader = req.body.getReader()
      const chunks: Uint8Array[] = []
      let done = false

      while (!done) {
        const { value, done: streamDone } = await reader.read()
        done = streamDone
        if (value) {
          chunks.push(value)
        }
      }

      const bodyText = new TextDecoder().decode(
        new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0)).map((_, i) => {
          let offset = 0
          for (const chunk of chunks) {
            if (i < offset + chunk.length) {
              return chunk[i - offset]
            }
            offset += chunk.length
          }
          return 0
        }),
      )

      try {
        parsedBody = JSON.parse(bodyText)
      } catch (parseError) {
        console.error('JSON parse error:', parseError)
        return new Response(
          JSON.stringify({
            status: 400,
            description: 'Invalid JSON in request body',
          }),
          {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }
    } else if (typeof req.body === 'object' && req.body !== null) {
      parsedBody = req.body
    } else {
      return new Response(
        JSON.stringify({
          status: 400,
          description: 'Invalid request body format',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    console.log('Parsed body:', parsedBody)

    // Frontend sends: ref_no, amount, currency, gateway_transaction_id, gateway_transaction_date, customer_name, customer_account_number
    // Server handles: api_client_id, secret, and hash generation
    const {
      ref_no,
      amount,
      currency,
      gateway_transaction_id,
      gateway_transaction_date,
      customer_name,
      customer_account_number,
    } = parsedBody

    // Validate required fields from frontend
    const missingFields = [
      !ref_no && 'ref_no',
      !amount && 'amount',
      !currency && 'currency',
      !gateway_transaction_id && 'gateway_transaction_id',
      !gateway_transaction_date && 'gateway_transaction_date',
      !customer_name && 'customer_name',
      !customer_account_number && 'customer_account_number',
    ].filter(Boolean)

    if (missingFields.length > 0) {
      return new Response(
        JSON.stringify({
          status: 400,
          description: 'Missing required fields',
          missingFields,
          message: `Please provide the following required fields: ${missingFields.join(', ')}`,
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Use HashService to generate hash automatically
    let hashService: HashService
    try {
      hashService = HashService.getInstance()
    } catch (error) {
      console.error('HashService initialization failed:', error)
      return new Response(
        JSON.stringify({
          status: 500,
          description: 'Internal Error - Missing configuration',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Generate hash and payload using HashService
    const {
      hash: secureHash,
      api_client_id: apiClientID,
      payload,
    } = hashService.generatePaymentConfirmationHash(
      ref_no,
      amount,
      currency,
      gateway_transaction_id,
      gateway_transaction_date,
      customer_name,
      customer_account_number,
    )

    // Get external service URL
    const { PESAFLOW_UAT_SERVER_URL: pesaflowServerUrl } = process.env

    // Option 1: Send to external Pesaflow service
    if (pesaflowServerUrl) {
      try {
        // Use the payload generated by HashService
        const payloadToSend = payload

        const baseUrl = pesaflowServerUrl.endsWith('/')
          ? pesaflowServerUrl.slice(0, -1)
          : pesaflowServerUrl

        console.log('Making external confirmation call to:', `${baseUrl}/api/payment/confirm`)

        const response = await fetch(`${baseUrl}/api/payment/confirm`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payloadToSend),
        })

        const responseData = await response.json()

        // Return the response from external service
        return new Response(JSON.stringify(responseData), {
          status: response.status,
          headers: { 'Content-Type': 'application/json' },
        })
      } catch (error) {
        console.error('External confirmation service error:', error)
        return new Response(
          JSON.stringify({
            status: 500,
            description: 'Internal Error - External service unavailable',
          }),
          {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }
    }

    // Option 2: Local payment confirmation processing
    const confirmationResult = await processPaymentConfirmation({
      ref_no,
      amount,
      currency,
      gateway_transaction_id,
      gateway_transaction_date,
      customer_name,
      customer_account_number,
    })

    if (confirmationResult.success) {
      return new Response(
        JSON.stringify({
          status: 200,
          description: 'Success',
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    } else {
      return new Response(
        JSON.stringify({
          status: 500,
          description: 'Failed',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }
  } catch (error) {
    console.error('Payment confirmation error:', error)
    return new Response(
      JSON.stringify({
        status: 500,
        description: 'Internal Error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}

// Helper function to process payment confirmation locally
// This should be replaced with actual database/business logic
async function processPaymentConfirmation(paymentData: any) {
  // Simulate payment confirmation processing
  // In a real implementation, this would:
  // 1. Update payment status in database
  // 2. Send notifications
  // 3. Update invoice status
  // 4. Trigger any business logic

  console.log('Processing payment confirmation:', paymentData)

  // For demo purposes, assume success
  return {
    success: true,
    transactionId: paymentData.gateway_transaction_id,
  }
}
