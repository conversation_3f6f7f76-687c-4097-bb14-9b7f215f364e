import type { PayloadRequest } from 'payload'

/**
 * Endpoint to seed test data for the registration and payment flow
 * This creates sample service packages for testing
 */
export const seedTestDataEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('Seeding test data for registration and payment flow...')

    // Check if user is admin
    if (!req.user || req.user.role !== 'admin') {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Admin access required to seed test data',
          },
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    const testPackages = [
      {
        name: 'Basic Package',
        description:
          'Perfect for individuals and small projects. Includes essential features to get you started.',
        price: 1500,
        currency: 'KES',
        packageType: 'basic',
        isActive: true,
        isFeatured: false,
        displayOrder: 1,
        duration: {
          value: 12,
          unit: 'months',
        },
        features: [
          { feature: 'Email Support', included: true },
          { feature: 'Basic Analytics', included: true },
          { feature: 'Up to 5 Users', included: true },
          { feature: 'Mobile App Access', included: true },
          { feature: 'Priority Support', included: false },
          { feature: 'Advanced Analytics', included: false },
        ],
        maxUsers: 5,
        setupFee: 0,
        metadata: {
          popular: false,
          recommended: false,
        },
      },
      {
        name: 'Premium Package',
        description:
          'Ideal for growing businesses. Enhanced features and priority support included.',
        price: 3000,
        currency: 'KES',
        packageType: 'premium',
        isActive: true,
        isFeatured: true,
        displayOrder: 2,
        duration: {
          value: 12,
          unit: 'months',
        },
        features: [
          { feature: 'Email Support', included: true },
          { feature: 'Basic Analytics', included: true },
          { feature: 'Up to 20 Users', included: true },
          { feature: 'Mobile App Access', included: true },
          { feature: 'Priority Support', included: true },
          { feature: 'Advanced Analytics', included: true },
          { feature: 'API Access', included: true },
          { feature: 'Custom Integrations', included: false },
        ],
        maxUsers: 20,
        setupFee: 500,
        metadata: {
          popular: true,
          recommended: true,
        },
      },
      {
        name: 'Enterprise Package',
        description: 'For large organizations requiring advanced features and dedicated support.',
        price: 5000,
        currency: 'KES',
        packageType: 'enterprise',
        isActive: true,
        isFeatured: false,
        displayOrder: 3,
        duration: {
          value: 12,
          unit: 'months',
        },
        features: [
          { feature: 'Email Support', included: true },
          { feature: 'Basic Analytics', included: true },
          { feature: 'Unlimited Users', included: true },
          { feature: 'Mobile App Access', included: true },
          { feature: 'Priority Support', included: true },
          { feature: 'Advanced Analytics', included: true },
          { feature: 'API Access', included: true },
          { feature: 'Custom Integrations', included: true },
          { feature: 'Dedicated Account Manager', included: true },
          { feature: 'SLA Guarantee', included: true },
        ],
        maxUsers: null, // Unlimited
        setupFee: 1000,
        metadata: {
          popular: false,
          recommended: false,
          enterprise: true,
        },
      },
    ]

    const createdPackages = []

    // Create each package
    for (const packageData of testPackages) {
      try {
        // Check if package already exists
        const existing = await req.payload.find({
          collection: 'delegatepackages',
          where: {
            name: {
              equals: packageData.name,
            },
          },
        })

        if (existing.docs.length > 0) {
          console.log(`Package "${packageData.name}" already exists, skipping...`)
          createdPackages.push(existing.docs[0])
          continue
        }

        const createdPackage = await req.payload.create({
          collection: 'delegatepackages',
          data: packageData,
        })

        console.log(`Created package: ${createdPackage.name}`)
        createdPackages.push(createdPackage)
      } catch (error) {
        console.error(`Failed to create package "${packageData.name}":`, error)
      }
    }

    // Also create a test county if needed
    let testCounty = null
    try {
      const existingCounties = await req.payload.find({
        collection: 'counties',
        where: {
          name: {
            equals: 'Test County',
          },
        },
      })

      if (existingCounties.docs.length === 0) {
        testCounty = await req.payload.create({
          collection: 'counties',
          data: {
            name: 'Test County',
            code: 'TEST-001',
            capital: 'Test City',
            isActive: true,
            population: 100000,
            area: 1000,
          },
        })
        console.log('Created test county')
      } else {
        testCounty = existingCounties.docs[0]
        console.log('Test county already exists')
      }
    } catch (error) {
      console.error('Failed to create test county:', error)
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: {
          packagesCreated: createdPackages.length,
          packages: createdPackages.map((pkg) => ({
            id: pkg.id,
            name: pkg.name,
            price: pkg.price,
            currency: pkg.currency,
          })),
          testCounty: testCounty
            ? {
                id: testCounty.id,
                name: testCounty.name,
                code: testCounty.code,
              }
            : null,
          message: 'Test data seeded successfully',
        },
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  } catch (error) {
    console.error('Seed test data endpoint error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: {
          code: 'SEED_ERROR',
          message: 'Failed to seed test data',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}
