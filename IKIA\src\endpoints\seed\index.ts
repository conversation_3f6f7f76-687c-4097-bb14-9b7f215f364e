import type { CollectionSlug, GlobalSlug, Payload, PayloadRequest, File } from 'payload'

import { contactForm as contactFormData } from './contact-form'
import { contact as contactPageData } from './contact-page'
import { home } from './home'
import { image1 } from './image-1'
import { image2 } from './image-2'
import { imageHero1 } from './image-hero-1'
import { post1 } from './post-1'
import { post2 } from './post-2'
import { post3 } from './post-3'

const collections: CollectionSlug[] = [
  'categories',
  'media',
  'pages',
  'posts',
  'forms',
  'form-submissions',
  'search',
  // IKIA specific collections
  'events',
  'speakers',
  'counties',
  'programs',
  'program-types',
  'thematic-areas',
  'target-audience',
  'partners',
  'sponsors',
  'sponsorship-packages',
  'enquiry',
  // Payment workflow collections
  'delegatepackages',
  'invoices',
  'pesaflow-notifications',
  'exhibitors',
  'users',
]
const globals: GlobalSlug[] = ['header', 'footer']

// Next.js revalidation errors are normal when seeding the database without a server running
// i.e. running `yarn seed` locally instead of using the admin UI within an active app
// The app is not running to revalidate the pages and so the API routes are not available
// These error messages can be ignored: `Error hitting revalidate route for...`
export const seed = async ({
  payload,
  req,
}: {
  payload: Payload
  req: PayloadRequest
}): Promise<void> => {
  payload.logger.info('Seeding database...')

  // we need to clear the media directory before seeding
  // as well as the collections and globals
  // this is because while `yarn seed` drops the database
  // the custom `/api/seed` endpoint does not
  payload.logger.info(`— Clearing collections and globals...`)

  // Check which collections exist
  const existingCollections = collections.filter((collection) => payload.collections[collection])
  const missingCollections = collections.filter((collection) => !payload.collections[collection])

  if (missingCollections.length > 0) {
    payload.logger.warn(`Missing collections: ${missingCollections.join(', ')}`)
  }

  payload.logger.info(`Clearing ${existingCollections.length} existing collections...`)

  // clear the database
  await Promise.all(
    globals.map((global) =>
      payload.updateGlobal({
        slug: global,
        data: {
          navItems: [],
        },
        depth: 0,
        context: {
          disableRevalidate: true,
        },
      }),
    ),
  )

  // Delete collections sequentially to avoid deadlocks
  for (const collection of existingCollections) {
    try {
      await payload.db.deleteMany({ collection, req, where: {} })
      payload.logger.info(`Cleared collection: ${collection}`)
    } catch (error) {
      payload.logger.warn(`Failed to clear collection ${collection}:`, error)
    }
  }

  // Delete versions sequentially as well
  const collectionsWithVersions = existingCollections.filter((collection) =>
    Boolean(payload.collections[collection].config.versions),
  )

  for (const collection of collectionsWithVersions) {
    try {
      await payload.db.deleteVersions({ collection, req, where: {} })
      payload.logger.info(`Cleared versions for collection: ${collection}`)
    } catch (error) {
      payload.logger.warn(`Failed to clear versions for collection ${collection}:`, error)
    }
  }

  payload.logger.info(`— Seeding admin users and validated citizens...`)

  // Clear existing demo users
  await Promise.all([
    payload.delete({
      collection: 'users',
      depth: 0,
      where: {
        email: {
          in: [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
          ],
        },
      },
    }),
  ])

  payload.logger.info(`— Seeding all 47 Kenyan counties...`)

  // Create all 47 Kenyan counties with official IEBC codes and accurate coordinates
  const allCountiesData = [
    {
      name: 'Mombasa',
      code: '001',
      lat: -4.043477,
      lng: 39.668206,
      desc: "Coastal county and major port city, Kenya's second largest city",
    },
    {
      name: 'Kwale',
      code: '002',
      lat: -4.174732,
      lng: 39.450264,
      desc: 'Coastal county known for tourism, Diani Beach, and cashew farming',
    },
    {
      name: 'Kilifi',
      code: '003',
      lat: -3.510692,
      lng: 39.905897,
      desc: 'Coastal county with beautiful beaches, historical sites, and Malindi',
    },
    {
      name: 'Tana River',
      code: '004',
      lat: -1.016667,
      lng: 40.1,
      desc: 'County along the Tana River with diverse pastoral communities',
    },
    {
      name: 'Lamu',
      code: '005',
      lat: -2.271707,
      lng: 40.902006,
      desc: 'Historic Swahili coastal county and UNESCO World Heritage site',
    },
    {
      name: 'Taita Taveta',
      code: '006',
      lat: -3.316667,
      lng: 38.35,
      desc: 'County known for Tsavo National Parks, wildlife, and Voi town',
    },
    {
      name: 'Garissa',
      code: '007',
      lat: -0.453611,
      lng: 39.640056,
      desc: 'Northeastern county and regional commercial hub',
    },
    {
      name: 'Wajir',
      code: '008',
      lat: 1.747119,
      lng: 40.057297,
      desc: 'Northeastern county with pastoral communities and livestock',
    },
    {
      name: 'Mandera',
      code: '009',
      lat: 3.936569,
      lng: 41.867046,
      desc: 'Northeastern border county with Somalia and Ethiopia',
    },
    {
      name: 'Marsabit',
      code: '010',
      lat: 2.328417,
      lng: 37.989861,
      desc: 'Northern county known for diverse cultures and wildlife',
    },
    {
      name: 'Isiolo',
      code: '011',
      lat: 0.355556,
      lng: 37.583333,
      desc: 'Northern county and gateway to northern Kenya',
    },
    {
      name: 'Meru',
      code: '012',
      lat: 0.05,
      lng: 37.65,
      desc: 'Central Kenya county known for agriculture, miraa, and Mount Kenya',
    },
    {
      name: 'Tharaka Nithi',
      code: '013',
      lat: -0.166667,
      lng: 37.983333,
      desc: 'County on the slopes of Mount Kenya, known for coffee',
    },
    {
      name: 'Embu',
      code: '014',
      lat: -0.516667,
      lng: 37.45,
      desc: 'Central Kenya county known for coffee and tea farming',
    },
    {
      name: 'Kitui',
      code: '015',
      lat: -1.366667,
      lng: 38.016667,
      desc: 'Eastern county known for honey production and dry land farming',
    },
    {
      name: 'Machakos',
      code: '016',
      lat: -1.516667,
      lng: 37.266667,
      desc: 'Eastern county close to Nairobi with growing urban centers',
    },
    {
      name: 'Makueni',
      code: '017',
      lat: -1.8,
      lng: 37.616667,
      desc: 'Eastern county known for fruit farming and Chyulu Hills',
    },
    {
      name: 'Nyandarua',
      code: '018',
      lat: -0.383333,
      lng: 36.366667,
      desc: 'Central Kenya county known for potato farming and Aberdare Ranges',
    },
    {
      name: 'Nyeri',
      code: '019',
      lat: -0.416667,
      lng: 36.95,
      desc: 'Central Kenya county and home to Mount Kenya',
    },
    {
      name: 'Kirinyaga',
      code: '020',
      lat: -0.666667,
      lng: 37.3,
      desc: 'Central Kenya county known for rice farming and Mount Kenya',
    },
    {
      name: "Murang'a",
      code: '021',
      lat: -0.716667,
      lng: 37.15,
      desc: 'Central Kenya county known for coffee farming',
    },
    {
      name: 'Kiambu',
      code: '022',
      lat: -1.166667,
      lng: 36.833333,
      desc: 'Central Kenya county neighboring Nairobi, known for coffee',
    },
    {
      name: 'Turkana',
      code: '023',
      lat: 3.116667,
      lng: 35.6,
      desc: 'Northwestern county with Lake Turkana and oil deposits',
    },
    {
      name: 'West Pokot',
      code: '024',
      lat: 1.216667,
      lng: 35.116667,
      desc: 'Northwestern county known for pastoralism and agriculture',
    },
    {
      name: 'Samburu',
      code: '025',
      lat: 1.0,
      lng: 37.0,
      desc: 'Northern county known for wildlife conservancies and Samburu culture',
    },
    {
      name: 'Trans Nzoia',
      code: '026',
      lat: 1.016667,
      lng: 35.0,
      desc: 'Western county known as the granary of Kenya',
    },
    {
      name: 'Uasin Gishu',
      code: '027',
      lat: 0.516667,
      lng: 35.283333,
      desc: 'Rift Valley county and home to Eldoret city',
    },
    {
      name: 'Elgeyo Marakwet',
      code: '028',
      lat: 0.833333,
      lng: 35.5,
      desc: 'Rift Valley county known for athletics and the Kerio Valley',
    },
    {
      name: 'Nandi',
      code: '029',
      lat: 0.183333,
      lng: 35.1,
      desc: 'Rift Valley county known for tea farming and athletics',
    },
    {
      name: 'Baringo',
      code: '030',
      lat: 0.633333,
      lng: 35.966667,
      desc: 'Rift Valley county with Lake Baringo and geothermal energy',
    },
    {
      name: 'Laikipia',
      code: '031',
      lat: 0.033333,
      lng: 36.783333,
      desc: 'Central Kenya county known for wildlife conservancies',
    },
    {
      name: 'Nakuru',
      code: '032',
      lat: -0.3,
      lng: 36.066667,
      desc: 'Rift Valley county with Lake Nakuru National Park',
    },
    {
      name: 'Narok',
      code: '033',
      lat: -1.083333,
      lng: 35.866667,
      desc: 'Rift Valley county home to Maasai Mara National Reserve',
    },
    {
      name: 'Kajiado',
      code: '034',
      lat: -1.85,
      lng: 36.783333,
      desc: 'Rift Valley county neighboring Nairobi and Tanzania border',
    },
    {
      name: 'Kericho',
      code: '035',
      lat: -0.366667,
      lng: 35.283333,
      desc: 'Rift Valley county known for tea farming',
    },
    {
      name: 'Bomet',
      code: '036',
      lat: -0.783333,
      lng: 35.333333,
      desc: 'Rift Valley county known for tea and dairy farming',
    },
    {
      name: 'Kakamega',
      code: '037',
      lat: 0.283333,
      lng: 34.75,
      desc: 'Western county known for gold mining and Kakamega Forest',
    },
    {
      name: 'Vihiga',
      code: '038',
      lat: 0.083333,
      lng: 34.716667,
      desc: 'Western county known for tea farming and high population density',
    },
    {
      name: 'Bungoma',
      code: '039',
      lat: 0.566667,
      lng: 34.566667,
      desc: 'Western county known for maize and sugarcane farming',
    },
    {
      name: 'Busia',
      code: '040',
      lat: 0.466667,
      lng: 34.116667,
      desc: 'Western border county with Uganda',
    },
    {
      name: 'Siaya',
      code: '041',
      lat: 0.062629,
      lng: 34.287807,
      desc: 'Western county along Lake Victoria',
    },
    {
      name: 'Kisumu',
      code: '042',
      lat: -0.091702,
      lng: 34.767956,
      desc: 'Lakeside county on Lake Victoria and major port city',
    },
    {
      name: 'Homa Bay',
      code: '043',
      lat: -0.516667,
      lng: 34.45,
      desc: 'Western county along Lake Victoria known for fishing',
    },
    {
      name: 'Migori',
      code: '044',
      lat: -1.066667,
      lng: 34.466667,
      desc: 'Western county known for gold mining and tobacco farming',
    },
    {
      name: 'Kisii',
      code: '045',
      lat: -0.683333,
      lng: 34.766667,
      desc: 'Western county known for bananas and soapstone carving',
    },
    {
      name: 'Nyamira',
      code: '046',
      lat: -0.566667,
      lng: 34.933333,
      desc: 'Western county known for tea farming and bananas',
    },
    {
      name: 'Nairobi',
      code: '047',
      lat: -1.286389,
      lng: 36.817223,
      desc: 'Capital city and county of Kenya',
    },
  ]

  // Create counties in batches to avoid overwhelming the database
  const batchSize = 10
  const countyBatches = []
  for (let i = 0; i < allCountiesData.length; i += batchSize) {
    countyBatches.push(allCountiesData.slice(i, i + batchSize))
  }

  const allCounties = []
  for (const batch of countyBatches) {
    const batchCounties = await Promise.all(
      batch.map((county) =>
        payload.create({
          collection: 'counties',
          data: {
            name: county.name,
            code: county.code,
            coordinates: {
              latitude: county.lat,
              longitude: county.lng,
            },
            description: county.desc,
            isActive: true,
          },
        }),
      ),
    )
    allCounties.push(...batchCounties)
    payload.logger.info(
      `Created ${batchCounties.length} counties (${allCounties.length}/${allCountiesData.length} total)`,
    )
  }

  // Get specific counties for user assignment
  const nairobiCounty = allCounties.find((c) => c.name === 'Nairobi')
  const mombasaCounty = allCounties.find((c) => c.name === 'Mombasa')
  const kisumuCounty = allCounties.find((c) => c.name === 'Kisumu')

  payload.logger.info(`— Seeding media...`)

  const [image1Buffer, image2Buffer, image3Buffer, hero1Buffer] = await Promise.all([
    fetchLocalFile('src/endpoints/seed/image-post1.webp'),
    fetchLocalFile('src/endpoints/seed/image-post2.webp'),
    fetchLocalFile('src/endpoints/seed/image-post3.webp'),
    fetchLocalFile('src/endpoints/seed/image-hero1.webp'),
  ])

  // Create users with different roles and validation status
  const [
    superAdmin,
    adminUser,
    demoAuthor,
    validatedCitizen1,
    validatedCitizen2,
    exhibitorUser,
    image1Doc,
    image2Doc,
    image3Doc,
    imageHomeDoc,
  ] = await Promise.all([
    // Super Admin User
    payload.create({
      collection: 'users',
      data: {
        name: 'Super Administrator',
        email: '<EMAIL>',
        password: 'SuperAdmin123!',
        role: 'admin',
        _verified: true,
        _verificationToken: null,
        loginAttempts: 0,
        lockUntil: null,
        profile: {
          bio: 'System super administrator with full access to all IKIA platform features.',
          location: 'Nairobi, Kenya',
          website: 'https://ikia.go.ke',
        },
        preferences: {
          theme: 'dark',
          language: 'en',
          notifications: {
            email: true,
            sms: true,
            push: true,
          },
        },
      },
    }),
    // Regular Admin User
    payload.create({
      collection: 'users',
      data: {
        name: 'IKIA Administrator',
        email: '<EMAIL>',
        password: 'Admin123!',
        role: 'admin',
        _verified: true,
        _verificationToken: null,
        loginAttempts: 0,
        lockUntil: null,
        profile: {
          bio: 'IKIA platform administrator managing user registrations and content.',
          location: 'Nairobi, Kenya',
        },
        preferences: {
          theme: 'light',
          language: 'en',
          notifications: {
            email: true,
            sms: false,
            push: true,
          },
        },
      },
    }),
    // Demo Author (for content creation)
    payload.create({
      collection: 'users',
      data: {
        name: 'Demo Author',
        email: '<EMAIL>',
        password: 'password',
        role: 'citizen',
        _verified: true,
        _verificationToken: null,
        loginAttempts: 0,
        lockUntil: null,
        profile: {
          bio: 'Demo content author for IKIA platform.',
          location: 'Nairobi, Kenya',
        },
      },
    }),
    // Validated Citizen 1
    payload.create({
      collection: 'users',
      data: {
        name: 'John Mwangi',
        email: '<EMAIL>',
        password: 'Citizen123!',
        role: 'citizen',
        _verified: true,
        _verificationToken: null,
        loginAttempts: 0,
        lockUntil: null,
        phone_number: '254712345678',
        id_number: '12345678',
        county: nairobiCounty.id,
        package_status: 'active',
        business_type: 'Services',
        registration_purpose: 'Business Registration',
        profile: {
          bio: 'Small business owner interested in traditional knowledge preservation.',
          location: 'Nairobi, Kenya',
        },
        payment_profile: {
          preferred_currency: 'KES',
          daily_limit: 50000,
          monthly_limit: 500000,
        },
      },
    }),
    // Validated Citizen 2
    payload.create({
      collection: 'users',
      data: {
        name: 'Grace Wanjiku',
        email: '<EMAIL>',
        password: 'Citizen456!',
        role: 'citizen',
        _verified: true,
        _verificationToken: null,
        loginAttempts: 0,
        lockUntil: null,
        phone_number: '254723456789',
        id_number: '23456789',
        county: kisumuCounty.id,
        package_status: 'active',
        business_type: 'Other',
        registration_purpose: 'Professional Certification',
        profile: {
          bio: 'Traditional medicine practitioner specializing in herbal remedies.',
          location: 'Kisumu, Kenya',
        },
        payment_profile: {
          preferred_currency: 'KES',
          daily_limit: 30000,
          monthly_limit: 300000,
        },
      },
    }),
    // Exhibitor User
    payload.create({
      collection: 'users',
      data: {
        name: 'David Kiprotich',
        email: '<EMAIL>',
        password: 'Exhibitor789!',
        role: 'business',
        _verified: true,
        _verificationToken: null,
        loginAttempts: 0,
        lockUntil: null,
        phone_number: '254734567890',
        id_number: '34567890',
        county: nairobiCounty.id,
        package_status: 'active',
        business_type: 'Services',
        registration_purpose: 'Exhibition Participation',
        profile: {
          bio: 'Cultural tourism operator showcasing traditional Kenyan heritage.',
          location: 'Eldoret, Kenya',
          website: 'https://heritage-tours.co.ke',
        },
        payment_profile: {
          preferred_currency: 'KES',
          daily_limit: 100000,
          monthly_limit: 1000000,
        },
      },
    }),
    payload.create({
      collection: 'media',
      data: image1,
      file: image1Buffer,
    }),
    payload.create({
      collection: 'media',
      data: image2,
      file: image2Buffer,
    }),
    payload.create({
      collection: 'media',
      data: image2,
      file: image3Buffer,
    }),
    payload.create({
      collection: 'media',
      data: imageHero1,
      file: hero1Buffer,
    }),

    payload.create({
      collection: 'categories',
      data: {
        title: 'Technology',
        breadcrumbs: [
          {
            label: 'Technology',
            url: '/technology',
          },
        ],
      },
    }),

    payload.create({
      collection: 'categories',
      data: {
        title: 'News',
        breadcrumbs: [
          {
            label: 'News',
            url: '/news',
          },
        ],
      },
    }),

    payload.create({
      collection: 'categories',
      data: {
        title: 'Finance',
        breadcrumbs: [
          {
            label: 'Finance',
            url: '/finance',
          },
        ],
      },
    }),
    payload.create({
      collection: 'categories',
      data: {
        title: 'Design',
        breadcrumbs: [
          {
            label: 'Design',
            url: '/design',
          },
        ],
      },
    }),

    payload.create({
      collection: 'categories',
      data: {
        title: 'Software',
        breadcrumbs: [
          {
            label: 'Software',
            url: '/software',
          },
        ],
      },
    }),

    payload.create({
      collection: 'categories',
      data: {
        title: 'Engineering',
        breadcrumbs: [
          {
            label: 'Engineering',
            url: '/engineering',
          },
        ],
      },
    }),
  ])

  payload.logger.info(`— Seeding posts...`)

  // Do not create posts with `Promise.all` because we want the posts to be created in order
  // This way we can sort them by `createdAt` or `publishedAt` and they will be in the expected order
  const post1Doc = await payload.create({
    collection: 'posts',
    depth: 0,
    context: {
      disableRevalidate: true,
    },
    data: post1({ heroImage: image1Doc, blockImage: image2Doc, author: demoAuthor }),
  })

  const post2Doc = await payload.create({
    collection: 'posts',
    depth: 0,
    context: {
      disableRevalidate: true,
    },
    data: post2({ heroImage: image2Doc, blockImage: image3Doc, author: demoAuthor }),
  })

  const post3Doc = await payload.create({
    collection: 'posts',
    depth: 0,
    context: {
      disableRevalidate: true,
    },
    data: post3({ heroImage: image3Doc, blockImage: image1Doc, author: demoAuthor }),
  })

  // update each post with related posts
  await payload.update({
    id: post1Doc.id,
    collection: 'posts',
    data: {
      relatedPosts: [post2Doc.id, post3Doc.id],
    },
  })
  await payload.update({
    id: post2Doc.id,
    collection: 'posts',
    data: {
      relatedPosts: [post1Doc.id, post3Doc.id],
    },
  })
  await payload.update({
    id: post3Doc.id,
    collection: 'posts',
    data: {
      relatedPosts: [post1Doc.id, post2Doc.id],
    },
  })

  payload.logger.info(`— Seeding contact form...`)

  const contactForm = await payload.create({
    collection: 'forms',
    depth: 0,
    data: contactFormData,
  })

  payload.logger.info(`— Seeding pages...`)

  const [_, contactPage] = await Promise.all([
    payload.create({
      collection: 'pages',
      depth: 0,
      data: home({ heroImage: imageHomeDoc, metaImage: image2Doc }),
    }),
    payload.create({
      collection: 'pages',
      depth: 0,
      data: contactPageData({ contactForm: contactForm }),
    }),
  ])

  payload.logger.info(`— Seeding globals...`)

  await Promise.all([
    payload.updateGlobal({
      slug: 'header',
      data: {
        navItems: [
          {
            link: {
              type: 'custom',
              label: 'Posts',
              url: '/posts',
            },
          },
          {
            link: {
              type: 'reference',
              label: 'Contact',
              reference: {
                relationTo: 'pages',
                value: contactPage.id,
              },
            },
          },
        ],
      },
    }),
    payload.updateGlobal({
      slug: 'footer',
      data: {
        navItems: [
          {
            link: {
              type: 'custom',
              label: 'Admin',
              url: '/admin',
            },
          },
          {
            link: {
              type: 'custom',
              label: 'Source Code',
              newTab: true,
              url: 'https://github.com/payloadcms/payload/tree/main/templates/website',
            },
          },
          {
            link: {
              type: 'custom',
              label: 'Payload',
              newTab: true,
              url: 'https://payloadcms.com/',
            },
          },
        ],
      },
    }),
  ])

  payload.logger.info(`— Seeding payment workflow data...`)

  // Create sample delegate packages
  const [basicPackage, premiumPackage, enterprisePackage] = await Promise.all([
    payload.create({
      collection: 'delegatepackages',
      data: {
        name: 'Basic Delegate Package',
        description: 'Essential package for individual conference delegates',
        price: 15000,
        currency: 'KES',
        duration: '3 days',
        packageType: 'delegate',
        isActive: true,
        isFeatured: false,
        displayOrder: 1,
        features: [
          { feature: 'Conference access', included: true },
          { feature: 'Welcome kit', included: true },
          { feature: 'Lunch and refreshments', included: true },
          { feature: 'Certificate of attendance', included: true },
        ],
        inclusions: {
          conferenceAccess: true,
          meals: 'lunch',
          materials: true,
          certificate: true,
          networking: false,
          accommodation: false,
          transport: false,
        },
      },
    }),
    payload.create({
      collection: 'delegatepackages',
      data: {
        name: 'Premium Business Package',
        description: 'Comprehensive package for business delegates with networking',
        price: 35000,
        currency: 'KES',
        duration: '3 days',
        packageType: 'delegate',
        isActive: true,
        isFeatured: true,
        displayOrder: 2,
        features: [
          { feature: 'Conference access', included: true },
          { feature: 'VIP welcome kit', included: true },
          { feature: 'All meals included', included: true },
          { feature: 'Networking events access', included: true },
          { feature: 'Premium seating', included: true },
        ],
        inclusions: {
          conferenceAccess: true,
          meals: 'all',
          materials: true,
          certificate: true,
          networking: true,
          accommodation: false,
          transport: false,
        },
      },
    }),
    payload.create({
      collection: 'delegatepackages',
      data: {
        name: 'Exhibition Package',
        description: 'Complete exhibition package with booth space and marketing',
        price: 75000,
        currency: 'KES',
        duration: '3 days',
        packageType: 'exhibition',
        isActive: true,
        isFeatured: true,
        displayOrder: 3,
        features: [
          { feature: '3x6m exhibition space', included: true },
          { feature: 'Premium furniture package', included: true },
          { feature: 'Electricity and internet', included: true },
          { feature: 'Marketing materials', included: true },
          { feature: 'Networking events access', included: true },
          { feature: 'Featured catalog listing', included: true },
        ],
        inclusions: {
          conferenceAccess: true,
          meals: 'all',
          materials: true,
          certificate: true,
          networking: true,
          accommodation: false,
          transport: false,
        },
      },
    }),
  ])

  // Create sample exhibitors
  const [exhibitor1, exhibitor2] = await Promise.all([
    payload.create({
      collection: 'exhibitors',
      data: {
        firstName: 'Sarah',
        lastName: 'Njeri',
        email: '<EMAIL>',
        phone: '254745123456',
        position: 'Founder & CEO',
        country: 'Kenya',
        city: 'Nairobi',
        hasCompany: true,
        companyName: 'Heritage Crafts Kenya',
        website: 'https://heritage-crafts.co.ke',
        address: '123 Craft Street, Nairobi, Kenya',
        businessType: 'Traditional Foods & Nutrition',
        businessDescription:
          'We preserve and promote traditional Kenyan food preparation methods and recipes.',
        productsServices: 'Traditional food products, cooking workshops, recipe books',
        targetMarket: 'Restaurants, hotels, cultural centers, international markets',
        yearsInBusiness: '8',
        selectedPackage: enterprisePackage.id,
        boothRequirement: 'premium_3x6',
        additionalServices: [
          { service: 'electricity' },
          { service: 'internet' },
          { service: 'av_equipment' },
          { service: 'furniture' },
        ],
        specialRequirements: 'Need refrigeration for food samples and cooking demonstration area',
        hasAdditionalReps: true,
        additionalRepresentatives: [
          {
            name: 'James Mwangi',
            email: '<EMAIL>',
            phone: '************',
            position: 'Head Chef',
          },
        ],
        registrationStatus: 'payment_confirmed',
        registrationDate: new Date(),
        termsAccepted: true,
        exhibitorGuidelines: true,
        mediaConsent: true,
      },
    }),
    payload.create({
      collection: 'exhibitors',
      data: {
        firstName: 'Michael',
        lastName: 'Ochieng',
        email: '<EMAIL>',
        phone: '************',
        position: 'Traditional Healer',
        country: 'Kenya',
        city: 'Kisumu',
        hasCompany: true,
        companyName: 'Luo Traditional Medicine Center',
        website: 'https://luo-medicine.co.ke',
        address: '456 Healing Avenue, Kisumu, Kenya',
        businessType: 'Local Remedies & Traditional Medicine',
        businessDescription:
          'Traditional healing center specializing in Luo medicinal practices and herbal remedies.',
        productsServices: 'Herbal medicines, healing consultations, traditional therapy sessions',
        targetMarket: 'Healthcare institutions, wellness centers, research organizations',
        yearsInBusiness: '15',
        selectedPackage: premiumPackage.id,
        boothRequirement: 'standard_3x3',
        additionalServices: [
          { service: 'electricity' },
          { service: 'storage' },
          { service: 'marketing' },
        ],
        specialRequirements:
          'Need secure storage for medicinal herbs and consultation privacy area',
        hasAdditionalReps: false,
        additionalRepresentatives: [],
        registrationStatus: 'registration_complete',
        registrationDate: new Date(),
        termsAccepted: true,
        exhibitorGuidelines: true,
        mediaConsent: true,
      },
    }),
  ])

  // Create sample invoices
  await Promise.all([
    payload.create({
      collection: 'invoices',
      data: {
        invoice_number: 'INV-2024-001',
        user: validatedCitizen1.id,
        package: basicPackage.id,
        amount: basicPackage.price,
        currency: 'KES',
        status: 'settled',
        payment_method: 'mpesa',
        payment_reference: 'MPX123456789',
        due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        paid_at: new Date().toISOString(),
        notes: 'Payment completed via M-Pesa',
      },
    }),
    payload.create({
      collection: 'invoices',
      data: {
        invoice_number: 'INV-2024-002',
        user: validatedCitizen2.id,
        package: premiumPackage.id,
        amount: premiumPackage.price,
        currency: 'KES',
        status: 'settled',
        payment_method: 'bank_transfer',
        payment_reference: 'BT987654321',
        due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        paid_at: new Date().toISOString(),
        notes: 'Payment completed via bank transfer - Premium Business Package',
      },
    }),
  ])

  payload.logger.info(`— Seeding Pesaflow notifications...`)

  // Create sample Pesaflow notifications
  await Promise.all([
    // Successful payment notification
    payload.create({
      collection: 'pesaflow-notifications',
      data: {
        payment_channel: 'M-Pesa',
        client_invoice_ref: 'SS6489db2e1fdfd',
        payment_reference: 'ABCDEF-925',
        payment_date: '2023-06-14T15:33:16',
        inserted_at: '2023-06-14T15:33:16',
        currency: 'KES',
        amount_paid: 102.0,
        invoice_amount: 102.0,
        last_payment_amount: 102.0,
        status: 'settled',
        invoice_number: 'ABCDEF',
        secure_hash:
          'NTU4NzEzNzhiOTI1N2NlODY3YWYzYjVhYjQ4MzNiNDYzY2M3MzQwYmNlZDc4ZDJlZjg3ZDZkOTQ5ZjUyM2EzNQ==',
        invoice: null, // Will be linked to actual invoice in real scenario
        user: validatedCitizen1.id,
        service_package: basicPackage.id,
        processing_status: 'processed',
        processed_at: new Date().toISOString(),
        processing_notes: 'Pesaflow payment settled successfully via M-Pesa',
        hash_verified: true,
        ip_address: '**********',
        user_agent: 'Pesaflow-Notification/1.0',
        raw_payload: {
          payment_channel: 'M-Pesa',
          client_invoice_ref: 'SS6489db2e1fdfd',
          payment_reference: [
            {
              payment_reference: 'ABCDEF-925',
              payment_date: '2023-06-14T15:33:16',
              inserted_at: '2023-06-14T15:33:16',
              currency: 'KES',
              amount: '102.00',
            },
          ],
          currency: 'KES',
          amount_paid: '102.00',
          invoice_amount: '102.00',
          status: 'settled',
          invoice_number: 'ABCDEF',
          payment_date: '2023-06-14 15:33:16Z',
          last_payment_amount: '102.00',
          secure_hash:
            'NTU4NzEzNzhiOTI1N2NlODY3YWYzYjVhYjQ4MzNiNDYzY2M3MzQwYmNlZDc4ZDJlZjg3ZDZkOTQ5ZjUyM2EzNQ==',
        },
      },
    }),
    // Failed payment notification
    payload.create({
      collection: 'pesaflow-notifications',
      data: {
        payment_channel: 'Bank Transfer',
        client_invoice_ref: 'SS6489db2e1fdfe',
        payment_reference: 'FAILED-123',
        payment_date: '2023-06-15T10:00:00',
        inserted_at: '2023-06-15T10:00:00',
        currency: 'KES',
        amount_paid: 0.0,
        invoice_amount: 15000.0,
        last_payment_amount: 0.0,
        status: 'failed',
        invoice_number: 'INV-FAIL-001',
        secure_hash: 'YWJjZGVmZ2hpams1NjEyMzQ1Njc4OWFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6',
        invoice: null, // Will be linked to actual invoice in real scenario
        user: validatedCitizen2.id,
        service_package: premiumPackage.id,
        processing_status: 'processed',
        processed_at: new Date().toISOString(),
        processing_notes: 'Payment failed - insufficient funds',
        hash_verified: true,
        ip_address: '**********',
        user_agent: 'Pesaflow-Notification/1.0',
        raw_payload: {
          payment_channel: 'Bank Transfer',
          client_invoice_ref: 'SS6489db2e1fdfe',
          payment_reference: [
            {
              payment_reference: 'FAILED-123',
              payment_date: '2023-06-15T10:00:00',
              inserted_at: '2023-06-15T10:00:00',
              currency: 'KES',
              amount: '0.00',
            },
          ],
          currency: 'KES',
          amount_paid: '0.00',
          invoice_amount: '15000.00',
          status: 'failed',
          invoice_number: 'INV-FAIL-001',
          payment_date: '2023-06-15 10:00:00Z',
          last_payment_amount: '0.00',
          secure_hash: 'YWJjZGVmZ2hpams1NjEyMzQ1Njc4OWFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6',
        },
      },
    }),
  ])

  payload.logger.info(`— Seeding events and speakers...`)

  // Create sample speakers
  const [speaker1, speaker2] = await Promise.all([
    payload.create({
      collection: 'speakers',
      data: {
        name: 'Dr. Jane Wanjiku',
        title: 'Innovation Director',
        company: 'Kenya Innovation Hub',
        bio: {
          root: {
            children: [
              {
                children: [
                  {
                    text: 'Dr. Jane Wanjiku is a leading expert in innovation and entrepreneurship in Kenya. She has over 15 years of experience in fostering startup ecosystems and has helped launch over 200 successful businesses.',
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        slug: 'dr-jane-wanjiku',
      },
    }),
    payload.create({
      collection: 'speakers',
      data: {
        name: 'Michael Ochieng',
        title: 'Investment Manager',
        company: 'East Africa Venture Capital',
        bio: {
          root: {
            children: [
              {
                children: [
                  {
                    text: 'Michael Ochieng specializes in early-stage investments and has been instrumental in funding innovative startups across East Africa. He brings deep expertise in fintech and agtech sectors.',
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        slug: 'michael-ochieng',
      },
    }),
  ])

  // Create sample events
  await Promise.all([
    payload.create({
      collection: 'events',
      data: {
        title: 'IKIA Innovation Summit 2024',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    text: 'Join us for the premier innovation and investment summit in Kenya. Connect with investors, entrepreneurs, and industry leaders to explore opportunities in the growing Kenyan market.',
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        type: 'keynote',
        date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(), // 60 days from now
        startTime: '09:00',
        endTime: '17:00',
        day: 1,
        location: 'Nairobi Convention Centre',
        speakers: [speaker1.id, speaker2.id],
      },
    }),
    payload.create({
      collection: 'events',
      data: {
        title: 'Startup Pitch Competition',
        description: {
          root: {
            children: [
              {
                children: [
                  {
                    text: 'Early-stage startups compete for seed funding and mentorship opportunities. Open to all sectors with focus on technology and innovation.',
                  },
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                type: 'paragraph',
                version: 1,
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        },
        type: 'panel',
        date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        startTime: '14:00',
        endTime: '16:00',
        day: 2,
        location: 'iHub Nairobi',
        speakers: [speaker2.id],
      },
    }),
  ])

  payload.logger.info('Seeded database successfully!')
  payload.logger.info('— Created users:')
  payload.logger.info(`   • Super Admin: ${superAdmin.email}`)
  payload.logger.info(`   • Admin User: ${adminUser.email}`)
  payload.logger.info(`   • Demo Author: ${demoAuthor.email}`)
  payload.logger.info(`   • Validated Citizen 1: ${validatedCitizen1.email}`)
  payload.logger.info(`   • Validated Citizen 2: ${validatedCitizen2.email}`)
  payload.logger.info(`   • Exhibitor User: ${exhibitorUser.email}`)
  payload.logger.info('— Created delegate packages:')
  payload.logger.info(`   • ${basicPackage.name} - ${basicPackage.currency} ${basicPackage.price}`)
  payload.logger.info(
    `   • ${premiumPackage.name} - ${premiumPackage.currency} ${premiumPackage.price}`,
  )
  payload.logger.info(
    `   • ${enterprisePackage.name} - ${enterprisePackage.currency} ${enterprisePackage.price}`,
  )
  payload.logger.info('— Created exhibitors:')
  payload.logger.info(`   • ${exhibitor1.companyName} (${exhibitor1.email})`)
  payload.logger.info(`   • ${exhibitor2.companyName} (${exhibitor2.email})`)
}

async function fetchLocalFile(filePath: string): Promise<File> {
  try {
    const fs = await import('fs')
    const path = await import('path')

    const fullPath = path.resolve(process.cwd(), filePath)
    const data = fs.readFileSync(fullPath)
    const fileName = path.basename(filePath)
    const extension = path.extname(filePath).substring(1)

    return {
      name: fileName,
      data: data,
      mimetype: `image/${extension}`,
      size: data.length,
    }
  } catch (error: any) {
    console.warn(`Failed to read local file from ${filePath}:`, error.message)

    // Create a simple 1x1 PNG placeholder
    const simplePNG = Buffer.from([
      0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49, 0x48, 0x44,
      0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1f,
      0x15, 0xc4, 0x89, 0x00, 0x00, 0x00, 0x0a, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9c, 0x63, 0x00,
      0x01, 0x00, 0x00, 0x05, 0x00, 0x01, 0x0d, 0x0a, 0x2d, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82,
    ])

    return {
      name: `placeholder-${filePath.split('/').pop()?.replace('.webp', '.png') || 'image.png'}`,
      data: simplePNG,
      mimetype: 'image/png',
      size: simplePNG.length,
    }
  }
}

async function fetchFileByURL(url: string): Promise<File> {
  try {
    // Create an AbortController for timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

    const res = await fetch(url, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'User-Agent': 'IKIA-Seeder/1.0',
      },
    })

    clearTimeout(timeoutId)

    if (!res.ok) {
      throw new Error(`Failed to fetch file from ${url}, status: ${res.status}`)
    }

    const data = await res.arrayBuffer()

    return {
      name: url.split('/').pop() || `file-${Date.now()}`,
      data: Buffer.from(data),
      mimetype: `image/${url.split('.').pop()}`,
      size: data.byteLength,
    }
  } catch (error: any) {
    console.warn(`Failed to fetch file from ${url}:`, error.message)

    // Create a simple 1x1 PNG placeholder instead of WebP to avoid Sharp issues
    const simplePNG = Buffer.from([
      0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49, 0x48, 0x44,
      0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1f,
      0x15, 0xc4, 0x89, 0x00, 0x00, 0x00, 0x0a, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9c, 0x63, 0x00,
      0x01, 0x00, 0x00, 0x05, 0x00, 0x01, 0x0d, 0x0a, 0x2d, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82,
    ])

    return {
      name: `placeholder-${url.split('/').pop()?.replace('.webp', '.png') || 'image.png'}`,
      data: simplePNG,
      mimetype: 'image/png',
      size: simplePNG.length,
    }
  }
}
