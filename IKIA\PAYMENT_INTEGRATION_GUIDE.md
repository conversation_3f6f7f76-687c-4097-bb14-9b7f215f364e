# Comprehensive Payment Integration Workflow Guide

## Overview
This guide provides a complete implementation strategy for integrating payment functionality into the Payload CMS application, leveraging the existing eCitizen SSO and Pesaflow payment infrastructure.

## 1. User Authentication Flow

### 1.1 Payload CMS Authentication
**Current Implementation Status**: ✅ Complete
- Built-in Payload CMS authentication system
- JWT-based session management
- User registration and login through Payload admin/frontend
- Role-based access control

### 1.2 Authentication Workflow
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant P as Payload CMS

    U->>F: Enter email/password
    F->>P: POST /api/users/login
    P->>P: Validate credentials
    P->>F: Return JWT token + user data
    F->>F: Store token securely

    Note over F,P: For subsequent requests
    F->>P: API calls with Authorization header
    P->>P: Validate JWT token
    P->>F: Return requested data
```

### 1.3 Session Management
- **Token Storage**: HTTP-only cookies (recommended) or secure localStorage
- **Token Expiration**: Configurable via Payload config (default: 2 hours)
- **Refresh Strategy**: Automatic token refresh using Payload's built-in mechanism
- **Logout**: Clear tokens via `/api/users/logout` endpoint

### 1.4 User Role Permissions for Payments
**Enhanced Users Collection with Payment Roles**:
```typescript
// Payment-specific user roles and permissions
roles: ['citizen', 'business', 'admin', 'payment_processor']
payment_permissions: {
  canInitiatePayments: boolean
  canViewPaymentHistory: boolean
  canProcessRefunds: boolean
  maxPaymentAmount: number
  dailyPaymentLimit: number
}
```

### 1.5 eCitizen Integration Context
**Important**: eCitizen is NOT used for user authentication in the app. Instead:
- eCitizen SSO endpoints are available for backend payment validation
- eCitizen user data can be linked to Payload users for payment processing
- eCitizen integration happens during payment flow, not login flow

## 2. Payment Workflow Design

### 2.1 End-to-End Payment Process
```mermaid
flowchart TD
    A[User Logs into Payload App] --> B[User Initiates Payment]
    B --> C[Validate Payload JWT Session]
    C --> D[Create Invoice Record]
    D --> E[Generate Payment Reference]
    E --> F[Call Pesaflow Checkout API]
    F --> G[Redirect to Payment Gateway]
    G --> H[User Completes Payment via M-PESA/Card]
    H --> I[Pesaflow IPN Webhook to Backend]
    I --> J[Verify Webhook Signature]
    J --> K[Update Payment Status in Payload]
    K --> L[Send Confirmation to User]
    L --> M[Trigger Fulfillment Process]

    subgraph "Backend Only"
        N[eCitizen Validation APIs]
        O[USSD Integration]
    end

    F -.-> N
    I -.-> O
```

### 2.2 Payment States
- `pending`: Payment initiated, awaiting user action
- `processing`: Payment in progress at gateway
- `settled`: Payment completed successfully
- `partial`: Partial payment received
- `failed`: Payment failed or cancelled
- `refunded`: Payment refunded
- `expired`: Payment session expired

### 2.3 Error Handling & Retry Mechanisms
- **Network Failures**: Exponential backoff retry (3 attempts)
- **Gateway Timeouts**: 30-second timeout with fallback
- **Invalid Responses**: Log and return user-friendly error
- **Webhook Failures**: Queue for retry processing
- **Duplicate Payments**: Idempotency key validation

## 3. Payload CMS Collections Schema

### 3.1 Enhanced Users Collection
```typescript
export const Users: CollectionConfig = {
  slug: 'users',
  auth: {
    tokenExpiration: 7200, // 2 hours
    maxLoginAttempts: 5,
    lockTime: 600000, // 10 minutes
  },
  fields: [
    // Existing fields...
    {
      name: 'ecitizen_id',
      type: 'text',
      unique: true,
      admin: { readOnly: true }
    },
    {
      name: 'phone_number',
      type: 'text',
      validate: (val) => /^254\d{9}$/.test(val)
    },
    {
      name: 'id_number',
      type: 'text',
      unique: true
    },
    {
      name: 'payment_profile',
      type: 'group',
      fields: [
        {
          name: 'preferred_currency',
          type: 'select',
          options: ['KES', 'USD'],
          defaultValue: 'KES'
        },
        {
          name: 'payment_limit',
          type: 'number',
          defaultValue: 100000
        }
      ]
    }
  ]
}
```

### 3.2 Invoices Collection
```typescript
export const Invoices: CollectionConfig = {
  slug: 'invoices',
  access: {
    create: authenticated,
    read: ({ req }) => ({ user: { equals: req.user?.id } }),
    update: ({ req }) => ({ user: { equals: req.user?.id } }),
    delete: () => false // Prevent deletion for audit
  },
  fields: [
    {
      name: 'invoice_number',
      type: 'text',
      unique: true,
      admin: { readOnly: true }
    },
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      required: true
    },
    {
      name: 'amount',
      type: 'number',
      required: true,
      min: 1
    },
    {
      name: 'currency',
      type: 'select',
      options: ['KES', 'USD'],
      defaultValue: 'KES'
    },
    {
      name: 'description',
      type: 'text',
      required: true
    },
    {
      name: 'status',
      type: 'select',
      options: ['draft', 'pending', 'processing', 'settled', 'failed', 'expired'],
      defaultValue: 'draft'
    },
    {
      name: 'payment_reference',
      type: 'text',
      unique: true
    },
    {
      name: 'pesaflow_data',
      type: 'group',
      fields: [
        { name: 'bill_ref_number', type: 'text' },
        { name: 'checkout_url', type: 'text' },
        { name: 'gateway_response', type: 'json' }
      ]
    },
    {
      name: 'due_date',
      type: 'date'
    }
  ],
  hooks: {
    beforeChange: [
      ({ data, operation }) => {
        if (operation === 'create') {
          data.invoice_number = `INV-${Date.now()}`
          data.payment_reference = `REF-${Date.now()}`
        }
      }
    ]
  }
}
```

### 3.3 Payments Collection
```typescript
export const Payments: CollectionConfig = {
  slug: 'payments',
  access: {
    create: () => false, // Only created via webhooks
    read: ({ req }) => ({ 'invoice.user': { equals: req.user?.id } }),
    update: () => false,
    delete: () => false
  },
  fields: [
    {
      name: 'transaction_id',
      type: 'text',
      unique: true,
      required: true
    },
    {
      name: 'invoice',
      type: 'relationship',
      relationTo: 'invoices',
      required: true
    },
    {
      name: 'amount_paid',
      type: 'number',
      required: true
    },
    {
      name: 'payment_method',
      type: 'select',
      options: ['M-PESA', 'CARD', 'BANK_TRANSFER']
    },
    {
      name: 'status',
      type: 'select',
      options: ['pending', 'settled', 'failed', 'refunded']
    },
    {
      name: 'gateway_data',
      type: 'json'
    },
    {
      name: 'processed_at',
      type: 'date'
    }
  ]
}
```

### 3.4 Payment Logs Collection
```typescript
export const PaymentLogs: CollectionConfig = {
  slug: 'payment-logs',
  access: {
    create: () => false,
    read: ({ req }) => req.user?.role === 'admin',
    update: () => false,
    delete: () => false
  },
  fields: [
    {
      name: 'event_type',
      type: 'select',
      options: ['payment_initiated', 'payment_completed', 'webhook_received', 'error_occurred']
    },
    {
      name: 'invoice_reference',
      type: 'text'
    },
    {
      name: 'user_id',
      type: 'text'
    },
    {
      name: 'data',
      type: 'json'
    },
    {
      name: 'ip_address',
      type: 'text'
    },
    {
      name: 'user_agent',
      type: 'text'
    }
  ]
}
```

## 4. Data Flow and State Management

### 4.1 Payment Initiation Data Flow
1. **User Input Capture**:
   - Amount, description, currency
   - User authentication verification
   - Payment method preference

2. **Invoice Creation**:
   - Generate unique invoice number
   - Store user relationship
   - Set initial status to 'draft'

3. **Pesaflow Integration**:
   - Generate secure hash using HashService
   - Call checkout endpoint
   - Store gateway response

4. **Status Updates**:
   - Update invoice status to 'pending'
   - Log payment initiation event
   - Return checkout URL to frontend

### 4.2 Webhook Processing Data Flow
1. **Webhook Reception**:
   - Verify webhook signature
   - Parse payment notification data
   - Validate required fields

2. **Payment Record Creation**:
   - Create payment record
   - Link to existing invoice
   - Store gateway transaction data

3. **Status Synchronization**:
   - Update invoice status
   - Calculate payment totals
   - Handle partial payments

4. **Notification Triggers**:
   - Send email confirmations
   - Update user dashboard
   - Trigger fulfillment processes

### 4.3 Database Operations Strategy
- **Transactions**: Use database transactions for critical operations
- **Idempotency**: Implement idempotency keys for webhook processing
- **Audit Trail**: Maintain complete audit log of all payment events
- **Data Retention**: Archive old payment data according to compliance requirements

## 5. Frontend Integration Points

### 5.1 Authentication API Endpoints (Payload CMS)
```typescript
// User authentication (Payload built-in)
POST /api/users/login
POST /api/users/logout
POST /api/users/refresh-token
GET /api/users/me
POST /api/users/forgot-password
POST /api/users/reset-password

// User registration
POST /api/users
```

### 5.2 Payment API Endpoints
```typescript
// Payment operations
POST /api/payments/initiate
GET /api/payments/status/:reference
POST /api/payments/cancel/:reference
GET /api/payments/history

// Invoice management
POST /api/invoices
GET /api/invoices/:id
GET /api/invoices/user/:userId
PUT /api/invoices/:id/status
```

### 5.3 Request/Response Formats

#### Payment Initiation Request
```json
{
  "amount": 1500.00,
  "currency": "KES",
  "description": "Service payment",
  "user_id": "user_123",
  "callback_url": "https://app.com/payment/callback"
}
```

#### Payment Initiation Response
```json
{
  "success": true,
  "invoice_id": "inv_456",
  "payment_reference": "REF-1234567890",
  "checkout_url": "https://pesaflow.com/checkout/...",
  "expires_at": "2024-01-01T15:30:00Z"
}
```

#### Payment Status Response
```json
{
  "invoice_id": "inv_456",
  "status": "settled",
  "amount_expected": 1500.00,
  "amount_paid": 1500.00,
  "payment_method": "M-PESA",
  "completed_at": "2024-01-01T14:30:00Z",
  "transaction_id": "TXN123456789"
}
```

### 5.4 UI State Management
```typescript
// Payment flow state
interface PaymentState {
  currentStep: 'initiate' | 'processing' | 'complete' | 'error'
  invoice: Invoice | null
  paymentUrl: string | null
  error: string | null
  loading: boolean
}

// User session state (Payload CMS)
interface AuthState {
  user: PayloadUser | null
  token: string | null
  isAuthenticated: boolean
  permissions: {
    canInitiatePayments: boolean
    canViewPaymentHistory: boolean
    maxPaymentAmount: number
  }
}

// Payload user type
interface PayloadUser {
  id: string
  email: string
  name?: string
  role: 'citizen' | 'business' | 'admin' | 'payment_processor'
  phone_number?: string
  payment_profile?: {
    preferred_currency: 'KES' | 'USD'
    daily_limit: number
    monthly_limit: number
  }
}
```

## 6. Implementation Sequence and Dependencies

### Phase 1: Foundation (Week 1)
1. ✅ eCitizen SSO integration (Complete)
2. ✅ Basic payment endpoints (Complete)
3. ✅ HashService implementation (Complete)

### Phase 2: Collections & Data Models (Week 2)
1. Create enhanced Users collection
2. Implement Invoices collection
3. Implement Payments collection
4. Implement PaymentLogs collection
5. Add collection relationships and hooks

### Phase 3: Payment Processing (Week 3)
1. Enhance payment initiation endpoint
2. Implement comprehensive webhook processing
3. Add payment status tracking
4. Implement error handling and retry logic

### Phase 4: Frontend Integration (Week 4)
1. Create payment UI components
2. Implement state management
3. Add payment history views
4. Integrate with authentication flow

### Phase 5: Testing & Security (Week 5)
1. Unit tests for all payment functions
2. Integration tests for payment flow
3. Security audit and penetration testing
4. Performance optimization

### Dependencies
- ✅ Payload CMS setup
- ✅ eCitizen SSO credentials
- ✅ Pesaflow API credentials
- ✅ Database configuration
- 🔄 SSL certificates for production
- 🔄 Webhook endpoint security
- 🔄 Email service integration

## 7. Security Considerations

### 7.1 Authentication Security
- **Token Management**: JWT tokens with short expiration times
- **Secure Storage**: HTTP-only cookies with SameSite=Strict
- **CSRF Protection**: Anti-CSRF tokens for state-changing operations
- **Session Validation**: Validate tokens on every payment operation

### 7.2 Payment Security
- **Hash Verification**: All webhook payloads verified using HashService
- **Idempotency**: Prevent duplicate payment processing
- **Amount Validation**: Server-side validation of all payment amounts
- **Rate Limiting**: Prevent payment spam and abuse

### 7.3 Data Protection
- **PCI Compliance**: No card data stored locally
- **Data Encryption**: Sensitive data encrypted at rest
- **Audit Logging**: Complete audit trail for compliance
- **Access Control**: Role-based access to payment data

## 8. Error Handling Strategies

### 8.1 Payment Gateway Errors
```typescript
interface PaymentError {
  code: string
  message: string
  retryable: boolean
  userMessage: string
}

const errorHandlers = {
  'INSUFFICIENT_FUNDS': {
    retryable: false,
    userMessage: 'Insufficient funds. Please check your account balance.'
  },
  'NETWORK_ERROR': {
    retryable: true,
    userMessage: 'Connection issue. Please try again.'
  },
  'INVALID_PHONE': {
    retryable: false,
    userMessage: 'Invalid phone number format.'
  }
}
```

### 8.2 Webhook Processing Errors
- **Signature Verification Failure**: Log and reject
- **Duplicate Processing**: Check idempotency key
- **Database Errors**: Queue for retry with exponential backoff
- **Invalid Data**: Log error and notify administrators

## 9. Monitoring and Analytics

### 9.1 Key Metrics
- Payment success rate
- Average payment processing time
- Failed payment reasons
- User conversion rates
- Revenue tracking

### 9.2 Alerting
- Failed webhook processing
- High error rates
- Unusual payment patterns
- Security incidents

## 10. Testing Strategy

### 10.1 Unit Tests
```typescript
describe('Payment Processing', () => {
  test('should create invoice with valid data', async () => {
    const invoice = await createInvoice({
      amount: 1000,
      currency: 'KES',
      userId: 'user_123'
    })
    expect(invoice.status).toBe('draft')
  })

  test('should validate payment amounts', () => {
    expect(() => validateAmount(-100)).toThrow('Amount must be positive')
  })
})
```

### 10.2 Integration Tests
- End-to-end payment flow testing
- Webhook processing simulation
- Authentication flow testing
- Error scenario testing

### 10.3 Load Testing
- Concurrent payment processing
- Webhook handling under load
- Database performance testing
- API response time testing

## Next Steps
1. Review and approve this implementation guide
2. Set up development environment with test credentials
3. Begin Phase 2 implementation with collection creation
4. Establish testing procedures and CI/CD pipeline
5. Configure monitoring and alerting systems
