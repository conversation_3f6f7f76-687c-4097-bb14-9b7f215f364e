import { CollectionConfig } from 'payload'

import { anyone } from '../access/anyone'
import { authenticated } from '../access/authenticated'
import { slugField } from '@/fields/slug'

export const IKIAAsset: CollectionConfig = {
  slug: 'ikia-asset',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
    },
    {
      name: 'categories',
      type: 'array',
      label: 'Categories',
      fields: [
        {
          name: 'name',
          type: 'text',
        },
      ],
    },
    {
      name: 'tags',
      type: 'array',
      label: 'Tags',
      fields: [
        {
          name: 'name',
          type: 'text',
        },
      ],
    },
    {
      name: 'location',
      type: 'text',
      required: true,
    },
    {
      name: 'county',
      type: 'relationship',
      relationTo: 'counties',
      label: 'County',
      admin: {
        description: 'County where this IKIA asset is located',
      },
    },
    {
      name: 'thematicArea',
      type: 'relationship',
      relationTo: 'thematic-areas',
      required: true,
      label: 'Thematic Area',
      admin: {
        description: 'Thematic area this IKIA asset belongs to',
      },
    },
    {
      name: 'documentedBy',
      label: 'Documented By',
      type: 'text',
    },
    {
      name: 'yearDocumented',
      label: 'Year Documented',
      type: 'number',
    },
    {
      name: 'investmentPotential',
      type: 'select',
      label: 'Investment Potential',
      options: [
        { label: 'High', value: 'high' },
        { label: 'Medium', value: 'medium' },
        { label: 'Low', value: 'low' },
      ],
    },
    {
      name: 'featured',
      type: 'checkbox',
      label: 'Featured',
      defaultValue: false,
      admin: {
        description: 'Featured IKIA assets appear in special sections',
      },
      hooks: {
        beforeChange: [
          async ({ value, originalDoc, req }) => {
            if (value === true) {
              const { docs } = await req.payload.find({
                collection: 'ikia-asset',
                where: {
                  featured: {
                    equals: true,
                  },
                },
                limit: 6,
              })

              // Allow if editing and not increasing total count
              const isSameDoc = originalDoc && docs.some((doc) => doc.id === originalDoc.id)

              if (docs.length >= 6 && !isSameDoc) {
                throw new Error('You can only have up to 6 featured products.')
              }
            }

            return value
          },
        ],
      },
    },
    {
      name: 'readyForInvestment',
      type: 'checkbox',
      label: 'Ready for Investment',
      defaultValue: true,
      admin: {
        description: 'Mark this IKIA asset as ready for investment opportunities',
      },
    },
    {
      name: 'investmentNeeded',
      type: 'number',
      label: 'Investment Needed ',
      admin: {
        description: 'Amount of investment needed',
        condition: (data) => data.readyForInvestment,
      },
    },
    {
      name: 'expectedReturn',
      type: 'text',
      label: 'Expected Return',
      admin: {
        description: 'Expected return percentage (e.g., "25-35%")',
        condition: (data) => data.readyForInvestment,
      },
    },
    {
      name: 'timeline',
      type: 'text',
      label: 'Investment Timeline',
      admin: {
        description: 'Expected timeline for returns (e.g., "18 months")',
        condition: (data) => data.readyForInvestment,
      },
    },
    {
      name: 'riskLevel',
      type: 'select',
      label: 'Risk Level',
      options: [
        { label: 'Low', value: 'low' },
        { label: 'Medium', value: 'medium' },
        { label: 'High', value: 'high' },
      ],
      admin: {
        description: 'Investment risk level',
        condition: (data) => data.readyForInvestment,
      },
    },
    {
      name: 'impactScore',
      type: 'number',
      label: 'Impact Score',
      admin: {
        description: 'Social/environmental impact score (1-10)',
        condition: (data) => data.readyForInvestment,
      },
      validate: (val: number | null | undefined) => {
        if (val !== null && val !== undefined && (val < 1 || val > 10)) {
          return 'Impact score must be between 1 and 10'
        }
        return true
      },
    },
    {
      name: 'highlights',
      type: 'array',
      label: 'Key Highlights',
      fields: [
        {
          name: 'highlight',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        description: 'Key selling points and highlights',
        condition: (data) => data.readyForInvestment,
      },
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media', // assumes you have a 'media' collection
    },
    ...slugField(),
  ],
}
