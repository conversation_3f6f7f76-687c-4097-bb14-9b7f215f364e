'use client'

import type React from 'react'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Send } from 'lucide-react'
import { useCreateEnquiryMutation } from '@/lib/api/enquiryApi'

export default function ContactForm() {
  const [formData, setFormData] = useState({
    fullname: '',
    email: '',
    subject: '',
    message: '',
  })

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  const [createEnquiry, { isLoading, isSuccess, error }] = useCreateEnquiryMutation()

  // Load saved data on mount
  useEffect(() => {
    const savedData = localStorage.getItem('contact_form_data')
    if (savedData) {
      setFormData(JSON.parse(savedData))
    }
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await createEnquiry(formData).unwrap()
      localStorage.removeItem('contact_form_data')
      setFormData({ fullname: '', email: '', subject: '', message: '' })
    } catch (err) {
      console.error('Failed to submit enquiry:', err)
    }
  }

  const validateField = (name: string, value: string) => {
    const errors: Record<string, string> = {}

    if (name === 'fullname' && !value.trim()) {
      errors.fullname = 'Full name is required'
    }
    if (name === 'email' && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      errors.email = 'Please enter a valid email address'
    }
    if (name === 'message' && !value.trim()) {
      errors.message = 'Message is required'
    }

    setValidationErrors(prev => ({ ...prev, [name]: errors[name] || '' }))
  }

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target
    setFormData({ ...formData, [name]: value })
    validateField(name, value)
  }

  if (isSuccess) {
    return (
      <section className="max-w-4xl mx-auto">
        <div className="bg-white shadow-lg border border-gray-100 overflow-hidden">
          <div className="bg-[#7E2518] p-6 text-white text-center">
            <h2 className="text-2xl font-bold">Thank You!</h2>
            <p className="text-[#E8B32C] mt-2">Your message has been sent successfully</p>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="max-w-4xl mx-auto">
      <div className="bg-white shadow-lg border border-gray-100 overflow-hidden">
        <div className="bg-[#7E2518] p-6 text-white">
          <h2 className="text-2xl font-bold">Contact Form</h2>
          <p className="text-[#E8B32C] mt-2">We&apos;d love to hear from you</p>
        </div>

        <form onSubmit={handleSubmit} className="p-8 space-y-6">
          {/* Full Name */}
          <div>
            <label htmlFor="fullname" className="block text-sm font-medium text-[#7E2518] mb-2">
              Full Name *
            </label>
            <input
              type="text"
              id="fullname"
              name="fullname"
              required
              value={formData.fullname}
              onChange={handleChange}
              className="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
              placeholder="Enter your full name"
            />
            {validationErrors.fullname && (
              <p className="text-red-600 text-sm mt-1">{validationErrors.fullname}</p>
            )}
          </div>

          {/* Email Address */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-[#7E2518] mb-2">
              Email Address *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              required
              value={formData.email}
              onChange={handleChange}
              className="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
              placeholder="Enter your email address"
            />
          </div>

          {/* Subject */}
          <div>
            <label htmlFor="subject" className="block text-sm font-medium text-[#7E2518] mb-2">
              Subject
            </label>
            <select
              id="subject"
              name="subject"
              value={formData.subject}
              onChange={handleChange}
              className="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors"
            >
              <option value="">Select a subject</option>
              <option value="general">General Inquiry</option>
              <option value="exhibition">Exhibition Information</option>
              <option value="investment">Investment Opportunities</option>
              <option value="partnership">Partnership</option>
              <option value="media">Media Inquiry</option>
            </select>
          </div>

          {/* Message */}
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-[#7E2518] mb-2">
              Message *
            </label>
            <textarea
              id="message"
              name="message"
              required
              rows={6}
              value={formData.message}
              onChange={handleChange}
              className="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-[#7E2518] focus:border-transparent transition-colors resize-none"
              placeholder="Enter your message here..."
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm">
              Failed to send message. Please try again.
            </div>
          )}

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full bg-[#7E2518] hover:bg-[#6B1F14] text-white py-3 text-lg font-medium disabled:opacity-50"
          >
            <Send className="w-5 h-5 mr-2" />
            {isLoading ? 'Sending...' : 'Send Message'}
          </Button>
        </form>
      </div>
    </section>
  )
}
