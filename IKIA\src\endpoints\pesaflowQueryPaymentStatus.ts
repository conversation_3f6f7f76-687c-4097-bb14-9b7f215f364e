import type { PayloadRequest } from 'payload'
import { HashService } from '../utils/pesaflowHash'

// Types for the eCitizen payment status query endpoint
export const pesaflowQueryPaymentStatusEndpoint = async (
  req: PayloadRequest,
): Promise<Response> => {
  try {
    console.log('Payment status query endpoint called')
    console.log('Request query params:', req.query)

    // Extract query parameters (GET request) - only ref_no is required from frontend
    const { ref_no } = req.query as any

    // Validate required fields
    if (!ref_no) {
      return new Response(
        JSON.stringify({
          status: 400,
          description: 'Missing required query parameter',
          missingFields: ['ref_no'],
          message: 'Please provide the ref_no query parameter',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Use HashService to generate hash and query parameters
    let hashService: HashService
    try {
      hashService = HashService.getInstance()
    } catch (error) {
      console.error('HashService initialization failed:', error)
      return new Response(
        JSON.stringify({
          status: 500,
          description: 'Internal Error - Missing configuration',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get environment variables for hash generation
    const { PESAFLOW_REQUEST_SERVICE_ID: serviceID, PESAFLOW_BILL_DESC: billDesc } = process.env

    // Look up the actual invoice/payment details using ref_no
    let invoice
    try {
      // Try to find invoice by payment reference or invoice number
      const invoiceResult = await req.payload.find({
        collection: 'invoices',
        where: {
          or: [
            { payment_reference: { equals: ref_no } },
            { invoice_number: { equals: ref_no } },
            { id: { equals: ref_no } },
          ],
        },
        limit: 1,
        depth: 2, // Include related data
      })

      if (invoiceResult.docs.length === 0) {
        return new Response(
          JSON.stringify({
            status: 400,
            description: 'Invoice not found',
            message: `No invoice found with reference: ${ref_no}`,
          }),
          {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }

      invoice = invoiceResult.docs[0]
    } catch (error) {
      console.error('Error looking up invoice:', error)
      return new Response(
        JSON.stringify({
          status: 500,
          description: 'Database error',
          message: 'Could not retrieve invoice details',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Extract actual payment details from invoice
    const amountExpected = invoice.amount.toString()
    const currency = invoice.currency || 'KES'
    const clientName = invoice.customer_name || 'Unknown Customer'
    const clientIDNumber = invoice.customer_id_number || 'N/A'

    // Validate required fields
    if (!serviceID) {
      return new Response(
        JSON.stringify({
          status: 500,
          description: 'Missing service configuration',
          message: 'PESAFLOW_REQUEST_SERVICE_ID not configured',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Generate hash with actual invoice data
    const { hash, api_client_id } = hashService.generatePaymentStatusHash(
      ref_no,
      amountExpected,
      serviceID,
      clientIDNumber,
      currency,
      billDesc || 'Payment for services',
      clientName,
    )

    // Create query parameters
    const queryParams = new URLSearchParams({
      api_client_id,
      ref_no,
      secure_hash: hash,
    })

    // Get external service URL
    const { PESAFLOW_UAT_SERVER_URL: pesaflowServerUrl } = process.env

    // Option 1: Query external Pesaflow service
    if (pesaflowServerUrl) {
      try {
        const baseUrl = pesaflowServerUrl.endsWith('/')
          ? pesaflowServerUrl.slice(0, -1)
          : pesaflowServerUrl

        // Correct eCitizen API endpoint for payment status
        const externalUrl = `${baseUrl}/api/PaymentAPI/paymentstatus?${queryParams}`

        console.log('🔍 Making external payment status query:')
        console.log('   Environment Check:')
        console.log('     - PESAFLOW_UAT_SERVER_URL:', process.env.PESAFLOW_UAT_SERVER_URL)
        console.log('     - Base URL:', baseUrl)
        console.log('     - Full URL:', externalUrl)
        console.log('   Query Params:', queryParams.toString())
        console.log('   Parameters breakdown:')
        console.log('     - api_client_id:', queryParams.get('api_client_id'))
        console.log('     - ref_no:', queryParams.get('ref_no'))
        console.log('     - secure_hash:', queryParams.get('secure_hash'))

        console.log('   🚀 Making direct API request...')

        // Add timeout to prevent hanging
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout

        const response = await fetch(externalUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'IKIA-PaymentStatus/1.0',
          },
          signal: controller.signal,
        })

        clearTimeout(timeoutId)

        console.log('📡 External API Response:')
        console.log('   Status:', response.status, response.statusText)
        console.log('   Headers:', Object.fromEntries(response.headers.entries()))

        // Handle different response types
        const contentType = response.headers.get('content-type')
        let responseData: any

        if (contentType && contentType.includes('application/json')) {
          responseData = await response.json()
          console.log('   JSON Response:', JSON.stringify(responseData, null, 2))
        } else {
          const textResponse = await response.text()
          console.log('   Non-JSON Response:', textResponse)

          // Try to handle non-JSON responses gracefully
          responseData = {
            status: 'error',
            message: 'External service returned non-JSON response',
            raw_response: textResponse,
            response_status: response.status,
          }
        }

        // Return the response from external service
        return new Response(JSON.stringify(responseData), {
          status: response.ok ? 200 : response.status,
          headers: { 'Content-Type': 'application/json' },
        })
      } catch (error) {
        console.error('🚨 External status query service error:')
        console.error('   Error Type:', error?.constructor?.name || 'Unknown')
        console.error('   Error Message:', error instanceof Error ? error.message : 'Unknown error')
        console.error('   Error Stack:', error instanceof Error ? error.stack : 'No stack trace')

        // Log additional error details if available
        if (error instanceof TypeError && error.message.includes('fetch')) {
          console.error('   🌐 Network Error Details:')
          console.error('     - This is likely a network connectivity issue')
          console.error('     - Check if the UAT server URL is accessible')
          console.error('     - Verify DNS resolution and firewall settings')
          console.error('     - UAT Server URL:', process.env.PESAFLOW_UAT_SERVER_URL)
        }

        if (error instanceof Error && error.name === 'AbortError') {
          console.error('   ⏱️ Request Timeout:')
          console.error('     - Request took longer than 30 seconds')
          console.error('     - External service may be slow or unresponsive')
        }

        console.error('   📋 Fallback: Using local validation instead')
        // Fall through to local validation instead of returning error
      }
    }

    // Option 2: Local payment status query
    const paymentStatus = await queryPaymentStatusLocally(ref_no)

    return new Response(JSON.stringify(paymentStatus), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('Payment status query error:', error)
    return new Response(
      JSON.stringify({
        status: 500,
        description: 'Internal Error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}

// Helper function to query payment status locally
// This should be replaced with actual database logic
async function queryPaymentStatusLocally(ref_no: string) {
  // Simulate database lookup
  // In a real implementation, this would query your database for payment status

  console.log('Querying payment status for:', ref_no)

  // For demo purposes, return sample status based on ref_no pattern
  if (ref_no.startsWith('PAID')) {
    return {
      status: 'settled',
      ref_no: ref_no,
      name: 'John Doe',
      currency: 'KES',
      client_invoice_ref: `CLIENT-${ref_no}`,
      payment_date: '2024-01-01 12:30:01',
      amount_paid: '100.00',
      amount_expected: '100.00',
    }
  } else if (ref_no.startsWith('PARTIAL')) {
    return {
      status: 'partial',
      ref_no: ref_no,
      name: 'Jane Smith',
      currency: 'KES',
      client_invoice_ref: `CLIENT-${ref_no}`,
      payment_date: '2024-01-01 12:30:01',
      amount_paid: '50.00',
      amount_expected: '100.00',
    }
  } else {
    return {
      status: 'pending',
      ref_no: ref_no,
      name: 'Test User',
      currency: 'KES',
      client_invoice_ref: `CLIENT-${ref_no}`,
      payment_date: null,
      amount_paid: '0.00',
      amount_expected: '100.00',
    }
  }
}
