'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ExternalLink, Building, Search, Star, Award, Trophy, Medal, Crown } from 'lucide-react'
import { sponsors } from '../../partners-sponsors/data/partners'
import type { FilterType, TierFilter } from '../../partners-sponsors/types'
import Image from 'next/image'

const filterOptions = [
  { value: 'all' as FilterType, label: 'All Categories' },
  { value: 'financial' as FilterType, label: 'Financial' },
  { value: 'service' as FilterType, label: 'Service' },
  { value: 'media' as FilterType, label: 'Media' },
  { value: 'special' as FilterType, label: 'Special Events' },
]

const tierOptions = [
  { value: 'all' as TierFilter, label: 'All Tiers', icon: Star },
  { value: 'title' as TierFilter, label: 'Title', icon: Crown },
  { value: 'platinum' as TierFilter, label: 'Platinum', icon: Award },
  { value: 'gold' as TierFilter, label: 'Gold', icon: Trophy },
  { value: 'silver' as TierFilter, label: 'Silver', icon: Medal },
  { value: 'bronze' as TierFilter, label: 'Bronze', icon: Building },
]

const tierColors = {
  title: '#7E2518',
  platinum: '#81B1DB',
  gold: '#E8B32C',
  silver: '#9CA3AF',
  bronze: '#C86E36',
}

const categoryColors = {
  financial: '#159147',
  service: '#7E2518',
  media: '#E8B32C',
  special: '#81B1DB',
}

export default function SponsorsGridWithFilters() {
  const [categoryFilter, setCategoryFilter] = useState<FilterType>('all')
  const [tierFilter, setTierFilter] = useState<TierFilter>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [hoveredCard, setHoveredCard] = useState<string | null>(null)

  // Group sponsors by tier
  const sponsorsByTier = sponsors.reduce(
    (acc, sponsor) => {
      const tier = sponsor.tier || 'other'
      if (!acc[tier]) acc[tier] = []
      acc[tier].push(sponsor)
      return acc
    },
    {} as Record<string, typeof sponsors>,
  )

  const tierOrder = ['title', 'platinum', 'gold', 'silver', 'bronze', 'other']

  const filteredSponsors = sponsors.filter((sponsor) => {
    const matchesCategory = categoryFilter === 'all' || sponsor.category === categoryFilter
    const matchesTier = tierFilter === 'all' || sponsor.tier === tierFilter
    const matchesSearch =
      sponsor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      sponsor.description.toLowerCase().includes(searchQuery.toLowerCase())

    return matchesCategory && matchesTier && matchesSearch
  })

  return (
    <section className="py-16 lg:py-20 section-bg-primary">
      <div className="container mx-auto px-4">
        {/* Filters Section */}
        <div className="mb-12">
          {/* Search Bar */}
          <div className="max-w-md mx-auto mb-8">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                placeholder="Search sponsors..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-3 border-gray-200 focus:border-[#7E2518] focus:ring-[#7E2518] font-['Myriad_Pro',Arial,sans-serif]"
              />
            </div>
          </div>

          {/* Category Filters */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-[#7E2518] mb-4 text-center font-['Myriad_Pro',Arial,sans-serif]">
              Filter by Category
            </h3>
            <div className="flex flex-wrap justify-center gap-3">
              {filterOptions.map((option) => {
                const isActive = categoryFilter === option.value
                return (
                  <Button
                    key={option.value}
                    variant={isActive ? 'default' : 'outline'}
                    onClick={() => setCategoryFilter(option.value)}
                    className={`px-6 py-2 font-medium transition-all duration-300 main-shadow font-['Myriad_Pro',Arial,sans-serif] ${
                      isActive
                        ? 'bg-[#7E2518] text-white hover:bg-[#159147]'
                        : 'border-[#7E2518]/30 text-[#7E2518] hover:bg-[#7E2518]/5'
                    }`}
                  >
                    {option.label}
                  </Button>
                )
              })}
            </div>
          </div>

          {/* Tier Filters */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-[#7E2518] mb-4 text-center font-['Myriad_Pro',Arial,sans-serif]">
              Filter by Tier
            </h3>
            <div className="flex flex-wrap justify-center gap-3">
              {tierOptions.map((option) => {
                const IconComponent = option.icon
                const isActive = tierFilter === option.value
                return (
                  <Button
                    key={option.value}
                    variant={isActive ? 'default' : 'outline'}
                    onClick={() => setTierFilter(option.value)}
                    className={`flex items-center gap-2 px-6 py-2 font-medium transition-all duration-300 main-shadow font-['Myriad_Pro',Arial,sans-serif] ${
                      isActive
                        ? 'bg-[#7E2518] text-white hover:bg-[#159147]'
                        : 'border-[#7E2518]/30 text-[#7E2518] hover:bg-[#7E2518]/5'
                    }`}
                  >
                    <IconComponent className="w-4 h-4" />
                    {option.label}
                  </Button>
                )
              })}
            </div>
          </div>

          {/* Results Count */}
          <div className="text-center">
            <p className="text-gray-600 font-['Myriad_Pro',Arial,sans-serif]">
              Showing {filteredSponsors.length} of {sponsors.length} sponsors
            </p>
          </div>
        </div>

        {/* Sponsors Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredSponsors.map((sponsor) => (
            <Card
              key={sponsor.id}
              className="group bg-white border border-gray-200 main-shadow hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden"
              onMouseEnter={() => setHoveredCard(sponsor.id)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              {/* Background Overlay */}
              <div
                className={`absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
                style={{
                  backgroundColor: sponsor.tier
                    ? tierColors[sponsor.tier]
                    : categoryColors[sponsor.category],
                }}
              ></div>

              {/* Animated Border Glow */}
              {hoveredCard === sponsor.id && (
                <div className="absolute -inset-1 bg-[#7E2518]/20 blur-sm animate-pulse"></div>
              )}

              <CardContent className="relative p-8">
                {/* Header with Logo and Badges */}
                <div className="flex items-start justify-between mb-6">
                  <div className="relative w-16 h-16 bg-gray-100 flex items-center justify-center flex-shrink-0">
                    <Image
                      src={sponsor.logo}
                      alt={sponsor.name}
                      width={48}
                      height={48}
                      className="object-contain"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement
                        target.style.display = 'none'
                        target.nextElementSibling?.classList.remove('hidden')
                      }}
                    />
                    <div className="hidden w-12 h-12 bg-[#7E2518] flex items-center justify-center">
                      <Building className="w-6 h-6 text-white" />
                    </div>
                  </div>

                  <div className="flex flex-col gap-2">
                    {sponsor.tier && (
                      <Badge
                        className="text-white border-0 text-xs"
                        style={{ backgroundColor: tierColors[sponsor.tier] }}
                      >
                        {sponsor.tier}
                      </Badge>
                    )}
                    <Badge
                      variant="outline"
                      className="text-white border-0 text-xs"
                      style={{ backgroundColor: categoryColors[sponsor.category] }}
                    >
                      {sponsor.category}
                    </Badge>
                  </div>
                </div>

                {/* Sponsor Name */}
                <h3 className="text-xl font-bold text-[#7E2518] mb-3 group-hover:text-[#159147] transition-all duration-500 font-['Myriad_Pro',Arial,sans-serif]">
                  {sponsor.name}
                </h3>

                {/* Description */}
                <p className="text-gray-600 leading-relaxed mb-6 text-sm font-['Myriad_Pro',Arial,sans-serif]">
                  {sponsor.description}
                </p>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  {sponsor.website && (
                    <Button
                      asChild
                      variant="outline"
                      size="sm"
                      className="flex-1 border-[#7E2518]/30 text-[#7E2518] hover:bg-[#7E2518] hover:text-white transition-all duration-300 main-shadow font-['Myriad_Pro',Arial,sans-serif]"
                    >
                      <a
                        href={sponsor.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center gap-2"
                      >
                        <ExternalLink className="w-4 h-4" />
                        Visit
                      </a>
                    </Button>
                  )}

                  {sponsor.hasDetailsPage && (
                    <Button
                      asChild
                      size="sm"
                      className="flex-1 text-white border-0 transition-all duration-300 main-shadow font-['Myriad_Pro',Arial,sans-serif]"
                      style={{
                        backgroundColor: sponsor.tier
                          ? tierColors[sponsor.tier]
                          : categoryColors[sponsor.category],
                      }}
                    >
                      <a
                        href={`/sponsors/${sponsor.id}`}
                        className="flex items-center justify-center gap-2"
                      >
                        Learn More
                      </a>
                    </Button>
                  )}
                </div>

                {/* Progress Bar Animation */}
                <div className="mt-6 h-1 bg-gray-100 overflow-hidden">
                  <div
                    className="h-full transform -translate-x-full group-hover:translate-x-0 transition-transform duration-1000 ease-out"
                    style={{
                      backgroundColor: sponsor.tier
                        ? tierColors[sponsor.tier]
                        : categoryColors[sponsor.category],
                    }}
                  ></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredSponsors.length === 0 && (
          <div className="text-center py-16">
            <Building className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-gray-500 mb-2 font-['Myriad_Pro',Arial,sans-serif]">
              No sponsors found
            </h3>
            <p className="text-gray-400 font-['Myriad_Pro',Arial,sans-serif]">
              Try adjusting your filters or search query
            </p>
          </div>
        )}
      </div>
    </section>
  )
}
