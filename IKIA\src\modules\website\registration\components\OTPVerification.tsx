"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Shield, Mail, Phone, Clock, CheckCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface OTPVerificationProps {
  email: string
  phone: string
  onVerificationComplete: (verified: boolean) => void
  className?: string
}

export function OTPVerification({ 
  email, 
  phone, 
  onVerificationComplete, 
  className = "" 
}: OTPVerificationProps) {
  const [selectedMethod, setSelectedMethod] = useState<'email' | 'phone'>('email')
  const [otp, setOtp] = useState('')
  const [isOtpSent, setIsOtpSent] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [isVerified, setIsVerified] = useState(false)
  const [error, setError] = useState('')
  const [countdown, setCountdown] = useState(0)
  const [mockOtp, setMockOtp] = useState('')

  // Countdown timer for resend
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [countdown])

  const generateMockOtp = () => {
    return Math.floor(100000 + Math.random() * 900000).toString()
  }

  const sendOtp = async () => {
    setError('')
    setIsOtpSent(false)
    
    try {
      // Generate mock OTP for development
      const generatedOtp = generateMockOtp()
      setMockOtp(generatedOtp)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // In production, this would make an actual API call
      console.log(`Sending OTP ${generatedOtp} to ${selectedMethod === 'email' ? email : phone}`)
      
      setIsOtpSent(true)
      setCountdown(60) // 60 second countdown
      setOtp('')
    } catch (err) {
      setError('Failed to send verification code. Please try again.')
    }
  }

  const verifyOtp = async () => {
    if (otp.length !== 6) {
      setError('Please enter a 6-digit code')
      return
    }

    setIsVerifying(true)
    setError('')

    try {
      // Simulate API verification
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // In development, check against mock OTP
      if (process.env.NODE_ENV === 'development' && otp === mockOtp) {
        setIsVerified(true)
        onVerificationComplete(true)
      } else if (process.env.NODE_ENV === 'production') {
        // In production, this would verify against the backend
        // For now, simulate success
        setIsVerified(true)
        onVerificationComplete(true)
      } else {
        setError('Invalid verification code. Please try again.')
      }
    } catch (err) {
      setError('Verification failed. Please try again.')
    } finally {
      setIsVerifying(false)
    }
  }

  const resendOtp = () => {
    sendOtp()
  }

  if (isVerified) {
    return (
      <Card className={cn("border-green-200 bg-green-50", className)}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center space-x-3 text-green-700">
            <CheckCircle className="w-6 h-6" />
            <span className="font-medium">Verification Complete</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Shield className="w-5 h-5" />
          <span>Identity Verification</span>
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Verify your identity to secure your registration
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isOtpSent ? (
          <>
            {/* Method Selection */}
            <div className="space-y-3">
              <Label>Choose verification method</Label>
              <Select value={selectedMethod} onValueChange={(value: 'email' | 'phone') => setSelectedMethod(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="email">
                    <div className="flex items-center space-x-2">
                      <Mail className="w-4 h-4" />
                      <span>Email: {email}</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="phone">
                    <div className="flex items-center space-x-2">
                      <Phone className="w-4 h-4" />
                      <span>Phone: {phone}</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Send OTP Button */}
            <Button onClick={sendOtp} className="w-full">
              Send Verification Code
            </Button>
          </>
        ) : (
          <>
            {/* OTP Input */}
            <div className="space-y-3">
              <Label htmlFor="otp">Enter Verification Code</Label>
              <p className="text-sm text-muted-foreground">
                We sent a 6-digit code to your {selectedMethod === 'email' ? 'email' : 'phone number'}.
              </p>
              <Input
                id="otp"
                type="text"
                placeholder="Enter 6-digit code"
                value={otp}
                onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
                className="text-center text-lg tracking-widest"
                maxLength={6}
              />
              
              {/* Development helper */}
              {process.env.NODE_ENV === 'development' && mockOtp && (
                <Alert>
                  <AlertDescription>
                    <strong>Development Mode:</strong> Use code: {mockOtp}
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Verify Button */}
            <Button 
              onClick={verifyOtp} 
              disabled={otp.length !== 6 || isVerifying}
              className="w-full"
            >
              {isVerifying ? 'Verifying...' : 'Verify Code'}
            </Button>

            {/* Resend Option */}
            <div className="text-center">
              {countdown > 0 ? (
                <p className="text-sm text-muted-foreground flex items-center justify-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>Resend code in {countdown}s</span>
                </p>
              ) : (
                <Button variant="ghost" onClick={resendOtp} className="text-sm">
                  Resend verification code
                </Button>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
