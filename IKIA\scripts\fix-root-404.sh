#!/bin/bash

# =============================================================================
# Root Path 404 Fix Script for Next.js on cPanel
# =============================================================================
# This script specifically addresses the issue where the root path (/) returns
# 404 but other pages work fine on cPanel hosting with PM2.

set -e

# Configuration
APP_NAME="ikia-conference"
APP_DIR="/home/<USER>/public_html"
LOG_FILE="$APP_DIR/logs/root-404-fix.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Create logs directory
mkdir -p "$APP_DIR/logs"

log_info "Starting root path 404 fix for $APP_NAME..."

# Test root path specifically
test_root_path() {
    log_info "Testing root path access..."
    
    # Test local connection to root
    if curl -f -s -m 10 http://127.0.0.1:3000/ > /dev/null 2>&1; then
        log_success "Local root path accessible"
    else
        log_error "Local root path not accessible"
        log_info "Testing with verbose output:"
        curl -v http://127.0.0.1:3000/ 2>&1 | head -20 | tee -a "$LOG_FILE"
        return 1
    fi
    
    # Test external connection to root
    if curl -f -s -m 10 https://ikiaconference.or.ke/ > /dev/null 2>&1; then
        log_success "External root path accessible"
    else
        log_error "External root path not accessible"
        log_info "Testing with verbose output:"
        curl -v https://ikiaconference.or.ke/ 2>&1 | head -20 | tee -a "$LOG_FILE"
        return 1
    fi
}

# Test other pages to confirm they work
test_other_pages() {
    log_info "Testing other pages to confirm they work..."
    
    local test_pages=("/about" "/contact" "/events" "/speakers" "/api/health-check")
    
    for page in "${test_pages[@]}"; do
        if curl -f -s -m 10 "https://ikiaconference.or.ke$page" > /dev/null 2>&1; then
            log_success "Page $page is accessible"
        else
            log_warning "Page $page is not accessible"
        fi
    done
}

# Check .htaccess configuration specifically for root path
check_htaccess_root() {
    log_info "Checking .htaccess configuration for root path handling..."
    
    if [ -f "$APP_DIR/.htaccess" ]; then
        log_info "Current .htaccess content:"
        cat "$APP_DIR/.htaccess" | tee -a "$LOG_FILE"
        
        # Check if root path rule exists
        if grep -q "RewriteRule \^\$ " "$APP_DIR/.htaccess"; then
            log_success "Root path specific rule found"
        else
            log_warning "No specific root path rule found"
            return 1
        fi
    else
        log_error ".htaccess file not found"
        return 1
    fi
}

# Fix .htaccess for root path
fix_htaccess_root() {
    log_info "Applying .htaccess fix for root path..."
    
    # Backup current .htaccess
    if [ -f "$APP_DIR/.htaccess" ]; then
        cp "$APP_DIR/.htaccess" "$APP_DIR/.htaccess.backup.$(date +%Y%m%d_%H%M%S)"
        log_info "Backed up current .htaccess"
    fi
    
    # Create improved .htaccess with explicit root path handling
    cat > "$APP_DIR/.htaccess" << 'EOF'
# Test if .htaccess is working
RewriteEngine On

# Exclude cPanel system files from proxying
RewriteCond %{REQUEST_URI} !^/cgi-sys/
RewriteCond %{REQUEST_URI} !^/cpanel/
RewriteCond %{REQUEST_URI} !^/.well-known/

# Special handling for root path - explicit proxy to avoid issues
RewriteRule ^$ http://127.0.0.1:3000/ [P,L]

# Proxy all other requests to Node.js app
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ http://127.0.0.1:3000/$1 [P,L]

# Alternative: Direct proxy (uncomment if above doesn't work)
# ProxyPreserveHost On
# ProxyPass /cgi-sys !
# ProxyPass / http://127.0.0.1:3000/
# ProxyPassReverse / http://127.0.0.1:3000/

# Static file handling
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 year"
    Header append Cache-Control "public, immutable"
</FilesMatch>

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>
EOF
    
    log_success "Updated .htaccess with explicit root path handling"
}

# Check Next.js routing configuration
check_nextjs_routing() {
    log_info "Checking Next.js routing configuration..."
    
    # Check if root page exists
    if [ -f "$APP_DIR/src/app/(frontend)/page.tsx" ]; then
        log_success "Root page component exists"
        log_info "Root page content preview:"
        head -10 "$APP_DIR/src/app/(frontend)/page.tsx" | tee -a "$LOG_FILE"
    else
        log_error "Root page component not found"
        return 1
    fi
    
    # Check layout
    if [ -f "$APP_DIR/src/app/(frontend)/layout.tsx" ]; then
        log_success "Frontend layout exists"
    else
        log_error "Frontend layout not found"
        return 1
    fi
}

# Restart PM2 with specific logging
restart_pm2_with_logging() {
    log_info "Restarting PM2 with enhanced logging..."
    
    cd "$APP_DIR"
    
    # Stop current process
    if pm2 describe "$APP_NAME" > /dev/null 2>&1; then
        pm2 stop "$APP_NAME"
        pm2 delete "$APP_NAME"
    fi
    
    # Start with production environment
    pm2 start ecosystem.config.cjs --env production
    
    # Wait for startup
    sleep 5
    
    # Check status
    pm2 status | tee -a "$LOG_FILE"
    
    # Show recent logs
    log_info "Recent PM2 logs:"
    pm2 logs "$APP_NAME" --lines 20 | tee -a "$LOG_FILE"
}

# Comprehensive verification
verify_root_fix() {
    log_info "Verifying root path fix..."
    
    # Wait a moment for everything to settle
    sleep 3
    
    # Test local root path
    if curl -f -s -m 10 http://127.0.0.1:3000/ > /dev/null 2>&1; then
        log_success "Local root path working"
    else
        log_error "Local root path still not working"
        return 1
    fi
    
    # Test external root path
    if curl -f -s -m 10 https://ikiaconference.or.ke/ > /dev/null 2>&1; then
        log_success "External root path working"
    else
        log_error "External root path still not working"
        return 1
    fi
    
    # Test that other pages still work
    if curl -f -s -m 10 https://ikiaconference.or.ke/about > /dev/null 2>&1; then
        log_success "Other pages still working"
    else
        log_warning "Other pages may have been affected"
    fi
    
    log_success "Root path 404 fix verification completed successfully!"
}

# Main execution
main() {
    log_info "=== Root Path 404 Fix Script Started ==="
    
    # Run diagnostics
    test_root_path || log_warning "Root path test failed"
    test_other_pages
    check_htaccess_root || log_warning "htaccess root check failed"
    check_nextjs_routing || log_warning "Next.js routing check failed"
    
    # Ask user if they want to apply fixes
    echo ""
    read -p "Do you want to apply the root path fix? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        fix_htaccess_root
        restart_pm2_with_logging
        verify_root_fix
    fi
    
    log_info "=== Script completed. Check logs at: $LOG_FILE ==="
}

# Script execution
case "${1:-main}" in
    "main"|"")
        main
        ;;
    "test")
        test_root_path
        test_other_pages
        ;;
    "fix")
        fix_htaccess_root
        restart_pm2_with_logging
        verify_root_fix
        ;;
    "verify")
        verify_root_fix
        ;;
    *)
        echo "Usage: $0 [main|test|fix|verify]"
        echo "  main   - Run full diagnostic and optional fix (default)"
        echo "  test   - Run tests only"
        echo "  fix    - Apply fixes only"
        echo "  verify - Verify fix only"
        ;;
esac
