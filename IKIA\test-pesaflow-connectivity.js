#!/usr/bin/env node

/**
 * Test script to check Pesaflow API connectivity
 */

async function testPesaflowConnectivity() {
  console.log('🌐 PESAFLOW CONNECTIVITY TEST')
  console.log('=============================')

  const pesaflowUrls = [
    'https://test.pesaflow.com',
    'https://test.pesaflow.com/api/PaymentAPI/checkout',
    'https://uat.ecitizen.go.ke',
    'https://uat.ecitizen.go.ke/api/PaymentAPI/checkout'
  ]

  for (const url of pesaflowUrls) {
    console.log(`\n🔍 Testing: ${url}`)
    
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout
      
      const startTime = Date.now()
      const response = await fetch(url, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'User-Agent': 'IKIA-Connectivity-Test/1.0'
        }
      })
      
      clearTimeout(timeoutId)
      const responseTime = Date.now() - startTime
      
      console.log(`✅ Response: ${response.status} ${response.statusText} (${responseTime}ms)`)
      console.log(`   Content-Type: ${response.headers.get('content-type') || 'unknown'}`)
      
      // Try to read a small portion of the response
      try {
        const text = await response.text()
        const preview = text.substring(0, 200).replace(/\s+/g, ' ').trim()
        console.log(`   Preview: ${preview}${text.length > 200 ? '...' : ''}`)
      } catch (e) {
        console.log(`   Could not read response body`)
      }
      
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log(`❌ Timeout after 10 seconds`)
      } else {
        console.log(`❌ Error: ${error.message}`)
      }
    }
  }

  console.log('\n📋 RECOMMENDATIONS')
  console.log('==================')
  console.log('1. If all URLs timeout, check your internet connection')
  console.log('2. If only Pesaflow URLs timeout, the service may be down')
  console.log('3. If you get 404 errors, the endpoint might have changed')
  console.log('4. If you get 403/401 errors, authentication might be required')
  console.log('5. Try the alternative URL in your .env file if needed')
}

async function testPesaflowPost() {
  console.log('\n🧪 PESAFLOW POST TEST')
  console.log('=====================')
  
  const testUrl = 'https://test.pesaflow.com/api/PaymentAPI/checkout'
  const testPayload = {
    apiClientID: '18',
    serviceID: '233285',
    billRefNumber: 'TEST-' + Date.now(),
    billDesc: 'Test payment',
    clientMSISDN: '254700000000',
    clientIDNumber: '12345678',
    clientName: 'Test User',
    clientEmail: '<EMAIL>',
    notificationURL: 'https://example.com/notify',
    callBackURLOnSuccess: 'https://example.com/success',
    currency: 'KES',
    amountExpected: '100',
    format: 'html',
    sendSTK: 'true',
    secureHash: 'test-hash'
  }

  console.log(`📤 Testing POST to: ${testUrl}`)
  console.log(`📦 Payload size: ${JSON.stringify(testPayload).length} bytes`)

  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 15000) // 15 second timeout
    
    const startTime = Date.now()
    const response = await fetch(testUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/html, */*',
        'User-Agent': 'IKIA-Test/1.0'
      },
      body: JSON.stringify(testPayload),
      signal: controller.signal,
    })
    
    clearTimeout(timeoutId)
    const responseTime = Date.now() - startTime
    
    console.log(`📡 Response: ${response.status} ${response.statusText} (${responseTime}ms)`)
    console.log(`   Content-Type: ${response.headers.get('content-type') || 'unknown'}`)
    
    if (response.status === 200 || response.status === 302) {
      console.log('✅ POST request successful!')
      
      // Check for redirect
      const location = response.headers.get('location')
      if (location) {
        console.log(`🔄 Redirect to: ${location}`)
      }
      
      try {
        const responseText = await response.text()
        if (responseText.length < 1000) {
          console.log(`📄 Response: ${responseText}`)
        } else {
          console.log(`📄 Response preview: ${responseText.substring(0, 500)}...`)
        }
      } catch (e) {
        console.log('Could not read response body')
      }
      
    } else {
      console.log(`⚠️ Unexpected status code: ${response.status}`)
      try {
        const errorText = await response.text()
        console.log(`Error response: ${errorText.substring(0, 500)}`)
      } catch (e) {
        console.log('Could not read error response')
      }
    }
    
  } catch (error) {
    if (error.name === 'AbortError') {
      console.log(`❌ POST request timed out after 15 seconds`)
      console.log('   This suggests the Pesaflow API is slow or unresponsive')
    } else {
      console.log(`❌ POST request failed: ${error.message}`)
    }
  }
}

// Run tests
async function runTests() {
  await testPesaflowConnectivity()
  await testPesaflowPost()
  
  console.log('\n🎯 NEXT STEPS')
  console.log('=============')
  console.log('1. If connectivity tests pass, the issue might be with payload format')
  console.log('2. If POST test times out, consider using fallback URL generation')
  console.log('3. Check Pesaflow documentation for any API changes')
  console.log('4. Contact Pesaflow support if issues persist')
}

runTests().catch(console.error)
