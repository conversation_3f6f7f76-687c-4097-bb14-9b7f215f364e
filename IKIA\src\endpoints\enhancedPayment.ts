import type { PayloadRequest } from 'payload'
import { HashService } from '../utils/pesaflowHash'
import { EmailService } from '../utils/emailService'

interface EnhancedPaymentRequest {
  // Payment can be initiated by invoice reference (guest) or by authenticated user
  paymentMethod: 'invoice_reference' | 'authenticated_user'

  // For invoice reference payments (guest payments)
  invoiceReference?: string

  // For authenticated user payments
  packageId?: string

  // Customer information (required for guest payments, optional for authenticated)
  customerInfo?: {
    name: string
    email: string
    phone: string
    idNumber: string
  }

  // Payment gateway options
  currency?: string
  sendSTK?: string
  format?: string
}

export const enhancedPaymentEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('Enhanced payment endpoint called')
    console.log('Request body:', req.body)
    console.log('User authenticated:', !!req.user)

    const {
      paymentMethod,
      invoiceReference,
      packageId,
      customerInfo,
      currency = 'KES',
      sendSTK = 'yes',
      format = 'json',
    } = req.body as EnhancedPaymentRequest

    // Validate payment method
    if (!paymentMethod || !['invoice_reference', 'authenticated_user'].includes(paymentMethod)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'INVALID_PAYMENT_METHOD',
            message: 'Payment method must be either "invoice_reference" or "authenticated_user"',
          },
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    let invoice
    let user
    let servicePackage

    if (paymentMethod === 'invoice_reference') {
      // Guest payment using invoice reference
      if (!invoiceReference) {
        return new Response(
          JSON.stringify({
            success: false,
            error: {
              code: 'MISSING_INVOICE_REFERENCE',
              message: 'Invoice reference is required for guest payments',
            },
          }),
          {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }

      // Find invoice by payment reference
      const invoices = await req.payload.find({
        collection: 'invoices',
        where: {
          payment_reference: {
            equals: invoiceReference,
          },
        },
      })

      if (invoices.docs.length === 0) {
        return new Response(
          JSON.stringify({
            success: false,
            error: {
              code: 'INVOICE_NOT_FOUND',
              message: 'Invoice not found with the provided reference',
            },
          }),
          {
            status: 404,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }

      invoice = invoices.docs[0]

      // Get associated user and package
      user = await req.payload.findByID({
        collection: 'users',
        id: invoice.user,
      })

      servicePackage = await req.payload.findByID({
        collection: 'service-packages',
        id: invoice.package,
      })
    } else {
      // Authenticated user payment
      if (!req.user) {
        return new Response(
          JSON.stringify({
            success: false,
            error: {
              code: 'AUTHENTICATION_REQUIRED',
              message: 'User must be authenticated for this payment method',
            },
          }),
          {
            status: 401,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }

      if (!packageId) {
        return new Response(
          JSON.stringify({
            success: false,
            error: {
              code: 'MISSING_PACKAGE_ID',
              message: 'Package ID is required for authenticated user payments',
            },
          }),
          {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }

      user = req.user

      // Get the selected package
      servicePackage = await req.payload.findByID({
        collection: 'delegatepackages',
        id: packageId,
      })

      if (!servicePackage) {
        return new Response(
          JSON.stringify({
            success: false,
            error: {
              code: 'PACKAGE_NOT_FOUND',
              message: 'Selected package not found',
            },
          }),
          {
            status: 404,
            headers: { 'Content-Type': 'application/json' },
          },
        )
      }

      // Create new invoice for authenticated user
      invoice = await req.payload.create({
        collection: 'invoices',
        data: {
          user: user.id,
          package: packageId,
          amount: servicePackage.price,
          currency: servicePackage.currency,
          status: 'pending',
          customer_info: {
            name: user.name || customerInfo?.name,
            email: user.email,
            phone: user.phone_number || customerInfo?.phone,
            id_number: user.id_number || customerInfo?.idNumber,
          },
        },
      })
    }

    // Validate invoice status
    if (invoice.status === 'settled') {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'INVOICE_ALREADY_PAID',
            message: 'This invoice has already been paid',
          },
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    if (invoice.status === 'expired' || invoice.status === 'cancelled') {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'INVOICE_NOT_PAYABLE',
            message: `Invoice is ${invoice.status} and cannot be paid`,
          },
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Prepare customer information for Pesaflow
    const clientName = invoice.customer_info?.name || user?.name || customerInfo?.name
    const clientEmail = invoice.customer_info?.email || user?.email || customerInfo?.email
    const clientMSISDN = invoice.customer_info?.phone || user?.phone_number || customerInfo?.phone
    const clientIDNumber =
      invoice.customer_info?.id_number || user?.id_number || customerInfo?.idNumber

    // Validate required customer information
    const missingCustomerInfo = [
      !clientName && 'name',
      !clientEmail && 'email',
      !clientMSISDN && 'phone',
      !clientIDNumber && 'ID number',
    ].filter(Boolean)

    if (missingCustomerInfo.length > 0) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'MISSING_CUSTOMER_INFO',
            message: `Missing customer information: ${missingCustomerInfo.join(', ')}`,
            details: { missingFields: missingCustomerInfo },
          },
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Get environment variables for Pesaflow
    const {
      PESAFLOW_REQUEST_SERVICE_ID: serviceID,
      PESAFLOW_BILL_DESC: billDesc,
      PESAFLOW_NOTIFICATION_URL: notificationURL,
      PESAFLOW_PICTURE_URL: pictureURL,
      PESAFLOW_CALLBACK_SUCCESS_URL: callBackURLOnSuccess,
      PESAFLOW_UAT_SERVER_URL: pesaflowServerUrl,
    } = process.env

    // Check for required environment variables
    const missingConfig = [
      !serviceID && 'PESAFLOW_REQUEST_SERVICE_ID',
      !billDesc && 'PESAFLOW_BILL_DESC',
      !pesaflowServerUrl && 'PESAFLOW_UAT_SERVER_URL',
    ].filter(Boolean)

    if (missingConfig.length > 0) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'MISSING_PESAFLOW_CONFIG',
            message: `Missing Pesaflow configuration: ${missingConfig.join(', ')}`,
          },
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Generate unique bill reference if not exists
    let billRefNumber = invoice.pesaflow_data?.bill_ref_number
    if (!billRefNumber) {
      billRefNumber = `bill-${invoice.payment_reference}-${Date.now()}`
    }

    // Initialize HashService
    let hashService: HashService
    try {
      hashService = HashService.getInstance()
    } catch (error) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'HASH_SERVICE_ERROR',
            message: 'Payment processing service unavailable',
          },
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Generate hash for Pesaflow with simplified format: api_client_id + ref_no + amount + secret
    const { hash: secureHash, api_client_id: apiClientID } = hashService.generateCheckoutHash(
      billRefNumber,
      invoice.amount.toString(),
    )

    // Prepare payload for Pesaflow
    const pesaflowPayload = {
      apiClientID,
      serviceID,
      billRefNumber,
      billDesc,
      clientMSISDN,
      clientIDNumber,
      clientName,
      clientEmail,
      notificationURL,
      pictureURL,
      callBackURLOnSuccess,
      currency: invoice.currency,
      amountExpected: invoice.amount.toString(),
      format,
      sendSTK,
      secureHash,
    }

    // Call Pesaflow checkout API
    try {
      const baseUrl = pesaflowServerUrl?.endsWith('/')
        ? pesaflowServerUrl.slice(0, -1)
        : pesaflowServerUrl || ''

      // Add timeout for the checkout request
      const CHECKOUT_TIMEOUT = 45000 // 45 seconds
      const controller = new AbortController()
      const timeoutId = setTimeout(() => {
        controller.abort()
      }, CHECKOUT_TIMEOUT)

      console.log(`🚀 Enhanced payment checkout request (timeout: ${CHECKOUT_TIMEOUT / 1000}s)...`)
      const startTime = Date.now()

      const response = await fetch(`${baseUrl}/api/PaymentAPI/checkout`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(pesaflowPayload),
        signal: controller.signal,
      })

      clearTimeout(timeoutId)
      const responseTime = Date.now() - startTime
      console.log(`📡 Enhanced payment response received in ${responseTime}ms`)

      // Handle different response types
      const contentType = response.headers.get('content-type')
      let pesaflowResponse: any

      if (!contentType?.includes('application/json')) {
        // Handle non-JSON responses (redirects, HTML, etc.)
        if (response.ok || (response.status >= 300 && response.status < 400)) {
          const location = response.headers.get('location')
          if (location) {
            // Direct redirect
            pesaflowResponse = {
              success: true,
              redirect_url: location,
              message: 'Redirect to payment gateway',
            }
          } else {
            // HTML or other content
            const text = await response.text()
            const redirectMatch = text.match(
              /(?:window\.location|location\.href)\s*=\s*["']([^"']+)["']/i,
            )
            const formActionMatch = text.match(/<form[^>]+action\s*=\s*["']([^"']+)["']/i)
            const reloadMatch = text.match(/location\.reload\(\)/i)
            const metaRefreshMatch = text.match(
              /<meta[^>]+http-equiv\s*=\s*["']refresh["'][^>]+content\s*=\s*["'][^"']*url=([^"']+)["']/i,
            )

            if (redirectMatch) {
              pesaflowResponse = {
                success: true,
                redirect_url: redirectMatch[1],
                message: 'Redirect to payment gateway',
              }
            } else if (formActionMatch) {
              pesaflowResponse = {
                success: true,
                redirect_url: formActionMatch[1],
                html_content: text,
                message: 'Form submission required',
              }
            } else if (metaRefreshMatch) {
              pesaflowResponse = {
                success: true,
                redirect_url: metaRefreshMatch[1],
                message: 'Redirect to payment gateway',
              }
            } else if (reloadMatch) {
              pesaflowResponse = {
                success: true,
                reload_required: true,
                html_content: text,
                message: 'Payment gateway requires page reload',
              }
            } else {
              pesaflowResponse = {
                success: true,
                html_content: text,
                message: 'Payment gateway response',
              }
            }
          }
        } else {
          // Non-JSON error
          const text = await response.text()
          pesaflowResponse = {
            success: false,
            error: 'Payment gateway error',
            message: text || 'Unknown error from payment gateway',
          }
        }
      } else {
        // JSON response
        pesaflowResponse = await response.json()
      }

      // Update invoice with Pesaflow data
      await req.payload.update({
        collection: 'invoices',
        id: invoice.id,
        data: {
          status: 'processing',
          pesaflow_data: {
            bill_ref_number: billRefNumber,
            checkout_url:
              pesaflowResponse.invoice_link ||
              pesaflowResponse.checkout_url ||
              pesaflowResponse.redirectUrl,
            gateway_response: pesaflowResponse,
            last_gateway_sync: new Date(),
          },
        },
      })

      // Update user package status if this is a package payment
      if (
        paymentMethod === 'authenticated_user' ||
        invoice.registration_context?.is_registration_payment
      ) {
        await req.payload.update({
          collection: 'users',
          id: user.id,
          data: {
            package_status: 'payment_pending',
          },
        })
      }

      return new Response(
        JSON.stringify({
          success: true,
          data: {
            invoiceId: invoice.id,
            paymentReference: invoice.payment_reference,
            checkoutUrl:
              pesaflowResponse.invoice_link ||
              pesaflowResponse.checkout_url ||
              pesaflowResponse.redirectUrl ||
              pesaflowResponse.redirect_url,
            amount: invoice.amount,
            currency: invoice.currency,
            packageName: servicePackage?.name,
            billRefNumber,
            // Include additional fields for non-JSON responses
            ...(pesaflowResponse.html_content && { htmlContent: pesaflowResponse.html_content }),
            ...(pesaflowResponse.message && { gatewayMessage: pesaflowResponse.message }),
            ...(pesaflowResponse.reload_required && {
              reloadRequired: pesaflowResponse.reload_required,
            }),
          },
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    } catch (error) {
      console.error('🚨 Pesaflow API error:', error)

      let errorMessage = 'Payment processing failed. Please try again.'
      let errorCode = 'PAYMENT_GATEWAY_ERROR'
      let statusCode = 500

      // Handle timeout specifically
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('⏱️ Enhanced payment request timed out after 45 seconds')
        errorMessage =
          'The payment gateway is taking too long to respond. This may be due to high traffic or server issues. Please try again in a few minutes.'
        errorCode = 'PAYMENT_TIMEOUT'
        statusCode = 408
      } else if (error instanceof TypeError && error.message.includes('fetch')) {
        console.error('🌐 Network connectivity error:', error.message)
        errorMessage =
          'Unable to connect to the payment gateway. The service may be temporarily unavailable.'
        errorCode = 'GATEWAY_UNAVAILABLE'
        statusCode = 503
      }

      // Update invoice status to failed
      await req.payload.update({
        collection: 'invoices',
        id: invoice.id,
        data: {
          status: 'failed',
          pesaflow_data: {
            ...invoice.pesaflow_data,
            gateway_response: {
              error: error instanceof Error ? error.message : 'Unknown error',
              error_code: errorCode,
              timestamp: new Date().toISOString(),
            },
            last_gateway_sync: new Date().toISOString(),
          },
        },
      })

      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: errorCode,
            message: errorMessage,
            requestId: invoice.payment_reference,
            ...(errorCode === 'PAYMENT_TIMEOUT' && {
              timeout: '45 seconds',
              suggestions: [
                'Wait a few minutes and try again',
                'Check your internet connection',
                'Contact support if the problem persists',
              ],
            }),
          },
        }),
        {
          status: statusCode,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }
  } catch (error) {
    console.error('Enhanced payment endpoint error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
        },
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}
