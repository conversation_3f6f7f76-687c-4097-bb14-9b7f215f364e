'use client'

import Image from 'next/image'
import Link from 'next/link'
import { ArrowLeft, Calendar, Loader2, TriangleAlert } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { notFound, useParams } from 'next/navigation'
import { useGetNewsMediaQuery } from '@/lib/api/newsMediaApi'
import RichText from '@/components/RichText'

export default function DetailsPage() {
  const params = useParams()
  const category = params?.category as string
  const slug = params?.slug as string

  const { data, isLoading, error } = useGetNewsMediaQuery({
    where: {
      slug,
    },
  })

  const dataa = data?.docs[0]

  if (!category || !slug) {
    // This will trigger Next.js to show the 404 page
    return notFound()
  }

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen text-black">
        <Loader2 className="h-16 w-16 animate-spin text-palette-green" />
        <p className="mt-4 text-lg font-medium">Loading. Please wait...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen text-gray-700 p-4 text-center">
        <TriangleAlert className="h-24 w-24 text-palette-red mb-6 animate-pulse" />
        <h1 className="text-4xl font-bold mb-4">Something went wrong!</h1>
        <p className="text-lg text-gray-300 max-w-md mb-8">
          We&apos;re sorry, but an unexpected error occurred. Please try again later or go back to
          the homepage.
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <Button
            onClick={() => {}}
            className="font-semibold text-sm transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02] transform hover:-translate-y-0.5 rounded-lg px-8 py-4 bg-palette-green hover:bg-palette-green/90 text-black"
          >
            Try Again
          </Button>
          <Button
            asChild
            className="font-semibold text-sm transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02] transform hover:-translate-y-0.5 rounded-lg px-8 py-4 bg-transparent border-2 border-palette-blue text-palette-blue hover:bg-palette-blue hover:text-black"
          >
            <Link href="/">Go Home</Link>
          </Button>
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Article Not Found</h1>
          <Link href="/news-media" className="text-blue-600 hover:underline">
            Back to News & Media
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Link
          href="/news-media"
          className="inline-flex items-center text-red-600 hover:text-red-700 mb-6"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to News & Media
        </Link>

        <div className="mb-8">
          <Badge className="bg-red-600 text-white mb-4">{dataa.category}</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 leading-tight">
            {dataa.title}
          </h1>

          <div className="flex items-center space-x-4 border-b border-gray-200 pb-6">
            <Image
              src={dataa.authorImage || '/placeholder.svg'}
              alt={dataa.author}
              width={50}
              height={50}
              className="rounded-full"
            />
            <div>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span className="font-medium text-gray-900">{dataa.author}</span>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  {new Date(dataa.createdAt).toLocaleDateString()}
                </div>
                {/* <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  {dataa.readTime}
                </div> */}
              </div>
            </div>
          </div>
        </div>

        <div className="relative h-96 md:h-[500px] mb-8 rounded-lg overflow-hidden">
          <Image
            src={dataa.image?.url || '/placeholder.svg'}
            alt={dataa.title}
            fill
            className="object-cover"
          />
        </div>

        <section className="mb-10">
          {/* <h2 className="text-2xl font-semibold mb-3 text-gray-800">Summary</h2> */}
          {/* <p className="text-lg md:text-xl text-gray-700 leading-relaxed">{dataa.summary}</p> */}
          <RichText data={dataa.content} />
        </section>

        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Image
                src={dataa.authorImage || '/placeholder.svg'}
                alt={dataa.author}
                width={60}
                height={60}
                className="rounded-full"
              />
              <div>
                <h3 className="font-semibold text-gray-900">{dataa.author}</h3>
                <p className="text-gray-600">News Reporter</p>
              </div>
            </div>
            <Button asChild>
              <Link href="news-media">View More News & Media</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
