import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'

export const Invoices: CollectionConfig = {
  slug: 'invoices',
  access: {
    create: authenticated,
    read: ({ req }) => {
      if (req.user?.role === 'admin') return true
      return {
        user: {
          equals: req.user?.id,
        },
      }
    },
    update: ({ req }) => {
      if (req.user?.role === 'admin') return true
      return {
        user: {
          equals: req.user?.id,
        },
      }
    },
    delete: ({ req }) => req.user?.role === 'admin',
  },
  admin: {
    defaultColumns: ['invoice_number', 'user', 'package', 'amount', 'status', 'created_at'],
    useAsTitle: 'invoice_number',
    group: 'Payments',
  },

  fields: [
    {
      name: 'invoice_number',
      type: 'text',
      unique: true,
      required: true,
      admin: {
        readOnly: true,
        description: 'Auto-generated invoice number',
      },
    },
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      admin: {
        description: 'User who owns this invoice',
      },
    },
    {
      name: 'package',
      type: 'relationship',
      relationTo: 'delegatepackages',
      required: true,
      admin: {
        description: 'Delegate package being purchased',
      },
    },
    {
      name: 'amount',
      type: 'number',
      required: true,
      min: 1,
      admin: {
        description: 'Invoice amount in the specified currency',
      },
    },
    {
      name: 'currency',
      type: 'select',
      options: [
        { label: 'Kenyan Shilling (KES)', value: 'KES' },
        { label: 'US Dollar (USD)', value: 'USD' },
      ],
      defaultValue: 'KES',
      required: true,
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Pending Payment', value: 'pending' },
        { label: 'Processing', value: 'processing' },
        { label: 'Paid', value: 'settled' },
        { label: 'Partially Paid', value: 'partial' },
        { label: 'Failed', value: 'failed' },
        { label: 'Expired', value: 'expired' },
        { label: 'Cancelled', value: 'cancelled' },
      ],
      defaultValue: 'draft',
      required: true,
      admin: {
        description: 'Current status of the invoice',
      },
    },
    {
      name: 'payment_reference',
      type: 'text',
      unique: true,
      admin: {
        readOnly: true,
        description: 'Unique payment reference for tracking',
      },
    },
    {
      name: 'due_date',
      type: 'date',
      admin: {
        description: 'When payment is due',
      },
    },
    // Customer Information (for guest payments)
    {
      name: 'customer_info',
      type: 'group',
      label: 'Customer Information',
      fields: [
        {
          name: 'name',
          type: 'text',
          admin: { description: 'Customer name' },
        },
        {
          name: 'email',
          type: 'email',
          admin: { description: 'Customer email address' },
        },
        {
          name: 'phone',
          type: 'text',
          admin: { description: 'Customer phone number' },
        },
        {
          name: 'id_number',
          type: 'text',
          admin: { description: 'Customer ID number' },
        },
      ],
    },
    // Pesaflow Integration Data
    {
      name: 'pesaflow_data',
      type: 'group',
      label: 'Pesaflow Data',
      admin: {
        readOnly: true,
      },
      fields: [
        {
          name: 'bill_ref_number',
          type: 'text',
          admin: { readOnly: true },
        },
        {
          name: 'checkout_url',
          type: 'text',
          admin: { readOnly: true },
        },
        {
          name: 'gateway_response',
          type: 'json',
          admin: { readOnly: true },
        },
        {
          name: 'last_gateway_sync',
          type: 'date',
          admin: { readOnly: true },
        },
      ],
    },
    // Payment Summary
    {
      name: 'payment_summary',
      type: 'group',
      label: 'Payment Summary',
      admin: {
        readOnly: true,
      },
      fields: [
        {
          name: 'amount_paid',
          type: 'number',
          defaultValue: 0,
          admin: { readOnly: true },
        },
        {
          name: 'amount_remaining',
          type: 'number',
          admin: { readOnly: true },
        },
        {
          name: 'payment_count',
          type: 'number',
          defaultValue: 0,
          admin: { readOnly: true },
        },
        {
          name: 'last_payment_date',
          type: 'date',
          admin: { readOnly: true },
        },
      ],
    },
    // Registration Context
    {
      name: 'registration_context',
      type: 'group',
      label: 'Registration Context',
      admin: {
        readOnly: true,
      },
      fields: [
        {
          name: 'is_registration_payment',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            readOnly: true,
            description: 'Whether this invoice was created during user registration',
          },
        },
        {
          name: 'user_created_during_flow',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            readOnly: true,
            description: 'Whether user was created during this payment flow',
          },
        },
        {
          name: 'temporary_password_sent',
          type: 'checkbox',
          defaultValue: false,
          admin: { readOnly: true, description: 'Whether temporary password was sent to user' },
        },
      ],
    },
    // Metadata
    {
      name: 'metadata',
      type: 'json',
      admin: {
        description: 'Additional data for integration purposes',
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data, operation }) => {
        if (operation === 'create') {
          // Generate invoice number
          const timestamp = Date.now()
          data.invoice_number = `INV-${timestamp}`
          // Generate payment reference
          data.payment_reference = `REF-${timestamp}`

          // Set due date to 7 days from now if not specified
          if (!data.due_date) {
            const dueDate = new Date()
            dueDate.setDate(dueDate.getDate() + 7)
            data.due_date = dueDate
          }
        }

        // Calculate remaining amount
        if (data.payment_summary) {
          data.payment_summary.amount_remaining =
            data.amount - (data.payment_summary.amount_paid || 0)
        }
      },
    ],
    afterChange: [
      ({ doc, operation }) => {
        if (operation === 'create') {
          console.log(`New invoice created: ${doc.invoice_number} for user ${doc.user}`)
        }

        // Update package statistics if needed
        if (doc.status === 'settled') {
          // This could trigger package usage updates
          console.log(`Invoice ${doc.invoice_number} marked as settled`)
        }
      },
    ],
  },
  timestamps: true,
}

export default Invoices
