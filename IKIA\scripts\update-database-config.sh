#!/bin/bash

# Update Database Configuration Script
# This script updates the application to use the new DATABASE_URL configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="ikia-conference"
APP_DIR="/home/<USER>/public_html"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Update database configuration
update_database_config() {
    log_info "Updating database configuration..."
    cd "$APP_DIR"
    
    # Check if DATABASE_URL is set in .env
    if grep -q "^DATABASE_URL=" .env; then
        log_success "DATABASE_URL is already configured in .env"
    else
        log_error "DATABASE_URL is not set in .env file"
        log_info "Please ensure DATABASE_URL is set in your .env file"
        return 1
    fi
    
    # Stop PM2 instances
    log_info "Stopping PM2 instances..."
    pm2 stop "$APP_NAME" || true
    
    # Wait for processes to stop
    sleep 3
    
    # Delete PM2 processes to ensure clean restart
    log_info "Deleting PM2 processes..."
    pm2 delete "$APP_NAME" || true
    
    # Start fresh instances
    log_info "Starting fresh PM2 instances with new database configuration..."
    pm2 start ecosystem.config.cjs --env production
    
    # Wait for stabilization
    log_info "Waiting for application to stabilize..."
    sleep 10
    
    # Check if application is running
    if pm2 describe "$APP_NAME" | grep -q "online"; then
        log_success "Application restarted successfully with new database configuration"
        
        # Show status
        pm2 status
        
        # Check logs for any database connection errors
        log_info "Checking recent logs for database connection status..."
        pm2 logs "$APP_NAME" --lines 20 --nostream
        
    else
        log_error "Application failed to restart"
        log_info "Checking error logs..."
        pm2 logs "$APP_NAME" --lines 50 --nostream
        return 1
    fi
    
    log_success "Database configuration update completed!"
    log_info "Monitor the application with: pm2 monit"
    log_info "View logs with: pm2 logs $APP_NAME"
}

# Test database connection
test_database_connection() {
    log_info "Testing database connection..."
    cd "$APP_DIR"
    
    if [ -f "test-db-connection.js" ]; then
        node test-db-connection.js
    else
        log_warning "Database connection test script not found"
    fi
}

# Main execution
case "${1:-update}" in
    "update")
        update_database_config
        ;;
    "test")
        test_database_connection
        ;;
    "full")
        update_database_config
        test_database_connection
        ;;
    *)
        echo "Usage: $0 {update|test|full}"
        echo ""
        echo "Commands:"
        echo "  update - Update and restart application with new database config"
        echo "  test   - Test database connection"
        echo "  full   - Update configuration and test connection"
        exit 1
        ;;
esac
