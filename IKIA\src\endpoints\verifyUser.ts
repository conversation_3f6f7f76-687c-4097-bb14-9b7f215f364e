import type { PayloadRequest } from 'payload'

/**
 * Endpoint to manually verify a user (for debugging/admin purposes)
 */
export const verifyUserEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    const { email } = req.body

    if (!email) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'MISSING_EMAIL',
            message: 'Email is required'
          }
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    // Find user by email
    const users = await req.payload.find({
      collection: 'users',
      where: {
        email: {
          equals: email
        }
      }
    })

    if (users.docs.length === 0) {
      return new Response(
        JSON.stringify({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found'
          }
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    const user = users.docs[0]

    // Update user to be verified
    const updatedUser = await req.payload.update({
      collection: 'users',
      id: user.id,
      data: {
        _verified: true,
        _verificationtoken: null // Clear verification token
      }
    })

    console.log(`User verified: ${email}`)

    return new Response(
      JSON.stringify({
        success: true,
        message: 'User verified successfully',
        data: {
          userId: updatedUser.id,
          email: updatedUser.email,
          name: updatedUser.name,
          verified: updatedUser._verified,
          verificationToken: updatedUser._verificationtoken
        }
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    )

  } catch (error: any) {
    console.error('Verify user endpoint error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error',
          details: error.message
        }
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}
