'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { ChevronDown, TrendingUp, Star } from 'lucide-react'

interface InvestDropdownProps {
  className?: string
  isScrolled?: boolean
}

export default function InvestDropdown({
  className = '',
  isScrolled = false,
}: InvestDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)

  const dropdownItems = [
    {
      href: '/invest',
      title: 'Investor Hub',
      icon: TrendingUp,
      color: 'text-primary',
    },
    {
      href: '/invest/success-stories',
      title: 'Success Stories',
      icon: Star,
      color: 'text-primary',
    },
  ]

  return (
    <div
      className={`relative ${className}`}
      onMouseEnter={() => setIsOpen(true)}
      onMouseLeave={() => setIsOpen(false)}
    >
      {/* Trigger */}
      <div
        className={`relative font-myriad font-semibold text-xs transition-colors duration-300 group cursor-pointer ${
          isScrolled ? 'text-foreground hover:text-primary' : 'text-white hover:text-accent'
        }`}
        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
      >
        <span className="relative z-10 flex items-center gap-1">
          INVEST
          <ChevronDown
            className={`w-3 h-3 transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}
          />
        </span>
        {/* Active indicator */}
        <div
          className={`absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full`}
        ></div>
      </div>

      {/* Dropdown Menu */}
      <div
        className={`absolute top-full left-0 mt-2 w-64 bg-background shadow-2xl border border-border overflow-hidden transition-all duration-300 z-50 ${
          isOpen
            ? 'opacity-100 visible transform translate-y-0'
            : 'opacity-0 invisible transform -translate-y-2'
        }`}
      >
        {/* Menu Items */}
        <div className="py-2">
          {dropdownItems.map((item, index) => (
            <Link
              key={index}
              href={item.href}
              className="block px-6 py-4 hover:bg-muted transition-colors duration-200 group"
            >
              <div className="flex items-start gap-4">
                <div
                  className={`p-2 bg-muted group-hover:bg-muted/80 transition-colors ${item.color}`}
                >
                  <item.icon className="w-5 h-5" />
                </div>
                <div className="flex-1">
                  <h4
                    className="font-semibold text-foreground group-hover:text-primary transition-colors font-myriad"
                    style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                  >
                    {item.title}
                  </h4>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}
