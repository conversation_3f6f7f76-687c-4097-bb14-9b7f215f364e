'use client'

import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  CheckCircle,
  Download,
  Calendar,
  MapPin,
  Mail,
  Phone,
  Home,
  FileText,
  Share2,
  QrCode,
  TrendingUp,
} from 'lucide-react'
import {
  generateQRCode,
  downloadQRCode,
  loadFormDataFromStorage,
} from '@/modules/website/registration/lib/registration-utils'
import { ConferenceBadge } from '@/modules/website/registration/components/ConferenceBadge'

export default function RegistrationSuccess() {
  const router = useRouter()
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('')
  const [registrationDetails, setRegistrationDetails] = useState<any>(null)

  // Helper function to check if this is an investment intent registration
  const isInvestmentIntent = (registrationType: string) => {
    return (
      registrationType?.toLowerCase().includes('investment') ||
      registrationType?.toLowerCase().includes('investor')
    )
  }

  // Load actual registration data from localStorage
  useEffect(() => {
    // First, check URL parameters for registration type
    const urlParams = new URLSearchParams(window.location.search)
    const urlType = urlParams.get('type')

    const formData = loadFormDataFromStorage('ikia-registration')

    // For UI testing, prioritize URL parameter over form data
    if (
      urlType &&
      (urlType === 'investment-intent' ||
        urlType === 'investor' ||
        urlType === 'exhibitor' ||
        urlType === 'vip' ||
        urlType === 'sponsor')
    ) {
      // Use URL parameter to override any existing form data for testing
      let mockRegistrationType = 'Conference Registration'
      let mockPackage = 'Standard Package'
      let mockAmount = 'KES 25,000'
      let mockPaymentMethod = 'M-Pesa'

      // Check URL parameter to determine registration type
      if (urlType === 'investment-intent' || urlType === 'investor') {
        mockRegistrationType = 'Investment Intent'
        mockPackage = 'Interest Registration'
        mockAmount = 'Free'
        mockPaymentMethod = 'N/A'
      } else if (urlType === 'exhibitor') {
        mockRegistrationType = 'Exhibitor'
        mockPackage = 'Standard Booth'
        mockAmount = 'KES 75,000'
      } else if (urlType === 'vip') {
        mockRegistrationType = 'VIP'
        mockPackage = 'Premium Experience'
        mockAmount = 'KES 150,000'
      } else if (urlType === 'sponsor') {
        mockRegistrationType = 'Conference Sponsor'
        mockPackage = 'Partnership Package'
        mockAmount = 'KES 500,000'
      }

      setRegistrationDetails({
        confirmationNumber: 'IKIA2025-REG-001234',
        registrationType: mockRegistrationType,
        package: mockPackage,
        amount: mockAmount,
        paymentMethod: mockPaymentMethod,
        registrantName: 'John Doe',
        email: '<EMAIL>',
        registrationDate: new Date().toLocaleDateString(),
      })
    } else if (formData) {
      // Generate confirmation number based on registration type and timestamp
      const timestamp = Date.now().toString().slice(-6)
      const typePrefix = formData.registrationType?.slice(0, 3).toUpperCase() || 'REG'
      const confirmationNumber = `IKIA2025-${typePrefix}-${timestamp}`

      // Handle group registration vs individual registration
      if (formData.isGroupRegistration && formData.groupMembers) {
        const memberCount = formData.groupMembers.length
        const primaryMember = formData.groupMembers[0] || {}

        setRegistrationDetails({
          confirmationNumber,
          registrationType: `${formData.registrationType} (Group of ${memberCount})`,
          package:
            primaryMember.selectedPackage || primaryMember.selectedTier || 'Standard Package',
          amount: formData.amount || 'TBD',
          paymentMethod: formData.paymentMethod || 'Pending',
          registrantName:
            `${primaryMember.firstName || ''} ${primaryMember.lastName || ''}`.trim() ||
            'Group Registration',
          email: primaryMember.email || '',
          registrationDate: new Date().toLocaleDateString(),
          groupMembers: formData.groupMembers,
          isGroupRegistration: true,
        })
      } else {
        setRegistrationDetails({
          confirmationNumber,
          registrationType: formData.registrationType || 'Conference Registration',
          package: formData.selectedPackage || formData.selectedTier || 'Standard Package',
          amount: formData.amount || 'TBD',
          paymentMethod: formData.paymentMethod || 'Pending',
          registrantName:
            `${formData.firstName || ''} ${formData.lastName || ''}`.trim() || 'Registrant',
          email: formData.email || '',
          registrationDate: new Date().toLocaleDateString(),
          isGroupRegistration: false,
        })
      }
    } else {
      // Fallback to default mock data if no URL parameter and no form data
      setRegistrationDetails({
        confirmationNumber: 'IKIA2025-REG-001234',
        registrationType: 'Conference Registration',
        package: 'Standard Package',
        amount: 'KES 25,000',
        paymentMethod: 'M-Pesa',
        registrantName: 'John Doe',
        email: '<EMAIL>',
        registrationDate: new Date().toLocaleDateString(),
      })
    }
  }, [])

  const conferenceDetails = {
    name: '1st International Investment Conference and Trade Fair on Indigenous Knowledge Intellectual Assets',
    dates: 'November 18-21, 2025',
    venue: "Murang'a County",
    website: 'www.ikia-conference.org',
    email: '<EMAIL>',
    phone: '+254 700 000 000',
  }

  const nextSteps = [
    {
      title: 'Check Your Email',
      description: isInvestmentIntent(registrationDetails?.registrationType || '')
        ? 'A confirmation email with your investment interest details has been sent to your email address.'
        : 'A confirmation email with your registration details and receipt has been sent to your email address.',
      icon: Mail,
    },
    {
      title: isInvestmentIntent(registrationDetails?.registrationType || '')
        ? 'Investment Matching'
        : 'Download Materials',
      description: isInvestmentIntent(registrationDetails?.registrationType || '')
        ? 'Our team will review your preferences and match you with relevant investment opportunities.'
        : 'Access your registration certificate and conference materials from the downloads section.',
      icon: Download,
    },
    {
      title: isInvestmentIntent(registrationDetails?.registrationType || '')
        ? 'Opportunity Updates'
        : 'Mark Your Calendar',
      description: isInvestmentIntent(registrationDetails?.registrationType || '')
        ? 'Receive curated investment opportunities and pre-conference matching notifications.'
        : 'Add the conference dates to your calendar and start planning your attendance.',
      icon: Calendar,
    },
    {
      title: 'Stay Updated',
      description: isInvestmentIntent(registrationDetails?.registrationType || '')
        ? 'Follow our website and social media for investment showcases and networking events.'
        : 'Follow our website and social media for the latest updates and announcements.',
      icon: Share2,
    },
  ]

  // Generate QR code when registration details are available
  useEffect(() => {
    if (!registrationDetails) return

    const generateQR = async () => {
      const qrData = {
        confirmationNumber: registrationDetails.confirmationNumber,
        registrantName: registrationDetails.registrantName,
        email: registrationDetails.email,
        registrationType: registrationDetails.registrationType,
        package: registrationDetails.package,
        eventDate: conferenceDetails.dates,
        venue: conferenceDetails.venue,
      }

      try {
        const qrCodeUrl = await generateQRCode(qrData)
        setQrCodeDataUrl(qrCodeUrl)
      } catch (error) {
        console.error('Failed to generate QR code:', error)
      }
    }

    generateQR()
  }, [registrationDetails])

  const handleDownloadQRCode = () => {
    if (qrCodeDataUrl) {
      downloadQRCode(qrCodeDataUrl, `IKIA2025-Badge-${registrationDetails.confirmationNumber}`)
    }
  }

  const handleDownloadCertificate = () => {
    // In a real app, this would generate and download the certificate
    console.log('Downloading registration certificate...')
  }

  const handleDownloadReceipt = () => {
    // In a real app, this would generate and download the payment receipt
    console.log('Downloading payment receipt...')
  }

  // Show loading state while registration details are being loaded
  if (!registrationDetails) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your registration details...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Success Header */}
      <div className="text-center mb-12">
        <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-green-500 to-green-600 rounded-full mb-6 shadow-2xl">
          <CheckCircle className="w-12 h-12 text-white" />
        </div>

        <h1
          className="text-4xl font-bold text-gray-900 mb-4"
          style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
        >
          {isInvestmentIntent(registrationDetails.registrationType)
            ? 'Investment Intent Submitted!'
            : 'Registration Successful!'}
        </h1>

        <p className="text-xl text-gray-600 mb-2">
          {isInvestmentIntent(registrationDetails.registrationType)
            ? 'Thank you for expressing your investment interest in IKIA opportunities'
            : 'Thank you for registering for the IKIA Conference 2025'}
        </p>

        <div className="inline-flex items-center space-x-2 bg-green-100 text-green-800 px-4 py-2 rounded-full">
          <span className="font-semibold">
            {isInvestmentIntent(registrationDetails.registrationType)
              ? 'Intent ID:'
              : 'Confirmation #:'}
          </span>
          <span className="font-mono">{registrationDetails.confirmationNumber}</span>
        </div>
      </div>

      <div className="space-y-8">
        {/* Registration Summary */}
        <Card>
          <CardHeader>
            <CardTitle
              className="text-xl font-bold"
              style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
            >
              {isInvestmentIntent(registrationDetails.registrationType)
                ? 'Investment Intent Summary'
                : 'Registration Summary'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  {isInvestmentIntent(registrationDetails.registrationType)
                    ? 'Investor'
                    : 'Registrant'}
                </h3>
                <p className="text-gray-700">{registrationDetails.registrantName}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Email</h3>
                <p className="text-gray-700">{registrationDetails.email}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  {isInvestmentIntent(registrationDetails.registrationType)
                    ? 'Intent Type'
                    : 'Registration Type'}
                </h3>
                <p className="text-gray-700">{registrationDetails.registrationType}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  {isInvestmentIntent(registrationDetails.registrationType) ? 'Status' : 'Package'}
                </h3>
                <p className="text-gray-700">{registrationDetails.package}</p>
              </div>
              {!isInvestmentIntent(registrationDetails.registrationType) && (
                <>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Amount Paid</h3>
                    <p className="text-green-600 font-bold text-lg">{registrationDetails.amount}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Payment Method</h3>
                    <p className="text-gray-700">{registrationDetails.paymentMethod}</p>
                  </div>
                </>
              )}
              {isInvestmentIntent(registrationDetails.registrationType) && (
                <div className="md:col-span-2">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 className="font-semibold text-green-900 mb-2">
                      Investment Interest Status
                    </h3>
                    <p className="text-green-800 text-sm">
                      Your investment interest has been recorded. No payment is required for this
                      registration type. Our team will review your preferences and connect you with
                      relevant opportunities.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Group Members (if group registration) */}
        {registrationDetails.isGroupRegistration && registrationDetails.groupMembers && (
          <Card>
            <CardHeader>
              <CardTitle
                className="text-xl font-bold"
                style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
              >
                Group Members ({registrationDetails.groupMembers.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {registrationDetails.groupMembers.map((member: any, index: number) => (
                  <div key={index} className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">
                          {member.firstName} {member.lastName}
                        </h4>
                        <p className="text-sm text-gray-600">{member.email}</p>
                      </div>
                    </div>
                    {member.organization && (
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Organization:</span> {member.organization}
                      </p>
                    )}
                    {member.position && (
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Position:</span> {member.position}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Conference Information */}
        <Card>
          <CardHeader>
            <CardTitle
              className="text-xl font-bold"
              style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
            >
              {isInvestmentIntent(registrationDetails.registrationType)
                ? 'Investment Opportunities Conference'
                : 'Conference Information'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">{conferenceDetails.name}</h3>
              {isInvestmentIntent(registrationDetails.registrationType) && (
                <p className="text-gray-600 text-sm mb-4">
                  This conference will showcase Indigenous Knowledge investment opportunities that
                  match your expressed interests.
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center space-x-3">
                <Calendar className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="font-semibold text-gray-900">Dates</h4>
                  <p className="text-gray-700">{conferenceDetails.dates}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <MapPin className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="font-semibold text-gray-900">Venue</h4>
                  <p className="text-gray-700">{conferenceDetails.venue}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="font-semibold text-gray-900">Email</h4>
                  <p className="text-gray-700">{conferenceDetails.email}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="font-semibold text-gray-900">Phone</h4>
                  <p className="text-gray-700">{conferenceDetails.phone}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Downloads */}
        <Card>
          <CardHeader>
            <CardTitle
              className="text-xl font-bold"
              style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
            >
              {isInvestmentIntent(registrationDetails.registrationType)
                ? 'Investment Information'
                : 'Downloads'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isInvestmentIntent(registrationDetails.registrationType) ? (
              /* Investment Intent Downloads */
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  onClick={handleDownloadCertificate}
                  variant="outline"
                  className="flex items-center space-x-2 h-auto p-4"
                >
                  <FileText className="w-5 h-5" />
                  <div className="text-left">
                    <div className="font-semibold">Investment Interest Confirmation</div>
                    <div className="text-sm text-gray-600">PDF format</div>
                  </div>
                </Button>

                <Button
                  onClick={() => window.open('/investment-opportunities', '_blank')}
                  variant="outline"
                  className="flex items-center space-x-2 h-auto p-4"
                >
                  <Download className="w-5 h-5" />
                  <div className="text-left">
                    <div className="font-semibold">Investment Prospectus</div>
                    <div className="text-sm text-gray-600">PDF format</div>
                  </div>
                </Button>
              </div>
            ) : (
              /* Regular Registration Downloads */
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  onClick={handleDownloadCertificate}
                  variant="outline"
                  className="flex items-center space-x-2 h-auto p-4"
                >
                  <FileText className="w-5 h-5" />
                  <div className="text-left">
                    <div className="font-semibold">Registration Certificate</div>
                    <div className="text-sm text-gray-600">PDF format</div>
                  </div>
                </Button>

                <Button
                  onClick={handleDownloadReceipt}
                  variant="outline"
                  className="flex items-center space-x-2 h-auto p-4"
                >
                  <Download className="w-5 h-5" />
                  <div className="text-left">
                    <div className="font-semibold">Payment Receipt</div>
                    <div className="text-sm text-gray-600">PDF format</div>
                  </div>
                </Button>

                <Button
                  onClick={handleDownloadQRCode}
                  variant="outline"
                  className="flex items-center space-x-2 h-auto p-4"
                  disabled={!qrCodeDataUrl}
                >
                  <QrCode className="w-5 h-5" />
                  <div className="text-left">
                    <div className="font-semibold">Conference Badge QR</div>
                    <div className="text-sm text-gray-600">PNG format</div>
                  </div>
                </Button>
              </div>
            )}

            {/* Conference Badge - Show only for non-investment registrations */}
            {!isInvestmentIntent(registrationDetails.registrationType) && (
              <div className="mt-6">
                <h4 className="font-semibold text-gray-900 mb-4 text-center">
                  Your Conference Access Badge
                </h4>
                <ConferenceBadge
                  registrationDetails={{
                    confirmationNumber: registrationDetails.confirmationNumber,
                    registrantName: registrationDetails.registrantName,
                    email: registrationDetails.email,
                    registrationType: registrationDetails.registrationType,
                    package: registrationDetails.package,
                    organization: registrationDetails.organization,
                    position: registrationDetails.position,
                  }}
                  qrCodeDataUrl={qrCodeDataUrl}
                  onDownload={handleDownloadQRCode}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card>
          <CardHeader>
            <CardTitle
              className="text-xl font-bold"
              style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
            >
              {isInvestmentIntent(registrationDetails.registrationType)
                ? 'What Happens Next?'
                : "What's Next?"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {nextSteps.map((step, index) => {
                const IconComponent = step.icon
                return (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <IconComponent className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">{step.title}</h3>
                      <p className="text-sm text-gray-600">{step.description}</p>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button onClick={() => router.push('/')} variant="outline" className="px-8 py-3">
            <Home className="w-4 h-4 mr-2" />
            Return to Home
          </Button>

          <Button onClick={() => window.print()} className="px-8 py-3">
            <FileText className="w-4 h-4 mr-2" />
            {isInvestmentIntent(registrationDetails.registrationType)
              ? 'Print Confirmation'
              : 'Print Confirmation'}
          </Button>

          {isInvestmentIntent(registrationDetails.registrationType) && (
            <Button
              onClick={() => router.push('/investment-opportunities')}
              variant="outline"
              className="px-8 py-3"
            >
              <TrendingUp className="w-4 h-4 mr-2" />
              View Opportunities
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
