export function serializeParams(obj: Record<string, any>, prefix = ''): URLSearchParams {
  const params = new URLSearchParams()

  const process = (value: any, keyPrefix: string) => {
    if (Array.isArray(value)) {
      value.forEach((val, i) => process(val, `${keyPrefix}[${i}]`))
    } else if (typeof value === 'object' && value !== null) {
      for (const key in value) {
        process(value[key], `${keyPrefix}[${key}]`)
      }
    } else if (value !== undefined && value !== '') {
      params.append(keyPrefix, value)
    }
  }

  for (const key in obj) {
    process(obj[key], prefix ? `${prefix}[${key}]` : key)
  }

  return params
}
