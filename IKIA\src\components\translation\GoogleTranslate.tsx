'use client'

import React, { useEffect, useRef } from 'react'
import { Globe, ChevronDown } from 'lucide-react'
import { cn } from '@/utilities/ui'

interface GoogleTranslateProps {
  className?: string
  isScrolled?: boolean
}

declare global {
  interface Window {
    google: any
    googleTranslateElementInit: () => void
  }
}

export const GoogleTranslate: React.FC<GoogleTranslateProps> = ({
  className,
  isScrolled = true,
}) => {
  const googleTranslateRef = useRef<HTMLDivElement>(null)
  const isInitialized = useRef(false)
  const [isStylesApplied, setIsStylesApplied] = React.useState(false)

  useEffect(() => {
    // Avoid multiple initializations
    if (isInitialized.current) return

    // Apply custom styles to Google Translate elements
    const applyCustomStyles = () => {
      const applyStyles = () => {
        // Target the Google Translate widget with high specificity
        const style = document.createElement('style')
        style.id = 'ikia-google-translate-styles'

        // Remove existing styles if they exist
        const existingStyle = document.getElementById('ikia-google-translate-styles')
        if (existingStyle) {
          existingStyle.remove()
        }

        style.textContent = `
          /* High specificity selectors for Google Translate */
          #google_translate_element .goog-te-gadget,
          .google-translate-container .goog-te-gadget,
          .google-translate-wrapper .goog-te-gadget {
            font-family: inherit !important;
            font-size: 12px !important;
          }

          #google_translate_element .goog-te-gadget-simple,
          .google-translate-container .goog-te-gadget-simple,
          .google-translate-wrapper .goog-te-gadget-simple {
            background-color: transparent !important;
            border: 1px solid transparent !important;
            border-radius: 6px !important;
            font-size: 12px !important;
            display: inline-block !important;
            padding: 8px 12px !important;
            margin: 0 !important;
            transition: all 0.3s ease !important;
            cursor: pointer !important;
            font-family: inherit !important;
            ${isScrolled ? `color: #374151 !important;` : `color: white !important;`}
          }

          #google_translate_element .goog-te-gadget-simple:hover,
          .google-translate-container .goog-te-gadget-simple:hover,
          .google-translate-wrapper .goog-te-gadget-simple:hover {
            ${
              isScrolled
                ? `
                color: #7E2518 !important;
                background-color: rgba(126, 37, 24, 0.05) !important;
                border-color: rgba(126, 37, 24, 0.2) !important;
              `
                : `
                color: #E8B32C !important;
                background-color: rgba(255, 255, 255, 0.1) !important;
                border-color: rgba(255, 255, 255, 0.2) !important;
              `
            }
          }

          #google_translate_element .goog-te-gadget-simple .goog-te-menu-value,
          .google-translate-container .goog-te-gadget-simple .goog-te-menu-value,
          .google-translate-wrapper .goog-te-gadget-simple .goog-te-menu-value {
            color: inherit !important;
            font-family: inherit !important;
            font-size: 12px !important;
            font-weight: 600 !important;
            text-decoration: none !important;
          }

          /* Add globe icon before text */
          #google_translate_element .goog-te-gadget-simple .goog-te-menu-value:before,
          .google-translate-container .goog-te-gadget-simple .goog-te-menu-value:before,
          .google-translate-wrapper .goog-te-gadget-simple .goog-te-menu-value:before {
            content: '🌍 ' !important;
            margin-right: 4px !important;
          }

          /* Hide the default arrow and add custom one */
          #google_translate_element .goog-te-gadget-simple .goog-te-menu-value span:first-child,
          .google-translate-container .goog-te-gadget-simple .goog-te-menu-value span:first-child,
          .google-translate-wrapper .goog-te-gadget-simple .goog-te-menu-value span:first-child {
            display: none !important;
          }

          #google_translate_element .goog-te-gadget-simple .goog-te-menu-value:after,
          .google-translate-container .goog-te-gadget-simple .goog-te-menu-value:after,
          .google-translate-wrapper .goog-te-gadget-simple .goog-te-menu-value:after {
            content: ' ▼' !important;
            font-size: 10px !important;
            margin-left: 4px !important;
          }

          /* Hide the default icon */
          .goog-te-gadget-icon {
            display: none !important;
          }

          /* Hide Google Translate branding */
          .goog-logo-link {
            display: none !important;
          }

          .goog-te-banner-frame {
            display: none !important;
          }

          body {
            top: 0 !important;
          }

          /* Style the dropdown menu */
          .goog-te-menu-frame {
            max-height: 300px !important;
            overflow-y: auto !important;
            border: 2px solid #7E2518 !important;
            border-radius: 8px !important;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
            background-color: white !important;
            z-index: 9999 !important;
          }

          .goog-te-menu-frame .goog-te-menu2 {
            background-color: white !important;
            border: none !important;
            font-family: inherit !important;
          }

          .goog-te-menu-frame .goog-te-menu2-item {
            font-family: inherit !important;
            font-size: 12px !important;
            color: #374151 !important;
            padding: 8px 12px !important;
            border-bottom: 1px solid #f3f4f6 !important;
            cursor: pointer !important;
          }

          .goog-te-menu-frame .goog-te-menu2-item:hover {
            background-color: #7E2518 !important;
            color: white !important;
          }

          .goog-te-menu-frame .goog-te-menu2-item:last-child {
            border-bottom: none !important;
          }
        `

        document.head.appendChild(style)
        setIsStylesApplied(true)
        console.log('IKIA Google Translate styles applied')
      }

      // Apply styles immediately and also after delays
      applyStyles()
      setTimeout(applyStyles, 100)
      setTimeout(applyStyles, 500)
      setTimeout(applyStyles, 1000)
    }

    // Initialize Google Translate
    const initializeGoogleTranslate = () => {
      if (window.google && window.google.translate) {
        new window.google.translate.TranslateElement(
          {
            pageLanguage: 'en',
            includedLanguages: 'en,sw,fr,ar,es,pt,de,it,zh,ja,ko,hi,ur,am,so,ha',
            layout: window.google.translate.TranslateElement.InlineLayout.SIMPLE,
            autoDisplay: false,
            multilanguagePage: true,
          },
          googleTranslateRef.current,
        )
        isInitialized.current = true

        // Apply custom styles after initialization
        setTimeout(applyCustomStyles, 100)
        setTimeout(applyCustomStyles, 500)
        setTimeout(applyCustomStyles, 1000)
      }
    }

    // Set up global function for Google Translate
    window.googleTranslateElementInit = initializeGoogleTranslate

    // Set up MutationObserver to watch for Google Translate elements
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element
              if (
                element.classList?.contains('goog-te-gadget') ||
                element.classList?.contains('goog-te-menu-frame') ||
                element.querySelector?.('.goog-te-gadget')
              ) {
                console.log('Google Translate element detected, applying styles...')
                setTimeout(applyCustomStyles, 50)
              }
            }
          })
        }
      })
    })

    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    })

    // Load Google Translate script
    const script = document.createElement('script')
    script.src = 'https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit'
    script.async = true
    script.defer = true
    document.head.appendChild(script)

    // Cleanup function
    return () => {
      // Disconnect observer
      observer.disconnect()

      // Remove script if component unmounts
      const existingScript = document.querySelector('script[src*="translate.google.com"]')
      if (existingScript) {
        existingScript.remove()
      }

      // Remove custom styles
      const customStyles = document.getElementById('ikia-google-translate-styles')
      if (customStyles) {
        customStyles.remove()
      }
    }
  }, [])

  return (
    <div className={cn('google-translate-wrapper', className)}>
      <div
        ref={googleTranslateRef}
        className="google-translate-container"
        role="region"
        aria-label="Language translation options"
      />
      {isStylesApplied && (
        <div className="text-xs text-gray-500 hidden">
          Styles applied: {isStylesApplied ? 'Yes' : 'No'}
        </div>
      )}
    </div>
  )
}
