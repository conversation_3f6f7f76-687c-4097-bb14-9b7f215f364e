import type { PayloadRequest } from 'payload'

export interface CustomerData {
  clientMSISDN: string
  clientIDNumber: string
  clientName: string
  clientEmail: string
}

export interface ExtractedCustomerData extends CustomerData {
  currency: string
  amountExpected: string
  billRefNumber: string
  billDesc: string
}

/**
 * Service to extract and validate customer data from various sources
 */
export class CustomerDataService {
  private req: PayloadRequest

  constructor(req: PayloadRequest) {
    this.req = req
  }

  /**
   * Extract customer data from invoice
   */
  async extractFromInvoice(invoiceId: string | number): Promise<ExtractedCustomerData> {
    try {
      // Get invoice with related data
      const invoice = await this.req.payload.findByID({
        collection: 'invoices',
        id: invoiceId,
        depth: 2, // Include related user and package data
      })

      if (!invoice) {
        throw new Error(`Invoice not found: ${invoiceId}`)
      }

      // Extract customer info from invoice
      const customerInfo = invoice.customer_info || {}
      const user = invoice.user || {}
      
      // Get package information for bill description
      let packageName = 'Service Payment'
      if (invoice.package && typeof invoice.package === 'object') {
        packageName = invoice.package.name || packageName
      }

      // Build customer data with fallbacks
      const customerData: ExtractedCustomerData = {
        clientMSISDN: this.formatPhoneNumber(
          customerInfo.phone || 
          user.phone || 
          user.phone_number || 
          ''
        ),
        clientIDNumber: 
          customerInfo.id_number || 
          user.id_number || 
          user.national_id || 
          'N/A',
        clientName: 
          customerInfo.name || 
          user.name || 
          `${user.first_name || ''} ${user.last_name || ''}`.trim() || 
          'Unknown Customer',
        clientEmail: 
          customerInfo.email || 
          user.email || 
          '',
        currency: invoice.currency || 'KES',
        amountExpected: invoice.amount?.toString() || '0',
        billRefNumber: invoice.payment_reference || invoice.invoice_number || invoiceId.toString(),
        billDesc: `${packageName} - Invoice ${invoice.invoice_number}`,
      }

      // Validate required fields
      this.validateCustomerData(customerData)

      return customerData
    } catch (error) {
      console.error('Error extracting customer data from invoice:', error)
      throw new Error(`Failed to extract customer data: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Extract customer data from registration data
   */
  extractFromRegistration(registrationData: any, packageData: any, invoiceData: any): ExtractedCustomerData {
    const customerData: ExtractedCustomerData = {
      clientMSISDN: this.formatPhoneNumber(registrationData.phone_number || registrationData.phone || ''),
      clientIDNumber: registrationData.id_number || registrationData.national_id || 'N/A',
      clientName: registrationData.name || `${registrationData.firstName || ''} ${registrationData.lastName || ''}`.trim() || 'Unknown Customer',
      clientEmail: registrationData.email || '',
      currency: packageData.currency || 'KES',
      amountExpected: packageData.price?.toString() || '0',
      billRefNumber: invoiceData.payment_reference || invoiceData.invoice_number || '',
      billDesc: `${packageData.name} - Registration Payment`,
    }

    // Validate required fields
    this.validateCustomerData(customerData)

    return customerData
  }

  /**
   * Format phone number to ensure it starts with country code
   */
  private formatPhoneNumber(phone: string): string {
    if (!phone) return ''
    
    // Remove any non-digit characters
    const cleanPhone = phone.replace(/\D/g, '')
    
    // Handle Kenyan phone numbers
    if (cleanPhone.startsWith('254')) {
      return cleanPhone
    } else if (cleanPhone.startsWith('0') && cleanPhone.length === 10) {
      return '254' + cleanPhone.substring(1)
    } else if (cleanPhone.length === 9) {
      return '254' + cleanPhone
    }
    
    // Return as-is if we can't determine format
    return cleanPhone || phone
  }

  /**
   * Validate that all required customer data is present
   */
  private validateCustomerData(data: CustomerData): void {
    const missingFields: string[] = []

    if (!data.clientMSISDN) missingFields.push('clientMSISDN (phone number)')
    if (!data.clientIDNumber || data.clientIDNumber === 'N/A') missingFields.push('clientIDNumber (ID number)')
    if (!data.clientName || data.clientName === 'Unknown Customer') missingFields.push('clientName (customer name)')
    if (!data.clientEmail) missingFields.push('clientEmail (email address)')

    if (missingFields.length > 0) {
      throw new Error(`Missing required customer data: ${missingFields.join(', ')}`)
    }

    // Validate phone number format
    if (!data.clientMSISDN.match(/^254\d{9}$/)) {
      console.warn(`Invalid phone number format: ${data.clientMSISDN}. Expected format: 254XXXXXXXXX`)
    }

    // Validate email format
    if (!data.clientEmail.includes('@')) {
      throw new Error(`Invalid email format: ${data.clientEmail}`)
    }
  }

  /**
   * Get default customer data for testing/fallback
   */
  static getDefaultCustomerData(): CustomerData {
    return {
      clientMSISDN: '254700000000',
      clientIDNumber: '12345678',
      clientName: 'Test Customer',
      clientEmail: '<EMAIL>',
    }
  }
}

export default CustomerDataService
