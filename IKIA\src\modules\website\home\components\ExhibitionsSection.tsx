'use client'

import { SkewedContainer } from '@/components/ui/SkewedContainer'
import { IkiaActionButtons } from '@/components/ikia-navbar'
import { Play, Handshake } from 'lucide-react'
import { useState } from 'react'
import { exhibitionsButtons } from '../data/buttonConfigs'

export default function ExhibitionsSection() {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false)

  return (
    <section className="py-12 sm:py-16 lg:py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6">
        {/* Section Header */}
        <div className="text-center mb-12 sm:mb-16">
          <div className="flex justify-center mb-4">
            <SkewedContainer variant="outlined" size="sm">
              INVESTMENT OPPORTUNITIES
            </SkewedContainer>
          </div>
          <h3 className="font-myriad font-bold text-2xl sm:text-3xl lg:text-4xl text-black mb-4 sm:mb-6">
            Discover Exhibition Investments
          </h3>
          <p className="font-myriad text-base sm:text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Explore viable investment opportunities in indigenous knowledge assets and traditional
            products through our comprehensive trade fair exhibitions. Connect with innovative
            projects ready for commercial partnership.
          </p>
        </div>

        {/* Video Section */}
        <div className="relative max-w-4xl mx-auto mb-16">
          <div className="relative bg-white/50 rounded-none overflow-hidden border-2 border-[#A0503A]/30 shadow-2xl">
            {/* Video Container */}
            <div className="relative aspect-[4/3] sm:aspect-[16/9] lg:aspect-[21/9] bg-gradient-to-br from-[#7E2518]/20 to-[#E8B32C]/20">
              {!isVideoPlaying ? (
                // Video Thumbnail/Placeholder
                <div className="absolute inset-0 flex flex-col items-center justify-center space-y-6 bg-gradient-to-br from-[#7E2518]/10 to-[#E8B32C]/10">
                  {/* Play Button */}
                  <button
                    onClick={() => setIsVideoPlaying(true)}
                    className="w-24 h-24 bg-white/90 hover:bg-white rounded-full flex items-center justify-center shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 group"
                  >
                    <Play
                      className="w-10 h-10 text-[#7E2518] ml-1 group-hover:text-[#E8B32C] transition-colors"
                      fill="currentColor"
                    />
                  </button>

                  {/* Video Title */}
                  <div className="text-center space-y-2">
                    <h4 className="font-myriad font-bold text-xl text-black">
                      IKIA Investment Showcase
                    </h4>
                    <p className="font-myriad text-gray-700 max-w-md">
                      Watch how indigenous knowledge assets are transforming into sustainable
                      investment opportunities
                    </p>
                  </div>

                  {/* Decorative Elements */}
                  <div className="absolute top-8 left-8 w-16 h-16 bg-[#E8B32C]/20 rounded-full animate-pulse"></div>
                  <div className="absolute bottom-8 right-8 w-12 h-12 bg-[#7E2518]/20 rounded-full animate-bounce-slow"></div>
                  <div className="absolute top-1/3 right-16 w-8 h-8 bg-[#A0503A]/20 rounded-full animate-pulse animation-delay-2000"></div>
                </div>
              ) : (
                // Actual Video
                <video
                  autoPlay
                  controls
                  className="w-full h-full object-cover"
                  onEnded={() => setIsVideoPlaying(false)}
                >
                  <source src="/exhibitions-showcase.mp4" type="video/mp4" />
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                    <p className="font-myriad text-gray-600">
                      Video not available. Please check back later.
                    </p>
                  </div>
                </video>
              )}
            </div>

            {/* Video Caption */}
            <div className="p-6 bg-white border-t border-gray-200">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div>
                  <h5 className="font-myriad font-bold text-lg text-black mb-2">
                    Investment Conference Preview
                  </h5>
                  <p className="font-myriad text-sm text-gray-600">
                    Get an inside look at the investment opportunities and exhibition highlights at
                    the IKIA Investment Conference.
                  </p>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <span className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></span>
                  <span className="font-myriad">Live Preview</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Call-to-Action Buttons */}
        <div className="flex justify-center mt-16">
          <IkiaActionButtons
            buttons={exhibitionsButtons}
            size="large"
            showOnMobile={true}
            layout="horizontal"
            className="gap-6 flex-wrap justify-center"
          />
        </div>
      </div>
    </section>
  )
}
