'use client'

import { useState, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ExternalLink, X } from 'lucide-react'
import FilterSection from './FilterSection'
import { thematicAreas } from '@/modules/website/registration/lib/registration-data'
import { useGetFeaturedIkiaAssetsQuery } from '@/lib/api/ikiaAssetApi'

import { IKIAAsset } from '../types'

const counties = ['MACHAKOS', 'MURANGA', 'TURKANA', 'KAKAMEGA', 'MOMBASA']

const assets = {
  MACHAKOS: [
    {
      id: 1,
      name: 'Machakos Wild Herbal Tea Blend',
      description: 'Traditional tea blend used for digestive health',
      fullDescription:
        'A comprehensive traditional herbal tea blend utilizing indigenous medicinal plants from the Machakos region. This IKIA represents generations of accumulated knowledge about plant-based healing systems, specifically targeting digestive wellness and overall health maintenance.',
      tags: ['Traditional Medicine', 'Plant Systems'],
      category: 'Digestive Health',
      location: 'Machakos County',
      documentedBy: 'Machakos Cultural Heritage Center',
      yearDocumented: '2020',
      investmentPotential: 'High - Ready for commercial scaling',
      thematicArea: 'Traditional Medicine',
      county: 'MACHAKOS',
    },
    {
      id: 2,
      name: 'Kamba Drought-Resistant Crops',
      description: 'Indigenous crop varieties adapted to arid conditions',
      fullDescription:
        'Traditional crop varieties developed by the Kamba community over centuries to withstand drought conditions. These indigenous seeds represent valuable genetic resources with proven resilience to climate change challenges.',
      tags: ['Traditional Medicine', 'Food Systems'],
      category: 'Agricultural Systems',
      location: 'Machakos County',
      documentedBy: 'Kenya Agricultural Research Institute',
      yearDocumented: '2019',
      investmentPotential: 'Medium - Requires further research',
      thematicArea: 'Agricultural Practices',
      county: 'MACHAKOS',
    },
  ],
  MURANGA: [
    {
      id: 3,
      name: 'Kikuyu Coffee Processing',
      description: 'Traditional coffee processing methods',
      fullDescription:
        'Indigenous coffee processing techniques developed by the Kikuyu community, including traditional fermentation and drying methods that enhance coffee quality and flavor profiles. This knowledge system represents sustainable agricultural practices.',
      tags: ['Traditional Medicine', 'Food Systems'],
      category: 'Food Processing',
      location: 'Muranga County',
      documentedBy: 'Coffee Research Institute',
      yearDocumented: '2021',
      investmentPotential: 'High - Export market ready',
      thematicArea: 'Agricultural Practices',
      county: 'MURANGA',
    },
  ],
  TURKANA: [
    {
      id: 4,
      name: 'Pastoralist Water Management',
      description: 'Indigenous water harvesting and management',
      fullDescription:
        'Traditional water management systems developed by pastoralist communities in arid regions. These techniques include water harvesting, storage, and conservation methods adapted to harsh climatic conditions.',
      tags: ['Traditional Medicine', 'Water Systems'],
      category: 'Water Management',
      location: 'Turkana County',
      documentedBy: 'Arid Lands Research Institute',
      yearDocumented: '2018',
      investmentPotential: 'Medium - Climate adaptation focus',
      thematicArea: 'Environmental Conservation',
      county: 'TURKANA',
    },
  ],
  KAKAMEGA: [
    {
      id: 5,
      name: 'Luhya Medicinal Plants',
      description: 'Forest-based traditional medicine knowledge',
      fullDescription:
        'Comprehensive knowledge system of medicinal plants from the Kakamega Forest ecosystem. This IKIA includes identification, preparation, and application methods for various forest-based remedies.',
      tags: ['Traditional Medicine', 'Plant Systems'],
      category: 'Medicinal Plants',
      location: 'Kakamega County',
      documentedBy: 'Kenya Forestry Research Institute',
      yearDocumented: '2020',
      investmentPotential: 'High - Pharmaceutical potential',
      thematicArea: 'Traditional Medicine',
      county: 'KAKAMEGA',
    },
  ],
  MOMBASA: [
    {
      id: 6,
      name: 'Coastal Salt Production',
      description: 'Traditional sea salt harvesting methods',
      fullDescription:
        'Traditional salt production techniques practiced by coastal communities, including solar evaporation methods and quality enhancement processes that produce premium sea salt products.',
      tags: ['Traditional Medicine', 'Food Systems'],
      category: 'Food Systems',
      location: 'Mombasa County',
      documentedBy: 'Coastal Development Authority',
      yearDocumented: '2019',
      investmentPotential: 'Medium - Niche market opportunity',
      thematicArea: 'Traditional Knowledge Systems',
      county: 'MOMBASA',
    },
  ],
}

export default function AssetsSection() {
  const [selectedAsset, setSelectedAsset] = useState<IKIAAsset | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCountyFilter, setSelectedCountyFilter] = useState('all')
  const [selectedThematicArea, setSelectedThematicArea] = useState('all')

  const { data, error, isLoading } = useGetFeaturedIkiaAssetsQuery()

  const openModal = (asset: IKIAAsset) => {
    setSelectedAsset(asset)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setSelectedAsset(null)
    setIsModalOpen(false)
  }

  // Flatten all assets for filtering
  const allAssets = useMemo(() => {
    return Object.values(assets).flat()
  }, [])

  // Filter assets based on search and filters
  const filteredAssets = useMemo(() => {
    return allAssets.filter((asset) => {
      const matchesSearch =
        asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        asset.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        asset.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))

      const matchesCounty = selectedCountyFilter === 'all' || asset.county === selectedCountyFilter
      const matchesThematicArea =
        selectedThematicArea === 'all' || asset.thematicArea === selectedThematicArea

      return matchesSearch && matchesCounty && matchesThematicArea
    })
  }, [allAssets, searchTerm, selectedCountyFilter, selectedThematicArea])

  // Get active filters count
  const activeFiltersCount = useMemo(() => {
    let count = 0
    if (searchTerm) count++
    if (selectedCountyFilter !== 'all') count++
    if (selectedThematicArea !== 'all') count++
    return count
  }, [searchTerm, selectedCountyFilter, selectedThematicArea])

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('')
    setSelectedCountyFilter('all')
    setSelectedThematicArea('all')
  }

  // Determine which assets to show
  const displayAssets = activeFiltersCount > 0 ? filteredAssets : allAssets

  return (
    <section className="py-16 bg-white" id="exhibitions-section">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#7E2518] mb-4">
            EXPLORE ALL IKIA ASSETS
            <br />
            BY COUNTIES
          </h2>
        </div>

        {/* Filter Section */}
        <FilterSection
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          selectedCounty={selectedCountyFilter}
          onCountyChange={setSelectedCountyFilter}
          selectedThematicArea={selectedThematicArea}
          onThematicAreaChange={setSelectedThematicArea}
          counties={counties}
          thematicAreas={thematicAreas}
          onClearFilters={clearFilters}
          activeFiltersCount={activeFiltersCount}
        />

        {/* Results Summary */}
        {activeFiltersCount > 0 && (
          <div className="text-center mb-6">
            <p className="text-gray-600">
              Showing {displayAssets.length} asset{displayAssets.length !== 1 ? 's' : ''}
              {searchTerm && ` matching "${searchTerm}"`}
            </p>
          </div>
        )}

        {/* Asset Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {isLoading ? (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-500 text-lg">Loading featured IKIA assets...</p>
            </div>
          ) : error ? (
            <div className="col-span-full text-center py-12">
              <p className="text-red-500 text-lg">Failed to load assets. Please try again later.</p>
            </div>
          ) : data?.docs?.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-500 text-lg">No featured assets available.</p>
            </div>
          ) : (
            data.docs.map((asset: IKIAAsset) => (
              <Card
                key={asset.id}
                className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer rounded-none"
                onClick={() => openModal(asset)}
              >
                <div className="h-32 sm:h-36 bg-gray-200 flex items-center justify-center overflow-hidden">
                  <span className="text-gray-500">Asset Image</span>
                </div>
                <CardContent className="p-6">
                  <div className="mb-4">
                    <h3 className="text-xl font-bold text-[#7E2518] mb-2">{asset.title}</h3>
                    <p className="text-gray-600 mb-3 line-clamp-2">{asset.description}</p>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {asset.tags?.map((tag: { id: string; name: string }, tagIndex: number) => (
                      <Badge
                        key={tagIndex}
                        variant="secondary"
                        className="bg-[#E8B32C] text-[#7E2518] rounded-none"
                      >
                        {tag.name}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center justify-between">
                    <Badge className="bg-[#159147] text-white rounded-none">
                      {asset.categories?.[0]?.name || 'Uncategorized'}
                    </Badge>
                    <Button
                      size="sm"
                      className="bg-[#7E2518] hover:bg-[#6B1F14] rounded-none"
                      onClick={(e) => {
                        e.stopPropagation()
                        openModal(asset)
                      }}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>

      {/* Modal */}
      {isModalOpen && selectedAsset && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white max-w-2xl w-full max-h-[90vh] overflow-y-auto rounded-none">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex justify-between items-start mb-6">
                <h2 className="text-2xl font-bold text-[#7E2518]">{selectedAsset.title}</h2>
                <button onClick={closeModal} className="text-gray-500 hover:text-gray-700 p-1">
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* Modal Content */}
              <div className="space-y-6">
                {/* Image */}
                <div className="h-64 bg-gray-200 flex items-center justify-center rounded-none">
                  <span className="text-gray-500">Asset Image</span>
                </div>

                {/* Description */}
                <div>
                  <h3 className="text-lg font-semibold text-[#7E2518] mb-2">Description</h3>
                  <p className="text-gray-700">{selectedAsset.description}</p>
                </div>

                {/* Details Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold text-[#7E2518] mb-1">Location</h4>
                    <p className="text-gray-600">{selectedAsset.location}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-[#7E2518] mb-1">Year Documented</h4>
                    <p className="text-gray-600">{selectedAsset.yearDocumented}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-[#7E2518] mb-1">Documented By</h4>
                    <p className="text-gray-600">{selectedAsset.documentedBy}</p>
                  </div>
                  <div>
                    {/* Category */}
                    <div>
                      <h4 className="font-semibold text-[#7E2518] mb-1">Categories</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedAsset.categories.map((cat, index) => (
                          <Badge key={index} className="bg-[#159147] text-white rounded-none">
                            {cat.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Investment Potential */}
                <div>
                  <h4 className="font-semibold text-[#7E2518] mb-2">Investment Potential</h4>
                  <p className="text-gray-700 bg-gray-50 p-3 rounded-none border-l-4 border-[#7E2518]">
                    {selectedAsset.investmentPotential}
                  </p>
                </div>

                {/* Tags */}
                <div>
                  {/* Tags */}
                  <div>
                    <h4 className="font-semibold text-[#7E2518] mb-2">Tags</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedAsset.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="bg-[#E8B32C] text-[#7E2518] rounded-none"
                        >
                          {tag.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-4 pt-4">
                  <Button className="bg-[#7E2518] hover:bg-[#6B1F14] rounded-none flex-1">
                    Request Investment Info
                  </Button>
                  <Button
                    variant="outline"
                    className="border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white rounded-none flex-1"
                    onClick={closeModal}
                  >
                    Close
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  )
}
