'use client'

import { useState, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ExternalLink, X } from 'lucide-react'
import FilterSection from './FilterSection'
import { thematicAreas } from '@/modules/website/registration/lib/registration-data'

const counties = ['MACHAKOS', 'MURANGA', 'TURKANA', 'KAKAMEGA', 'MOMBASA']

const exhibitors = {
  MACHAKOS: [
    {
      id: 1,
      name: 'Machakos Wild Herbal Tea Blend',
      description: 'Traditional tea blend used for digestive health',
      fullDescription:
        'A carefully crafted herbal tea blend using indigenous plants from the Machakos region. This traditional remedy has been used for generations to support digestive health and overall wellness. The blend includes locally sourced herbs known for their therapeutic properties.',
      tags: ['Traditional Medicine', 'Plant Systems'],
      category: 'Digestive Health',
      thematicArea: 'Traditional Medicine',
      county: 'MACHAKOS',
      image: '/api/placeholder/300/200',
      contact: '<EMAIL>',
      location: 'Machakos County',
      established: '2018',
    },
    {
      id: 2,
      name: 'Kamba Traditional Basketry',
      description: 'Handwoven baskets using indigenous techniques',
      fullDescription:
        'Authentic Kamba basketry crafted using traditional weaving techniques passed down through generations. Each basket is handmade using locally sourced materials and represents the rich cultural heritage of the Kamba people.',
      tags: ['Traditional Crafts', 'Cultural Heritage'],
      category: 'Handicrafts',
      thematicArea: 'Cultural Heritage Preservation',
      county: 'MACHAKOS',
      image: '/api/placeholder/300/200',
      contact: '<EMAIL>',
      location: 'Machakos County',
      established: '2015',
    },
  ],
  MURANGA: [
    {
      id: 3,
      name: 'Kikuyu Traditional Honey',
      description: 'Pure honey from indigenous beekeeping practices',
      tags: ['Traditional Medicine', 'Food Systems'],
      category: 'Digestive Health',
      thematicArea: 'Traditional Medicine',
      county: 'MURANGA',
      image: '/api/placeholder/300/200',
    },
  ],
  TURKANA: [
    {
      id: 4,
      name: 'Turkana Livestock Medicine',
      description: 'Traditional veterinary practices for livestock',
      tags: ['Traditional Medicine', 'Livestock'],
      category: 'Animal Health',
      thematicArea: 'Traditional Medicine',
      county: 'TURKANA',
      image: '/api/placeholder/300/200',
    },
  ],
  KAKAMEGA: [
    {
      id: 5,
      name: 'Luhya Forest Conservation',
      description: 'Indigenous forest management techniques',
      tags: ['Environmental', 'Conservation'],
      category: 'Forest Management',
      thematicArea: 'Environmental Conservation',
      county: 'KAKAMEGA',
      image: '/api/placeholder/300/200',
    },
  ],
  MOMBASA: [
    {
      id: 6,
      name: 'Swahili Coastal Fishing',
      description: 'Traditional coastal fishing methods and tools',
      tags: ['Traditional Practices', 'Marine'],
      category: 'Fisheries',
      thematicArea: 'Traditional Knowledge Systems',
      county: 'MOMBASA',
      image: '/api/placeholder/300/200',
    },
  ],
}

export default function ExhibitorsSection() {
  const [activeCounty, setActiveCounty] = useState('MACHAKOS')
  const [selectedExhibitor, setSelectedExhibitor] = useState<
    (typeof exhibitors.MACHAKOS)[0] | null
  >(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCountyFilter, setSelectedCountyFilter] = useState('all')
  const [selectedThematicArea, setSelectedThematicArea] = useState('all')

  const openModal = (exhibitor) => {
    setSelectedExhibitor(exhibitor)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setSelectedExhibitor(null)
    setIsModalOpen(false)
  }

  // Flatten all exhibitors for filtering
  const allExhibitors = useMemo(() => {
    return Object.values(exhibitors).flat()
  }, [])

  // Filter exhibitors based on search and filters
  const filteredExhibitors = useMemo(() => {
    return allExhibitors.filter((exhibitor) => {
      const matchesSearch =
        exhibitor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exhibitor.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exhibitor.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))

      const matchesCounty =
        selectedCountyFilter === 'all' || exhibitor.county === selectedCountyFilter
      const matchesThematicArea =
        selectedThematicArea === 'all' || exhibitor.thematicArea === selectedThematicArea

      return matchesSearch && matchesCounty && matchesThematicArea
    })
  }, [allExhibitors, searchTerm, selectedCountyFilter, selectedThematicArea])

  // Get active filters count
  const activeFiltersCount = useMemo(() => {
    let count = 0
    if (searchTerm) count++
    if (selectedCountyFilter !== 'all') count++
    if (selectedThematicArea !== 'all') count++
    return count
  }, [searchTerm, selectedCountyFilter, selectedThematicArea])

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('')
    setSelectedCountyFilter('all')
    setSelectedThematicArea('all')
  }

  // Determine which exhibitors to show
  const displayExhibitors = activeFiltersCount > 0 ? filteredExhibitors : allExhibitors

  return (
    <section className="py-16 bg-gray-50" id="exhibitors-section">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#7E2518] mb-4">
            EXPLORE ALL EXHIBITORS
            <br />
            BY COUNTIES
          </h2>
        </div>

        {/* Filter Section */}
        <FilterSection
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          selectedCounty={selectedCountyFilter}
          onCountyChange={setSelectedCountyFilter}
          selectedThematicArea={selectedThematicArea}
          onThematicAreaChange={setSelectedThematicArea}
          counties={counties}
          thematicAreas={thematicAreas}
          onClearFilters={clearFilters}
          activeFiltersCount={activeFiltersCount}
        />

        {/* Results Summary */}
        {activeFiltersCount > 0 && (
          <div className="text-center mb-6">
            <p className="text-gray-600">
              Showing {displayExhibitors.length} exhibitor
              {displayExhibitors.length !== 1 ? 's' : ''}
              {searchTerm && ` matching "${searchTerm}"`}
            </p>
          </div>
        )}

        {/* Exhibitor Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayExhibitors.length > 0 ? (
            displayExhibitors.map((exhibitor) => (
              <Card
                key={exhibitor.id}
                className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer rounded-none"
                onClick={() => openModal(exhibitor)}
              >
                <div className="h-32 sm:h-36 bg-gray-200 flex items-center justify-center overflow-hidden">
                  <span className="text-gray-500">Exhibitor Image</span>
                </div>
                <CardContent className="p-6">
                  <div className="mb-4">
                    <h3 className="text-xl font-bold text-[#7E2518] mb-2">{exhibitor.name}</h3>
                    <p className="text-gray-600 mb-3">{exhibitor.description}</p>
                    {activeFiltersCount > 0 && (
                      <p className="text-sm text-gray-500 mb-2">
                        <span className="font-medium">County:</span> {exhibitor.county} |
                        <span className="font-medium"> Theme:</span> {exhibitor.thematicArea}
                      </p>
                    )}
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {exhibitor.tags.map((tag, tagIndex) => (
                      <Badge
                        key={tagIndex}
                        variant="secondary"
                        className="bg-[#E8B32C] text-[#7E2518] rounded-none"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center justify-between">
                    <Badge className="bg-[#159147] text-white rounded-none">
                      {exhibitor.category}
                    </Badge>
                    <Button
                      size="sm"
                      className="bg-[#7E2518] hover:bg-[#6B1F14] rounded-none"
                      onClick={(e) => {
                        e.stopPropagation()
                        openModal(exhibitor)
                      }}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-500 text-lg">No exhibitors found matching your criteria.</p>
              <Button
                variant="outline"
                onClick={clearFilters}
                className="mt-4 border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white"
              >
                Clear Filters
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Modal */}
      {isModalOpen && selectedExhibitor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white max-w-2xl w-full max-h-[90vh] overflow-y-auto rounded-none">
            <div className="p-6">
              {/* Modal Header */}
              <div className="flex justify-between items-start mb-6">
                <h2 className="text-2xl font-bold text-[#7E2518]">{selectedExhibitor.name}</h2>
                <button onClick={closeModal} className="text-gray-500 hover:text-gray-700 p-1">
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* Modal Content */}
              <div className="space-y-6">
                {/* Image */}
                <div className="h-64 bg-gray-200 flex items-center justify-center rounded-none">
                  <span className="text-gray-500">Exhibitor Image</span>
                </div>

                {/* Description */}
                <div>
                  <h3 className="text-lg font-semibold text-[#7E2518] mb-2">Description</h3>
                  <p className="text-gray-700">{selectedExhibitor.fullDescription}</p>
                </div>

                {/* Details Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold text-[#7E2518] mb-1">Location</h4>
                    <p className="text-gray-600">{selectedExhibitor.location}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-[#7E2518] mb-1">Established</h4>
                    <p className="text-gray-600">{selectedExhibitor.established}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-[#7E2518] mb-1">Contact</h4>
                    <p className="text-gray-600">{selectedExhibitor.contact}</p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-[#7E2518] mb-1">Category</h4>
                    <Badge className="bg-[#159147] text-white rounded-none">
                      {selectedExhibitor.category}
                    </Badge>
                  </div>
                </div>

                {/* Tags */}
                <div>
                  <h4 className="font-semibold text-[#7E2518] mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedExhibitor.tags.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="bg-[#E8B32C] text-[#7E2518] rounded-none"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-4 pt-4">
                  <Button className="bg-[#7E2518] hover:bg-[#6B1F14] rounded-none flex-1">
                    Contact Exhibitor
                  </Button>
                  <Button
                    variant="outline"
                    className="border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white rounded-none flex-1"
                    onClick={closeModal}
                  >
                    Close
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  )
}
