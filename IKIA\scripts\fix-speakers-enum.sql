-- Fix speakers category enum type mismatch
-- This script resolves the enum type conflict in the speakers table

-- First, check current enum values
SELECT enumlabel FROM pg_enum WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'enum_speakers_category');

-- Drop the enum constraint temporarily
ALTER TABLE speakers ALTER COLUMN category TYPE text;

-- Drop the old enum type
DROP TYPE IF EXISTS enum_speakers_category;

-- Create the new enum type with correct values
CREATE TYPE enum_speakers_category AS ENUM ('keynote', 'plenary', 'other');

-- Update the column to use the new enum type
ALTER TABLE speakers ALTER COLUMN category TYPE enum_speakers_category USING category::enum_speakers_category;

-- Verify the fix
SELECT enumlabel FROM pg_enum WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'enum_speakers_category');
