'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { ArrowUpRight, TrendingUp, Handshake, ArrowRight, LucideIcon } from 'lucide-react'

interface ActionButton {
  text: string
  href: string
  variant?: 'default' | 'outline' | 'green'
  icon?: string | LucideIcon
  iconPosition?: 'left' | 'right'
}

interface IkiaActionButtonsProps {
  className?: string
  isMobile?: boolean
  buttons?: ActionButton[]
  size?: 'small' | 'large'
  showOnMobile?: boolean
  layout?: 'horizontal' | 'vertical'
  fullWidth?: boolean
}

const actionButtons: ActionButton[] = [
  {
    text: 'REGISTRATION',
    href: '/registration',
    variant: 'default',
    icon: ArrowUpRight,
  },
  {
    text: 'SPONSORS',
    href: '/sponsors',
    variant: 'green',
  },
]

export const IkiaActionButtons: React.FC<IkiaActionButtonsProps> = ({
  className = '',
  isMobile = false,
  buttons,
  size = 'small',
  showOnMobile = false,
  layout = 'horizontal',
  fullWidth = false,
}) => {
  const getBaseClasses = () => {
    if (isMobile) {
      return 'flex flex-col space-y-3 w-full'
    }

    if (showOnMobile) {
      return layout === 'vertical'
        ? `flex flex-col gap-3 ${fullWidth ? 'w-full' : 'items-center'}`
        : 'flex flex-col sm:flex-row gap-3 items-center'
    }

    return layout === 'vertical'
      ? `hidden lg:flex flex-col space-y-3 ${fullWidth ? 'w-full' : 'items-center'}`
      : 'hidden lg:flex items-center space-x-3'
  }

  const baseClasses = getBaseClasses()

  // Icon mapping for string-based icons
  const iconMap: Record<string, LucideIcon> = {
    ArrowUpRight,
    ArrowRight,
    TrendingUp,
    Handshake,
  }

  const getIconComponent = (icon: string | LucideIcon | undefined): LucideIcon => {
    if (!icon) return ArrowUpRight
    if (typeof icon === 'string') {
      return iconMap[icon] || ArrowUpRight
    }
    return icon
  }

  const getButtonClasses = (variant: ActionButton['variant'], isMobile: boolean) => {
    const isLarge = size === 'large'
    const baseButtonClasses = `
      font-myriad font-medium transition-all duration-300
      shadow-md hover:shadow-lg transform hover:-translate-y-0.5 rounded-none
      ${isLarge ? 'text-xs px-8 py-4' : 'text-xs px-6 py-2'}
      ${(isMobile || fullWidth) && !isLarge ? 'w-full px-6 py-3' : ''}
      ${(isMobile || fullWidth) && isLarge ? 'w-full px-8 py-4' : ''}
    `

    if (variant === 'outline') {
      return `${baseButtonClasses}
        bg-transparent border-2 border-primary text-primary
        hover:bg-primary hover:text-primary-foreground`
    }

    if (variant === 'green') {
      return `${baseButtonClasses}
        bg-secondary hover:bg-secondary/90 text-secondary-foreground`
    }

    return `${baseButtonClasses}
      bg-primary hover:bg-primary/90 text-primary-foreground`
  }

  const buttonsToRender = buttons || actionButtons

  return (
    <div className={`${baseClasses} ${className}`}>
      {buttonsToRender.map((button) => {
        const iconPosition = button.iconPosition || 'right'
        const showIcon = button.icon || size === 'large'
        const IconComponent = getIconComponent(button.icon)

        return (
          <Link key={button.text} href={button.href}>
            <Button className={getButtonClasses(button.variant, isMobile)}>
              <span className="flex items-center gap-2">
                {showIcon && iconPosition === 'left' && (
                  <IconComponent className={size === 'large' ? 'h-5 w-5' : 'h-4 w-4'} />
                )}
                {button.text}
                {showIcon && iconPosition === 'right' && (
                  <IconComponent className={size === 'large' ? 'h-5 w-5' : 'h-4 w-4'} />
                )}
              </span>
            </Button>
          </Link>
        )
      })}
    </div>
  )
}
