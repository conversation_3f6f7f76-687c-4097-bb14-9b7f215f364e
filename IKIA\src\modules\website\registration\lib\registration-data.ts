// Shared data structures for registration system

export interface PackageOption {
  id: string
  name: string
  price: string
  duration?: string
  description: string
  features: string[]
  popular?: boolean
  category: 'delegate' | 'vip' | 'sponsor' | 'exhibitor'
}

export interface SponsorshipTier {
  id: string
  name: string
  price: string
  benefits: string[]
  delegateTickets: number
  vipTickets: number
  category: 'main' | 'special'
  description?: string
}

export interface RegistrationFormData {
  // Personal Information
  title?: string
  firstName: string
  lastName: string
  email: string
  phone: string
  organization?: string
  position?: string
  country: string
  city: string
  address?: string

  // Package/Tier Selection
  selectedPackage?: string
  selectedTier?: string

  // Group Registration
  isGroupRegistration?: boolean
  groupMembers?: RegistrationFormData[]

  // Additional fields based on registration type
  [key: string]: any
}

export interface QRCodeData {
  registrationId: string
  name: string
  email: string
  type: string
  package?: string
  tier?: string
  timestamp: string
}

// Delegate Packages
export const delegatePackages: PackageOption[] = [
  {
    id: '3-day-delegate',
    name: '3-Day Delegate Ticket',
    price: '15000',
    duration: '3 days',
    description: 'Full access to all sessions, conference materials, tea breaks, lunch, and a gift pack',
    features: [
      'Full access to all sessions',
      'Conference materials',
      'Tea breaks and lunch',
      'Gift pack (branded tote bag, conference program booklet, branded notebook & pen, cultural souvenir item)',
      'Networking opportunities',
      'Certificate of attendance'
    ],
    popular: true,
    category: 'delegate'
  },
  {
    id: 'daily-delegate',
    name: 'Daily Delegate Ticket',
    price: '8000',
    duration: '1 day',
    description: 'Covers delegate access to all conference sessions, site visits, exhibition area, gift hamper, meals, and refreshments for 1 day',
    features: [
      'Access to all conference sessions',
      'Site visits',
      'Exhibition area access',
      'Gift hamper',
      'Meals and refreshments',
      'Networking opportunities'
    ],
    category: 'delegate'
  },
  {
    id: '3-day-student',
    name: '3-Day Student Ticket',
    price: '1000',
    duration: '3 days',
    description: 'Covers delegate access to all conference sessions, site visits, and the exhibition area for 3 days',
    features: [
      'Access to all conference sessions',
      'Site visits',
      'Exhibition area access',
      'Student networking sessions',
      'Certificate of attendance',
      'Requires student ID for accreditation'
    ],
    category: 'delegate'
  },
  {
    id: 'daily-student',
    name: 'Daily Student Ticket',
    price: '500',
    duration: '1 day',
    description: 'Covers delegate access to all conference sessions and the exhibition area for 1 day',
    features: [
      'Access to conference sessions',
      'Exhibition area access',
      'Student networking',
      'Requires student ID for accreditation'
    ],
    category: 'delegate'
  }
]

// VIP Package (Fixed price)
export const vipPackage: PackageOption = {
  id: 'vip',
  name: 'VIP Delegate Ticket',
  price: '30000',
  duration: '3 days',
  description: 'Covers access to Dealrooms, VIP Holding Areas, Gala Dinner, all conference sessions, site visits, exhibition area, gift hamper, meals, and refreshments for 3 days',
  features: [
    'Access to Dealrooms',
    'VIP Holding Areas',
    'Gala Dinner',
    'All conference sessions',
    'Site visits',
    'Exhibition area access',
    'Gift hamper',
    'Meals and refreshments',
    'Priority registration desk',
    'VIP car badge',
    'Premium gift pack (branded jute bag, conference program booklet, branded notebook & pen, cultural souvenir item, flash drive with conference materials)',
    'VIP certificate'
  ],
  category: 'vip'
}

// Sponsorship Tiers
export const sponsorshipTiers: SponsorshipTier[] = [
  {
    id: 'title',
    name: 'Title Sponsor',
    price: '10000000',
    benefits: [
      'Headline branding on all conference materials',
      'Opportunity to address plenary session',
      'Dedicated branded booth (9m x 3m)',
      'Prominent logo placement',
      'Invitations to VIP networking',
      'Fifteen complimentary delegate passes'
    ],
    delegateTickets: 15,
    vipTickets: 15,
    category: 'main',
    description: '1 Partner'
  },
  {
    id: 'platinum',
    name: 'Platinum Package',
    price: '5000000',
    benefits: [
      'Logo prominently displayed on major materials',
      'Opportunity for remarks during a session',
      'Branded booth (6m x 3m)',
      'Invitation to VIP networking',
      'Ten complimentary delegate passes'
    ],
    delegateTickets: 10,
    vipTickets: 0,
    category: 'main',
    description: '3 Partners'
  },
  {
    id: 'gold',
    name: 'Gold Package',
    price: '3000000',
    benefits: [
      'Logo displayed on website, banners, and brochures',
      'Standard exhibition booth (3m x 3m)',
      'Invitation to networking sessions',
      'Five complimentary delegate passes'
    ],
    delegateTickets: 5,
    vipTickets: 0,
    category: 'main',
    description: '5 Partners'
  },
  {
    id: 'silver',
    name: 'Silver Package',
    price: '1000000',
    benefits: [
      'Logo displayed on website and selected banners',
      'Standard exhibition booth (3m x 3m)',
      'Invitation to networking sessions',
      'Three complimentary delegate passes'
    ],
    delegateTickets: 3,
    vipTickets: 0,
    category: 'main',
    description: '10 Partners'
  },
  {
    id: 'bronze',
    name: 'Bronze Package',
    price: '500000',
    benefits: [
      'Logo displayed on the website\'s sponsor page',
      'Standard exhibition booth (3m x 3m)',
      'Invitation to networking sessions',
      'Two complimentary delegate passes'
    ],
    delegateTickets: 2,
    vipTickets: 0,
    category: 'main',
    description: '15 Partners'
  }
]

// Special Sponsorship Packages
export const specialSponsorshipTiers: SponsorshipTier[] = [
  {
    id: 'media-partner',
    name: 'Media Partner Sponsorship Package',
    price: '0',
    benefits: [
      'Brand visibility',
      'On-site presence with complimentary exhibition booth',
      'Exclusive interviews',
      'Co-branded content',
      'Pre-event promotion',
      'Real-time event coverage',
      'Post-event feature'
    ],
    delegateTickets: 2,
    vipTickets: 0,
    category: 'special',
    description: 'Designed for media organizations providing coverage and promotional support'
  },
  {
    id: 'networking-reception',
    name: 'Networking Reception Package',
    price: '6000000',
    benefits: [
      'Exclusive branding for Host Governor\'s Dinner Cocktail Reception',
      'In-kind contribution for beverages and catering',
      'Canapés and light snacks provision',
      'Set up/service staff',
      'High-level networking opportunities',
      'Ten complimentary delegate passes',
      'Digital recognition'
    ],
    delegateTickets: 10,
    vipTickets: 0,
    category: 'special',
    description: '1 Partner | November 19th, 2025'
  },
  {
    id: 'merchandise-sponsor',
    name: 'Merchandise Sponsor',
    price: '3000000',
    benefits: [
      'Provide branded conference merchandise for delegates',
      'Co-branding on bags, notebooks, pens, water bottles, small gifts',
      'Recognition as "Official Merchandise Partner"',
      'Tangible brand presence through merchandise',
      'Logo on all branded items',
      'Two complimentary delegate passes'
    ],
    delegateTickets: 2,
    vipTickets: 0,
    category: 'special',
    description: '1 Partner'
  },
  {
    id: 'internet-sponsor',
    name: 'Internet Sponsor',
    price: '1000000',
    benefits: [
      'Provide reliable and high-speed internet access (Wi-Fi)',
      'Coverage for all attendees, exhibitors, and organizers',
      'Service from November 18th to 21st, 2025',
      'Prominent signage as "Official Connectivity Sponsor"',
      'Enhanced attendee experience',
      'Two complimentary delegate passes for technical team'
    ],
    delegateTickets: 2,
    vipTickets: 0,
    category: 'special',
    description: '1 Partner | Thika Green Golf Resort'
  }
]

// All sponsorship tiers combined
export const allSponsorshipTiers = [...sponsorshipTiers, ...specialSponsorshipTiers]

// Exhibition Packages
export const exhibitionPackages: PackageOption[] = [
  {
    id: 'exhibitor',
    name: 'Exhibitor Package',
    price: '70000',
    duration: '3 days',
    description: 'Covers a standard 3x3m booth, dealrooms, delegate pass for 2 pax for 3 days, access to all conference sessions, site visits, gift hamper, dinner, meals, and refreshments',
    features: [
      'Standard 3x3m booth',
      'Dealrooms access',
      'Delegate pass for 2 people for 3 days',
      'Access to all conference sessions',
      'Site visits',
      'Gift hamper',
      'Dinner included',
      'Meals and refreshments',
      'Networking opportunities',
      'Media coverage',
      'Certificate of Participation'
    ],
    popular: true,
    category: 'exhibitor'
  }
]

// Alias for backward compatibility
export const exhibitorPackages = exhibitionPackages

// Booth requirements for exhibitors
export const boothRequirements = [
  "Standard 3x3m booth",
  "Corner booth (additional cost)",
  "Double booth 6x3m (additional cost)",
  "Premium location (additional cost)",
  "Custom booth design (additional cost)"
]

// Additional services for exhibitors
export const additionalServices = [
  "Audio/Visual equipment",
  "Additional furniture",
  "Electrical connections",
  "Internet connectivity",
  "Storage space",
  "Promotional materials display",
  "Lead capture system",
  "Security services"
]

// Investment Focus Areas
export const investmentFocusAreas = [
  'Technology & Innovation',
  'Healthcare & Biotechnology',
  'Financial Services',
  'Agriculture & Food Security',
  'Renewable Energy',
  'Infrastructure Development',
  'Education Technology',
  'Manufacturing',
  'Real Estate',
  'Tourism & Hospitality',
  'Retail & E-commerce',
  'Transportation & Logistics'
]

// Investment Stages
export const investmentStages = [
  'Pre-seed',
  'Seed',
  'Series A',
  'Series B',
  'Series C+',
  'Growth Stage',
  'Private Equity',
  'Public Markets'
]

// Investment Ticket Sizes
export const investmentTicketSizes = [
  'Under $50K',
  '$50K - $250K',
  '$250K - $1M',
  '$1M - $5M',
  '$5M - $25M',
  '$25M - $100M',
  'Over $100M'
]

// Countries list (commonly used in Kenya/East Africa)
export const countries = [
  'Kenya',
  'Uganda',
  'Tanzania',
  'Rwanda',
  'Burundi',
  'South Sudan',
  'Ethiopia',
  'Somalia',
  'Democratic Republic of Congo',
  'Nigeria',
  'Ghana',
  'South Africa',
  'Egypt',
  'Morocco',
  'United States',
  'United Kingdom',
  'Germany',
  'France',
  'China',
  'India',
  'Other'
]

// Thematic areas for conference
export const thematicAreas = [
  'Traditional Foods & Nutrition',
  'Local Remedies & Traditional Medicine',
  'Musicology & Cultural Arts',
  'Cultural Tourism & Heritage',
  'Traditional Crafts & Artisanship',
  'Indigenous Knowledge Systems',
  'Environmental Conservation',
  'Agricultural Practices',
  'Community Development',
  'Education & Research',
  'Technology & Innovation',
  'Policy & Governance'
]
