import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

// Types for Thematic Areas
export interface ThematicArea {
  id: number
  name: string
  description?: string
  image?: {
    id: number
    url: string
    alt?: string
    width?: number
    height?: number
  }
  color?: string
  slug?: string
  isActive: boolean
  updatedAt: string
  createdAt: string
}

export interface ThematicAreasResponse {
  docs: ThematicArea[]
  totalDocs: number
  limit: number
  totalPages: number
  page: number
  pagingCounter: number
  hasPrevPage: boolean
  hasNextPage: boolean
  prevPage?: number | null
  nextPage?: number | null
}

export interface ThematicAreasQueryParams {
  limit?: number
  page?: number
  sort?: string
  where?: {
    isActive?: {
      equals?: boolean
    }
    name?: {
      contains?: string
    }
  }
}

export const thematicAreasApi = createApi({
  reducerPath: 'thematicAreasApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api/' }),
  tagTypes: ['ThematicArea'],
  endpoints: (builder) => ({
    // Get all thematic areas with optional filtering
    getThematicAreas: builder.query<ThematicAreasResponse, ThematicAreasQueryParams | void>({
      query: (params) => {
        const searchParams = new URLSearchParams()

        // Handle the case where params might be void
        if (!params) {
          return 'thematic-areas?where[isActive][equals]=true&limit=0&sort=name'
        }

        // Add basic params
        if (params.limit) searchParams.append('limit', params.limit.toString())
        if (params.page) searchParams.append('page', params.page.toString())
        if (params.sort) searchParams.append('sort', params.sort)

        // Add where conditions directly as URL parameters
        if (params.where) {
          // Handle active filter
          if (params.where.isActive?.equals !== undefined) {
            searchParams.append('where[isActive][equals]', params.where.isActive.equals.toString())
          }

          // Handle name search
          if (params.where.name?.contains) {
            searchParams.append('where[name][contains]', params.where.name.contains)
          }
        }

        return `thematic-areas?${searchParams.toString()}`
      },
      providesTags: ['ThematicArea'],
    }),

    // Get active thematic areas only (for dropdowns)
    getActiveThematicAreas: builder.query<ThematicAreasResponse, { limit?: number }>({
      query: ({ limit = 0 } = {}) =>
        `thematic-areas?where[isActive][equals]=true&limit=${limit}&sort=name`,
      providesTags: ['ThematicArea'],
    }),

    // Get single thematic area by ID
    getThematicArea: builder.query<ThematicArea, string | number>({
      query: (id) => `thematic-areas/${id}`,
      providesTags: ['ThematicArea'],
    }),

    // Get single thematic area by slug
    getThematicAreaBySlug: builder.query<ThematicArea, string>({
      query: (slug) => `thematic-areas?where[slug][equals]=${slug}&limit=1`,
      transformResponse: (response: ThematicAreasResponse) => response.docs[0],
      providesTags: ['ThematicArea'],
    }),
  }),
})

export const {
  useGetThematicAreasQuery,
  useGetActiveThematicAreasQuery,
  useGetThematicAreaQuery,
  useGetThematicAreaBySlugQuery,
} = thematicAreasApi
