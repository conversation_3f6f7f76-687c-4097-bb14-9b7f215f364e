module.exports = {
  apps: [
    {
      name: 'ikia-conference',
      script: './server.js',
      // Alternative: Use Next.js directly
      // script: './node_modules/.bin/next',
      // args: 'start',
      cwd: '/home/<USER>/public_html',
      instances: 'max', // Use all available CPU cores (8 instances)
      // Alternative options:
      // instances: 4,     // Fixed number of instances
      // instances: 1,     // Single instance (development)
      // instances: 'max', // All CPU cores (production - current setting)
      exec_mode: 'cluster', // Enable cluster mode for better performance
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      min_uptime: '10s', // Minimum uptime before considering the app stable
      max_restarts: 10, // Maximum number of restarts within restart_delay
      restart_delay: 4000, // Delay between restarts
      kill_timeout: 5000, // Time to wait before force killing
      listen_timeout: 3000, // Time to wait for app to listen
      wait_ready: false, // Next.js doesn't emit ready event
      env: {
        NODE_ENV: 'development',
        PORT: 3000,
        NODE_OPTIONS: '--no-deprecation',
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
        HOST: '127.0.0.1', // Explicitly bind to localhost for cPanel
        NODE_OPTIONS: '--no-deprecation --max-old-space-size=2048',
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 3000,
        NODE_OPTIONS: '--no-deprecation',
      },
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 3000,
        HOST: '127.0.0.1', // Explicitly bind to localhost for cPanel
        NODE_OPTIONS: '--no-deprecation --max-old-space-size=2048',
      },
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      time: true,
      // Health check configuration
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
      // Monitoring
      pmx: true,
      // Advanced settings
      node_args: '--max-old-space-size=2048',
      source_map_support: true,
      instance_var: 'INSTANCE_ID',
      // Graceful shutdown
      shutdown_with_message: true,
      // Log rotation
      log_type: 'json',
      merge_logs: true,
    },
  ],

  // Deployment configuration
  deploy: {
    production: {
      user: 'ikiaconferenceor',
      host: 'ikiaconference.or.ke',
      ref: 'origin/main',
      repo: 'https://github.com/apprenticecloud/IKIA.git',
      path: '/home/<USER>/public_html',
      'pre-deploy-local': '',
      'post-deploy':
        'pnpm install --frozen-lockfile && pnpm run build:production && pm2 reload ecosystem.config.cjs --env production',
      'pre-setup': '',
    },
    staging: {
      user: 'ikiaconferenceor',
      host: 'staging.ikiaconference.or.ke',
      ref: 'origin/staging',
      repo: 'https://github.com/apprenticecloud/IKIA.git',
      path: '/home/<USER>/staging',
      'post-deploy':
        'pnpm install --frozen-lockfile && pnpm run build:production && pm2 reload ecosystem.config.cjs --env staging',
    },
  },
}
