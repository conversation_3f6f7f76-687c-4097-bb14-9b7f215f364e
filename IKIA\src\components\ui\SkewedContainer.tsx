import React from 'react'

interface SkewedContainerProps {
  children: React.ReactNode
  variant?: 'filled' | 'outlined'
  className?: string
  size?: 'sm' | 'md' | 'lg'
  angleDeg?: number // skew angle (default 12)
  borderWidth?: number // outline thickness (default 2)
  fillColor?: string // filled bg (default #E8B32C)
  strokeColor?: string // outline color (default #7E2518)
  textColor?: string // text color (default #7E2518)
}

export const SkewedContainer: React.FC<SkewedContainerProps> = ({
  children,
  variant = 'filled',
  className = '',
  size = 'md',
  angleDeg = 12,
  borderWidth = 2,
  fillColor = '#E8B32C',
  strokeColor = '#7E2518',
  textColor = '#7E2518',
}) => {
  // Map Tailwind-ish paddings into px so we can compute compensation
  const pad = {
    sm: { x: 16, y: 8 }, // px-4 py-2
    md: { x: 32, y: 12 }, // px-8 py-3
    lg: { x: 48, y: 16 }, // px-12 py-4
  } as const
  const { x: basePx, y: basePy } = pad[size]

  // Compensation: skew eats horizontal space roughly tan(angle) * height/2 each side.
  // We approximate with tan(angle) * paddingY.
  const rad = (Math.abs(angleDeg) * Math.PI) / 180
  const compX = Math.ceil(Math.tan(rad) * basePy)

  const outerPaddingLeft = basePx + compX
  const outerPaddingRight = basePx + compX
  const outerPaddingTop = basePy
  const outerPaddingBottom = basePy

  // Skew outer, unskew inner text
  const outerSkew: React.CSSProperties = {
    transform: `skewX(-${angleDeg}deg)`,
    transformOrigin: 'center',
  }
  const innerUnskew: React.CSSProperties = {
    transform: `skewX(${angleDeg}deg)`,
    transformOrigin: 'center',
  }

  // Gradient outline (top, bottom, left, right)
  const borderImgs = [
    `linear-gradient(${strokeColor}, ${strokeColor})`,
    `linear-gradient(${strokeColor}, ${strokeColor})`,
    `linear-gradient(${strokeColor}, ${strokeColor})`,
    `linear-gradient(${strokeColor}, ${strokeColor})`,
  ].join(',')

  const borderPos = ['left top', 'left bottom', 'left top', 'right top'].join(',')
  const borderSize = [
    `100% ${borderWidth}px`,
    `100% ${borderWidth}px`,
    `${borderWidth}px 100%`,
    `${borderWidth}px 100%`,
  ].join(',')

  // OUTER styles
  const outerStyle: React.CSSProperties =
    variant === 'outlined'
      ? {
          ...outerSkew,
          padding: `${outerPaddingTop}px ${outerPaddingRight}px ${outerPaddingBottom}px ${outerPaddingLeft}px`,
          backgroundImage: borderImgs,
          backgroundRepeat: 'no-repeat',
          backgroundPosition: borderPos,
          backgroundSize: borderSize,
        }
      : {
          ...outerSkew,
          padding: `${outerPaddingTop}px ${outerPaddingRight}px ${outerPaddingBottom}px ${outerPaddingLeft}px`,
          backgroundColor: fillColor,
        }

  const textBase = 'font-myriad font-bold uppercase tracking-wide leading-tight'

  return (
    <span className={`relative inline-block align-middle ${className}`} style={outerStyle}>
      <span className={`${textBase} inline-block`} style={{ ...innerUnskew, color: textColor }}>
        {children}
      </span>
    </span>
  )
}
