#!/usr/bin/env node

// Enhanced server.js file for cPanel/PM2 compatibility
import { createServer } from 'http'
import { parse } from 'url'
import next from 'next'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Set environment variables with defaults
const dev = process.env.NODE_ENV !== 'production'
const hostname = process.env.HOST || '127.0.0.1' // Explicitly bind to localhost for cPanel
const port = parseInt(process.env.PORT, 10) || 3000

console.log('Starting Next.js server...')
console.log('Environment:', process.env.NODE_ENV)
console.log('Hostname:', hostname)
console.log('Port:', port)
console.log('Dev mode:', dev)

// Create Next.js app
const app = next({ dev, hostname, port, dir: __dirname })
const handle = app.getRequestHandler()

app
  .prepare()
  .then(() => {
    const server = createServer(async (req, res) => {
      try {
        // Parse the URL
        const parsedUrl = parse(req.url, true)

        // Add security headers
        res.setHeader('X-Content-Type-Options', 'nosniff')
        res.setHeader('X-Frame-Options', 'DENY')
        res.setHeader('X-XSS-Protection', '1; mode=block')

        // Handle the request with Next.js
        await handle(req, res, parsedUrl)
      } catch (err) {
        console.error('Error occurred handling', req.url, err)
        res.statusCode = 500
        res.end('Internal Server Error')
      }
    })

    server.listen(port, hostname, (err) => {
      if (err) {
        console.error('Failed to start server:', err)
        process.exit(1)
      }
      console.log(`> Ready on http://${hostname}:${port}`)
    })

    // Graceful shutdown handlers
    const gracefulShutdown = (signal) => {
      console.log(`Received ${signal}, shutting down gracefully...`)
      server.close(() => {
        console.log('Server closed')
        process.exit(0)
      })
    }

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
    process.on('SIGINT', () => gracefulShutdown('SIGINT'))

    // Handle uncaught exceptions
    process.on('uncaughtException', (err) => {
      console.error('Uncaught Exception:', err)
      process.exit(1)
    })

    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason)
      process.exit(1)
    })
  })
  .catch((ex) => {
    console.error('Failed to start Next.js app:', ex)
    process.exit(1)
  })
