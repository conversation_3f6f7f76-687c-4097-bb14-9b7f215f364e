'use client'

import type React from 'react'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { ArrowLeft, Crown, ChevronRight, ChevronLeft } from 'lucide-react'
import { StepIndicator, vipSteps } from '@/modules/website/registration/components/StepIndicator'
import { OTPVerification } from '@/modules/website/registration/components/OTPVerification'
import { ImageUpload } from '@/modules/website/registration/components/ImageUpload'
import { PaymentMethod } from '@/modules/website/registration/components/PaymentMethod'
import { saveFormDataToStorage } from '@/modules/website/registration/lib/registration-utils'
import { GroupRegistration } from '@/modules/website/registration/components/GroupRegistration'
import {
  initializeGroupRegistration,
  addGroupMember,
  updateGroupMember,
  removeGroupMember,
  navigateToMember,
  type GroupRegistrationState,
} from '@/modules/website/registration/lib/registration-utils'
import { vipPackage } from '@/modules/website/registration/lib/registration-data'

const vipCategories = [
  'Government Official',
  'Keynote Speaker',
  'Distinguished Guest',
  'Diplomatic Representative',
  'International Organization Representative',
  'Media Representative',
  'Academic Leader',
  'Industry Leader',
]

const countries = [
  'Kenya',
  'Uganda',
  'Tanzania',
  'Rwanda',
  'Burundi',
  'South Sudan',
  'Ethiopia',
  'Somalia',
  'United States',
  'United Kingdom',
  'Other',
]

const specialServices = [
  'Airport pickup/drop-off',
  'Dedicated liaison officer',
  'Translation services',
  'Special dietary requirements',
  'Accessibility accommodations',
  'Security arrangements',
  'Private meeting rooms',
  'Media interview coordination',
]

export default function VIPRegistrationForm() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [groupState, setGroupState] = useState<GroupRegistrationState>(
    initializeGroupRegistration(),
  )
  const [isOtpVerified, setIsOtpVerified] = useState(false)
  const [profileImage, setProfileImage] = useState<File | null>(null)
  const [profileImagePreview, setProfileImagePreview] = useState<string | null>(null)

  const [formData, setFormData] = useState({
    // Personal Information
    title: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    organization: '',
    position: '',
    country: '',
    city: '',

    // VIP Information
    vipCategory: '',
    invitationReference: '',
    biography: '',
    achievements: '',

    // Travel Information
    arrivalDate: '',
    departureDate: '',
    accommodationNeeds: '',
    accommodationPreferences: '',
    transportationNeeds: '',

    // Special Requirements
    specialServices: [] as string[],
    dietaryRequirements: '',
    accessibilityNeeds: '',
    securityRequirements: '',
    specialRequests: '',

    // Media & Protocol
    mediaConsent: false,
    photographyConsent: false,
    protocolRequirements: '',

    // Agreements
    termsAccepted: false,
    confidentialityAgreement: false,
  })

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleServiceChange = (service: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      specialServices: checked
        ? [...prev.specialServices, service]
        : prev.specialServices.filter((s) => s !== service),
    }))
  }

  // Group registration handlers
  const handleToggleGroupMode = (enabled: boolean) => {
    setGroupState((prev) => ({ ...prev, isGroupMode: enabled }))
  }

  const handleAddMember = () => {
    setGroupState((prev) => addGroupMember(prev, formData))
    // Reset form for next member
    setFormData({
      title: '',
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      organization: '',
      position: '',
      country: '',
      city: '',
      vipCategory: '',
      invitationReference: '',
      biography: '',
      achievements: '',
      arrivalDate: '',
      departureDate: '',
      accommodationNeeds: '',
      accommodationPreferences: '',
      transportationNeeds: '',
      specialServices: [],
      dietaryRequirements: '',
      accessibilityNeeds: '',
      securityRequirements: '',
      specialRequests: '',
      mediaConsent: false,
      photographyConsent: false,
      protocolRequirements: '',
      termsAccepted: false,
      confidentialityAgreement: false,
    })
  }

  const handleNavigateToMember = (index: number) => {
    // Save current form data before navigating
    setGroupState((prev) => updateGroupMember(prev, prev.currentMemberIndex, formData))

    // Navigate to the selected member
    const newState = navigateToMember(groupState, index)
    setGroupState(newState)

    // Load the member's data into the form
    if (index < newState.members.length) {
      const memberData = newState.members[index] as any
      setFormData(memberData)
    } else {
      // New member - reset form
      setFormData({
        title: '',
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        organization: '',
        position: '',
        country: '',
        city: '',
        vipCategory: '',
        invitationReference: '',
        biography: '',
        achievements: '',
        arrivalDate: '',
        departureDate: '',
        accommodationNeeds: '',
        accommodationPreferences: '',
        transportationNeeds: '',
        specialServices: [],
        dietaryRequirements: '',
        accessibilityNeeds: '',
        securityRequirements: '',
        specialRequests: '',
        mediaConsent: false,
        photographyConsent: false,
        protocolRequirements: '',
        termsAccepted: false,
        confidentialityAgreement: false,
      })
    }
  }

  const handleRemoveMember = (index: number) => {
    setGroupState((prev) => removeGroupMember(prev, index))
  }

  // Multi-step handlers
  const handleImageChange = (file: File | null, preview: string | null) => {
    setProfileImage(file)
    setProfileImagePreview(preview)
  }

  const handleOtpVerification = (verified: boolean) => {
    setIsOtpVerified(verified)
  }

  const nextStep = () => {
    if (currentStep < vipSteps.length) {
      setCompletedSteps((prev) => [...prev, currentStep])
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const canProceedToNextStep = () => {
    switch (currentStep) {
      case 1: // Personal details step
        return (
          formData.firstName &&
          formData.lastName &&
          formData.email &&
          formData.phone &&
          formData.country &&
          formData.vipCategory &&
          isOtpVerified &&
          formData.termsAccepted
        )
      case 2: // Review step
        return true
      case 3: // Payment step
        return true
      default:
        return false
    }
  }

  const handlePaymentComplete = (paymentData: any) => {
    // Save registration data
    const registrationData = {
      ...formData,
      profileImage: profileImagePreview,
      paymentData,
      registrationType: 'vip',
      isGroupRegistration: groupState.isGroupMode,
      groupMembers: groupState.isGroupMode ? groupState.members : undefined,
      submissionDate: new Date().toISOString(),
    }

    saveFormDataToStorage('ikia-registration', registrationData)

    // Navigate to success page
    router.push('/registration/success')
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Prepare registration data
    let registrationData

    if (groupState.isGroupMode) {
      // Update current member data and include all group members
      const updatedGroupState = updateGroupMember(
        groupState,
        groupState.currentMemberIndex,
        formData,
      )
      registrationData = {
        registrationType: 'VIP Registration',
        isGroupRegistration: true,
        groupMembers: [...updatedGroupState.members, formData],
        amount: `KES ${(updatedGroupState.members.length + 1) * 30000}`,
        pricePerPerson: 'KES 30,000',
        submissionDate: new Date().toISOString(),
      }
    } else {
      // Single registration
      registrationData = {
        ...formData,
        registrationType: 'VIP Registration',
        isGroupRegistration: false,
        amount: 'KES 30,000',
        pricePerPerson: 'KES 30,000',
        submissionDate: new Date().toISOString(),
      }
    }

    saveFormDataToStorage('ikia-registration', registrationData)

    console.log('VIP form submitted:', registrationData)
    router.push('/registration/success')
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8 font-myriad">
      {/* Enhanced Header with Homepage Design */}
      <div className="mb-12">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-8 font-myriad hover:bg-primary/10 text-primary"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Registration Types
        </Button>

        {/* Hero Section matching homepage style */}
        <div className="text-center mb-8 relative">
          <div className="relative bg-gradient-to-br from-background via-card to-muted/20 p-8 md:p-12 border border-border overflow-hidden main-shadow">
            {/* Heritage Elements */}
            <div className="absolute top-6 left-8 heritage-dot heritage-dot-accent"></div>
            <div
              className="absolute top-12 right-12 heritage-dot heritage-dot-primary"
              style={{ animationDelay: '1s' }}
            ></div>
            <div
              className="absolute bottom-8 left-12 heritage-dot heritage-dot-secondary"
              style={{ animationDelay: '2s' }}
            ></div>

            <div className="relative z-10">
              {/* Icon */}
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-accent to-accent/80 mb-6 shadow-lg">
                <Crown className="w-8 h-8 text-accent-foreground" />
              </div>

              <h1 className="text-3xl md:text-5xl font-bold text-foreground mb-4 font-myriad">
                VIP <span className="text-accent">Registration</span>
              </h1>

              <p className="text-base md:text-lg text-muted-foreground mb-4 font-myriad">
                Experience <span className="text-accent font-semibold">Exclusive Access</span> and{' '}
                <span className="text-primary font-semibold">Premium Benefits</span>
              </p>

              <p className="text-muted-foreground text-sm italic font-myriad">
                &ldquo;Where <span className="text-accent">distinction</span> meets{' '}
                <span className="text-primary">excellence</span>&rdquo;
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Step Indicator */}
      <div className="step-indicator mb-8">
        <StepIndicator
          steps={vipSteps}
          currentStep={currentStep}
          completedSteps={completedSteps}
          className="font-myriad"
        />
      </div>

      {/* Step Content */}
      <div className="space-y-8">
        {/* Step 1: Personal Details & VIP Information */}
        {currentStep === 1 && (
          <div className="space-y-6">
            {/* Group Registration */}
            <GroupRegistration
              groupState={groupState}
              onToggleGroupMode={handleToggleGroupMode}
              onAddMember={handleAddMember}
              onNavigateToMember={handleNavigateToMember}
              onRemoveMember={handleRemoveMember}
              currentMemberData={formData}
            />

            {/* Personal Information */}
            <Card className="form-card main-shadow hover-shadow">
              <CardHeader>
                <CardTitle className="text-xl font-bold font-myriad text-primary">
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 font-myriad">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Select
                      value={formData.title}
                      onValueChange={(value) => handleInputChange('title', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select title" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Mr.">Mr.</SelectItem>
                        <SelectItem value="Mrs.">Mrs.</SelectItem>
                        <SelectItem value="Ms.">Ms.</SelectItem>
                        <SelectItem value="Dr.">Dr.</SelectItem>
                        <SelectItem value="Prof.">Prof.</SelectItem>
                        <SelectItem value="Hon.">Hon.</SelectItem>
                        <SelectItem value="H.E.">H.E.</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="organization">Organization/Institution</Label>
                    <Input
                      id="organization"
                      value={formData.organization}
                      onChange={(e) => handleInputChange('organization', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="position">Position/Title</Label>
                    <Input
                      id="position"
                      value={formData.position}
                      onChange={(e) => handleInputChange('position', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="country">Country *</Label>
                    <Select
                      value={formData.country}
                      onValueChange={(value) => handleInputChange('country', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select your country" />
                      </SelectTrigger>
                      <SelectContent>
                        {countries.map((country) => (
                          <SelectItem key={country} value={country}>
                            {country}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={formData.city}
                      onChange={(e) => handleInputChange('city', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* VIP Package Display */}
            <Card>
              <CardHeader>
                <CardTitle
                  className="text-xl font-bold"
                  style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
                >
                  VIP Package
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-4 bg-purple-50 border-purple-200">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-semibold text-lg">{vipPackage.name}</h3>
                    <span className="text-xl font-bold text-purple-600">{vipPackage.price}</span>
                  </div>
                  <p className="text-gray-600 mb-3">{vipPackage.description}</p>
                  <ul className="space-y-1">
                    {vipPackage.features.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2 text-sm">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Professional Photo Upload */}
            <ImageUpload
              onImageChange={handleImageChange}
              currentImage={profileImagePreview}
              label="Professional Photo"
              description="Upload a professional headshot for VIP materials and badges"
            />

            {/* OTP Verification */}
            <OTPVerification
              email={formData.email}
              phone={formData.phone}
              onVerificationComplete={handleOtpVerification}
            />

            {/* Terms and Conditions */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="terms"
                      checked={formData.termsAccepted}
                      onCheckedChange={(checked) => handleInputChange('termsAccepted', checked)}
                    />
                    <Label htmlFor="terms" className="text-sm leading-relaxed">
                      I agree to the{' '}
                      <a href="#" className="text-blue-600 hover:underline">
                        VIP Terms and Conditions
                      </a>{' '}
                      and
                      <a href="#" className="text-blue-600 hover:underline ml-1">
                        Privacy Policy
                      </a>{' '}
                      *
                    </Label>
                  </div>
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="confidentiality"
                      checked={formData.confidentialityAgreement}
                      onCheckedChange={(checked) =>
                        handleInputChange('confidentialityAgreement', checked)
                      }
                    />
                    <Label htmlFor="confidentiality" className="text-sm leading-relaxed">
                      I agree to the{' '}
                      <a href="#" className="text-blue-600 hover:underline">
                        Confidentiality Agreement
                      </a>
                    </Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Step 2: Review Details */}
        {currentStep === 2 && (
          <Card>
            <CardHeader>
              <CardTitle
                className="text-xl font-bold"
                style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}
              >
                Review Your Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="font-semibold">Name</Label>
                    <p>
                      {formData.title} {formData.firstName} {formData.lastName}
                    </p>
                  </div>
                  <div>
                    <Label className="font-semibold">Email</Label>
                    <p>{formData.email}</p>
                  </div>
                  <div>
                    <Label className="font-semibold">Phone</Label>
                    <p>{formData.phone}</p>
                  </div>
                  <div>
                    <Label className="font-semibold">Country</Label>
                    <p>{formData.country}</p>
                  </div>
                  {formData.organization && (
                    <div>
                      <Label className="font-semibold">Organization</Label>
                      <p>{formData.organization}</p>
                    </div>
                  )}
                  {formData.position && (
                    <div>
                      <Label className="font-semibold">Position</Label>
                      <p>{formData.position}</p>
                    </div>
                  )}
                  <div>
                    <Label className="font-semibold">VIP Category</Label>
                    <p>{formData.vipCategory}</p>
                  </div>
                  <div>
                    <Label className="font-semibold">Package</Label>
                    <p>
                      {vipPackage.name} - {vipPackage.price}
                    </p>
                  </div>
                </div>

                {formData.invitationReference && (
                  <div>
                    <Label className="font-semibold">Invitation Reference</Label>
                    <p>{formData.invitationReference}</p>
                  </div>
                )}

                {formData.biography && (
                  <div>
                    <Label className="font-semibold">Biography</Label>
                    <p>{formData.biography}</p>
                  </div>
                )}

                {profileImagePreview && (
                  <div>
                    <Label className="font-semibold">Professional Photo</Label>
                    <img
                      src={profileImagePreview}
                      alt="Professional Photo"
                      className="w-20 h-20 rounded-lg object-cover"
                    />
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Label className="font-semibold">OTP Verification</Label>
                  <span
                    className={`px-2 py-1 rounded text-sm ${isOtpVerified ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
                  >
                    {isOtpVerified ? 'Verified' : 'Not Verified'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Payment */}
        {currentStep === 3 && (
          <PaymentMethod
            amount={vipPackage.price.replace('KES ', '').replace(',', '')}
            currency="KES"
            onPaymentComplete={handlePaymentComplete}
            registrationType="vip"
          />
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={currentStep === 1 ? () => router.push('/registration') : prevStep}
            className="flex items-center space-x-2"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>{currentStep === 1 ? 'Back to Registration Types' : 'Previous'}</span>
          </Button>

          <Button
            type="button"
            onClick={nextStep}
            disabled={!canProceedToNextStep()}
            className="flex items-center space-x-2"
          >
            <span>{currentStep === vipSteps.length ? 'Complete Registration' : 'Next Step'}</span>
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
