import type { CollectionConfig } from 'payload'

export const PartnershipMatching: CollectionConfig = {
  slug: 'partnership-matching',
  admin: {
    useAsTitle: 'fullName',
  },
  fields: [
    // Step 1: Tell us about yourself
    {
      name: 'userType',
      type: 'select',
      required: true,
      options: [
        { label: 'Investor', value: 'investor' },
        { label: 'Entrepreneur', value: 'entrepreneur' },
        { label: 'Researcher', value: 'researcher' },
        { label: 'Indigenous Knowledge Holder', value: 'ik-holder' },
      ],
    },
    {
      name: 'lookingFor',
      type: 'select',
      required: true,
      options: [
        { label: 'Investment Partner', value: 'investor' },
        { label: 'Knowledge Holder', value: 'knowledge-holder' },
        { label: 'Business Partner', value: 'business-partner' },
        { label: 'Mentor/Advisor', value: 'mentor' },
      ],
    },
    // Step 2: Contact Information
    {
      name: 'fullName',
      type: 'text',
      required: true,
    },
    {
      name: 'email',
      type: 'email',
      required: true,
    },
    {
      name: 'phone',
      type: 'text',
      required: true,
    },
    {
      name: 'organization',
      type: 'text',
      required: true,
    },
    // Step 3: Project Details
    {
      name: 'areaOfInterest',
      type: 'text',
      required: true,
    },
    {
      name: 'investmentRange',
      type: 'select',
      options: [
        { label: 'Under $50,000', value: 'under-50k' },
        { label: '$50,000 - $100,000', value: '50k-100k' },
        { label: '$100,000 - $500,000', value: '100k-500k' },
        { label: '$500,000 - $1,000,000', value: '500k-1m' },
        { label: 'Over $1,000,000', value: 'over-1m' },
      ],
    },
    {
      name: 'experience',
      type: 'textarea',
    },
    {
      name: 'projectDescription',
      type: 'textarea',
      required: true,
    },
    {
      name: 'timeline',
      type: 'select',
      options: [
        { label: 'Immediate (0-3 months)', value: 'immediate' },
        { label: 'Short-term (3-12 months)', value: 'short-term' },
        { label: 'Medium-term (1-3 years)', value: 'medium-term' },
        { label: 'Long-term (3+ years)', value: 'long-term' },
      ],
    },
  ],
}

export default PartnershipMatching





