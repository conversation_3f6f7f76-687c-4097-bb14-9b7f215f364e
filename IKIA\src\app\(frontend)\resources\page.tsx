'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Download,
  ExternalLink,
  FileText,
  Users,
  Building,
  UserPlus,
  BookOpen,
  ClipboardList,
  Phone,
  Mail,
  MapPin,
  ArrowRight,
} from 'lucide-react'

export default function ResourcesPage() {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)

  const downloadResources = [
    {
      id: 1,
      title: 'Conference Program',
      description:
        'A detailed schedule outlining sessions, speakers, and activities throughout the event.',
      icon: ClipboardList,
      action: 'Download',
      href: '/downloads/conference-program.pdf',
      color: 'from-[#C86E36] to-[#a85a2b]',
      hoverColor: 'hover:shadow-[#C86E36]/25',
    },
    {
      id: 2,
      title:
        'First International Investment Conference and Trade Fair on Indigenous Knowledge Intellectual Assets 2025 Concept Note',
      description: `A high-level summary of the conference's purpose, theme, and strategic objectives.`,
      icon: FileText,
      action: 'Download',
      href: '/downloads/ikia-concept-note.pdf',
      color: 'from-[#159147] to-[#0f7a3a]',
      hoverColor: 'hover:shadow-[#159147]/25',
    },
    {
      id: 3,
      title: 'Delegate Handbook for the 1st IKIA Conference 2025',
      description:
        'Information pack for attendees covering logistics, venue maps, contacts and rules.',
      icon: ExternalLink,
      action: 'Download',
      href: '/downloads/delegate-handbook.pdf',
      color: 'from-[#81B1DB] to-[#6a9bc7]',
      hoverColor: 'hover:shadow-[#81B1DB]/25',
    },
    {
      id: 4,
      title: 'Sponsorship Package',
      description:
        'A breakdown of sponsorship tiers, benefits and opportunities for brand visibility.',
      icon: Building,
      action: 'Download',
      href: '/downloads/sponsorship-package.pdf',
      color: 'from-[#E8B32C] to-[#d19d1f]',
      hoverColor: 'hover:shadow-[#E8B32C]/25',
    },
    {
      id: 5,
      title: 'Delegate Ticket Options',
      description:
        'A document outlining the access, benefits, and entitlements provided to conference attendees upon registration.',
      icon: Users,
      action: 'Download',
      href: '/downloads/delegate-ticket-options.pdf',
      color: 'from-[#159147] to-[#0f7a3a]',
      hoverColor: 'hover:shadow-[#159147]/25',
    },
    {
      id: 6,
      title: 'Exhibitors Guide',
      description:
        'A guide with key information for exhibitors, including booth setup, logistics, and guidelines.',
      icon: ClipboardList,
      action: 'Download',
      href: '/downloads/exhibitors-guide.pdf',
      color: 'from-[#C86E36] to-[#a85a2b]',
      hoverColor: 'hover:shadow-[#C86E36]/25',
    },

    {
      id: 7,
      title: 'IK Investments Guide',
      description:
        'A publication highlighting opportunities and frameworks for investing in Indigenous Knowledge systems.',
      icon: BookOpen,
      action: 'Download',
      href: '/downloads/ik-investments-guide.pdf',
      color: 'from-[#7E2518] to-[#5a1a10]',
      hoverColor: 'hover:shadow-[#7E2518]/25',
    },
    {
      id: 8,
      title: 'Investor Interest Form (Expression of Interest Form)',
      description:
        'A form for prospective investors to express interest in supporting Indigenous Knowledge innovations.',
      icon: UserPlus,
      action: 'Download',
      href: '/downloads/investor-interest-form.pdf',
      color: 'from-[#81B1DB] to-[#6a9bc7]',
      hoverColor: 'hover:shadow-[#81B1DB]/25',
    },
    {
      id: 9,
      title: 'IK holder Guide',
      description:
        'A resource to help Indigenous Knowledge holders prepare for and engage meaningfully at the event.',
      icon: MapPin,
      action: 'Download',
      href: '/downloads/ik-holder-guide.pdf',
      color: 'from-[#E8B32C] to-[#d19d1f]',
      hoverColor: 'hover:shadow-[#E8B32C]/25',
    },
  ]

  const allResources = [...downloadResources]

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-[#7E2518]/5 via-white to-[#159147]/5 pt-24 pb-16 lg:pt-28 lg:pb-24 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -left-40 w-60 h-60 bg-[#E8B32C]/5 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -right-40 w-72 h-72 bg-[#81B1DB]/5 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            {/* Conference Badge */}
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-[#7E2518]/8 to-[#159147]/8 px-4 py-2 rounded-full border border-[#7E2518]/15 mb-6">
              <div className="w-2 h-2 bg-[#E8B32C] rounded-full animate-pulse"></div>
              <span
                className="text-sm font-medium text-[#7E2518]"
                style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
              >
                QUICK ACCESS RESOURCES
              </span>
            </div>

            {/* Main Heading */}
            <h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-[#7C2313] mb-6 leading-tight"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              INVESTMENT CONFERENCE AND TRADE FAIR RESOURCES
              {/* <span className="block text-transparent bg-clip-text text-[#149047]">RESOURCES</span> */}
            </h1>

            <div className="w-24 h-1 bg-[#7E2518] rounded-full mx-auto mb-6"></div>

            <p
              className="text-lg md:text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto"
              style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
            >
              Access essential documents, registration forms, and resources for the 1<sup>st</sup>{' '}
              IKIA Investment Conference and Trade Fair 2025. Download guides, or express your
              interest in indigenous knowledge investment opportunities.
            </p>
          </div>
        </div>
      </section>

      {/* Resources Grid Section */}
      <section className="py-16 lg:py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
            {allResources.map((resource) => {
              const IconComponent = resource.icon
              const isHovered = hoveredCard === resource.id

              return (
                <Card
                  key={resource.id}
                  className={`group relative bg-white border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2 cursor-pointer overflow-hidden ${resource.hoverColor}`}
                  onMouseEnter={() => setHoveredCard(resource.id)}
                  onMouseLeave={() => setHoveredCard(null)}
                >
                  {/* Background Gradient Overlay */}
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${resource.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}
                  ></div>

                  {/* Animated Border Glow */}
                  {isHovered && (
                    <div className="absolute -inset-1 bg-gradient-to-r from-[#159147]/20 via-[#E8B32C]/20 to-[#7E2518]/20 rounded-lg blur-sm animate-pulse"></div>
                  )}

                  <CardContent className="relative p-8 h-full flex flex-col">
                    {/* Icon and Action Badge */}
                    <div className="flex items-center justify-between mb-6">
                      <div
                        className={`p-4 bg-gradient-to-br ${resource.color} rounded-xl shadow-lg transform group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 relative overflow-hidden`}
                      >
                        <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        <IconComponent className="w-8 h-8 text-white relative z-10" />
                      </div>

                      <div className="flex items-center gap-2 px-3 py-1 bg-gray-100 rounded-full text-xs font-medium text-gray-600 group-hover:bg-gray-200 transition-colors duration-300">
                        {resource.action === 'Download' ? (
                          <Download className="w-3 h-3" />
                        ) : (
                          <ExternalLink className="w-3 h-3" />
                        )}
                        <span style={{ fontFamily: 'ACQUIRE, Arial, sans-serif' }}>
                          {resource.action}
                        </span>
                      </div>
                    </div>

                    {/* Title */}
                    <h3
                      className="text-xl font-bold uppercase text-[#7E2518] mb-3 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-[#7E2518] group-hover:to-[#159147] transition-all duration-500"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      {resource.title}
                    </h3>

                    {/* Description */}
                    <p
                      className="text-gray-600 leading-relaxed flex-grow group-hover:text-gray-700 transition-colors duration-300 text-sm"
                      style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                    >
                      {resource.description}
                    </p>

                    {/* Action Button */}
                    <div className="mt-6">
                      <Button
                        asChild
                        className={`w-full bg-gradient-to-r ${resource.color} hover:shadow-lg hover:shadow-current/25 text-white border-0 rounded-lg font-semibold tracking-wide transition-all duration-500 transform group-hover:scale-105 group-hover:-translate-y-1`}
                        style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                      >
                        <a
                          href={resource.href}
                          className="flex uppercase items-center justify-center gap-2"
                        >
                          {resource.action === 'Download' ? (
                            <Download className="w-4 h-4" />
                          ) : (
                            <ExternalLink className="w-4 h-4" />
                          )}
                          {resource.action}
                          <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                        </a>
                      </Button>
                    </div>

                    {/* Progress Bar Animation */}
                    <div className="mt-4 h-1 bg-gray-100 rounded-full overflow-hidden">
                      <div
                        className={`h-full bg-gradient-to-r ${resource.color} rounded-full transform -translate-x-full group-hover:translate-x-0 transition-transform duration-1000 ease-out`}
                      ></div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>
    </div>
  )
}
