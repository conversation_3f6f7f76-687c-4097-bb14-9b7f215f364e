'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'

import Link from 'next/link'
import { Card, CardContent } from '@/components/ui/card'
import { useGetNewsMediaQuery } from '@/lib/api/newsMediaApi'
import { NewsItem } from '../types'
import { getTimeAgo } from '@/lib/utils'

export default function AllPressReleases() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  const { data, isLoading, error } = useGetNewsMediaQuery({
    where: {
      category: { equals: 'news-feed' },
      type: { equals: 'regular' },
    },
  })

  console.log({ data, isLoading, error })

  const nextSlide = () => {
    if (!isAnimating && data?.docs.length > 1) {
      setIsAnimating(true)
      setCurrentSlide((prev) => (prev + 1) % data.docs.length)
      setTimeout(() => setIsAnimating(false), 1000)
    }
  }

  const goToSlide = (index: number) => {
    if (!isAnimating && index !== currentSlide) {
      setIsAnimating(true)
      setCurrentSlide(index)
      setTimeout(() => setIsAnimating(false), 1000)
    }
  }

  useEffect(() => {
    if (!data?.docs || data.docs.length <= 1) return
    const timer = setInterval(() => {
      nextSlide()
    }, 6000)
    return () => clearInterval(timer)
  }, [data])

  if (isLoading) {
    return (
      <div className="mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Latest News</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, idx) => (
            <div
              key={idx}
              className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer rounded-lg bg-white border animate-pulse"
            >
              {/* Image skeleton */}
              <div className="relative h-48 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200">
                <div className="absolute inset-0 bg-[radial-gradient(circle,_rgba(255,255,255,0.3)_0%,_rgba(255,255,255,0.1)_60%,_transparent_100%)]" />
              </div>

              {/* Content skeleton */}
              <div className="p-4 space-y-4">
                <div className="h-4 bg-gray-200 rounded w-5/6" />
                <div className="h-4 bg-gray-200 rounded w-1/2" />
                <div className="flex justify-between mt-4">
                  <div className="h-3 w-20 bg-gray-200 rounded" />
                  <div className="h-3 w-24 bg-gray-200 rounded" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="relative">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">All Press Releases</h2>

        <div className="absolute inset-0 flex flex-col items-center justify-center z-10 text-center px-4">
          <h2 className="text-2xl font-semibold text-red-700 mb-2">Oops! Something went wrong.</h2>
          <p className="text-gray-600">
            We couldn’t load the press releases. Please try again later.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 opacity-60 blur-sm pointer-events-none">
          {[...Array(6)].map((_, idx) => (
            <div
              key={idx}
              className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer rounded-lg bg-white border animate-pulse"
            >
              {/* Image skeleton */}
              <div className="relative h-48 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200">
                <div className="absolute inset-0 bg-[radial-gradient(circle,_rgba(255,255,255,0.3)_0%,_rgba(255,255,255,0.1)_60%,_transparent_100%)]" />
              </div>

              {/* Content skeleton */}
              <div className="p-4 space-y-4">
                <div className="h-4 bg-gray-200 rounded w-5/6" />
                <div className="h-4 bg-gray-200 rounded w-1/2" />
                <div className="flex justify-between mt-4">
                  <div className="h-3 w-20 bg-gray-200 rounded" />
                  <div className="h-3 w-24 bg-gray-200 rounded" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (data?.docs?.length === 0) {
    return (
      <div className="relative">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">All Press Releases</h2>

        {/* Centered Text */}
        <div className="absolute inset-0 flex flex-col items-center justify-center z-10 text-center px-4">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">
            No press releases are available at the moment.
          </h2>
          <p className="text-gray-600">Please check back later.</p>
        </div>

        {/* Pulse Boxes */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 opacity-60 blur-sm pointer-events-none">
          {[...Array(6)].map((_, idx) => (
            <div
              key={idx}
              className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer rounded-lg bg-white border animate-pulse"
            >
              {/* Image skeleton */}
              <div className="relative h-48 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200">
                <div className="absolute inset-0 bg-[radial-gradient(circle,_rgba(255,255,255,0.3)_0%,_rgba(255,255,255,0.1)_60%,_transparent_100%)]" />
              </div>

              {/* Content skeleton */}
              <div className="p-4 space-y-4">
                <div className="h-4 bg-gray-200 rounded w-5/6" />
                <div className="h-4 bg-gray-200 rounded w-1/2" />
                <div className="flex justify-between mt-4">
                  <div className="h-3 w-20 bg-gray-200 rounded" />
                  <div className="h-3 w-24 bg-gray-200 rounded" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <>
      {/* All press releases Section */}
      <div className="mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">All Press Releases</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {data.docs.map((item: NewsItem) => (
            <Link key={item.id} href={`/news-media/article/${item.slug}`}>
              <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                <div className="relative h-48">
                  <Image
                    src={
                      item?.image?.url ||
                      `/placeholder.svg?height=200&width=300&query=business news story ${item.id}`
                    }
                    alt={`News story ${item.id}`}
                    fill
                    className="object-cover"
                  />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{item.title}</h3>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>{getTimeAgo(item.createdAt)}</span>
                    <span>{item.author}</span>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </>
  )
}
