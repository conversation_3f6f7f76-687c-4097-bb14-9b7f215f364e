import { withPayload } from '@payloadcms/next/withPayload'

import redirects from './redirects.js'

const NEXT_PUBLIC_SERVER_URL = process.env.NEXT_PUBLIC_SERVER_URL || 'https://ikiaconference.or.ke'

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Allow cross-origin requests from production domain
  allowedDevOrigins: ['ikiaconference.or.ke', 'www.ikiaconference.or.ke'],

  // Output configuration for better cPanel compatibility
  output: 'standalone',

  // Disable x-powered-by header for security
  poweredByHeader: false,

  // Compress responses
  compress: true,

  images: {
    remotePatterns: [
      ...[NEXT_PUBLIC_SERVER_URL /* 'https://example.com' */].map((item) => {
        const url = new URL(item)

        return {
          hostname: url.hostname,
          protocol: url.protocol.replace(':', ''),
        }
      }),
    ],
  },
  webpack: (webpackConfig) => {
    webpackConfig.resolve.extensionAlias = {
      '.cjs': ['.cts', '.cjs'],
      '.js': ['.ts', '.tsx', '.js', '.jsx'],
      '.mjs': ['.mts', '.mjs'],
    }

    return webpackConfig
  },
  reactStrictMode: true,
  redirects,
  typescript: {
    // ignore TypeScript errors on build
    ignoreBuildErrors: true,
  },
  eslint: {
    // ignore ESLint errors on build
    ignoreDuringBuilds: true,
  },
}

export default withPayload(nextConfig, { devBundleServerPackages: false })
