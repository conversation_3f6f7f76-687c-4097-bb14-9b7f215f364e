import QRCode from 'qrcode'
import { RegistrationFormData, QRCodeData, SponsorshipTier, sponsorshipTiers } from './registration-data'

// Generate unique confirmation number
export function generateConfirmationNumber(registrationType: string): string {
  const prefix = getRegistrationPrefix(registrationType)
  const timestamp = Date.now().toString().slice(-6)
  const random = Math.random().toString(36).substring(2, 6).toUpperCase()
  return `IKIA2025-${prefix}-${timestamp}${random}`
}

// Get registration type prefix
function getRegistrationPrefix(type: string): string {
  const prefixes: Record<string, string> = {
    'delegate': 'DEL',
    'vip': 'VIP',
    'sponsor': 'SPO',
    'exhibitor': 'EXH',
    'investor': 'INV'
  }
  return prefixes[type.toLowerCase()] || 'REG'
}

// Generate QR code data URL
export async function generateQRCode(data: QRCodeData): Promise<string> {
  const qrData = JSON.stringify({
    id: data.registrationId,
    name: data.name,
    email: data.email,
    type: data.type,
    pkg: data.package,
    tier: data.tier,
    timestamp: data.timestamp
  })

  try {
    const qrCodeDataURL = await QRCode.toDataURL(qrData, {
      width: 300,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
    return qrCodeDataURL
  } catch (error) {
    console.error('Error generating QR code:', error)
    throw new Error('Failed to generate QR code')
  }
}

// Download QR code as image
export function downloadQRCode(dataURL: string, filename: string): void {
  const link = document.createElement('a')
  link.href = dataURL
  link.download = `${filename}.png`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// Validate form data
export function validateRegistrationForm(data: RegistrationFormData, requiredFields: string[]): string[] {
  const errors: string[] = []
  
  requiredFields.forEach(field => {
    if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
      errors.push(`${field} is required`)
    }
  })

  // Email validation
  if (data.email && !isValidEmail(data.email)) {
    errors.push('Please enter a valid email address')
  }

  // Phone validation
  if (data.phone && !isValidPhone(data.phone)) {
    errors.push('Please enter a valid phone number')
  }

  return errors
}

// Email validation
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Phone validation
function isValidPhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))
}

// Get sponsorship tier details
export function getSponsorshipTier(tierId: string): SponsorshipTier | undefined {
  return sponsorshipTiers.find(tier => tier.id === tierId)
}

// Calculate total sponsorship cost including tickets
export function calculateSponsorshipTotal(tier: SponsorshipTier, additionalTickets: number = 0): number {
  const basePrice = parseFloat(tier.price)
  const additionalTicketPrice = 20000 // Regular delegate price
  return basePrice + (additionalTickets * additionalTicketPrice)
}

// Format currency for display
export function formatCurrency(amount: string | number, currency: string = 'KES'): string {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  
  if (currency === 'KES') {
    return `KES ${numAmount.toLocaleString()}`
  }
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0
  }).format(numAmount)
}

// Generate registration summary
export function generateRegistrationSummary(data: RegistrationFormData, type: string): any {
  return {
    confirmationNumber: generateConfirmationNumber(type),
    registrationType: type,
    registrantName: `${data.firstName} ${data.lastName}`,
    email: data.email,
    phone: data.phone,
    organization: data.organization,
    package: data.selectedPackage,
    tier: data.selectedTier,
    registrationDate: new Date().toISOString(),
    status: 'pending_payment'
  }
}

// Calculate package price
export function calculatePackagePrice(packageId: string, packages: any[]): number {
  const pkg = packages.find(p => p.id === packageId)
  return pkg ? parseFloat(pkg.price) : 0
}

// Generate badge data
export function generateBadgeData(registrationData: RegistrationFormData, type: string): any {
  return {
    name: `${registrationData.firstName} ${registrationData.lastName}`,
    organization: registrationData.organization || '',
    position: registrationData.position || '',
    type: type.toUpperCase(),
    package: registrationData.selectedPackage || registrationData.selectedTier || '',
    qrData: {
      registrationId: generateConfirmationNumber(type),
      name: `${registrationData.firstName} ${registrationData.lastName}`,
      email: registrationData.email,
      type: type,
      package: registrationData.selectedPackage,
      tier: registrationData.selectedTier,
      timestamp: new Date().toISOString()
    }
  }
}

// Validate group registration
export function validateGroupRegistration(groupMembers: RegistrationFormData[]): string[] {
  const errors: string[] = []
  
  if (groupMembers.length < 2) {
    errors.push('Group registration requires at least 2 members')
  }

  groupMembers.forEach((member, index) => {
    if (!member.firstName || !member.lastName || !member.email) {
      errors.push(`Member ${index + 1}: Name and email are required`)
    }
    
    if (member.email && !isValidEmail(member.email)) {
      errors.push(`Member ${index + 1}: Invalid email address`)
    }
  })

  // Check for duplicate emails
  const emails = groupMembers.map(m => m.email).filter(Boolean)
  const duplicates = emails.filter((email, index) => emails.indexOf(email) !== index)
  if (duplicates.length > 0) {
    errors.push('Duplicate email addresses found in group members')
  }

  return errors
}

// Calculate group discount
export function calculateGroupDiscount(memberCount: number, basePrice: number): { discountPercent: number, totalPrice: number, savings: number } {
  let discountPercent = 0
  
  if (memberCount >= 10) {
    discountPercent = 15
  } else if (memberCount >= 5) {
    discountPercent = 10
  } else if (memberCount >= 3) {
    discountPercent = 5
  }

  const originalTotal = memberCount * basePrice
  const discountAmount = (originalTotal * discountPercent) / 100
  const totalPrice = originalTotal - discountAmount

  return {
    discountPercent,
    totalPrice,
    savings: discountAmount
  }
}

// Export registration data as CSV
export function exportRegistrationCSV(registrations: any[]): string {
  if (registrations.length === 0) return ''

  const headers = Object.keys(registrations[0])
  const csvContent = [
    headers.join(','),
    ...registrations.map(reg => 
      headers.map(header => {
        const value = reg[header]
        return typeof value === 'string' && value.includes(',') ? `"${value}"` : value
      }).join(',')
    )
  ].join('\n')

  return csvContent
}

// Download CSV file
export function downloadCSV(csvContent: string, filename: string): void {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `${filename}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// Group registration utilities
export interface GroupRegistrationState {
  isGroupMode: boolean
  currentMemberIndex: number
  members: RegistrationFormData[]
}

export function initializeGroupRegistration(): GroupRegistrationState {
  return {
    isGroupMode: false,
    currentMemberIndex: 0,
    members: []
  }
}

export function addGroupMember(
  state: GroupRegistrationState,
  memberData: RegistrationFormData
): GroupRegistrationState {
  return {
    ...state,
    members: [...state.members, memberData],
    currentMemberIndex: state.members.length
  }
}

export function updateGroupMember(
  state: GroupRegistrationState,
  index: number,
  memberData: RegistrationFormData
): GroupRegistrationState {
  const updatedMembers = [...state.members]
  updatedMembers[index] = memberData
  return {
    ...state,
    members: updatedMembers
  }
}

export function removeGroupMember(
  state: GroupRegistrationState,
  index: number
): GroupRegistrationState {
  const updatedMembers = state.members.filter((_, i) => i !== index)
  return {
    ...state,
    members: updatedMembers,
    currentMemberIndex: Math.min(state.currentMemberIndex, updatedMembers.length - 1)
  }
}

export function navigateToMember(
  state: GroupRegistrationState,
  index: number
): GroupRegistrationState {
  return {
    ...state,
    currentMemberIndex: Math.max(0, Math.min(index, state.members.length - 1))
  }
}

// Form data persistence utilities
export function saveFormDataToStorage(key: string, data: any): void {
  try {
    localStorage.setItem(key, JSON.stringify(data))
  } catch (error) {
    console.warn('Failed to save form data to localStorage:', error)
  }
}

export function loadFormDataFromStorage(key: string): any | null {
  try {
    const data = localStorage.getItem(key)
    return data ? JSON.parse(data) : null
  } catch (error) {
    console.warn('Failed to load form data from localStorage:', error)
    return null
  }
}

export function clearFormDataFromStorage(key: string): void {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.warn('Failed to clear form data from localStorage:', error)
  }
}
