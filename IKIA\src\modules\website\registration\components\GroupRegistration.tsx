'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Plus, ChevronLeft, ChevronRight, X, Users, User, AlertCircle } from 'lucide-react'
import { GroupRegistrationState } from '@/modules/website/registration/lib/registration-utils'

interface GroupRegistrationProps {
  groupState: GroupRegistrationState
  onToggleGroupMode: (enabled: boolean) => void
  onAddMember: () => void
  onNavigateToMember: (index: number) => void
  onRemoveMember: (index: number) => void
  currentMemberData?: any
  className?: string
}

export function GroupRegistration({
  groupState,
  onToggleGroupMode,
  onAddMember,
  onNavigateToMember,
  onRemoveMember,
  currentMemberData,
  className = '',
}: GroupRegistrationProps) {
  const { isGroupMode, currentMemberIndex, members } = groupState

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Users className="w-5 h-5" />
          <span>Group Registration</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Group Mode Toggle */}
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            <Switch id="group-mode" checked={isGroupMode} onCheckedChange={onToggleGroupMode} />
            <Label htmlFor="group-mode" className="font-medium">
              Enable Group Registration
            </Label>
          </div>
          {isGroupMode && (
            <Badge variant="secondary" className="ml-2">
              {members.length + 1} {members.length === 0 ? 'Member' : 'Members'}
            </Badge>
          )}
        </div>

        {isGroupMode && (
          <>
            {/* Group Navigation */}
            {members.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold text-gray-900">
                    Registrant {currentMemberIndex + 1} of {members.length + 1}
                  </h4>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onNavigateToMember(currentMemberIndex - 1)}
                      disabled={currentMemberIndex === 0}
                    >
                      <ChevronLeft className="w-4 h-4" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onNavigateToMember(currentMemberIndex + 1)}
                      disabled={currentMemberIndex >= members.length}
                    >
                      Next
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Member Navigation Dots */}
                <div className="flex items-center justify-center space-x-2">
                  {Array.from({ length: members.length + 1 }, (_, index) => (
                    <button
                      key={index}
                      onClick={() => onNavigateToMember(index)}
                      className={`w-3 h-3 rounded-full transition-colors ${
                        index === currentMemberIndex
                          ? 'bg-primary'
                          : index < currentMemberIndex ||
                              (index <= members.length && members[index - 1])
                            ? 'bg-green-500'
                            : 'bg-gray-300'
                      }`}
                      title={`Registrant ${index + 1}`}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Member List */}
            {members.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-900">Group Members</h4>
                <div className="space-y-2">
                  {members.map((member, index) => (
                    <div
                      key={index}
                      className={`flex items-center justify-between p-3 border rounded-lg transition-colors ${
                        index === currentMemberIndex
                          ? 'border-primary bg-primary/5'
                          : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            index === currentMemberIndex ? 'bg-primary text-white' : 'bg-gray-100'
                          }`}
                        >
                          <User className="w-4 h-4" />
                        </div>
                        <div>
                          <p className="font-medium">
                            {member.firstName && member.lastName
                              ? `${member.firstName} ${member.lastName}`
                              : `Member ${index + 2}`}
                          </p>
                          {member.email && <p className="text-sm text-gray-500">{member.email}</p>}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onNavigateToMember(index)}
                          className="text-primary hover:text-primary/80"
                        >
                          Edit
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onRemoveMember(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Add Member Button */}
            <div className="flex justify-center">
              <Button
                variant="outline"
                onClick={onAddMember}
                className="flex items-center space-x-2"
              >
                <Plus className="w-4 h-4" />
                <span>Add Another Member</span>
              </Button>
            </div>

            {/* Group Registration Info */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium mb-1">Group Registration Benefits:</p>
                  <ul className="space-y-1 text-blue-700">
                    <li>• Simplified registration process for multiple attendees</li>
                    <li>• Centralized billing and management</li>
                    <li>• Easy member management and updates</li>
                  </ul>
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
