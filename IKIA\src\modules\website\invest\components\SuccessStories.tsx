"use client"

import { useState } from "react"
import { Chevron<PERSON><PERSON>t, ChevronRight, Quote, TrendingUp, Users } from "lucide-react"
import { useGetSuccessStoriesQuery } from '@/lib/api/successStoriesApi'
import Image from 'next/image'

export default function SuccessStories() {
  const [currentStory, setCurrentStory] = useState(0)

  // Fetch success stories from API
  const { data: successStoriesData, isLoading, error } = useGetSuccessStoriesQuery({
    where: {
      isActive: { equals: true }
    },
    sort: '-createdAt'
  })

  // Show loading state
  if (isLoading) {
    return (
      <section className="py-20 bg-[#F5F5DC] relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#7E2518] mx-auto"></div>
            <p className="mt-4 text-[#7E2518]">Loading success stories...</p>
          </div>
        </div>
      </section>
    )
  }

  // Show error state
  if (error) {
    return (
      <section className="py-20 bg-[#F5F5DC] relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <div className="text-center">
            <p className="text-red-600">Failed to load success stories. Please try again later.</p>
          </div>
        </div>
      </section>
    )
  }

  // Use API data or fallback to empty array
  const successStories = successStoriesData?.docs || []

  // Show empty state if no stories
  if (successStories.length === 0) {
    return (
      <section className="py-20 bg-[#F5F5DC] relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-6 relative z-10">
          <div className="text-center">
            <h2 className="text-4xl md:text-5xl font-bold text-[#7E2518] mb-6" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
              Success <span className="text-[#E8B32C]">Stories</span>
            </h2>
            <p className="text-xl text-gray-700">No success stories available at the moment.</p>
          </div>
        </div>
      </section>
    )
  }

  const nextStory = () => {
    setCurrentStory((prev) => (prev + 1) % successStories.length)
  }

  const prevStory = () => {
    setCurrentStory((prev) => (prev - 1 + successStories.length) % successStories.length)
  }

  const story = successStories[currentStory]

  return (
    <section className="py-20 bg-[#F5F5DC] relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-6 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-[#7E2518] mb-6" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
              Success <span className="text-[#E8B32C]">Stories</span>
            </h2>
            <div className="w-24 h-1 bg-[#7E2518] mx-auto mb-6"></div>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
              Real partnerships, real impact. Discover how investors and Indigenous Knowledge holders are creating
              sustainable success together.
            </p>
          </div>

          {/* Story Showcase */}
          <div className="bg-white/95 backdrop-blur-md shadow-2xl overflow-hidden">
            <div className="grid lg:grid-cols-2 gap-0">
              {/* Story Image */}
              <div className="relative h-64 lg:h-auto">
                <Image
                  src={story.image?.url || "/placeholder.svg"}
                  alt={story.title}
                  fill
                  className="object-cover object-center"
                  sizes="(max-width: 768px) 100vw, 50vw"
                  priority={currentStory === 0}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute bottom-6 left-6 right-6">
                  <div className="bg-white/90 backdrop-blur-sm p-4">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center text-[#159147]">
                        <TrendingUp className="w-4 h-4 mr-1" />
                        <span className="font-bold" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                          {story.currency} {story.investmentAmount?.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex items-center text-[#7E2518]">
                        <Users className="w-4 h-4 mr-1" />
                        <span className="font-bold" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                          {story.impact?.[0]?.value || 'Impact'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Story Content */}
              <div className="p-8 lg:p-12">
                <div className="mb-6">
                  <h3 className="text-2xl lg:text-3xl font-bold text-[#7E2518] mb-4" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                    {story.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed mb-6" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                    {story.description}
                  </p>
                </div>

                {/* Partnership Details */}
                <div className="grid grid-cols-2 gap-6 mb-6">
                  <div>
                    <h4 className="text-sm font-semibold text-[#7E2518] mb-2" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                      INVESTOR
                    </h4>
                    <p className="font-bold text-gray-900" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                      {story.investor.name}
                    </p>
                    <p className="text-sm text-gray-600" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                      {story.investor.position}
                    </p>
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold text-[#7E2518] mb-2" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                      KNOWLEDGE HOLDER
                    </h4>
                    <p className="font-bold text-gray-900" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                      {story.knowledgeHolder.name}
                    </p>
                    <p className="text-sm text-gray-600" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                      {story.knowledgeHolder.title}
                    </p>
                  </div>
                </div>

                {/* Key Metrics */}
                <div className="grid grid-cols-3 gap-4 mb-6">
                  {story.impact?.slice(0, 3).map((metric, index) => (
                    <div key={index} className="text-center p-3 bg-gray-50">
                      <div className="text-lg font-bold text-[#7E2518]" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                        {metric.value}
                      </div>
                      <div className="text-xs text-gray-600" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                        {metric.metric}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Testimonial */}
                <div className="bg-[#159147]/10 p-4 mb-6">
                  <Quote className="w-6 h-6 text-[#159147] mb-2" />
                  <p className="text-gray-700 italic leading-relaxed" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                    "{story.message}"
                  </p>
                  <p className="text-sm font-semibold text-[#159147] mt-2" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                    - {story.messageAuthor || story.knowledgeHolder.name}
                  </p>
                </div>

                {/* Location & Timeline */}
                <div className="flex justify-between text-sm text-gray-600">
                  <span style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                     {story.location.county}, {story.location.country}
                  </span>
                  <span style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                     {story.timeline}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-center mt-8 space-x-4">
            <button
              onClick={prevStory}
              className="w-12 h-12 bg-[#7E2518]/20 hover:bg-[#7E2518]/30 flex items-center justify-center transition-colors"
            >
              <ChevronLeft className="w-6 h-6 text-[#7E2518]" />
            </button>

            <div className="flex space-x-2">
              {successStories.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentStory(index)}
                  className={`w-3 h-3 transition-all duration-300 ${
                    index === currentStory ? "bg-[#7E2518] scale-125" : "bg-[#7E2518]/50 hover:bg-[#7E2518]/70"
                  }`}
                />
              ))}
            </div>

            <button
              onClick={nextStory}
              className="w-12 h-12 bg-[#7E2518]/20 hover:bg-[#7E2518]/30 flex items-center justify-center transition-colors"
            >
              <ChevronRight className="w-6 h-6 text-[#7E2518]" />
            </button>
          </div>

          {/* Call to Action */}
          <div className="mt-16 text-center">
            <div className="bg-[#7E2518] border border-[#7E2518] p-8">
              <h3 className="text-2xl font-bold text-white mb-4" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>Ready to Create Your Success Story?</h3>
              <p className="text-lg text-white/90 mb-6 max-w-2xl mx-auto" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                Join our community of successful investors and knowledge holders who are making a real difference.
              </p>
              <button className="bg-[#E8B32C] text-[#7E2518] font-bold px-8 py-4 hover:bg-[#D4A429] transition-colors transform hover:scale-105 duration-300" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                Start Your Partnership Journey
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
