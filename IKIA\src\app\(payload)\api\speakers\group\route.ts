// src/app/(payload)/api/speakers/grouped/route.ts

import { NextRequest, NextResponse } from 'next/server'
import config from '@/payload.config'
import { getPayload } from 'payload'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Fetch keynote speakers
    const keynoteResult = await payload.find({
      collection: 'speakers',
      where: {
        category: {
          equals: 'keynote',
        },
      },
      sort: 'order',
      depth: 2,
      limit: 50,
    })

    // Fetch plenary speakers
    const plenaryResult = await payload.find({
      collection: 'speakers',
      where: {
        category: {
          equals: 'plenary',
        },
      },
      sort: 'order',
      depth: 2,
      limit: 20000,
    })

    // Fetch other speakers
    const othersResult = await payload.find({
      collection: 'speakers',
      where: {
        category: {
          equals: 'other',
        },
      },
      sort: 'order',
      depth: 2,
      limit: 20000,
    })

    return NextResponse.json({
      keynote: keynoteResult,
      plenary: plenaryResult,
      others: othersResult,
    })
  } catch (error) {
    console.error('Error in /api/speakers/grouped:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}
