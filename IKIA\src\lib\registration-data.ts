// Registration package data and configurations

export interface Package {
  id: string
  name: string
  price: number
  currency: string
  description: string
  features: string[]
  popular?: boolean
  maxAttendees?: number
}

export interface SponsorshipTier {
  id: string
  name: string
  price: number
  currency: string
  description: string
  benefits: string[]
  ticketAllocation: {
    vip: number
    regular: number
  }
  popular?: boolean
}

// Delegate packages
export const delegatePackages: Package[] = [
  {
    id: 'delegate-standard',
    name: 'Standard Delegate',
    price: 15000,
    currency: 'KES',
    description: 'Full conference access with networking opportunities',
    features: [
      'Access to all conference sessions',
      'Welcome reception',
      'Networking lunch',
      'Conference materials',
      'Certificate of attendance',
      'Access to exhibition area'
    ]
  },
  {
    id: 'delegate-premium',
    name: 'Premium Delegate',
    price: 25000,
    currency: 'KES',
    description: 'Enhanced conference experience with additional benefits',
    features: [
      'All Standard Delegate benefits',
      'Priority seating',
      'Exclusive networking session',
      'Welcome gift bag',
      'Access to VIP lounge',
      'Complimentary refreshments'
    ],
    popular: true
  }
]

// Exhibitor packages
export const exhibitorPackages: Package[] = [
  {
    id: 'exhibitor-basic',
    name: 'Basic Exhibition Package',
    price: 50000,
    currency: 'KES',
    description: 'Standard exhibition space with basic amenities',
    features: [
      '3m x 3m exhibition space',
      'Basic booth setup',
      '2 delegate passes included',
      'Company listing in program',
      'Access to networking events',
      'Basic marketing support'
    ]
  },
  {
    id: 'exhibitor-premium',
    name: 'Premium Exhibition Package',
    price: 100000,
    currency: 'KES',
    description: 'Enhanced exhibition space with premium benefits',
    features: [
      '6m x 3m exhibition space',
      'Premium booth setup',
      '4 delegate passes included',
      'Featured company listing',
      'Logo on conference materials',
      'Dedicated networking session',
      'Enhanced marketing support'
    ],
    popular: true
  }
]

// VIP package
export const vipPackage: Package = {
  id: 'vip-experience',
  name: 'VIP Experience',
  price: 30000,
  currency: 'KES',
  description: 'Exclusive VIP experience with premium access and benefits',
  features: [
    'All Premium Delegate benefits',
    'VIP-only sessions',
    'Meet & greet with keynote speakers',
    'Exclusive dining experiences',
    'Premium welcome gift',
    'Dedicated concierge service',
    'Priority access to all events',
    'VIP transportation (where applicable)'
  ]
}

// Sponsorship tiers
export const allSponsorshipTiers: SponsorshipTier[] = [
  {
    id: 'title-sponsor',
    name: 'Title Sponsor',
    price: 2000000,
    currency: 'KES',
    description: 'Highest level of sponsorship with maximum visibility and benefits',
    benefits: [
      'Event naming rights',
      'Logo on all marketing materials',
      'Keynote speaking opportunity',
      'Premium exhibition space',
      'Exclusive networking reception',
      'Media interview opportunities',
      'Year-round partnership recognition'
    ],
    ticketAllocation: {
      vip: 15,
      regular: 0
    }
  },
  {
    id: 'platinum-sponsor',
    name: 'Platinum Sponsor',
    price: 1500000,
    currency: 'KES',
    description: 'Premium sponsorship with extensive benefits and visibility',
    benefits: [
      'Logo on main stage backdrop',
      'Speaking opportunity',
      'Premium exhibition space',
      'Branded networking session',
      'Logo on conference app',
      'Social media promotion',
      'Press release inclusion'
    ],
    ticketAllocation: {
      vip: 5,
      regular: 5
    },
    popular: true
  },
  {
    id: 'gold-sponsor',
    name: 'Gold Sponsor',
    price: 1000000,
    currency: 'KES',
    description: 'Significant sponsorship with valuable benefits and recognition',
    benefits: [
      'Logo on conference materials',
      'Exhibition space included',
      'Networking lunch sponsorship',
      'Logo on website',
      'Social media mentions',
      'Conference bag insert'
    ],
    ticketAllocation: {
      vip: 2,
      regular: 3
    }
  },
  {
    id: 'silver-sponsor',
    name: 'Silver Sponsor',
    price: 500000,
    currency: 'KES',
    description: 'Solid sponsorship package with good visibility and benefits',
    benefits: [
      'Logo on select materials',
      'Website listing',
      'Social media recognition',
      'Networking opportunities',
      'Conference program listing'
    ],
    ticketAllocation: {
      vip: 1,
      regular: 2
    }
  },
  {
    id: 'bronze-sponsor',
    name: 'Bronze Sponsor',
    price: 250000,
    currency: 'KES',
    description: 'Entry-level sponsorship with essential benefits',
    benefits: [
      'Website listing',
      'Conference program mention',
      'Social media recognition',
      'Networking access'
    ],
    ticketAllocation: {
      vip: 0,
      regular: 2
    }
  }
]

// Investment areas for investor registration
export const investmentAreas = [
  'Traditional Foods & Nutrition',
  'Local Remedies & Traditional Medicine',
  'Musicology & Cultural Arts',
  'Cultural Tourism & Heritage',
  'Indigenous Technologies & Innovations',
  'Sui Generis Intellectual Property Systems',
  'Sustainable Agriculture',
  'Renewable Energy',
  'Community Development',
  'Education & Training'
]

// Investment types
export const investmentTypes = [
  'Angel Investment',
  'Venture Capital',
  'Private Equity',
  'Impact Investment',
  'Grant Funding',
  'Strategic Partnership',
  'Microfinance',
  'Crowdfunding'
]

// Investment ranges
export const investmentRanges = [
  'Under KES 100,000',
  'KES 100,000 - KES 500,000',
  'KES 500,000 - KES 1,000,000',
  'KES 1,000,000 - KES 5,000,000',
  'KES 5,000,000 - KES 10,000,000',
  'Over KES 10,000,000'
]

// Countries list
export const countries = [
  'Kenya',
  'Uganda',
  'Tanzania',
  'Rwanda',
  'Burundi',
  'South Sudan',
  'Ethiopia',
  'Somalia',
  'Democratic Republic of Congo',
  'United States',
  'United Kingdom',
  'Canada',
  'Australia',
  'Germany',
  'France',
  'Netherlands',
  'Sweden',
  'Norway',
  'Denmark',
  'Other'
]

// Dietary requirements options
export const dietaryOptions = [
  'No restrictions',
  'Vegetarian',
  'Vegan',
  'Halal',
  'Kosher',
  'Gluten-free',
  'Dairy-free',
  'Nut allergies',
  'Other (please specify)'
]
