"use client"

import React from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check, Star } from "lucide-react"
import { PackageOption } from '@/modules/website/registration/lib/registration-data'
import { formatCurrency } from '@/modules/website/registration/lib/registration-utils'

interface PackageGridProps {
  packages: PackageOption[]
  selectedPackage: string
  onPackageSelect: (packageId: string) => void
  className?: string
}

export function PackageGrid({ packages, selectedPackage, onPackageSelect, className = "" }: PackageGridProps) {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {packages.map((pkg) => (
        <Card
          key={pkg.id}
          className={`relative cursor-pointer transition-all duration-300 hover:shadow-lg ${
            selectedPackage === pkg.id
              ? 'ring-2 ring-blue-500 shadow-lg'
              : 'hover:shadow-md'
          }`}
          onClick={() => onPackageSelect(pkg.id)}
        >
          {pkg.popular && (
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <Badge className="bg-orange-500 text-white px-3 py-1">
                <Star className="w-3 h-3 mr-1" />
                Most Popular
              </Badge>
            </div>
          )}
          
          <CardContent className="p-6">
            <div className="text-center mb-4">
              <h3 className="text-xl font-bold text-gray-900 mb-2 font-myriad">
                {pkg.name}
              </h3>
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {formatCurrency(pkg.price)}
              </div>
              {pkg.duration && (
                <div className="text-sm text-gray-500">
                  {pkg.duration}
                </div>
              )}
              <p className="text-gray-600 text-sm mt-2">
                {pkg.description}
              </p>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900 text-sm">What's included:</h4>
              <ul className="space-y-2">
                {pkg.features.map((feature, index) => (
                  <li key={index} className="flex items-start text-sm text-gray-700">
                    <Check className="w-4 h-4 text-ikia-green mr-2 mt-0.5 flex-shrink-0" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            {selectedPackage === pkg.id && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center text-blue-800">
                  <Check className="w-4 h-4 mr-2" />
                  <span className="text-sm font-medium">Selected Package</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

interface SponsorshipTierGridProps {
  tiers: any[]
  selectedTier: string
  onTierSelect: (tierId: string) => void
  className?: string
}

export function SponsorshipTierGrid({ tiers, selectedTier, onTierSelect, className = "" }: SponsorshipTierGridProps) {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 gap-6 ${className}`}>
      {tiers.map((tier) => (
        <Card
          key={tier.id}
          className={`relative cursor-pointer transition-all duration-300 hover:shadow-lg ${
            selectedTier === tier.id
              ? 'ring-2 ring-orange-500 shadow-lg'
              : 'hover:shadow-md'
          }`}
          onClick={() => onTierSelect(tier.id)}
        >
          <CardContent className="p-6">
            <div className="text-center mb-4">
              <h3 className="text-xl font-bold text-gray-900 mb-2 font-myriad">
                {tier.name}
              </h3>
              <div className="text-2xl font-bold text-orange-600 mb-1">
                {formatCurrency(tier.price)}
              </div>
              <p className="text-gray-600 text-sm mt-2">
                {tier.description}
              </p>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900 text-sm">Benefits included:</h4>
              <ul className="space-y-2">
                {tier.benefits.map((benefit: string, index: number) => (
                  <li key={index} className="flex items-start text-sm text-gray-700">
                    <Check className="w-4 h-4 text-ikia-green mr-2 mt-0.5 flex-shrink-0" />
                    <span>{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>

            {tier.ticketAllocation && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <h5 className="font-medium text-gray-900 text-sm mb-2">Ticket Allocation:</h5>
                <div className="space-y-1 text-sm text-gray-600">
                  {tier.ticketAllocation.vip > 0 && (
                    <div>VIP Tickets: {tier.ticketAllocation.vip}</div>
                  )}
                  {tier.ticketAllocation.regular > 0 && (
                    <div>Regular Tickets: {tier.ticketAllocation.regular}</div>
                  )}
                </div>
              </div>
            )}

            {selectedTier === tier.id && (
              <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="flex items-center text-orange-800">
                  <Check className="w-4 h-4 mr-2" />
                  <span className="text-sm font-medium">Selected Tier</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
