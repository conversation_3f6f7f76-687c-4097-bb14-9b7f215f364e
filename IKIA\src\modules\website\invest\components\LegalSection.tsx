import { Button } from "@/components/ui/button"
import { Download, FileText, Shield, Scale, CheckCircle, AlertTriangle, Users } from "lucide-react"

const guides = [
  {
    title: "INVESTOR GUIDE",
    description: "Comprehensive guide for investors interested in IKIA opportunities",
    icon: FileText,
    color: "from-[#7E2518] to-[#C86E36]",
    features: [
      "Due diligence framework",
      "Risk assessment tools",
      "Legal compliance checklist",
      "ROI calculation methods",
    ],
    downloadCount: "2,400+",
  },
  {
    title: "IK HOLDER GUIDE",
    description: "Essential information for Indigenous Knowledge holders and communities",
    icon: Shield,
    color: "from-[#159147] to-[#81B1DB]",
    features: [
      "IP protection strategies",
      "Community consent protocols",
      "Benefit-sharing agreements",
      "Cultural preservation guidelines",
    ],
    downloadCount: "1,800+",
  },
  {
    title: "IP PROTECTION GUIDE",
    description: "Legal framework and intellectual property protection guidelines",
    icon: Scale,
    color: "from-[#C86E36] to-[#E8B32C]",
    features: [
      "Patent application process",
      "Traditional knowledge documentation",
      "International law compliance",
      "Dispute resolution procedures",
    ],
    downloadCount: "3,200+",
  },
]

const legalHighlights = [
  {
    icon: CheckCircle,
    title: "100% Compliant",
    description: "All partnerships follow international IP law and community consent protocols",
  },
  {
    icon: Shield,
    title: "Protected Assets",
    description: "Traditional knowledge is legally protected with proper documentation and agreements",
  },
  {
    icon: Users,
    title: "Community First",
    description: "Indigenous communities maintain ownership and control over their knowledge assets",
  },
  {
    icon: AlertTriangle,
    title: "Risk Mitigation",
    description: "Comprehensive legal framework minimizes investment and cultural risks",
  },
]

export default function LegalSection() {
  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="font-myriad font-bold text-sm uppercase tracking-wider text-muted-foreground mb-4" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
            LEGAL FRAMEWORK
          </h2>
          <h3 className="font-myriad font-bold text-3xl lg:text-4xl text-primary mb-6" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
            Legal & IP Framework
          </h3>
          <p className="font-myriad text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
            Our comprehensive legal framework ensures that all Indigenous Knowledge assets are properly protected,
            documented, and shared with full consent and equitable benefit-sharing agreements.
          </p>
        </div>

        {/* Legal Highlights */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {legalHighlights.map((highlight, index) => (
            <div
              key={index}
              className="bg-white border border-gray-200 p-6 text-center hover:shadow-lg transition-all duration-300 group"
            >
              <div className="w-16 h-16 bg-gradient-to-br from-[#7E2518] to-[#C86E36] flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                <highlight.icon className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-bold text-[#7E2518] mb-2" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>{highlight.title}</h3>
              <p className="text-gray-600 text-sm leading-relaxed" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>{highlight.description}</p>
            </div>
          ))}
        </div>

        {/* Enhanced Section Header */}
        <div className="bg-gradient-to-r from-[#7E2518] to-[#C86E36] text-white p-8 mb-12 text-center">
          <h3 className="text-3xl font-bold mb-4" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>LEGAL & INTELLECTUAL PROPERTY PRE-REQUISITES</h3>
          <p className="text-lg opacity-90 max-w-2xl mx-auto" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
            Download our comprehensive guides to understand the legal framework, protection mechanisms, and compliance
            requirements for IKIA investments.
          </p>
        </div>

        {/* Enhanced Guide Cards */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {guides.map((guide, index) => (
            <div
              key={index}
              className="bg-white border border-gray-200 shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden group hover:-translate-y-2"
            >
              {/* Header with Gradient */}
              <div className={`bg-gradient-to-br ${guide.color} text-white p-8 text-center relative overflow-hidden`}>
                <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 -translate-y-16 translate-x-16"></div>
                <div className="relative z-10">
                  <guide.icon className="w-16 h-16 mx-auto mb-4 group-hover:scale-110 transition-transform duration-300" />
                  <h3 className="text-xl font-bold mb-2" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>{guide.title}</h3>
                  <p className="text-white/90 text-sm" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>{guide.description}</p>
                </div>
              </div>

              {/* Content */}
              <div className="p-8">
                {/* Features List */}
                <div className="mb-6">
                  <h4 className="font-bold text-gray-800 mb-4" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>What's Included:</h4>
                  <ul className="space-y-2">
                    {guide.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="text-sm text-gray-600 flex items-center" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                        <CheckCircle className="w-4 h-4 text-[#159147] mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Download Stats */}
                <div className="bg-gray-50 p-4 mb-6">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>Downloads:</span>
                    <span className="font-bold text-[#159147]" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>{guide.downloadCount}</span>
                  </div>
                  <div className="w-full bg-gray-200 h-2 mt-2">
                    <div className="bg-gradient-to-r from-[#159147] to-[#81B1DB] h-2 w-3/4"></div>
                  </div>
                </div>

                {/* Download Button */}
                <Button
                  className={`w-full bg-gradient-to-r ${guide.color} hover:opacity-90 text-white font-bold py-3 transition-all duration-300 group-hover:scale-105`}
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  <Download className="w-5 h-5 mr-2" />
                  View and Download
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Legal Compliance Section */}
        <div className="bg-white border border-gray-200 shadow-xl p-8 lg:p-12">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-bold text-[#7E2518] mb-6" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>Legal Compliance & Protection</h3>
              <div className="space-y-4 text-gray-700 leading-relaxed">
                <p style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                  Our comprehensive legal framework ensures that all Indigenous Knowledge assets are properly
                  protected, documented, and shared with full consent and benefit-sharing agreements.
                </p>
                <p style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                  We work closely with legal experts, community leaders, and international organizations to maintain
                  the highest standards of intellectual property protection while respecting traditional governance
                  systems.
                </p>
                <p style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                  Every partnership is built on principles of mutual respect, equitable benefit-sharing, and long-term
                  sustainability for both investors and Indigenous communities.
                </p>
              </div>

              <div className="mt-8 flex flex-col sm:flex-row gap-4">
                <Button className="bg-[#7E2518] hover:bg-[#6B1F14] text-white font-bold px-6 py-3" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>
                  Schedule Legal Consultation
                </Button>
                <Button
                  variant="outline"
                  className="border-[#7E2518] text-[#7E2518] hover:bg-[#7E2518] hover:text-white bg-transparent font-bold px-6 py-3"
                  style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}
                >
                  View Legal Framework
                </Button>
              </div>
            </div>

            <div className="relative">
              <div className="bg-gradient-to-br from-[#7E2518]/10 to-[#159147]/10 p-8">
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-[#7E2518] mb-2" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>100%</div>
                    <div className="text-sm text-gray-600" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>Legal Compliance</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-[#159147] mb-2" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>500+</div>
                    <div className="text-sm text-gray-600" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>Protected Assets</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-[#C86E36] mb-2" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>47</div>
                    <div className="text-sm text-gray-600" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>Counties Covered</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-[#E8B32C] mb-2" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>24/7</div>
                    <div className="text-sm text-gray-600" style={{ fontFamily: 'Myriad Pro, Arial, sans-serif' }}>Legal Support</div>
                  </div>
                </div>
              </div>

              {/* Decorative Elements */}
              <div className="absolute -top-4 -right-4 w-8 h-8 bg-[#E8B32C] opacity-60"></div>
              <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-[#159147] opacity-60"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
