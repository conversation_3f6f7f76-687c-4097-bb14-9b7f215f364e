import { Users, FileText, TrendingUp, Map } from 'lucide-react'
import Image from 'next/image'

const stats = [
  {
    number: '13+',
    label: 'Counties Involved',
    subtitle: 'Across Kenya',
    icon: Map,
  },
  {
    number: '40+',
    label: 'IKIA Assets',
    subtitle: 'Fully Documented',
    icon: FileText,
  },
  {
    number: '1000+',
    label: 'Expected Attendees',
    subtitle: 'Local + Global',
    icon: Users,
  },
  {
    number: '25+',
    label: 'Investment Opportunities',
    subtitle: 'Across 5 Sectors',
    icon: TrendingUp,
  },
]

// Iconography categories representing thematic areas
const categories = [
  {
    name: 'Traditional Medicine for One-Health',
    image: '/assets/iconography/traditional-medicine-revised.png',
  },
  {
    name: 'Heritage Sites and Cultural Tourism',
    image: '/assets/iconography/heritage-sites-revised.png',
  },
  {
    name: 'Indigenous  Technologies for Industrialization',
    image: '/assets/iconography/traditional-technology-revised.png',
  },
  {
    name: 'Traditional Foods and Local Cuisines for Enhanced Nutrition',
    image: '/assets/iconography/traditional-foods-and-cuisines.png',
  },
  {
    name: 'Performing Arts and Creative Economy',
    image: '/assets/iconography/musicology-icon-revised.png',
  },
  {
    name: (
      <>
        Kenya's Indeginous Knowledge Intellectual Assets <i>sui generis</i> protection System
      </>
    ),
    image: '/assets/iconography/knowledge-systems-revised.png',
  },

  // {
  //   name: 'Indeginous Knowledge sui generis protection System',
  //   image: '/assets/iconography/knowledge-systems-revised.png',
  // },
]

export default function StatsSection() {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Category Icons */}
        <div className="flex justify-between items-center mb-20 px-4 sm:px-8 md:px-12 lg:px-16">
          {categories.map((category, index) => (
            <div
              key={index}
              className="flex flex-col items-center w-32 sm:w-28 md:w-32 lg:w-36 cursor-pointer group"
              title={category.name}
            >
              <div className="relative w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 group-hover:scale-110 transition-transform duration-300">
                <Image src={category.image} alt={category.name} fill className="object-contain" />
              </div>
              <p className="mt-2 text-center text-sm font-medium text-gray-700">{category.name}</p>
            </div>
          ))}
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="text-center p-6 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow"
            >
              <div className="mb-4 flex justify-center">
                <stat.icon className="w-12 h-12 text-[#7E2518]" />
              </div>
              <div className="text-3xl md:text-4xl font-bold text-[#7E2518] mb-2">
                {stat.number}
              </div>
              <div className="text-lg font-semibold text-gray-800 mb-1">{stat.label}</div>
              <div className="text-sm text-gray-600">{stat.subtitle}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
