'use client'

import Image from 'next/image'
import Link from 'next/link'

interface IkiaLogoProps {
  className?: string
}

export const Ikia<PERSON>ogo: React.FC<IkiaLogoProps> = ({ className = '' }) => {
  return (
    <Link
      href="/"
      className={`flex items-center hover:opacity-90 transition-opacity duration-200 ${className}`}
    >
      <div className="relative h-10 sm:h-12 w-auto">
        <Image
          src="/assets/iconography/logo-horizontal-no-bg.png"
          alt="IKIA Conference 2025 Logo"
          width={200}
          height={48}
          className="h-10 sm:h-12 w-auto object-contain max-w-[150px] sm:max-w-[200px]"
          priority
        />
      </div>
    </Link>
  )
}
