export interface SponsorshipPackage {
  id: string
  name: string
  price: string
  category: 'financial' | 'service' | 'media' | 'special'
  tier?: 'title' | 'platinum' | 'gold' | 'silver' | 'bronze'
  maxPartners: number
  currentPartners: number
  description: string
  benefits: string[]
  isInKind?: boolean
  featured?: boolean
}

export interface Sponsor {
  id: string
  name: string
  logo: string
  description: string
  website?: string
  packageId: string
  category: 'financial' | 'service' | 'media' | 'special'
  tier?: 'title' | 'platinum' | 'gold' | 'silver' | 'bronze'
  hasDetailsPage?: boolean
  additionalLogos?: {
    name: string
    logo: string
  }[]
}

export interface Partner {
  id: string
  name: string
  logo: string
  description: string
  website?: string
  type: 'strategic' | 'implementation' | 'media' | 'community'
  featured?: boolean
}

export type FilterType = 'all' | 'financial' | 'service' | 'media' | 'special'
export type TierFilter = 'all' | 'title' | 'platinum' | 'gold' | 'silver' | 'bronze'
export type PartnerFilter = 'all' | 'strategic' | 'implementation' | 'media' | 'community'
