import { MapPin, Navigation } from 'lucide-react'

export default function VenueMap() {
  return (
    <section className="max-w-6xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-[#7E2518] mb-4">Venue Site Map</h2>
        <p className="text-gray-600">Murang&apos;a County</p>
      </div>

      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="grid lg:grid-cols-2 gap-0">
          {/* Map Placeholder */}
          <div className="bg-gray-100 p-8 flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="w-32 h-32 border-4 border-[#7E2518] rounded-lg mx-auto mb-4 flex items-center justify-center">
                <MapPin className="w-12 h-12 text-[#7E2518]" />
              </div>
              <p className="text-[#7E2518] font-medium">Interactive Venue Map</p>
              <p className="text-sm text-gray-600 mt-2">Coming Soon</p>
            </div>
          </div>

          {/* Know Your Area */}
          <div className="p-8">
            <div className="flex items-center mb-4">
              <Navigation className="w-6 h-6 text-[#159147] mr-3" />
              <h3 className="text-2xl font-bold text-[#7E2518]">Know Your Area</h3>
            </div>
            <p className="text-gray-600 leading-relaxed mb-6">
              Know Your Area is an interactive site map designed to help conference attendees easily
              navigate the venue and its surroundings. It highlights key locations such as session
              halls, registration points, restrooms, dining areas, emergency exits, and nearby
              amenities like hotels, transport hubs, and restaurants.
            </p>
            <p className="text-gray-600 leading-relaxed">
              Whether you&apos;re attending a keynote or networking event, this tool ensures you
              stay oriented and make the most of your conference experience.
            </p>
            <div className="mt-6 p-4 bg-[#159147]/10 rounded-lg border border-[#159147]/20">
              <p className="text-[#159147] font-medium text-sm">
                📍 Located in the heart of Murang&apos;a County, easily accessible via major
                transport routes
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
