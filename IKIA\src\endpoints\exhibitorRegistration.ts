import type { PayloadRequest } from 'payload'

interface ExhibitorRegistrationData {
  // Personal Information
  firstName: string
  lastName: string
  email: string
  phone: string
  position?: string
  country: string
  city?: string

  // Company Information
  hasCompany?: boolean
  companyName?: string
  website?: string
  address?: string
  businessType?: string
  businessDescription?: string
  productsServices?: string
  targetMarket?: string
  yearsInBusiness?: string

  // Exhibition Details
  selectedPackage: string
  boothRequirement?: string
  additionalServices?: string[]
  specialRequirements?: string

  // Additional Representatives
  hasAdditionalReps?: boolean
  additionalRepresentatives?: Array<{
    name: string
    email: string
    phone: string
    position?: string
  }>

  // Agreements
  termsAccepted: boolean
  exhibitorGuidelines: boolean
  mediaConsent?: boolean
}

/**
 * Exhibitor Registration Endpoint
 * POST /api/exhibitors/register
 */
export const exhibitorRegistrationEndpoint = async (req: PayloadRequest): Promise<Response> => {
  try {
    console.log('🏢 Exhibitor registration request received:', {
      headers: req.headers,
      timestamp: new Date().toISOString(),
    })

    const registrationData: ExhibitorRegistrationData = req.json || {}

    console.log('Processing exhibitor registration for:', {
      email: registrationData.email,
      companyName: registrationData.companyName,
      selectedPackage: registrationData.selectedPackage,
    })

    // Validate required fields
    if (!registrationData.firstName || !registrationData.lastName) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'First name and last name are required',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    if (!registrationData.email) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Email address is required',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    if (!registrationData.phone) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Phone number is required',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    if (!registrationData.country) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Country is required',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    if (!registrationData.selectedPackage) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Service package selection is required',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    if (!registrationData.termsAccepted || !registrationData.exhibitorGuidelines) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Terms and conditions and exhibitor guidelines must be accepted',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Check if exhibitor already exists
    const existingExhibitor = await req.payload.find({
      collection: 'exhibitors',
      where: {
        email: { equals: registrationData.email },
      },
    })

    if (existingExhibitor.docs.length > 0) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'An exhibitor with this email address already exists',
        }),
        {
          status: 409,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Validate selected delegate package
    const delegatePackage = await req.payload.findByID({
      collection: 'delegatepackages',
      id: registrationData.selectedPackage,
    })

    if (!delegatePackage || !delegatePackage.isActive) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid or inactive delegate package selected',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    console.log('Selected package validated:', {
      id: delegatePackage.id,
      name: delegatePackage.name,
      price: delegatePackage.price,
      currency: delegatePackage.currency,
    })

    // Process additional services
    const additionalServicesData =
      registrationData.additionalServices?.map((service) => ({ service })) || []

    // Create exhibitor record
    const newExhibitor = await req.payload.create({
      collection: 'exhibitors',
      data: {
        // Personal Information
        firstName: registrationData.firstName,
        lastName: registrationData.lastName,
        email: registrationData.email,
        phone: registrationData.phone,
        position: registrationData.position || '',
        country: registrationData.country,
        city: registrationData.city || '',

        // Company Information
        hasCompany: registrationData.hasCompany || false,
        companyName: registrationData.companyName || '',
        website: registrationData.website || '',
        address: registrationData.address || '',
        businessType: registrationData.businessType || '',
        businessDescription: registrationData.businessDescription || '',
        productsServices: registrationData.productsServices || '',
        targetMarket: registrationData.targetMarket || '',
        yearsInBusiness: registrationData.yearsInBusiness || '',

        // Exhibition Details
        selectedPackage: registrationData.selectedPackage,
        boothRequirement: registrationData.boothRequirement || '',
        additionalServices: additionalServicesData,
        specialRequirements: registrationData.specialRequirements || '',

        // Additional Representatives
        hasAdditionalReps: registrationData.hasAdditionalReps || false,
        additionalRepresentatives: registrationData.additionalRepresentatives || [],

        // Status and Metadata
        registrationStatus: 'pending_payment',
        registrationDate: new Date(),

        // Agreements
        termsAccepted: registrationData.termsAccepted,
        exhibitorGuidelines: registrationData.exhibitorGuidelines,
        mediaConsent: registrationData.mediaConsent || false,
      },
    })

    console.log('Exhibitor created successfully:', {
      id: newExhibitor.id,
      email: newExhibitor.email,
      companyName: newExhibitor.companyName,
    })

    // Create invoice for the selected package
    const invoiceNumber = `EXH-${Date.now()}`
    const newInvoice = await req.payload.create({
      collection: 'invoices',
      data: {
        invoice_number: invoiceNumber,
        user: null, // Exhibitors don't have user accounts initially
        package: registrationData.selectedPackage,
        amount: delegatePackage.price,
        currency: delegatePackage.currency,
        status: 'pending',
        due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        payment_summary: {
          package_name: delegatePackage.name,
          package_price: delegatePackage.price,
          currency: delegatePackage.currency,
          registration_type: 'Exhibitor Registration',
        },
        registration_context: {
          type: 'exhibitor',
          exhibitor_id: newExhibitor.id,
          company_name:
            registrationData.companyName ||
            `${registrationData.firstName} ${registrationData.lastName}`,
          contact_email: registrationData.email,
          contact_phone: registrationData.phone,
        },
      },
    })

    console.log('Invoice created successfully:', {
      id: newInvoice.id,
      invoice_number: newInvoice.invoice_number,
      amount: newInvoice.amount,
      currency: newInvoice.currency,
    })

    // Get callback URLs from environment variables
    const {
      PESAFLOW_NOTIFICATION_URL: notificationURL,
      PESAFLOW_CALLBACK_SUCCESS_URL: successURL,
      PESAFLOW_CALLBACK_CANCEL_URL: cancelURL,
    } = process.env

    // Validate required callback URLs
    if (!notificationURL || !successURL || !cancelURL) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing callback URL configuration',
          missing: [
            !notificationURL && 'PESAFLOW_NOTIFICATION_URL',
            !successURL && 'PESAFLOW_CALLBACK_SUCCESS_URL',
            !cancelURL && 'PESAFLOW_CALLBACK_CANCEL_URL',
          ].filter(Boolean),
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    // Generate payment checkout URL
    const checkoutParams = new URLSearchParams({
      client_invoice_ref: newInvoice.id.toString(),
      invoice_number: invoiceNumber,
      amount: delegatePackage.price.toString(),
      currency: delegatePackage.currency,
      customer_email: registrationData.email,
      customer_phone: registrationData.phone,
      customer_name: `${registrationData.firstName} ${registrationData.lastName}`,
      description: `${delegatePackage.name} - Exhibitor Registration`,
      notificationURL: notificationURL,
      returnURL: `${successURL}&invoice=${newInvoice.id}`,
      cancelURL: `${cancelURL}&invoice=${newInvoice.id}`,
    })

    // Use UAT server URL from environment
    const pesaflowBaseURL = process.env.PESAFLOW_UAT_SERVER_URL
    if (!pesaflowBaseURL) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'PESAFLOW_UAT_SERVER_URL not configured',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        },
      )
    }

    const checkoutUrl = `${pesaflowBaseURL}/api/PaymentAPI/checkout?${checkoutParams.toString()}`

    console.log('Exhibitor registration workflow completed successfully:', {
      exhibitor_id: newExhibitor.id,
      invoice_id: newInvoice.id,
      checkout_url: checkoutUrl,
    })

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Exhibitor registration successful',
        data: {
          exhibitor: {
            id: newExhibitor.id,
            email: newExhibitor.email,
            companyName: newExhibitor.companyName,
            registrationStatus: newExhibitor.registrationStatus,
          },
          invoice: {
            id: newInvoice.id,
            invoice_number: newInvoice.invoice_number,
            amount: newInvoice.amount,
            currency: newInvoice.currency,
            due_date: newInvoice.due_date,
          },
          package: {
            id: delegatePackage.id,
            name: delegatePackage.name,
            price: delegatePackage.price,
            currency: delegatePackage.currency,
          },
        },
        checkout_url: checkoutUrl,
      }),
      {
        status: 201,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  } catch (error: any) {
    console.error('❌ Exhibitor registration error:', error)

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Exhibitor registration failed',
        message: error.message,
        timestamp: new Date().toISOString(),
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    )
  }
}
