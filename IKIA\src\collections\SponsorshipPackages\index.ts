import type { CollectionConfig } from 'payload'
import { authenticated } from '../../access/authenticated'
import { anyone } from '../../access/anyone'
import { slugField } from '@/fields/slug'

export const SponsorshipPackages: CollectionConfig = {
  slug: 'sponsorship-packages',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'tier', 'price', 'maxPartners', 'currentPartners'],
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Package Name',
    },
    {
      name: 'price',
      type: 'text',
      required: true,
      label: 'Price',
      admin: {
        description: 'e.g., "KES 10,000,000" or "In-Kind Contribution"',
      },
    },
    {
      name: 'priceAmount',
      type: 'number',
      label: 'Price Amount (KES)',
      admin: {
        description: 'Numeric value for sorting and calculations. Leave empty for in-kind packages.',
      },
    },
    {
      name: 'tier',
      type: 'select',
      label: 'Sponsorship Tier',
      options: [
        { label: 'Title Sponsor', value: 'title' },
        { label: 'Platinum', value: 'platinum' },
        { label: 'Gold', value: 'gold' },
        { label: 'Silver', value: 'silver' },
        { label: 'Bronze', value: 'bronze' },
        { label: 'Special Package', value: 'special' },
      ],
    },
    {
      name: 'category',
      type: 'select',
      label: 'Package Category',
      required: true,
      options: [
        { label: 'Financial Sponsorship', value: 'financial' },
        { label: 'Service Partnership', value: 'service' },
        { label: 'Media Partnership', value: 'media' },
        { label: 'Special Package', value: 'special' },
      ],
    },
    {
      name: 'maxPartners',
      type: 'number',
      required: true,
      label: 'Maximum Partners',
      defaultValue: 1,
      admin: {
        description: 'Maximum number of sponsors allowed for this package',
      },
    },
    {
      name: 'currentPartners',
      type: 'number',
      label: 'Current Partners',
      defaultValue: 0,
      admin: {
        description: 'Current number of sponsors in this package (auto-calculated)',
        readOnly: true,
      },
    },
    {
      name: 'description',
      type: 'richText',
      label: 'Package Description',
      admin: {
        description: 'Detailed description of the sponsorship package',
      },
    },
    {
      name: 'benefits',
      type: 'array',
      label: 'Package Benefits',
      fields: [
        {
          name: 'benefit',
          type: 'text',
          required: true,
          label: 'Benefit Description',
        },
      ],
    },
    {
      name: 'isInKind',
      type: 'checkbox',
      label: 'In-Kind Contribution',
      defaultValue: false,
      admin: {
        description: 'Check if this is an in-kind contribution rather than financial',
      },
    },
    {
      name: 'featured',
      type: 'checkbox',
      label: 'Featured Package',
      defaultValue: false,
      admin: {
        description: 'Mark as featured to highlight in package listings',
      },
    },
    {
      name: 'available',
      type: 'checkbox',
      label: 'Available for Purchase',
      defaultValue: true,
      admin: {
        description: 'Uncheck to hide from public listings (sold out or discontinued)',
      },
    },
    {
      name: 'prospectusDocument',
      type: 'upload',
      relationTo: 'media',
      label: 'Sponsorship Prospectus',
      admin: {
        description: 'Upload the detailed sponsorship prospectus document (PDF)',
      },
      filterOptions: {
        mimeType: { contains: 'pdf' },
      },
    },
    {
      name: 'displayOrder',
      type: 'number',
      label: 'Display Order',
      admin: {
        description: 'Lower numbers appear first. Leave empty for automatic ordering by price.',
      },
    },
    ...slugField(),
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Auto-generate slug if not provided
        if (!data.slug && data.name) {
          data.slug = data.name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '')
        }
        return data
      },
    ],
  },
}

export default SponsorshipPackages
