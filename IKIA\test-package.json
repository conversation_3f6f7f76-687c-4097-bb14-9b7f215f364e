{"name": "payment-workflow-test", "version": "1.0.0", "description": "Complete payment workflow test for IKIA payment system", "main": "test-payment-workflow.js", "scripts": {"test": "node test-payment-workflow.js", "test:verbose": "DEBUG=* node test-payment-workflow.js", "verify-db": "echo 'Run the SQL queries in verify-payment-relationships.sql against your database'"}, "dependencies": {"node-fetch": "^2.6.7"}, "devDependencies": {}, "keywords": ["payment", "workflow", "pesaflow", "test", "ikia"], "author": "IKIA Development Team", "license": "MIT"}