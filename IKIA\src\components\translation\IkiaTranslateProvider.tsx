'use client'

import React from 'react'
import { GoogleTranslateProvider } from 'react-custom-google-translate'

interface IkiaTranslateProviderProps {
  children: React.ReactNode
}

export const IkiaTranslateProvider: React.FC<IkiaTranslateProviderProps> = ({ children }) => {
  // Define IKIA-specific languages for the conference
  const ikiaLanguages = [
    { value: 'en|en', label: 'English' },
    { value: 'en|sw', label: 'Kiswahili' },
    { value: 'en|fr', label: 'Français' },
    { value: 'en|ar', label: 'العربية' },
    { value: 'en|es', label: 'Español' },
    { value: 'en|pt', label: 'Português' },
    { value: 'en|de', label: 'Deutsch' },
    { value: 'en|it', label: 'Italiano' },
    { value: 'en|zh', label: '中文' },
    { value: 'en|ja', label: '日本語' },
    { value: 'en|ko', label: '한국어' },
    { value: 'en|hi', label: 'हिंदी' },
    { value: 'en|ur', label: 'اردو' },
    { value: 'en|am', label: 'አማርኛ' },
    { value: 'en|so', label: 'Soomaali' },
    { value: 'en|ha', label: 'Hausa' },
  ]

  return (
    <GoogleTranslateProvider
      pageLanguage="en"
      availableLanguages={ikiaLanguages}
    >
      {children}
    </GoogleTranslateProvider>
  )
}
