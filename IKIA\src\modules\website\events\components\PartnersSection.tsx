'use client'

const partners = [
  {
    name: 'Republic of Kenya',
    logo: '/images/kenya-coat-of-arms.png',
    description: 'Government of Kenya',
  },
  {
    name: 'National Museum of Kenya',
    logo: '/images/national-museum-kenya.png',
    description: 'Cultural Heritage Institution',
  },
  {
    name: 'National Policy Institute',
    logo: '/images/npi-logo.png',
    description: 'Policy Research & Development',
  },
  {
    name: 'Kenya Vision 2030',
    logo: '/images/kenya-vision-2030.png',
    description: 'National Development Blueprint',
  },
  {
    name: 'Council of Governors',
    logo: '/images/cog-logo.png',
    description: 'County Government Leadership',
  },
]

export default function PartnersSection() {
  return (
    <section className="py-20 bg-gray-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-32 h-32 border-2 border-[#7E2518] transform rotate-45"></div>
        <div className="absolute bottom-10 right-10 w-24 h-24 border-2 border-[#159147] transform rotate-12"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 border-2 border-[#E8B32C] transform -rotate-12"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-[#7E2518] mb-4">
            Our Strategic Partners
          </h2>
          <div className="w-24 h-1 bg-[#E8B32C] mx-auto mb-6"></div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Collaborating with leading institutions to advance Indigenous Knowledge and Innovation
            Assets across Kenya
          </p>
        </div>

        {/* Premium Partners Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-10 items-center justify-items-center">
          {partners.map((partner, index) => (
            <div key={index} className="group relative cursor-pointer">
              {/* Outer Decorative Ring */}
              <div className="absolute -inset-3 border-2 border-[#E8B32C] opacity-30 group-hover:opacity-100 group-hover:border-[#7E2518] transition-all duration-700 group-hover:rotate-180 rounded-full"></div>

              {/* Middle Ring */}
              <div className="absolute -inset-1 border border-[#C86E36] opacity-20 group-hover:opacity-60 transition-all duration-500 group-hover:-rotate-90 rounded-full"></div>

              {/* Main Circle Container */}
              <div className="relative bg-white shadow-xl group-hover:shadow-2xl transition-all duration-500 group-hover:-translate-y-4 border-4 border-white group-hover:border-[#7E2518] overflow-hidden rounded-full w-[180px] h-[180px]">
                {/* Logo Display (Default State) */}
                <div className="absolute inset-0 flex items-center justify-center p-6 group-hover:opacity-0 transition-opacity duration-300">
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={partner.logo || '/placeholder.svg'}
                    alt={partner.name}
                    className="max-w-full max-h-full object-contain transition-all duration-500"
                    style={{
                      maxWidth: '85%',
                      maxHeight: '85%',
                      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))',
                    }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      target.src =
                        '/placeholder.svg?height=100&width=100&text=' +
                        encodeURIComponent(partner.name)
                    }}
                  />
                </div>

                {/* Hover Overlay (Information State) */}
                <div className="absolute inset-0 bg-[#7E2518] opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center text-center p-6 rounded-full">
                  <div className="text-white">
                    {/* Decorative Icon */}
                    <div className="mb-4">
                      <div className="w-8 h-8 border-2 border-[#E8B32C] mx-auto flex items-center justify-center rounded-full">
                        <div className="w-2 h-2 bg-[#E8B32C] rounded-full"></div>
                      </div>
                    </div>

                    {/* Partner Name */}
                    <h3 className="font-bold text-sm mb-3 leading-tight text-white">
                      {partner.name}
                    </h3>

                    {/* Divider */}
                    <div className="w-12 h-0.5 bg-[#E8B32C] mx-auto mb-3"></div>

                    {/* Description */}
                    <p className="text-xs text-[#E8B32C] leading-tight font-medium">
                      {partner.description}
                    </p>
                  </div>
                </div>
              </div>

              {/* Floating Glow Effect */}
              <div className="absolute inset-0 bg-[#7E2518] opacity-0 group-hover:opacity-5 blur-2xl transition-all duration-700 scale-150 pointer-events-none rounded-full"></div>
            </div>
          ))}
        </div>

        {/* Partnership Statement */}
        <div className="mt-20 text-center">
          <div className="bg-[#7E2518] text-white py-10 px-12 max-w-4xl mx-auto shadow-2xl">
            <h3 className="text-2xl font-bold mb-4">Partnership Excellence</h3>
            <p className="text-lg leading-relaxed text-[#E8B32C]">
              Together, we are building a sustainable future that honors Indigenous wisdom while
              fostering innovation and economic growth across Kenya&apos;s 47 counties.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
