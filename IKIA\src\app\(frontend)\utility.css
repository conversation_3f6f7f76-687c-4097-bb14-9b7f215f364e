/* IKIA Registration Form Utility Styles */

/* Shadow Effects */
.styles {
  --main-shadow: 0px 4px 20px 2px rgba(126, 37, 24, 0.08);
  --card-shadow: 0px 2px 12px 1px rgba(126, 37, 24, 0.06);
  --hover-shadow: 0px 8px 32px 4px rgba(126, 37, 24, 0.12);
  --focus-shadow: 0px 0px 0px 3px rgba(21, 145, 71, 0.2);
}

.main-shadow {
  box-shadow: var(--main-shadow);
  transition: box-shadow 0.3s ease;
}

.card-shadow {
  box-shadow: var(--card-shadow);
  transition: box-shadow 0.3s ease;
}

.hover-shadow:hover {
  box-shadow: var(--hover-shadow);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

.focus-shadow:focus-within {
  box-shadow: var(--focus-shadow);
}

/* Typography */
.font-myriad {
  font-family: 'Myriad Pro', 'Inter', 'system-ui', 'sans-serif';
}

/* IKIA Brand Colors */
.text-ikia-brown {
  color: #7E2518;
}

.text-ikia-green {
  color: #159147;
}

.text-ikia-yellow {
  color: #E8B32C;
}

.bg-ikia-brown {
  background-color: #7E2518;
}

.bg-ikia-green {
  background-color: #159147;
}

.bg-ikia-yellow {
  background-color: #E8B32C;
}

/* Form Enhancements */
.form-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(126, 37, 24, 0.1);
  border-radius: 0;
}

.form-input {
  border: 2px solid rgba(126, 37, 24, 0.1);
  border-radius: 0;
  transition: all 0.3s ease;
  font-family: 'Myriad Pro', 'Inter', 'system-ui', 'sans-serif';
}

.form-input:focus {
  border-color: #7E2518;
  box-shadow: 0px 0px 0px 3px rgba(126, 37, 24, 0.2);
  outline: none;
}

.form-button {
  background: linear-gradient(135deg, #7E2518 0%, #6B1F14 100%);
  color: white;
  border: none;
  border-radius: 0;
  padding: 12px 24px;
  font-family: 'Myriad Pro', 'Inter', 'system-ui', 'sans-serif';
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0px 4px 12px rgba(126, 37, 24, 0.3);
}

.form-button:hover {
  background: linear-gradient(135deg, #6B1F14 0%, #5A1A11 100%);
  transform: translateY(-2px);
  box-shadow: 0px 8px 20px rgba(126, 37, 24, 0.4);
}

.form-button:active {
  transform: translateY(0px);
  box-shadow: 0px 2px 8px rgba(126, 37, 24, 0.3);
}

/* Step Indicator Enhancements */
.step-indicator {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(126, 37, 24, 0.1);
  border-radius: 0;
  padding: 16px;
}

/* Heritage Elements */
.heritage-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.heritage-dot-primary {
  background-color: rgba(126, 37, 24, 0.6);
}

.heritage-dot-secondary {
  background-color: rgba(21, 145, 71, 0.6);
}

.heritage-dot-accent {
  background-color: rgba(232, 179, 44, 0.6);
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}
