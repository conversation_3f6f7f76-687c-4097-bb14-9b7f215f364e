# County-User Filters - Quick Reference

## 🔍 **Filter Users by County**

Use the existing `/api/users` endpoint with query filters to get users by county relationship.

## 📋 **Basic County Filters**

### **Users in Specific County**
```bash
# Get users in county ID 1
curl "http://localhost:3000/api/users?where[county][equals]=1"

# Get users in county ID 1 with county data populated
curl "http://localhost:3000/api/users?where[county][equals]=1&depth=1"

# Get users in county ID 1 with pagination
curl "http://localhost:3000/api/users?where[county][equals]=1&limit=10&page=1"
```

### **Users in Multiple Counties**
```bash
# Get users in counties 1, 2, or 3
curl "http://localhost:3000/api/users?where[county][in]=1,2,3"

# With county data populated
curl "http://localhost:3000/api/users?where[county][in]=1,2,3&depth=1"
```

### **Users Without County**
```bash
# Get users who don't have a county assigned
curl "http://localhost:3000/api/users?where[county][exists]=false"

# Get users who have any county assigned
curl "http://localhost:3000/api/users?where[county][exists]=true"
```

## 🔍 **Advanced County Filters**

### **Filter by County Properties**
```bash
# Users in counties named "Nairobi" (requires depth=1)
curl "http://localhost:3000/api/users?where[county.name][equals]=Nairobi&depth=1"

# Users in counties with code "KE-047"
curl "http://localhost:3000/api/users?where[county.code][equals]=KE-047&depth=1"

# Users in active counties only
curl "http://localhost:3000/api/users?where[county.isActive][equals]=true&depth=1"

# Users in counties containing "Nai" in name
curl "http://localhost:3000/api/users?where[county.name][contains]=Nai&depth=1"
```

### **Combined Filters**
```bash
# Users in county 1 with name containing "John"
curl "http://localhost:3000/api/users?where[county][equals]=1&where[name][contains]=John"

# Users in county 1, sorted by name
curl "http://localhost:3000/api/users?where[county][equals]=1&sort=name"

# Users in county 1, only return specific fields
curl "http://localhost:3000/api/users?where[county][equals]=1&select=name,email,county"

# Users in active counties, created in the last 30 days
curl "http://localhost:3000/api/users?where[county.isActive][equals]=true&where[createdAt][greater_than]=2024-01-01&depth=1"
```

## 📊 **Response Examples**

### **With County Data (depth=1)**
```json
{
  "docs": [
    {
      "id": "1",
      "name": "John Doe",
      "email": "<EMAIL>",
      "county": {
        "id": "1",
        "name": "Nairobi",
        "code": "KE-047",
        "coordinates": {
          "latitude": -1.2921,
          "longitude": 36.8219
        },
        "description": "Kenya's capital city",
        "isActive": true
      },
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "totalDocs": 1,
  "limit": 10,
  "totalPages": 1,
  "page": 1,
  "hasNextPage": false,
  "hasPrevPage": false
}
```

### **Without County Data (depth=0)**
```json
{
  "docs": [
    {
      "id": "1",
      "name": "John Doe",
      "email": "<EMAIL>",
      "county": "1",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "totalDocs": 1
}
```

## 🧮 **Statistics and Analytics**

### **Count Users per County**
```bash
# Get user count for specific county
curl -s "http://localhost:3000/api/users?where[county][equals]=1" | jq '.totalDocs'

# Get users and county info together
curl -s "http://localhost:3000/api/users?where[county][equals]=1&depth=1" | jq '{
  countyName: .docs[0].county.name,
  countyCode: .docs[0].county.code,
  userCount: .totalDocs,
  users: [.docs[].name]
}'
```

### **Users by County Status**
```bash
# Count users in active counties
curl -s "http://localhost:3000/api/users?where[county.isActive][equals]=true&depth=1" | jq '.totalDocs'

# Count users without county
curl -s "http://localhost:3000/api/users?where[county][exists]=false" | jq '.totalDocs'

# Count users with county
curl -s "http://localhost:3000/api/users?where[county][exists]=true" | jq '.totalDocs'
```

## 🔄 **Practical Examples**

### **Get All Users in Nairobi**
```bash
# Method 1: By county ID (if you know it)
curl "http://localhost:3000/api/users?where[county][equals]=1&depth=1"

# Method 2: By county name
curl "http://localhost:3000/api/users?where[county.name][equals]=Nairobi&depth=1"

# Method 3: By county code
curl "http://localhost:3000/api/users?where[county.code][equals]=KE-047&depth=1"
```

### **Get Users for Admin Dashboard**
```bash
# Get users with county info for admin dashboard
curl "http://localhost:3000/api/users?depth=1&limit=20&sort=-createdAt" | jq '.docs[] | {
  id,
  name,
  email,
  county: .county.name,
  countyCode: .county.code,
  createdAt
}'
```

### **Export Users by County**
```bash
# Export all users in county 1 to CSV format
curl -s "http://localhost:3000/api/users?where[county][equals]=1&depth=1&limit=1000" | \
jq -r '.docs[] | [.name, .email, .county.name, .county.code] | @csv'
```

## 🔧 **URL Encoding**

For complex queries, you might need to URL encode the parameters:

```bash
# Instead of spaces, use %20
curl "http://localhost:3000/api/users?where[county.name][equals]=New%20County"

# For special characters, use proper encoding
curl "http://localhost:3000/api/users?where[county.description][contains]=Kenya%27s%20capital"
```

## 📝 **Filter Operators Reference**

| Operator | Usage | Example |
|----------|-------|---------|
| `equals` | Exact match | `where[county][equals]=1` |
| `not_equals` | Not equal | `where[county][not_equals]=1` |
| `in` | In array | `where[county][in]=1,2,3` |
| `not_in` | Not in array | `where[county][not_in]=1,2,3` |
| `exists` | Field exists | `where[county][exists]=true` |
| `contains` | Contains text | `where[county.name][contains]=Nai` |
| `greater_than` | Greater than | `where[county][greater_than]=5` |
| `less_than` | Less than | `where[county][less_than]=10` |

## 🚀 **Performance Tips**

1. **Use depth wisely**: Only use `depth=1` when you need county data
2. **Limit results**: Always use reasonable `limit` values
3. **Index considerations**: County relationships are automatically indexed
4. **Pagination**: Use pagination for large result sets

## 🧪 **Testing Script**

```bash
#!/bin/bash

echo "Testing County-User Filters"

# Test 1: Users in specific county
echo "1. Users in county 1:"
curl -s "http://localhost:3000/api/users?where[county][equals]=1" | jq '.totalDocs'

# Test 2: Users without county
echo "2. Users without county:"
curl -s "http://localhost:3000/api/users?where[county][exists]=false" | jq '.totalDocs'

# Test 3: Users in multiple counties
echo "3. Users in counties 1,2,3:"
curl -s "http://localhost:3000/api/users?where[county][in]=1,2,3" | jq '.totalDocs'

# Test 4: Users in active counties
echo "4. Users in active counties:"
curl -s "http://localhost:3000/api/users?where[county.isActive][equals]=true&depth=1" | jq '.totalDocs'
```

Using filters on the existing users endpoint provides flexible and powerful querying capabilities for county-user relationships! 🎉
